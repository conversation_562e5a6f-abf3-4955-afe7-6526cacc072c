/**
 * URL参数加密工具
 * 用于保护URL中的敏感标识码，避免直接暴露风格ID和尺寸ID
 */

// 简单的Base64编码表，用于混淆
const CUSTOM_BASE64_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';

// 简单的替换映射，用于进一步混淆
const CHAR_MAP = {
  'a': 'x', 'b': 'y', 'c': 'z', 'd': 'a', 'e': 'b', 'f': 'c', 'g': 'd', 'h': 'e', 'i': 'f', 'j': 'g',
  'k': 'h', 'l': 'i', 'm': 'j', 'n': 'k', 'o': 'l', 'p': 'm', 'q': 'n', 'r': 'o', 's': 'p', 't': 'q',
  'u': 'r', 'v': 's', 'w': 't', 'x': 'u', 'y': 'v', 'z': 'w', 'A': 'X', 'B': 'Y', 'C': 'Z', 'D': 'A',
  'E': 'B', 'F': 'C', 'G': 'D', 'H': 'E', 'I': 'F', 'J': 'G', 'K': 'H', 'L': 'I', 'M': 'J', 'N': 'K',
  'O': 'L', 'P': 'M', 'Q': 'N', 'R': 'O', 'S': 'P', 'T': 'Q', 'U': 'R', 'V': 'S', 'W': 'T', 'X': 'U',
  'Y': 'V', 'Z': 'W', '0': '5', '1': '6', '2': '7', '3': '8', '4': '9', '5': '0', '6': '1', '7': '2',
  '8': '3', '9': '4'
};

// 反向映射
const REVERSE_CHAR_MAP = {};
Object.keys(CHAR_MAP).forEach(key => {
  REVERSE_CHAR_MAP[CHAR_MAP[key]] = key;
});

/**
 * 简单的字符串加密函数
 * @param {string} str - 需要加密的字符串
 * @returns {string} - 加密后的字符串
 */
const simpleEncrypt = (str) => {
  if (!str) return '';

  try {
    // 1. 转换为Base64
    const base64 = btoa(unescape(encodeURIComponent(str)));

    // 2. 字符替换混淆
    let encrypted = '';
    for (let i = 0; i < base64.length; i++) {
      const char = base64[i];
      encrypted += CHAR_MAP[char] || char;
    }

    // 3. 添加简单的前缀和后缀标识
    return `s${encrypted}e`;
  } catch (error) {
    console.warn('加密失败，返回原始值:', error);
    return str;
  }
};

/**
 * 简单的字符串解密函数
 * @param {string} encryptedStr - 需要解密的字符串
 * @returns {string} - 解密后的字符串
 */
const simpleDecrypt = (encryptedStr) => {
  if (!encryptedStr) return '';

  try {
    // 检查是否是加密格式
    if (!encryptedStr.startsWith('s') || !encryptedStr.endsWith('e')) {
      // 如果不是加密格式，直接返回原值
      return encryptedStr;
    }

    // 移除前缀和后缀
    const encrypted = encryptedStr.slice(1, -1);

    // 1. 反向字符替换
    let base64 = '';
    for (let i = 0; i < encrypted.length; i++) {
      const char = encrypted[i];
      base64 += REVERSE_CHAR_MAP[char] || char;
    }

    // 2. Base64解码
    try {
    const decoded = decodeURIComponent(escape(atob(base64)));
    return decoded;
    } catch (decodeError) {
      console.warn('Base64解码失败，返回原始值:', decodeError);
      return encryptedStr;
    }
  } catch (error) {
    console.warn('URL解密失败，参数格式可能不正确:', error);
    // 即使解密失败，也返回原始值，避免整个流程中断
    return encryptedStr;
  }
};

/**
 * 加密风格ID
 * @param {string|number} styleId - 风格ID
 * @returns {string} - 加密后的风格ID
 */
export const encryptStyleId = (styleId) => {
  if (!styleId) return '';
  return simpleEncrypt(String(styleId));
};

/**
 * 解密风格ID
 * @param {string} encryptedStyleId - 加密的风格ID
 * @returns {string} - 解密后的风格ID
 */
export const decryptStyleId = (encryptedStyleId) => {
  if (!encryptedStyleId) return '';
  return simpleDecrypt(encryptedStyleId);
};

/**
 * 加密尺寸ID
 * @param {string|number} sizeId - 尺寸ID
 * @returns {string} - 加密后的尺寸ID
 */
export const encryptSizeId = (sizeId) => {
  if (!sizeId) return '';
  return simpleEncrypt(String(sizeId));
};

/**
 * 解密尺寸ID
 * @param {string} encryptedSizeId - 加密的尺寸ID
 * @returns {string} - 解密后的尺寸ID
 */
export const decryptSizeId = (encryptedSizeId) => {
  if (!encryptedSizeId) return '';
  return simpleDecrypt(encryptedSizeId);
};

/**
 * 加密支付类型
 * @param {string} paymentType - 支付类型 (vip/points)
 * @returns {string} - 加密后的支付类型
 */
export const encryptPaymentType = (paymentType) => {
  if (!paymentType) return '';
  return simpleEncrypt(String(paymentType));
};

/**
 * 解密支付类型
 * @param {string} encryptedPaymentType - 加密的支付类型
 * @returns {string} - 解密后的支付类型
 */
export const decryptPaymentType = (encryptedPaymentType) => {
  if (!encryptedPaymentType) return '';
  return simpleDecrypt(encryptedPaymentType);
};

/**
 * 加密套餐ID
 * @param {string|number} packageId - 套餐ID
 * @returns {string} - 加密后的套餐ID
 */
export const encryptPackageId = (packageId) => {
  if (!packageId) return '';
  return simpleEncrypt(String(packageId));
};

/**
 * 解密套餐ID
 * @param {string} encryptedPackageId - 加密的套餐ID
 * @returns {string} - 解密后的套餐ID
 */
export const decryptPackageId = (encryptedPackageId) => {
  if (!encryptedPackageId) return '';
  return simpleDecrypt(encryptedPackageId);
};

/**
 * 加密订单号
 * @param {string} orderNo - 订单号
 * @returns {string} - 加密后的订单号
 */
export const encryptOrderNo = (orderNo) => {
  if (!orderNo) return '';
  return simpleEncrypt(String(orderNo));
};

/**
 * 解密订单号
 * @param {string} encryptedOrderNo - 加密的订单号
 * @returns {string} - 解密后的订单号
 */
export const decryptOrderNo = (encryptedOrderNo) => {
  if (!encryptedOrderNo) return '';
  return simpleDecrypt(encryptedOrderNo);
};

/**
 * 批量加密URL参数
 * @param {Object} params - 参数对象
 * @returns {Object} - 加密后的参数对象
 */
export const encryptUrlParams = (params) => {
  const encrypted = { ...params };

  if (encrypted.styleId) {
    encrypted.styleId = encryptStyleId(encrypted.styleId);
  }

  if (encrypted.sizeId) {
    encrypted.sizeId = encryptSizeId(encrypted.sizeId);
  }
  
  // 支付相关参数加密
  if (encrypted.type) {
    encrypted.type = encryptPaymentType(encrypted.type);
  }
  
  if (encrypted.package_id) {
    encrypted.package_id = encryptPackageId(encrypted.package_id);
  }
  
  if (encrypted.order_no) {
    encrypted.order_no = encryptOrderNo(encrypted.order_no);
  }

  return encrypted;
};

/**
 * 批量解密URL参数
 * @param {Object} params - 加密的参数对象
 * @returns {Object} - 解密后的参数对象
 */
export const decryptUrlParams = (params) => {
  const decrypted = { ...params };

  if (decrypted.styleId) {
    decrypted.styleId = decryptStyleId(decrypted.styleId);
  }

  if (decrypted.sizeId) {
    decrypted.sizeId = decryptSizeId(decrypted.sizeId);
  }
  
  // 支付相关参数解密
  if (decrypted.type) {
    decrypted.type = decryptPaymentType(decrypted.type);
  }
  
  if (decrypted.package_id) {
    decrypted.package_id = decryptPackageId(decrypted.package_id);
  }
  
  if (decrypted.order_no) {
    decrypted.order_no = decryptOrderNo(decrypted.order_no);
  }

  return decrypted;
};

/**
 * 测试加密解密功能
 * @param {string} testValue - 测试值
 */
export const testEncryption = (testValue = 'elegant') => {
  const encrypted = simpleEncrypt(testValue);
  const decrypted = simpleDecrypt(encrypted);
  return testValue === decrypted;
};

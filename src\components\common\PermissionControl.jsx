import React from 'react';
import { message } from 'antd';

/**
 * 权限受控组件包装器
 * @param {Object} props - 组件属性
 * @param {string} props.featureName - 功能名称
 * @param {Object} props.permissions - 权限对象
 * @param {React.ReactNode} props.children - 子组件
 * @param {React.ReactNode} props.fallback - 回退组件
 * @param {boolean} props.showMessage - 是否显示消息
 * @param {string} props.messageContent - 消息内容
 * @returns {React.ReactNode} 根据权限状态渲染的组件
 */
const PermissionControl = ({ 
  featureName, 
  permissions, 
  children, 
  fallback = null,
  showMessage = false,
  messageContent = '抱歉，该功能只针对vip用户'
}) => {
  const hasPermission = permissions && permissions[featureName];
  
  if (!hasPermission) {
    if (showMessage) {
      message.error(messageContent);
    }
    return fallback;
  }
  
  return children;
};

export default PermissionControl;

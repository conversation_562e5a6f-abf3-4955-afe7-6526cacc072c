/**
 * 资源管理系统
 * 实现高效的资源管理，优化静态页面中外部资源的加载和处理
 * 第十八阶段：专门解决背景图片等外部资源加载问题
 */

import logger from '../../../services/logs/frontendLogger';

/**
 * 资源类型枚举
 */
export const RESOURCE_TYPES = {
  IMAGE: 'image',
  CSS: 'css',
  FONT: 'font',
  SCRIPT: 'script',
  OTHER: 'other'
};

/**
 * 资源加载状态
 */
export const LOAD_STATUS = {
  PENDING: 'pending',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  TIMEOUT: 'timeout'
};

/**
 * 资源管理器类
 */
class ResourceManager {
  constructor() {
    this.cache = new Map();
    this.loadingPromises = new Map();
    this.config = {
      timeout: 10000, // 10秒超时
      retryCount: 2,
      enableCache: true,
      enablePreload: true
    };
    this.stats = {
      totalRequests: 0,
      successCount: 0,
      errorCount: 0,
      cacheHits: 0
    };
  }

  /**
   * 监控资源加载
   * @param {HTMLIFrameElement} iframe - iframe元素
   * @returns {Promise<Object>} 监控结果
   */
  async monitorResourceLoading(iframe) {
    if (!iframe || !iframe.contentDocument || !iframe.contentWindow) {
      throw new Error('无效的iframe元素或内容未加载');
    }

    const iframeDoc = iframe.contentDocument;
    const resources = this.extractResources(iframeDoc, iframe);

    logger.info('开始监控资源加载', {
      resourceCount: resources.length,
      types: this.getResourceTypeStats(resources)
    });

    const loadPromises = resources.map(resource =>
      this.loadResource(resource, iframeDoc)
    );

    try {
      const results = await Promise.allSettled(loadPromises);
      const summary = this.analyzLoadResults(results, resources);

      logger.info('资源加载监控完成', summary);
      return summary;
    } catch (error) {
      logger.error('资源加载监控失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 从文档中提取资源
   * @param {Document} doc - 文档对象
   * @param {HTMLIFrameElement} iframe - iframe元素
   * @returns {Array} 资源列表
   */
  extractResources(doc, iframe) {
    const resources = [];

    // 提取图片资源
    const images = doc.querySelectorAll('img[src]');
    images.forEach(img => {
      resources.push({
        type: RESOURCE_TYPES.IMAGE,
        url: img.src,
        element: img,
        priority: 'high'
      });
    });

    // 提取CSS背景图片
    const elementsWithBg = doc.querySelectorAll('*');
    elementsWithBg.forEach(el => {
      try {
        const style = iframe.contentWindow.getComputedStyle ?
          iframe.contentWindow.getComputedStyle(el) :
          el.currentStyle;

        if (style && style.backgroundImage && style.backgroundImage !== 'none') {
          const bgImage = style.backgroundImage;
          const urlMatch = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
          if (urlMatch && urlMatch[1]) {
            resources.push({
              type: RESOURCE_TYPES.IMAGE,
              url: urlMatch[1],
              element: el,
              priority: 'high',
              isBackground: true
            });
          }
        }
      } catch (error) {
        // 忽略样式获取错误，继续处理其他元素
        logger.warn('获取元素样式失败', { error: error.message });
      }
    });

    // 提取CSS文件
    const cssLinks = doc.querySelectorAll('link[rel="stylesheet"]');
    cssLinks.forEach(link => {
      resources.push({
        type: RESOURCE_TYPES.CSS,
        url: link.href,
        element: link,
        priority: 'high'
      });
    });

    // 提取字体资源
    const fontLinks = doc.querySelectorAll('link[href*="fonts"]');
    fontLinks.forEach(link => {
      resources.push({
        type: RESOURCE_TYPES.FONT,
        url: link.href,
        element: link,
        priority: 'medium'
      });
    });

    return resources;
  }

  /**
   * 加载单个资源
   * @param {Object} resource - 资源对象
   * @param {Document} doc - 文档对象
   * @returns {Promise} 加载Promise
   */
  async loadResource(resource, doc) {
    const { url, type } = resource;
    
    // 检查缓存
    if (this.config.enableCache && this.cache.has(url)) {
      this.stats.cacheHits++;
      return this.cache.get(url);
    }

    // 检查是否已在加载中
    if (this.loadingPromises.has(url)) {
      return this.loadingPromises.get(url);
    }

    this.stats.totalRequests++;

    const loadPromise = this.performResourceLoad(resource, doc);
    this.loadingPromises.set(url, loadPromise);

    try {
      const result = await loadPromise;
      
      if (this.config.enableCache) {
        this.cache.set(url, result);
      }
      
      this.stats.successCount++;
      return result;
    } catch (error) {
      this.stats.errorCount++;
      throw error;
    } finally {
      this.loadingPromises.delete(url);
    }
  }

  /**
   * 检测是否为外部资源
   * @param {string} url - 资源URL
   * @returns {boolean} 是否为外部资源
   */
  isExternalResource(url) {
    if (!url) return false;

    try {
      const resourceUrl = new URL(url, window.location.href);
      const currentOrigin = window.location.origin;

      // 检查是否为外部域名
      if (resourceUrl.origin !== currentOrigin) {
        return true;
      }

      // 检查是否为已知的外部图片域名
      const externalDomains = [
        'pics2.baidu.com',
        'img.baidu.com',
        'images.unsplash.com',
        'cdn.jsdelivr.net',
        'raw.githubusercontent.com'
      ];

      return externalDomains.some(domain => resourceUrl.hostname.includes(domain));
    } catch (error) {
      // URL解析失败，假设为外部资源
      return true;
    }
  }

  /**
   * 验证图片URL是否有效
   * @param {string} url - 图片URL
   * @returns {boolean} 是否为有效的图片URL
   */
  isValidImageUrl(url) {
    if (!url || typeof url !== 'string') return false;

    // 空白或占位符URL
    if (url.trim() === '' || url === 'about:blank') return false;

    // 检查是否为有效的data URL
    if (url.startsWith('data:')) {
      // 检查data URL格式
      const dataUrlPattern = /^data:image\/[a-zA-Z+]+;base64,/;
      if (!dataUrlPattern.test(url)) return false;

      // 检查base64内容是否有效（至少要有一些内容）
      const base64Part = url.split(',')[1];
      if (!base64Part || base64Part.length < 10) return false;

      // 检查是否为空白图片的base64
      const emptyImageBase64 = 'R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs='; // 1x1透明gif
      if (base64Part === emptyImageBase64) return false;
    }

    // 检查URL格式
    try {
      new URL(url, window.location.href);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 执行实际的资源加载
   * @param {Object} resource - 资源对象
   * @param {Document} doc - 文档对象
   * @returns {Promise} 加载Promise
   */
  performResourceLoad(resource, doc) {
    const { url, type, element } = resource;

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`资源加载超时: ${url}`));
      }, this.config.timeout);

      const cleanup = () => {
        clearTimeout(timeout);
      };

      if (type === RESOURCE_TYPES.IMAGE) {
        // 验证URL有效性
        if (!this.isValidImageUrl(url)) {
          logger.warn('跳过无效的图片URL', { url });
          cleanup();
          resolve({ success: false, url, error: '无效的图片URL' });
          return;
        }

        // 智能选择加载策略：外部资源直接使用代理，避免CORS错误
        if (this.isExternalResource(url)) {
          logger.info('检测到外部资源，使用代理加载', { url });
          this.loadImageWithProxy(url, element)
            .then(result => {
              cleanup();
              resolve(result);
            })
            .catch(error => {
              cleanup();
              reject(error);
            });
        } else {
          // 本地资源使用直接加载
          this.loadImage(url, element)
            .then(result => {
              cleanup();
              resolve(result);
            })
            .catch(error => {
              cleanup();
              // 本地资源加载失败时也尝试代理（可能是相对路径问题）
              this.loadImageWithProxy(url, element)
                .then(resolve)
                .catch(reject);
            });
        }
      } else if (type === RESOURCE_TYPES.CSS) {
        this.loadCSS(url, doc)
          .then(result => {
            cleanup();
            resolve(result);
          })
          .catch(error => {
            cleanup();
            reject(error);
          });
      } else {
        // 其他资源类型的默认处理
        cleanup();
        resolve({ url, type, status: LOAD_STATUS.SUCCESS });
      }
    });
  }

  /**
   * 加载图片资源（静默模式，减少控制台错误）
   * @param {string} url - 图片URL
   * @param {HTMLElement} element - 关联元素
   * @param {boolean} silent - 是否静默模式（不显示错误到控制台）
   * @returns {Promise} 加载Promise
   */
  loadImage(url, element, silent = false) {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        resolve({
          url,
          type: RESOURCE_TYPES.IMAGE,
          status: LOAD_STATUS.SUCCESS,
          width: img.width,
          height: img.height
        });
      };

      img.onerror = () => {
        const error = new Error(`图片加载失败: ${url}`);
        if (!silent) {
          // 只在非静默模式下记录错误
          logger.warn('图片直接加载失败', { url, error: error.message });
        }
        reject(error);
      };

      // 设置跨域属性
      img.crossOrigin = 'anonymous';
      img.src = url;
    });
  }

  /**
   * 检测URL是否已经是代理URL
   * @param {string} url - 要检测的URL
   * @returns {boolean} 是否为代理URL
   */
  isProxyUrl(url) {
    const proxyDomains = [
      'images.weserv.nl',
      'api.allorigins.win',
      'cors-anywhere.herokuapp.com',
      'thingproxy.freeboard.io',
      'api.codetabs.com'
    ];

    try {
      const urlObj = new URL(url);
      return proxyDomains.some(domain => urlObj.hostname.includes(domain));
    } catch (error) {
      return false;
    }
  }

  /**
   * 使用代理加载图片（解决CORS问题）
   * @param {string} url - 图片URL
   * @param {HTMLElement} element - 关联元素
   * @returns {Promise} 加载Promise
   */
  loadImageWithProxy(url, element) {
    // 如果URL已经是代理URL，直接尝试加载，避免双重代理
    if (this.isProxyUrl(url)) {
      logger.info('检测到代理URL，直接加载', { url });
      return this.loadImage(url, element, true);
    }

    // 使用可靠的CORS代理服务（移除不稳定的服务以避免404错误）
    const proxyUrls = [
      // 专业图片代理服务 - 最稳定
      `https://images.weserv.nl/?url=${encodeURIComponent(url)}&output=webp&q=85`,
      // 通用代理服务 - 备用
      `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`
      // 注意：移除了不稳定的代理服务以避免404错误
      // - cors-anywhere.herokuapp.com (经常不可用)
      // - thingproxy.freeboard.io (服务不稳定)
      // - api.codetabs.com (访问限制)
    ];

    return this.tryProxyUrls(url, proxyUrls, element);
  }

  /**
   * 尝试多个代理URL
   * @param {string} originalUrl - 原始URL
   * @param {Array} proxyUrls - 代理URL列表
   * @param {HTMLElement} element - 关联元素
   * @returns {Promise} 加载Promise
   */
  async tryProxyUrls(originalUrl, proxyUrls, element) {
    for (const proxyUrl of proxyUrls) {
      try {
        // 使用静默模式加载代理URL，减少控制台错误
        const result = await this.loadImage(proxyUrl, element, true);
        logger.info('代理加载成功', { originalUrl, proxyUrl });

        // 更新元素的背景图片
        if (element && element.style) {
          element.style.backgroundImage = `url(${proxyUrl})`;
        }

        return result;
      } catch (error) {
        // 只记录到日志，不显示到控制台
        logger.debug('代理尝试失败', { originalUrl, proxyUrl, error: error.message });
        continue;
      }
    }

    // 所有代理都失败时，使用渐变背景作为备用方案
    logger.warn('所有代理都无法加载图片，使用备用背景', { originalUrl });

    if (element && element.style) {
      // 创建一个渐变背景作为备用
      element.style.backgroundImage = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
      element.style.backgroundSize = 'cover';
      element.style.backgroundPosition = 'center';
    }

    return {
      url: originalUrl,
      type: RESOURCE_TYPES.IMAGE,
      status: LOAD_STATUS.ERROR,
      fallback: true
    };
  }

  /**
   * 加载CSS资源
   * @param {string} url - CSS URL
   * @param {Document} doc - 文档对象
   * @returns {Promise} 加载Promise
   */
  loadCSS(url, doc) {
    return new Promise((resolve, reject) => {
      const link = doc.createElement('link');
      link.rel = 'stylesheet';
      link.type = 'text/css';
      
      link.onload = () => {
        resolve({
          url,
          type: RESOURCE_TYPES.CSS,
          status: LOAD_STATUS.SUCCESS
        });
      };

      link.onerror = () => {
        reject(new Error(`CSS加载失败: ${url}`));
      };

      link.href = url;
      doc.head.appendChild(link);
    });
  }

  /**
   * 分析加载结果
   * @param {Array} results - Promise.allSettled结果
   * @param {Array} resources - 资源列表
   * @returns {Object} 分析结果
   */
  analyzLoadResults(results, resources) {
    const summary = {
      total: results.length,
      success: 0,
      failed: 0,
      failedResources: [],
      stats: { ...this.stats }
    };

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        summary.success++;
      } else {
        summary.failed++;
        summary.failedResources.push({
          resource: resources[index],
          error: result.reason.message
        });
      }
    });

    return summary;
  }

  /**
   * 获取资源类型统计
   * @param {Array} resources - 资源列表
   * @returns {Object} 类型统计
   */
  getResourceTypeStats(resources) {
    const stats = {};
    resources.forEach(resource => {
      stats[resource.type] = (stats[resource.type] || 0) + 1;
    });
    return stats;
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.clear();
    this.loadingPromises.clear();
    logger.info('资源缓存已清理');
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return { ...this.stats };
  }
}

// 创建全局实例
const resourceManager = new ResourceManager();

export default resourceManager;

/**
 * 便捷函数：监控iframe中的资源加载
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @returns {Promise<Object>} 监控结果
 */
export const monitorIframeResources = (iframe) => {
  return resourceManager.monitorResourceLoading(iframe);
};

/**
 * 便捷函数：清理资源缓存
 */
export const clearResourceCache = () => {
  resourceManager.clearCache();
};

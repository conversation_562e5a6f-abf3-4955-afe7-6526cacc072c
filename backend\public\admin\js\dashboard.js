// dashboard.js - 管理后台核心功能

// 全局变量
window.dashboardCurrentPage = 'dashboard'; // 当前显示的页面，使用window对象避免变量冲突

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
  // 加载登录状态
  checkLoginStatus();

  // 侧边栏导航事件绑定
  document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', function(e) {
      const targetPage = this.getAttribute('data-page');
      if (targetPage) {
        e.preventDefault();
        showPage(targetPage);
      }
    });
  });

  // 折叠菜单事件绑定
  document.querySelectorAll('.nav-link[data-bs-toggle="collapse"]').forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        if (target.classList.contains('show')) {
          target.classList.remove('show');
          this.setAttribute('aria-expanded', 'false');
        } else {
          target.classList.add('show');
          this.setAttribute('aria-expanded', 'true');
        }
      }
    });
  });

  // 退出登录按钮事件
  document.getElementById('logoutBtn').addEventListener('click', function(e) {
    e.preventDefault();
    logout();
  });

  // 加载统计数据
  loadDashboard();

  // 隐藏加载遮罩层
  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    loadingElement.style.display = 'none';
  }
});

// 检查登录状态
function checkLoginStatus() {
  const token = localStorage.getItem('token');

  if (!token) {
    window.location.href = 'index.html'; // 重定向到登录页
    return;
  }

  // 验证token有效性
  fetch('/api/auth/me', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => response.json())
  .then(data => {
    if (!data.success) {
      // token无效，重定向到登录页
      localStorage.removeItem('token');
      window.location.href = 'index.html';
    } else {
      // 验证用户是否为管理员
      if (data.data.role !== 'admin') {
        alert('您没有管理员权限');
        localStorage.removeItem('token');
        window.location.href = 'index.html';
      }
    }
  })
  .catch(error => {
    console.error('验证token失败:', error);
    // 发生错误时，清除token并重定向
    localStorage.removeItem('token');
    window.location.href = 'index.html';
  });
}

// 退出登录
function logout() {
  localStorage.removeItem('token');
  window.location.href = 'index.html';
}

// 显示指定页面
function showPage(pageName) {
  // 隐藏所有页面
  document.querySelectorAll('.content-page').forEach(page => {
    page.classList.add('d-none');
  });

  // 取消所有导航项的激活状态
  document.querySelectorAll('.nav-link').forEach(link => {
    link.classList.remove('active');
  });

  // 显示目标页面
  const targetPage = document.getElementById(pageName);
  if (targetPage) {
    targetPage.classList.remove('d-none');

    // 激活对应的导航项
    const navLink = document.querySelector(`.nav-link[data-page="${pageName}"]`);
    if (navLink) {
      navLink.classList.add('active');
      
      // 如果是子菜单项，展开父菜单
      const parentCollapse = navLink.closest('.collapse');
      if (parentCollapse) {
        parentCollapse.classList.add('show');
        const parentLink = document.querySelector(`[href="#${parentCollapse.id}"]`);
        if (parentLink) {
          parentLink.setAttribute('aria-expanded', 'true');
        }
      }
    }

    // 加载对应页面的数据
    switch (pageName) {
      case 'dashboard':
        loadDashboard();
        break;
      case 'users':
        loadUsers();
        break;
      case 'covers':
        loadCovers();
        break;
      case 'styles':
        loadStyles();
        break;
      case 'features':
        loadFeatures();
        break;
      case 'statistics':
        loadStatistics();
        break;
      case 'security-management':
        loadSecurityManagement();
        break;
      case 'security-check-records':
        loadSecurityCheckRecords();
        break;
      case 'settings':
        loadSettings();
        break;
      case 'logs':
        loadLogs();
        break;
      case 'point-records':
        loadPointRecords();
        break;
      case 'cover-records':
        loadCoverRecords();
        break;
      case 'tasks':
        loadTasks();
        break;
      case 'ai-services':
        loadAIServices();
        break;
      case 'system-monitor':
        loadSystemMonitor();
        break;
      // 支付管理相关页面
      case 'payment-orders':
        loadPaymentOrders();
        break;
      case 'member-packages':
        loadMemberPackages();
        break;
      case 'point-packages':
        loadPointPackages();
        break;
      case 'refunds':
        loadRefunds();
        break;
      case 'payment-settings':
        loadPaymentSettings();
        break;
      case 'member-benefits':
        loadMemberBenefits();
        break;
    }

    // 更新当前页面
    window.dashboardCurrentPage = pageName;
  }
}

// 加载控制台概览数据
function loadDashboard() {
  const token = localStorage.getItem('token');
  if (!token) return;

  // 加载概览数据
  fetch('/api/admin/stats', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // 更新统计卡片
      document.getElementById('userCount').textContent = data.data.user_count || 0;
      document.getElementById('coverCount').textContent = data.data.cover_count || 0;
      document.getElementById('orderCount').textContent = data.data.order_count || 0;
      document.getElementById('styleCount').textContent = data.data.style_count || 0;

      // 更新最近用户列表
      updateRecentUsers(data.data.recent_users || []);

      // 更新最近封面列表
      updateRecentCovers(data.data.recent_covers || []);
    } else {
      console.error('获取概览数据失败:', data.message);
    }
  })
  .catch(error => {
    console.error('获取概览数据失败:', error);
  });
}

// 更新最近用户列表
function updateRecentUsers(users) {
  const tbody = document.querySelector('#recentUsersTable tbody');
  tbody.innerHTML = '';

  if (users.length === 0) {
    const tr = document.createElement('tr');
    tr.innerHTML = '<td colspan="4" class="text-center">暂无数据</td>';
    tbody.appendChild(tr);
    return;
  }

  users.forEach(user => {
    const tr = document.createElement('tr');

    // 获取注册时间，尝试不同的字段名
    const createdDate = user.createdAt || user.created_at;

    tr.innerHTML = `
      <td>${user.id}</td>
      <td>${user.phone}</td>
      <td>${user.nickname}</td>
      <td>${formatDate(createdDate)}</td>
    `;

    tbody.appendChild(tr);
  });
}

// 更新最近封面列表
function updateRecentCovers(covers) {
  const tbody = document.querySelector('#recentCoversTable tbody');
  tbody.innerHTML = '';

  if (covers.length === 0) {
    const tr = document.createElement('tr');
    tr.innerHTML = '<td colspan="3" class="text-center">暂无数据</td>';
    tbody.appendChild(tr);
    return;
  }

  covers.forEach(cover => {
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td>${cover.title || '无标题'}</td>
      <td>${cover.username}</td>
      <td>${formatDate(cover.created_at)}</td>
    `;
    tbody.appendChild(tr);
  });
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '-';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '-'; // 如果无法解析，返回短横线
    }
    
    // 使用更简单的格式化方法，避免本地化问题
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    return '-'; // 如果发生错误，返回短横线
  }
}

// 模块加载函数
function loadUsers() {
  // 加载用户管理模块
  if (typeof window.userModule === 'undefined') {
    const script = document.createElement('script');
    script.src = 'js/modules/users.js';
    script.onload = function() {
      if (typeof window.userModule !== 'undefined') {
        window.userModule.init();
      }
    };
    document.head.appendChild(script);
  } else {
    window.userModule.init();
  }
}

function loadCovers() {
  // 加载封面管理模块
  if (typeof window.coverModule === 'undefined') {
    const script = document.createElement('script');
    script.src = 'js/modules/covers.js';
    script.onload = function() {
      if (typeof window.coverModule !== 'undefined') {
        window.coverModule.init();
      }
    };
    document.head.appendChild(script);
  } else {
    window.coverModule.init();
  }
}

function loadStyles() {
  // 加载风格管理模块
  if (typeof window.styleModule === 'undefined') {
    const script = document.createElement('script');
    script.src = 'js/modules/styles.js';
    script.onload = function() {
      if (typeof window.styleModule !== 'undefined') {
        window.styleModule.init();
      }
    };
    document.head.appendChild(script);
  } else {
    window.styleModule.init();
  }
}

function loadStatistics() {
  // 加载统计模块
  if (typeof window.statisticsModule === 'undefined') {
    const script = document.createElement('script');
    script.src = 'js/modules/statistics.js';
    script.onload = function() {
      if (typeof window.statisticsModule !== 'undefined') {
        window.statisticsModule.init();
      }
    };
    document.head.appendChild(script);
  } else {
    window.statisticsModule.init();
  }
}

function loadSettings() {
  // 加载设置模块
  if (typeof window.settingsModule === 'undefined') {
    const script = document.createElement('script');
    script.src = 'js/modules/settings.js';
    script.onload = function() {
      if (typeof window.settingsModule !== 'undefined') {
        window.settingsModule.init();
      }
    };
    document.head.appendChild(script);
  } else {
    window.settingsModule.init();
  }
}

function loadLogs() {
  // 加载日志模块
  console.log('尝试加载日志模块...');

  // 简化日志模块加载逻辑
  if (typeof window.logModule === 'undefined') {
    console.log('日志模块未加载，正在加载...');
    const script = document.createElement('script');
    script.src = 'js/modules/logs.js?v=' + new Date().getTime(); // 添加时间戳防止缓存
    script.onload = function() {
      console.log('日志模块脚本加载完成');
      if (window.logModule && typeof window.logModule.init === 'function') {
        console.log('日志模块初始化中...');
        try {
          window.logModule.init();
          console.log('日志模块初始化成功');
        } catch (error) {
          console.error('日志模块初始化失败:', error);
        }
      } else {
        console.error('日志模块加载失败，未找到logModule对象或init方法');
        console.log('window.logModule:', window.logModule);
      }
    };
    script.onerror = function(error) {
      console.error('日志模块脚本加载失败:', error);
    };
    document.head.appendChild(script);
  } else {
    console.log('日志模块已存在，直接初始化');
    try {
      window.logModule.init();
      console.log('日志模块初始化成功');
    } catch (error) {
      console.error('日志模块初始化失败:', error);
    }
  }
}

// 加载功能控制页面
function loadFeatures() {
  console.log('尝试加载功能控制模块...');
  // 初始化功能控制模块
  if (typeof window.featureModule === 'undefined') {
    console.log('功能控制模块未加载，正在加载...');
    const script = document.createElement('script');
    script.src = 'js/modules/features.js';
    script.onload = function() {
      console.log('功能控制模块脚本加载完成');
      if (window.featureModule && typeof window.featureModule.init === 'function') {
        console.log('功能控制模块初始化中...');
        window.featureModule.init();
      } else {
        console.error('功能控制模块加载失败，未找到featureModule对象或init方法');
      }
    };
    script.onerror = function(error) {
      console.error('功能控制模块脚本加载失败:', error);
    };
    document.head.appendChild(script);
  } else {
    console.log('功能控制模块已存在，直接初始化');
    window.featureModule.init();
  }
}

// 加载积分记录模块
function loadPointRecords() {
  // 初始化积分记录模块
  if (typeof window.pointRecordsModule === 'undefined') {
    console.log('尝试加载积分记录模块...');
    const script = document.createElement('script');
    script.src = 'js/modules/point-records.js'; // 使用正确的文件名
    script.onload = function() {
      if (typeof window.pointRecordsModule !== 'undefined') {
        window.pointRecordsModule.init();
      } else {
        console.error('积分记录模块加载后未定义 window.pointRecordsModule');
      }
    };
    script.onerror = function() {
      console.error('加载积分记录模块脚本失败');
    };
    document.head.appendChild(script);
  } else {
    window.pointRecordsModule.init();
  }
}

// 加载封面记录模块
function loadCoverRecords() {
  // 初始化封面记录模块
  if (typeof window.coverRecordsModule === 'undefined') {
    console.log('尝试加载封面记录模块...');
    const script = document.createElement('script');
    script.src = 'js/modules/cover-records.js'; // 使用正确的文件名
    script.onload = function() {
      if (typeof window.coverRecordsModule !== 'undefined') {
        window.coverRecordsModule.init();
      } else {
        console.error('封面记录模块加载后未定义 window.coverRecordsModule');
      }
    };
    script.onerror = function() {
      console.error('加载封面记录模块脚本失败');
    };
    document.head.appendChild(script);
  } else {
    window.coverRecordsModule.init();
  }
}

// 加载任务管理模块
function loadTasks() {
  // 加载任务管理模块
  if (typeof window.taskModule === 'undefined') {
    const script = document.createElement('script');
    script.src = 'js/modules/tasks.js';
    script.onload = function() {
      if (typeof window.taskModule !== 'undefined') {
        window.taskModule.init();
      }
    };
    document.head.appendChild(script);
  } else {
    window.taskModule.init();
  }
}

function loadAIServices() {
  // 加载AI服务管理模块
  loadModuleScript('ai-services');

  // 如果模块已加载，初始化它
  if (window.aiServicesModule) {
    window.aiServicesModule.init();
  } else {
    // 如果模块尚未加载完成，设置一个短暂的延迟
    setTimeout(() => {
      if (window.aiServicesModule) {
        window.aiServicesModule.init();
      } else {
        // 模块加载失败，显示错误提示
        showError('AI服务管理模块加载失败');
      }
    }, 500);
  }
}

function loadSystemMonitor() {
  // 加载系统监控模块
  if (typeof window.systemMonitorModule === 'undefined') {
    const script = document.createElement('script');
    script.src = 'js/modules/system-monitor.js';
    script.onload = function() {
      if (typeof window.systemMonitorModule !== 'undefined') {
        window.systemMonitorModule.init();
      }
    };
    document.head.appendChild(script);
  } else {
    window.systemMonitorModule.init();
  }
}

// 支付管理相关功能

// 加载订单管理
function loadPaymentOrders() {
  const token = localStorage.getItem('token');
  if (!token) return;

  // 清空表格
  const tbody = document.querySelector('#ordersTable tbody');
  tbody.innerHTML = '<tr><td colspan="8" class="text-center">加载中...</td></tr>';

  // 获取筛选条件
  const status = document.getElementById('orderStatus').value;
  const productType = document.getElementById('productType').value;
  const paymentType = document.getElementById('paymentType').value;
  const search = document.getElementById('orderSearch').value;
  const startDate = document.getElementById('orderStartDate').value;
  const endDate = document.getElementById('orderEndDate').value;
  const page = 1;
  const limit = 10;

  // 构建查询参数
  let queryParams = `page=${page}&limit=${limit}`;
  if (status) queryParams += `&status=${status}`;
  if (productType) queryParams += `&product_type=${productType}`;
  if (paymentType) queryParams += `&payment_type=${paymentType}`;
  if (search) queryParams += `&search=${encodeURIComponent(search)}`;
  if (startDate) queryParams += `&start_date=${startDate}`;
  if (endDate) queryParams += `&end_date=${endDate}`;

  // 请求订单数据
  fetch(`/api/admin/payment/orders?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // 更新表格
      updateOrdersTable(data.data.orders);
      
      // 更新分页
      updatePagination('ordersPagination', data.data.pagination, loadPaymentOrdersPage);
      
      // 更新订单总数显示
      document.getElementById('totalOrdersCount').textContent = `订单总数: ${data.data.pagination.total || 0}`;
  } else {
      console.error('获取订单列表失败:', data.message);
      tbody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">加载失败: ${data.message}</td></tr>`;
    }
  })
  .catch(error => {
    console.error('获取订单列表失败:', error);
    tbody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">加载失败，请稍后重试</td></tr>';
  });

  // 绑定搜索按钮事件
  document.getElementById('orderSearchBtn').addEventListener('click', loadPaymentOrders);
  
  // 绑定重置按钮事件
  document.getElementById('orderResetBtn').addEventListener('click', function() {
    document.getElementById('orderStatus').value = '';
    document.getElementById('productType').value = '';
    document.getElementById('paymentType').value = '';
    document.getElementById('orderSearch').value = '';
    document.getElementById('orderStartDate').value = '';
    document.getElementById('orderEndDate').value = '';
    loadPaymentOrders();
  });
}

// 更新订单表格
function updateOrdersTable(orders) {
  const tbody = document.querySelector('#ordersTable tbody');
  tbody.innerHTML = '';

  if (orders.length === 0) {
    tbody.innerHTML = '<tr><td colspan="10" class="text-center">暂无订单数据</td></tr>';
    return;
  }

  orders.forEach((order, index) => {
    const tr = document.createElement('tr');
    
    // 格式化产品类型
    let productType = order.product_type === 'vip' ? '会员套餐' : '积分套餐';
    
    // 格式化支付方式
    let paymentType = order.payment_type === 'wechat' ? '微信支付' : '支付宝';
    
    // 格式化状态
    let status = '';
    let statusClass = '';
    switch (order.payment_status) {
      case 'pending':
        status = '待支付';
        statusClass = 'text-warning';
        break;
      case 'success':
        status = '支付成功';
        statusClass = 'text-success';
        break;
      case 'failed':
        status = '支付失败';
        statusClass = 'text-danger';
        break;
      case 'refunded':
        status = '已退款';
        statusClass = 'text-info';
        break;
      case 'closed':
        status = '已关闭';
        statusClass = 'text-secondary';
        break;
      default:
        status = order.payment_status;
        statusClass = 'text-secondary';
    }
    
    // 格式化显示状态
    let displayStatus = order.display_status === '显示' ? '显示' : '隐藏';
    let displayStatusClass = order.display_status === '显示' ? 'text-success' : 'text-secondary';
    
    const formattedDate = formatDate(order.createdAt);
    
    tr.innerHTML = `
      <td>${index + 1}</td>
      <td>${order.order_no}</td>
      <td>${order.user ? order.user.nickname || order.user.phone || order.user_id : order.user_id}</td>
      <td>${productType}</td>
      <td>¥${order.amount}</td>
      <td>${paymentType}</td>
      <td><span class="${statusClass}">${status}</span></td>
      <td><span class="${displayStatusClass}">${displayStatus}</span></td>
      <td>${formattedDate}</td>
      <td>
        <button class="btn btn-sm btn-primary view-order" data-id="${order.id}" title="查看详情">
          <i class="bi bi-eye"></i> 查看
        </button>
        ${order.payment_status === 'pending' ? `
        <button class="btn btn-sm btn-success update-order-status" data-id="${order.id}" data-status="success" title="标记为支付成功">
          <i class="bi bi-check"></i> 标记成功
        </button>
        <button class="btn btn-sm btn-danger update-order-status" data-id="${order.id}" data-status="failed" title="标记为支付失败">
          <i class="bi bi-x"></i> 标记失败
        </button>
        <button class="btn btn-sm btn-warning close-order" data-id="${order.id}" title="关闭订单">
          <i class="bi bi-slash-circle"></i> 关闭订单
        </button>
        ` : ''}
        <button class="btn btn-sm btn-danger delete-order" data-id="${order.id}" data-order-no="${order.order_no}" title="删除订单">
          <i class="bi bi-trash"></i> 删除
        </button>
      </td>
    `;
    
    tbody.appendChild(tr);
  });
  
  // 绑定查看订单详情按钮事件
  document.querySelectorAll('.view-order').forEach(btn => {
    btn.addEventListener('click', function() {
      const orderId = this.getAttribute('data-id');
      viewOrderDetail(orderId);
    });
  });
  
  // 绑定更新订单状态按钮事件
  document.querySelectorAll('.update-order-status').forEach(btn => {
    btn.addEventListener('click', function() {
      const orderId = this.getAttribute('data-id');
      const status = this.getAttribute('data-status');
      updateOrderStatus(orderId, status);
    });
  });
  
  // 绑定删除订单按钮事件
  document.querySelectorAll('.delete-order').forEach(btn => {
    btn.addEventListener('click', function() {
      const orderId = this.getAttribute('data-id');
      const orderNo = this.getAttribute('data-order-no');
      showDeleteOrderConfirm(orderId, orderNo);
    });
  });
  
  // 绑定关闭订单按钮事件
  document.querySelectorAll('.close-order').forEach(btn => {
    btn.addEventListener('click', function() {
      const orderId = this.getAttribute('data-id');
      showCloseOrderConfirm(orderId);
    });
  });
}

// 加载指定页码的订单
function loadPaymentOrdersPage(page) {
  const token = localStorage.getItem('token');
  if (!token) return;

  // 显示加载中
  const tbody = document.querySelector('#ordersTable tbody');
  tbody.innerHTML = '<tr><td colspan="10" class="text-center">加载中...</td></tr>';

  // 获取当前的筛选条件
  const status = document.getElementById('orderStatus').value;
  const productType = document.getElementById('productType').value;
  const paymentType = document.getElementById('paymentType').value;
  const search = document.getElementById('orderSearch').value;
  const startDate = document.getElementById('orderStartDate').value;
  const endDate = document.getElementById('orderEndDate').value;
  const limit = 10;

  // 构建查询参数
  let queryParams = `page=${page}&limit=${limit}`;
  if (status) queryParams += `&status=${status}`;
  if (productType) queryParams += `&product_type=${productType}`;
  if (paymentType) queryParams += `&payment_type=${paymentType}`;
  if (search) queryParams += `&search=${encodeURIComponent(search)}`;
  if (startDate) queryParams += `&start_date=${startDate}`;
  if (endDate) queryParams += `&end_date=${endDate}`;

  // 请求订单数据
  fetch(`/api/admin/payment/orders?${queryParams}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // 更新表格
      updateOrdersTable(data.data.orders);
      
      // 更新分页
      updatePagination('ordersPagination', data.data.pagination, loadPaymentOrdersPage);
      
      // 更新订单总数显示
      document.getElementById('totalOrdersCount').textContent = `订单总数: ${data.data.pagination.total || 0}`;
    } else {
      console.error('获取订单列表失败:', data.message);
      tbody.innerHTML = `<tr><td colspan="10" class="text-center text-danger">加载失败: ${data.message}</td></tr>`;
    }
  })
  .catch(error => {
    console.error('获取订单列表失败:', error);
    tbody.innerHTML = '<tr><td colspan="10" class="text-center text-danger">加载失败，请稍后重试</td></tr>';
  });
}

// 查看订单详情
function viewOrderDetail(orderId) {
  const token = localStorage.getItem('token');
  if (!token) return;

  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    loadingElement.style.display = 'flex';
  }

  // 请求订单详情数据
  fetch(`/api/admin/payment/orders/${orderId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => response.json())
  .then(data => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }

    if (data.success) {
      const order = data.data.order;
      const packageInfo = data.data.packageInfo;

      // 填充订单详情模态框
      document.getElementById('orderDetailOrderNo').textContent = order.order_no || '-';
      document.getElementById('orderDetailCreatedAt').textContent = formatDate(order.createdAt) || '-';
      document.getElementById('orderDetailPaymentTime').textContent = formatDate(order.payment_time) || '-';
      
      // 设置支付状态
      let statusText = '';
      let statusClass = '';
      switch (order.payment_status) {
        case 'pending':
          statusText = '待支付';
          statusClass = 'text-warning';
          break;
        case 'success':
          statusText = '支付成功';
          statusClass = 'text-success';
          break;
        case 'failed':
          statusText = '支付失败';
          statusClass = 'text-danger';
          break;
        case 'refunded':
          statusText = '已退款';
          statusClass = 'text-info';
          break;
        case 'closed':
          statusText = '已关闭';
          statusClass = 'text-secondary';
          break;
        default:
          statusText = order.payment_status || '-';
          statusClass = 'text-secondary';
      }
      document.getElementById('orderDetailStatus').innerHTML = `<span class="${statusClass}">${statusText}</span>`;
      
      // 设置支付方式
      const paymentType = order.payment_type === 'wechat' ? '微信支付' : 
                         (order.payment_type === 'alipay' ? '支付宝' : order.payment_type || '-');
      document.getElementById('orderDetailPaymentType').textContent = paymentType;
      
      // 设置产品类型
      const productType = order.product_type === 'vip' ? '会员套餐' : 
                        (order.product_type === 'points' ? '积分套餐' : order.product_type || '-');
      document.getElementById('orderDetailProductType').textContent = productType;
      
      // 设置金额
      document.getElementById('orderDetailAmount').textContent = `¥${order.amount || '0'}`;
      
      // 设置退款状态
      let refundStatusText = '';
      let refundStatusClass = '';
      switch (order.refund_status) {
        case 'none':
          refundStatusText = '未退款';
          refundStatusClass = 'text-secondary';
          break;
        case 'partial':
          refundStatusText = '部分退款';
          refundStatusClass = 'text-warning';
          break;
        case 'full':
          refundStatusText = '全额退款';
          refundStatusClass = 'text-info';
          break;
        default:
          refundStatusText = order.refund_status || '未退款';
          refundStatusClass = 'text-secondary';
      }
      document.getElementById('orderDetailRefundStatus').innerHTML = `<span class="${refundStatusClass}">${refundStatusText}</span>`;
      
      // 设置用户信息
      const user = order.user || {};
      document.getElementById('orderDetailUserId').textContent = user.id || order.user_id || '-';
      document.getElementById('orderDetailUsername').textContent = user.nickname || user.phone || '-';
      document.getElementById('orderDetailEmail').textContent = user.email || '-';
      document.getElementById('orderDetailRole').textContent = user.role === 'vip' ? 'VIP会员' : 
                                                           (user.role === 'admin' ? '管理员' : '普通用户');
      
      // 设置套餐信息
      const packageInfoElement = document.getElementById('orderDetailPackageInfo');
      if (packageInfo) {
        let packageHtml = `
          <table class="table table-sm">
            <thead>
              <tr>
                <th>套餐名称</th>
                <th>${order.product_type === 'vip' ? '有效期(天)' : '积分数量'}</th>
                <th>价格</th>
                ${order.product_type === 'vip' ? '' : '<th>赠送积分</th>'}
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>${packageInfo.name || '-'}</td>
                <td>${order.product_type === 'vip' ? packageInfo.duration : packageInfo.points}</td>
                <td>¥${packageInfo.price}</td>
                ${order.product_type === 'vip' ? '' : `<td>${packageInfo.bonus_points || 0}</td>`}
              </tr>
            </tbody>
          </table>
        `;
        packageInfoElement.innerHTML = packageHtml;
      } else {
        packageInfoElement.innerHTML = '<div class="alert alert-warning">未找到套餐信息</div>';
      }
      
      // 根据订单状态显示/隐藏操作按钮
      const markSuccessBtn = document.getElementById('orderDetailMarkSuccessBtn');
      const markFailedBtn = document.getElementById('orderDetailMarkFailedBtn');
      const refundBtn = document.getElementById('orderDetailRefundBtn');
      const closeBtn = document.getElementById('orderDetailCloseBtn');
      
      // 只有待支付的订单可以标记为成功/失败或关闭
      if (order.payment_status === 'pending') {
        markSuccessBtn.style.display = 'inline-block';
        markFailedBtn.style.display = 'inline-block';
        closeBtn.style.display = 'inline-block';
      } else {
        markSuccessBtn.style.display = 'none';
        markFailedBtn.style.display = 'none';
        closeBtn.style.display = 'none';
      }
      
      // 只有支付成功的订单可以申请退款
      if (order.payment_status === 'success') {
        refundBtn.style.display = 'inline-block';
      } else {
        refundBtn.style.display = 'none';
      }
      
      // 绑定按钮事件
      markSuccessBtn.onclick = function() {
        updateOrderStatus(order.id, 'success', true);
      };
      
      markFailedBtn.onclick = function() {
        updateOrderStatus(order.id, 'failed', true);
      };
      
      refundBtn.onclick = function() {
        showRefundModal(order);
      };
      
      closeBtn.onclick = function() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('orderDetailModal'));
        modal.hide();
        showCloseOrderConfirm(order.id);
      };
      
      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'));
      modal.show();
    } else {
      alert('获取订单详情失败: ' + data.message);
    }
  })
  .catch(error => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
    console.error('获取订单详情失败:', error);
    alert('获取订单详情失败，请稍后重试');
  });
}

// 更新订单状态
function updateOrderStatus(orderId, status, closeModal = false) {
  const token = localStorage.getItem('token');
  if (!token) return;
  
  // 确认操作
  const statusTexts = {
    'success': '支付成功',
    'failed': '支付失败'
  };

  if (!confirm(`确定要将订单标记为${statusTexts[status] || status}吗？`)) {
    return;
  }

  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    loadingElement.style.display = 'flex';
  }
  
  // 请求更新订单状态
  fetch(`/api/admin/payment/orders/${orderId}/status`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ status })
  })
  .then(response => response.json())
  .then(data => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }

    if (data.success) {
      alert(`订单状态已更新为${statusTexts[status] || status}`);
      
      // 如果需要关闭模态框
      if (closeModal) {
        const modal = bootstrap.Modal.getInstance(document.getElementById('orderDetailModal'));
        if (modal) {
          modal.hide();
        }
      }
      
      // 重新加载订单列表
      loadPaymentOrders();
    } else {
      alert('更新订单状态失败: ' + data.message);
    }
  })
  .catch(error => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
    console.error('更新订单状态失败:', error);
    alert('更新订单状态失败，请稍后重试');
  });
}

// 显示退款模态框
function showRefundModal(order) {
  // 设置退款表单数据
  document.getElementById('refundPaymentId').value = order.id;
  document.getElementById('refundOrderNo').value = order.order_no;
  document.getElementById('refundAmount').value = order.amount;
  document.getElementById('refundAmount').max = order.amount;
  document.getElementById('refundMaxAmount').textContent = order.amount;
  document.getElementById('refundReason').value = '';

  // 绑定提交退款按钮事件
  document.getElementById('submitRefundBtn').onclick = function() {
    submitRefund();
  };

  // 显示模态框
  const modal = new bootstrap.Modal(document.getElementById('refundModal'));
  modal.show();
}

// 提交退款申请
function submitRefund() {
  const token = localStorage.getItem('token');
  if (!token) return;

  const paymentId = document.getElementById('refundPaymentId').value;
  const refundAmount = document.getElementById('refundAmount').value;
  const refundReason = document.getElementById('refundReason').value;

  // 验证表单
  if (!paymentId) {
    alert('订单ID不能为空');
    return;
  }

  if (!refundAmount || parseFloat(refundAmount) <= 0) {
    alert('退款金额必须大于0');
    return;
  }

  if (!refundReason) {
    alert('请输入退款原因');
    return;
  }

  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    loadingElement.style.display = 'flex';
  }

  // 请求创建退款
  fetch('/api/admin/payment/refunds', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      payment_id: paymentId,
      refund_amount: refundAmount,
      refund_reason: refundReason
    })
  })
  .then(response => response.json())
  .then(data => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
    
    if (data.success) {
      alert('退款申请已提交');
      
      // 关闭模态框
      const refundModal = bootstrap.Modal.getInstance(document.getElementById('refundModal'));
      const orderDetailModal = bootstrap.Modal.getInstance(document.getElementById('orderDetailModal'));
      
      if (refundModal) {
        refundModal.hide();
      }
      
      if (orderDetailModal) {
        orderDetailModal.hide();
      }
      
      // 重新加载订单列表
      loadPaymentOrders();
    } else {
      alert('提交退款申请失败: ' + data.message);
    }
  })
  .catch(error => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
    console.error('提交退款申请失败:', error);
    alert('提交退款申请失败，请稍后重试');
  });
}

// 加载会员套餐
function loadMemberPackages() {
  // 动态加载会员套餐管理模块
  const script = document.createElement('script');
  script.src = 'js/modules/member-packages.js';
  script.onload = function() {
    console.log('会员套餐管理模块加载成功');
  };
  script.onerror = function() {
    console.error('会员套餐管理模块加载失败');
    const errorMsg = '模块加载失败，请刷新页面重试';
    document.querySelector('#member-packages .card-body').innerHTML = `<div class="alert alert-danger">${errorMsg}</div>`;
  };
  document.head.appendChild(script);
}

// 加载积分套餐
function loadPointPackages() {
  // 动态加载积分套餐管理模块
  const script = document.createElement('script');
  script.src = 'js/modules/point-packages.js';
  script.onload = function() {
    console.log('积分套餐管理模块加载成功');
  };
  script.onerror = function() {
    console.error('积分套餐管理模块加载失败');
    const errorMsg = '模块加载失败，请刷新页面重试';
    document.querySelector('#point-packages .card-body').innerHTML = `<div class="alert alert-danger">${errorMsg}</div>`;
  };
  document.head.appendChild(script);
}

// 加载退款管理
function loadRefunds() {
  // 动态加载退款管理模块
  const script = document.createElement('script');
  script.src = 'js/modules/refunds.js';
  script.onload = function() {
    console.log('退款管理模块加载成功');
  };
  script.onerror = function() {
    console.error('退款管理模块加载失败');
    const errorMsg = '模块加载失败，请刷新页面重试';
    document.querySelector('#refunds .card-body').innerHTML = `<div class="alert alert-danger">${errorMsg}</div>`;
  };
  document.head.appendChild(script);
}

// 加载支付设置
function loadPaymentSettings() {
  // 动态加载支付设置模块
  const script = document.createElement('script');
  script.src = 'js/modules/payment-settings.js';
  script.onload = function() {
    console.log('支付设置模块加载成功');
  };
  script.onerror = function() {
    console.error('支付设置模块加载失败');
    const errorMsg = '模块加载失败，请刷新页面重试';
    document.querySelector('#payment-settings .card-body').innerHTML = `<div class="alert alert-danger">${errorMsg}</div>`;
  };
  document.head.appendChild(script);
}

// 加载会员权益
function loadMemberBenefits() {
  // 动态加载会员权益模块
  const script = document.createElement('script');
  script.src = 'js/modules/member-benefits.js';
  script.onload = function() {
    console.log('会员权益模块加载成功');
  };
  script.onerror = function() {
    console.error('会员权益模块加载失败');
    const errorMsg = '模块加载失败，请刷新页面重试';
    document.querySelector('#member-benefits .card-body').innerHTML = `<div class="alert alert-danger">${errorMsg}</div>`;
  };
  document.head.appendChild(script);
}

// 更新分页控件
function updatePagination(containerId, pagination, callback) {
  const container = document.getElementById(containerId);
  if (!container) return;
  
  container.innerHTML = '';
  
  if (pagination.total_pages <= 1) return;
  
  const ul = document.createElement('ul');
  ul.className = 'pagination';
  
  // 上一页按钮
  const prevLi = document.createElement('li');
  prevLi.className = `page-item ${pagination.page <= 1 ? 'disabled' : ''}`;
  const prevLink = document.createElement('a');
  prevLink.className = 'page-link';
  prevLink.href = '#';
  prevLink.textContent = '上一页';
  if (pagination.page > 1) {
    prevLink.addEventListener('click', function(e) {
      e.preventDefault();
      callback(pagination.page - 1);
    });
  }
  prevLi.appendChild(prevLink);
  ul.appendChild(prevLi);
  
  // 页码按钮
  let startPage = Math.max(1, pagination.page - 2);
  let endPage = Math.min(pagination.total_pages, startPage + 4);
  
  if (endPage - startPage < 4) {
    startPage = Math.max(1, endPage - 4);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    const li = document.createElement('li');
    li.className = `page-item ${i === pagination.page ? 'active' : ''}`;
    const link = document.createElement('a');
    link.className = 'page-link';
    link.href = '#';
    link.textContent = i;
    if (i !== pagination.page) {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        callback(i);
      });
    }
    li.appendChild(link);
    ul.appendChild(li);
  }
  
  // 下一页按钮
  const nextLi = document.createElement('li');
  nextLi.className = `page-item ${pagination.page >= pagination.total_pages ? 'disabled' : ''}`;
  const nextLink = document.createElement('a');
  nextLink.className = 'page-link';
  nextLink.href = '#';
  nextLink.textContent = '下一页';
  if (pagination.page < pagination.total_pages) {
    nextLink.addEventListener('click', function(e) {
      e.preventDefault();
      callback(pagination.page + 1);
    });
  }
  nextLi.appendChild(nextLink);
  ul.appendChild(nextLi);
  
  container.appendChild(ul);
}

// 加载模块脚本
const modulePaths = {
  // ... existing modules ...
  'refunds': 'js/modules/refunds.js',
  'payment-settings': 'js/modules/payment-settings.js',
  'member-benefits': 'js/modules/member-benefits.js',
  'member-packages': 'js/modules/member-packages.js',
  'point-packages': 'js/modules/point-packages.js',
  'ai-services': 'js/modules/aiServices.js'
};

// 动态加载模块脚本
function loadModuleScript(moduleId) {
  if (!modulePaths[moduleId]) {
    showError(`未找到模块: ${moduleId}`);
    return;
  }
  
  // 检查是否已加载
  const existingScript = document.querySelector(`script[src="${modulePaths[moduleId]}"]`);
  if (existingScript) {
    return;
  }
  
  const script = document.createElement('script');
  script.src = modulePaths[moduleId];
  script.onerror = function() {
    showError(`${moduleId}模块加载失败`);
  };
  document.body.appendChild(script);
}

// 显示删除订单确认对话框
function showDeleteOrderConfirm(orderId, orderNo) {
  // 创建确认对话框
  const confirmModal = new bootstrap.Modal(document.getElementById('confirmModal') || createConfirmModal());
  
  // 设置确认对话框内容
  document.getElementById('confirmModalTitle').textContent = '删除订单';
  document.getElementById('confirmModalBody').innerHTML = `
    <p>您确定要删除订单 <strong>${orderNo}</strong> 吗？</p>
    <div class="alert alert-warning">
      <i class="bi bi-exclamation-triangle-fill me-2"></i>此操作将永久删除该订单及其关联数据，且无法恢复！
    </div>
  `;
  
  // 设置确认按钮事件
  const confirmBtn = document.getElementById('confirmModalConfirmBtn');
  confirmBtn.textContent = '删除';
  confirmBtn.className = 'btn btn-danger';
  
  // 移除之前的事件监听器
  const newConfirmBtn = confirmBtn.cloneNode(true);
  confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
  
  // 添加新的事件监听器
  newConfirmBtn.addEventListener('click', function() {
    deleteOrder(orderId);
    confirmModal.hide();
  });
  
  // 显示确认对话框
  confirmModal.show();
}

// 如果不存在确认对话框模态框，则创建一个
function createConfirmModal() {
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = 'confirmModal';
  modal.tabIndex = '-1';
  modal.setAttribute('aria-labelledby', 'confirmModalTitle');
  modal.setAttribute('aria-hidden', 'true');
  
  modal.innerHTML = `
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="confirmModalTitle">确认操作</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body" id="confirmModalBody">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="confirmModalConfirmBtn">确认</button>
        </div>
      </div>
    </div>
  `;
  
  document.body.appendChild(modal);
  return modal;
}

// 删除订单
function deleteOrder(orderId) {
  const token = localStorage.getItem('token');
  if (!token) return;
  
  // 显示加载中
  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    loadingElement.style.display = 'flex';
  }
  
  // 发送删除请求
  fetch(`/api/admin/payment/orders/${orderId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => response.json())
  .then(data => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
    
    if (data.success) {
      // 显示成功提示
      showToast('success', '订单删除成功');
      
      // 重新加载当前页的订单数据
      const currentPage = document.querySelector('#ordersPagination .page-item.active .page-link');
      loadPaymentOrdersPage(currentPage ? parseInt(currentPage.textContent) : 1);
    } else {
      // 显示错误提示
      showToast('error', data.message || '订单删除失败');
    }
  })
  .catch(error => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
    
    console.error('删除订单失败:', error);
    showToast('error', '删除订单失败，请稍后重试');
  });
}

// 显示提示消息
function showToast(type, message) {
  // 如果页面中没有toast容器，则创建一个
  let toastContainer = document.querySelector('.toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(toastContainer);
  }
  
  // 创建toast元素
  const toastElement = document.createElement('div');
  toastElement.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
  toastElement.setAttribute('role', 'alert');
  toastElement.setAttribute('aria-live', 'assertive');
  toastElement.setAttribute('aria-atomic', 'true');
  
  toastElement.innerHTML = `
    <div class="d-flex">
      <div class="toast-body">
        ${message}
      </div>
      <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
  `;
  
  // 添加到容器中
  toastContainer.appendChild(toastElement);
  
  // 创建Bootstrap Toast实例并显示
  const toast = new bootstrap.Toast(toastElement, {
    delay: 3000
  });
  toast.show();
  
  // 自动移除
  toastElement.addEventListener('hidden.bs.toast', () => {
    toastElement.remove();
  });
}

// 显示关闭订单确认对话框
function showCloseOrderConfirm(orderId) {
  // 使用通用的确认对话框
  const confirmModal = document.getElementById('confirmModal');
  if (!confirmModal) {
    createConfirmModal();
  }
  
  // 设置确认对话框内容
  document.getElementById('confirmModalTitle').textContent = '关闭订单';
  document.getElementById('confirmModalBody').textContent = '确定要关闭此订单吗？关闭后订单状态将变为"已关闭"，且不可恢复。';
  
  // 设置确认按钮事件
  const confirmBtn = document.getElementById('confirmModalYesBtn');
  confirmBtn.textContent = '确认关闭';
  confirmBtn.className = 'btn btn-warning';
  
  // 移除之前的事件监听器
  const newConfirmBtn = confirmBtn.cloneNode(true);
  confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
  
  // 添加新的事件监听器
  newConfirmBtn.addEventListener('click', function() {
    closeOrder(orderId);
    const confirmModalInstance = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
    confirmModalInstance.hide();
  });
  
  // 显示确认对话框
  const confirmModalInstance = new bootstrap.Modal(document.getElementById('confirmModal'));
  confirmModalInstance.show();
}

// 关闭订单
function closeOrder(orderId) {
  const token = localStorage.getItem('token');
  if (!token) return;
  
  // 显示加载中
  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    loadingElement.style.display = 'flex';
  }
  
  // 发送关闭订单请求
  fetch(`/api/admin/payment/orders/${orderId}/close`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
    
    if (data.success) {
      // 显示成功提示
      showToast('success', '订单已成功关闭');
      
      // 重新加载当前页的订单数据
      const currentPage = document.querySelector('#ordersPagination .page-item.active .page-link');
      loadPaymentOrdersPage(currentPage ? parseInt(currentPage.textContent) : 1);
    } else {
      // 显示错误提示
      showToast('error', data.message || '关闭订单失败');
    }
  })
  .catch(error => {
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
    
    console.error('关闭订单失败:', error);
    showToast('error', '关闭订单失败，请稍后重试');
  });
}

// ==================== 安全管理相关功能 ====================

/**
 * 加载安全管理页面
 */
function loadSecurityManagement() {
  loadSecurityStatistics();
  loadSecurityRulesConfig();
  initSecurityEventListeners();
}

/**
 * 加载安全检查记录页面
 */
function loadSecurityCheckRecords() {
  if (window.securityCheckRecordsModule) {
    window.securityCheckRecordsModule.init();
  } else {
    console.error('安全检查记录模块未加载');
  }
}

/**
 * 加载安全统计数据
 */
function loadSecurityStatistics() {
  fetch('/api/admin/security/statistics', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      const stats = data.data;

      // 更新统计卡片
      document.getElementById('safeFilesCount').textContent = stats.safeFilesCount || '--';
      document.getElementById('riskFilesCount').textContent = stats.riskFilesCount || '--';
      document.getElementById('blockedFilesCount').textContent = stats.blockedFilesCount || '--';
      document.getElementById('detectionRate').textContent = stats.detectionRate || '--';

      // 绘制风险等级分布图表
      drawRiskLevelChart(stats.riskLevelDistribution || {});
    } else {
      console.error('加载安全统计数据失败:', data.message);
    }
  })
  .catch(error => {
    console.error('加载安全统计数据失败:', error);
  });
}

/**
 * 加载安全违规记录
 */
function loadSecurityViolations(page = 1, riskLevel = '') {
  const params = new URLSearchParams({
    page: page,
    limit: 10
  });

  if (riskLevel) {
    params.append('riskLevel', riskLevel);
  }

  fetch(`/api/admin/security/violations?${params}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      const tbody = document.querySelector('#securityViolationsTable tbody');
      tbody.innerHTML = '';

      data.data.forEach(violation => {
        const row = document.createElement('tr');

        // 格式化时间
        const uploadTime = new Date(violation.uploadTime).toLocaleString('zh-CN');

        // 风险等级样式
        const riskLevelClass = {
          'CRITICAL': 'danger',
          'HIGH': 'warning',
          'MEDIUM': 'info',
          'LOW': 'secondary'
        }[violation.riskLevel] || 'secondary';

        // 威胁列表
        const threats = violation.detectedThreats ?
          violation.detectedThreats.slice(0, 2).join(', ') +
          (violation.detectedThreats.length > 2 ? '...' : '') :
          '无';

        // 文件大小格式化
        const fileSize = violation.fileSize ?
          (violation.fileSize / 1024).toFixed(1) + ' KB' :
          '未知';

        row.innerHTML = `
          <td>${uploadTime}</td>
          <td>${violation.user ? violation.user.phone || violation.user.nickname : '未知用户'}</td>
          <td>${violation.fileName || '未知文件'}</td>
          <td><span class="badge bg-${riskLevelClass}">${violation.riskLevel}</span></td>
          <td title="${violation.detectedThreats ? violation.detectedThreats.join(', ') : ''}">${threats}</td>
          <td>${fileSize}</td>
          <td>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteSecurityViolation(${violation.id})">
              <i class="bi bi-trash"></i> 删除
            </button>
          </td>
        `;

        tbody.appendChild(row);
      });

      // 更新分页信息（如果需要）
      // updatePagination('securityViolationsPagination', data.pagination);
    } else {
      console.error('加载安全违规记录失败:', data.message);
    }
  })
  .catch(error => {
    console.error('加载安全违规记录失败:', error);
  });
}

/**
 * 加载安全规则配置
 */
function loadSecurityRulesConfig() {
  fetch('/api/admin/config/security-rules', {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      const config = data.data;

      // 更新安全规则开关状态
      document.getElementById('xssDetectionEnabled').checked = config.enableXssDetection;
      document.getElementById('maliciousScriptEnabled').checked = config.enableMaliciousScriptDetection;
      document.getElementById('dangerousTagsEnabled').checked = config.enableDangerousTagDetection;
      document.getElementById('externalResourcesEnabled').checked = config.enableExternalResourceValidation;
    } else {
      console.error('加载安全规则配置失败:', data.message);
    }
  })
  .catch(error => {
    console.error('加载安全规则配置失败:', error);
  });
}

/**
 * 初始化安全管理页面事件监听器
 */
function initSecurityEventListeners() {
  // 风险等级筛选
  const riskLevelFilter = document.getElementById('riskLevelFilter');
  if (riskLevelFilter) {
    riskLevelFilter.addEventListener('change', function() {
      loadSecurityViolations(1, this.value);
    });
  }

  // 刷新安全记录按钮
  const refreshBtn = document.getElementById('refreshSecurityRecords');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', function() {
      loadSecurityViolations();
    });
  }

  // 安全规则配置表单提交
  const securityRulesForm = document.getElementById('securityRulesForm');
  if (securityRulesForm) {
    securityRulesForm.addEventListener('submit', function(e) {
      e.preventDefault();
      saveSecurityRulesConfig();
    });
  }
}

/**
 * 保存安全规则配置
 */
function saveSecurityRulesConfig() {
  const config = {
    enableXssDetection: document.getElementById('xssDetectionEnabled').checked,
    enableMaliciousScriptDetection: document.getElementById('maliciousScriptEnabled').checked,
    enableDangerousTagDetection: document.getElementById('dangerousTagsEnabled').checked,
    enableExternalResourceValidation: document.getElementById('externalResourcesEnabled').checked
  };

  fetch('/api/admin/config/security-rules', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(config)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      showToast('success', '安全规则配置保存成功');
    } else {
      showToast('error', data.message || '保存安全规则配置失败');
    }
  })
  .catch(error => {
    console.error('保存安全规则配置失败:', error);
    showToast('error', '保存安全规则配置失败，请稍后重试');
  });
}

/**
 * 删除安全违规记录
 */
function deleteSecurityViolation(id) {
  if (!confirm('确定要删除这条安全违规记录吗？')) {
    return;
  }

  fetch(`/api/admin/security/violations/${id}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      showToast('success', '安全违规记录删除成功');
      loadSecurityViolations(); // 重新加载列表
    } else {
      showToast('error', data.message || '删除安全违规记录失败');
    }
  })
  .catch(error => {
    console.error('删除安全违规记录失败:', error);
    showToast('error', '删除安全违规记录失败，请稍后重试');
  });
}

/**
 * 绘制风险等级分布图表
 */
function drawRiskLevelChart(riskData) {
  const canvas = document.getElementById('riskLevelChart');
  if (!canvas) return;

  const ctx = canvas.getContext('2d');

  // 清除画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 数据准备
  const labels = ['低风险', '中风险', '高风险', '严重'];
  const data = [
    riskData.LOW || 0,
    riskData.MEDIUM || 0,
    riskData.HIGH || 0,
    riskData.CRITICAL || 0
  ];
  const colors = ['#28a745', '#ffc107', '#fd7e14', '#dc3545'];

  // 简单的柱状图绘制
  const maxValue = Math.max(...data, 1);
  const barWidth = canvas.width / labels.length - 20;
  const barMaxHeight = canvas.height - 60;

  data.forEach((value, index) => {
    const barHeight = (value / maxValue) * barMaxHeight;
    const x = index * (barWidth + 20) + 10;
    const y = canvas.height - barHeight - 30;

    // 绘制柱子
    ctx.fillStyle = colors[index];
    ctx.fillRect(x, y, barWidth, barHeight);

    // 绘制数值
    ctx.fillStyle = '#333';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(value.toString(), x + barWidth / 2, y - 5);

    // 绘制标签
    ctx.fillText(labels[index], x + barWidth / 2, canvas.height - 10);
  });
}

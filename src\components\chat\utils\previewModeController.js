/**
 * 双模式预览控制器
 * 实现标准模式和高级模式的无缝切换，为用户提供灵活的预览和编辑体验
 * 基于featureConfig.js的功能配置管理器
 */

import logger from '../../../services/logs/frontendLogger';
import featureConfig, { enableFeature, disableFeature, isFeatureEnabled } from './featureConfig';

/**
 * 预览模式枚举
 */
export const PREVIEW_MODES = {
  STANDARD: {
    id: 'standard',
    name: '标准模式',
    description: '兼容性优先，支持所有基础功能',
    security: 'basic',
    compatibility: 'high',
    features: 'standard',
    icon: '🛡️',
    color: 'blue'
  },
  ADVANCED: {
    id: 'advanced', 
    name: '高级模式',
    description: '增强功能，智能安全策略',
    security: 'enhanced',
    compatibility: 'medium', 
    features: 'advanced',
    icon: '⚡',
    color: 'purple'
  }
};

/**
 * 预览模式控制器类
 */
class PreviewModeController {
  constructor() {
    this.currentMode = PREVIEW_MODES.STANDARD;
    this.listeners = new Map();
    this.isTransitioning = false;
    this.userPreference = null;
    this.initialized = false;
  }

  /**
   * 初始化控制器
   */
  init() {
    if (this.initialized) {
      return;
    }

    try {
      // 初始化功能配置管理器
      featureConfig.init();

      // 从localStorage读取用户偏好
      this.loadUserPreference();

      // 根据当前功能配置确定初始模式
      this.syncModeWithFeatureConfig();

      this.initialized = true;
      logger.info('预览模式控制器初始化完成', {
        currentMode: this.currentMode.id,
        userPreference: this.userPreference
      });

    } catch (error) {
      logger.error('预览模式控制器初始化失败', { error: error.message });
      // 初始化失败时使用默认标准模式
      this.currentMode = PREVIEW_MODES.STANDARD;
      this.initialized = true;
    }
  }

  /**
   * 从localStorage加载用户偏好
   */
  loadUserPreference() {
    try {
      const saved = localStorage.getItem('chatPreview_previewMode');
      if (saved) {
        const preference = JSON.parse(saved);
        if (PREVIEW_MODES[preference.toUpperCase()]) {
          this.userPreference = preference;
        }
      }
    } catch (error) {
      logger.warn('加载用户预览模式偏好失败', { error: error.message });
    }
  }

  /**
   * 保存用户偏好到localStorage
   */
  saveUserPreference() {
    try {
      localStorage.setItem('chatPreview_previewMode', JSON.stringify(this.currentMode.id));
      this.userPreference = this.currentMode.id;
    } catch (error) {
      logger.warn('保存用户预览模式偏好失败', { error: error.message });
    }
  }

  /**
   * 同步模式与功能配置
   */
  syncModeWithFeatureConfig() {
    const isAdvancedEnabled = isFeatureEnabled('ADVANCED_HTML_LOADER');
    
    if (isAdvancedEnabled) {
      this.currentMode = PREVIEW_MODES.ADVANCED;
    } else {
      this.currentMode = PREVIEW_MODES.STANDARD;
    }
  }

  /**
   * 获取当前模式
   * @returns {Object} 当前预览模式
   */
  getCurrentMode() {
    if (!this.initialized) {
      this.init();
    }
    return this.currentMode;
  }

  /**
   * 获取所有可用模式
   * @returns {Array} 所有预览模式
   */
  getAllModes() {
    return Object.values(PREVIEW_MODES);
  }

  /**
   * 切换到指定模式
   * @param {string} modeId - 模式ID
   * @param {Object} options - 切换选项
   * @returns {Promise<boolean>} 是否切换成功
   */
  async switchToMode(modeId, options = {}) {
    const { 
      skipConfirmation = false, 
      preserveState = true,
      showAnimation = true 
    } = options;

    if (!this.initialized) {
      this.init();
    }

    // 验证模式ID
    const targetMode = Object.values(PREVIEW_MODES).find(mode => mode.id === modeId);
    if (!targetMode) {
      logger.error('无效的预览模式ID', { modeId });
      return false;
    }

    // 如果已经是目标模式，直接返回
    if (this.currentMode.id === targetMode.id) {
      logger.info('已经是目标模式，无需切换', { modeId });
      return true;
    }

    // 检查是否正在切换中
    if (this.isTransitioning) {
      logger.warn('模式切换正在进行中，请稍后再试');
      return false;
    }

    try {
      this.isTransitioning = true;

      // 通知切换开始
      this.notifyListeners('switchStart', {
        from: this.currentMode,
        to: targetMode,
        options
      });

      // 保存当前状态（如果需要）
      let savedState = null;
      if (preserveState) {
        savedState = await this.saveCurrentState();
      }

      // 执行模式切换
      const success = await this.performModeSwitch(targetMode, savedState);

      if (success) {
        const previousMode = this.currentMode;
        this.currentMode = targetMode;

        // 保存用户偏好
        this.saveUserPreference();

        // 通知切换完成
        this.notifyListeners('switchComplete', {
          from: previousMode,
          to: targetMode,
          success: true
        });

        logger.info('预览模式切换成功', { 
          from: previousMode.id, 
          to: targetMode.id 
        });

        return true;
      } else {
        // 切换失败，通知监听器
        this.notifyListeners('switchComplete', {
          from: this.currentMode,
          to: targetMode,
          success: false
        });

        logger.error('预览模式切换失败', { 
          from: this.currentMode.id, 
          to: targetMode.id 
        });

        return false;
      }

    } catch (error) {
      logger.error('预览模式切换出错', { 
        error: error.message,
        from: this.currentMode.id,
        to: targetMode.id
      });

      // 通知切换失败
      this.notifyListeners('switchComplete', {
        from: this.currentMode,
        to: targetMode,
        success: false,
        error: error.message
      });

      return false;

    } finally {
      this.isTransitioning = false;
    }
  }

  /**
   * 执行实际的模式切换
   * @param {Object} targetMode - 目标模式
   * @param {Object} savedState - 保存的状态
   * @returns {Promise<boolean>} 是否成功
   */
  async performModeSwitch(targetMode, savedState) {
    try {
      // 添加模式切换标记，禁用虚线框样式
      document.body.classList.add('mode-switching');
      logger.info('添加模式切换标记，禁用虚线框样式');
      
      // 1. 触发自动保存（如果有内容需要保存）
      await this.triggerAutoSave();

      // 2. 根据目标模式启用/禁用相应功能
      if (targetMode.id === 'advanced') {
        // 启用高级模式功能
        const success = enableFeature('ADVANCED_HTML_LOADER', true);
        if (!success) {
          throw new Error('启用高级HTML加载器失败');
        }
      } else {
        // 禁用高级模式功能，使用标准模式
        disableFeature('ADVANCED_HTML_LOADER', true);
      }

      // 3. 等待配置生效
      await new Promise(resolve => setTimeout(resolve, 200));

      // 4. 温和地刷新预览内容（不使用硬刷新）
      await this.refreshPreviewContent(targetMode);

      // 5. 恢复状态（如果有）
      if (savedState) {
        await this.restoreState(savedState);
      }

      return true;

    } catch (error) {
      logger.error('执行模式切换失败', { error: error.message });
      return false;
    } finally {
      // 无论成功与否，都移除模式切换标记，恢复正常样式
      // 延迟一点移除，确保新模式完全加载
      setTimeout(() => {
        document.body.classList.remove('mode-switching');
        logger.info('移除模式切换标记，恢复正常样式');
      }, 500);
    }
  }

  /**
   * 保存当前状态
   * @returns {Promise<Object>} 保存的状态
   */
  async saveCurrentState() {
    try {
      const state = {
        timestamp: Date.now(),
        mode: this.currentMode.id,
        editingState: null,
        historyState: null,
        uiState: null
      };

      // 保存编辑状态
      try {
        const iframe = document.querySelector('iframe[data-preview-iframe="true"]');
        if (iframe && iframe.contentDocument) {
          const editableElements = iframe.contentDocument.querySelectorAll('[contenteditable="true"]');
          const editingElements = [];

          editableElements.forEach((element, index) => {
            if (element.dataset.editing === 'true' || element.classList.contains('drag-mode')) {
              editingElements.push({
                index,
                id: element.id,
                isEditing: element.dataset.editing === 'true',
                isDragMode: element.classList.contains('drag-mode'),
                content: element.innerHTML,
                styles: element.getAttribute('style') || ''
              });
            }
          });

          if (editingElements.length > 0) {
            state.editingState = { editingElements };
          }
        }
      } catch (error) {
        logger.warn('保存编辑状态失败', { error: error.message });
      }

      // 保存历史记录状态
      try {
        if (window.editorHistoryRef && window.editorHistoryRef.current) {
          const history = window.editorHistoryRef.current;
          state.historyState = {
            canUndo: history.canUndo(),
            canRedo: history.canRedo(),
            // 不保存完整历史记录，只保存状态信息
          };
        }
      } catch (error) {
        logger.warn('保存历史记录状态失败', { error: error.message });
      }

      // 保存UI状态
      try {
        const previewContainer = document.querySelector('[data-preview-container="true"]');
        if (previewContainer) {
          state.uiState = {
            scale: previewContainer.dataset.scale || '1',
            scrollTop: previewContainer.scrollTop || 0,
            scrollLeft: previewContainer.scrollLeft || 0
          };
        }
      } catch (error) {
        logger.warn('保存UI状态失败', { error: error.message });
      }

      logger.info('当前状态保存完成', {
        hasEditingState: !!state.editingState,
        hasHistoryState: !!state.historyState,
        hasUiState: !!state.uiState
      });

      return state;

    } catch (error) {
      logger.warn('保存当前状态失败', { error: error.message });
      return null;
    }
  }

  /**
   * 恢复状态
   * @param {Object} state - 要恢复的状态
   * @returns {Promise<boolean>} 是否成功
   */
  async restoreState(state) {
    try {
      if (!state) return true;

      let restoredCount = 0;

      // 恢复编辑状态
      if (state.editingState) {
        try {
          // 等待iframe加载完成
          await new Promise(resolve => setTimeout(resolve, 500));

          const iframe = document.querySelector('iframe[data-preview-iframe="true"]');
          if (iframe && iframe.contentDocument) {
            const editableElements = iframe.contentDocument.querySelectorAll('[contenteditable="true"]');

            state.editingState.editingElements.forEach(savedElement => {
              const element = editableElements[savedElement.index] ||
                             iframe.contentDocument.getElementById(savedElement.id);

              if (element) {
                // 恢复内容和样式
                if (savedElement.content) {
                  element.innerHTML = savedElement.content;
                }
                if (savedElement.styles) {
                  element.setAttribute('style', savedElement.styles);
                }

                // 恢复编辑状态
                if (savedElement.isEditing) {
                  element.dataset.editing = 'true';
                  element.focus();
                }

                // 恢复拖拽模式
                if (savedElement.isDragMode) {
                  element.classList.add('drag-mode');
                }

                restoredCount++;
              }
            });
          }
        } catch (error) {
          logger.warn('恢复编辑状态失败', { error: error.message });
        }
      }

      // 恢复UI状态
      if (state.uiState) {
        try {
          const previewContainer = document.querySelector('[data-preview-container="true"]');
          if (previewContainer) {
            if (state.uiState.scale) {
              previewContainer.dataset.scale = state.uiState.scale;
            }
            if (state.uiState.scrollTop) {
              previewContainer.scrollTop = state.uiState.scrollTop;
            }
            if (state.uiState.scrollLeft) {
              previewContainer.scrollLeft = state.uiState.scrollLeft;
            }
            restoredCount++;
          }
        } catch (error) {
          logger.warn('恢复UI状态失败', { error: error.message });
        }
      }

      logger.info('状态恢复完成', {
        timestamp: state.timestamp,
        restoredCount,
        mode: state.mode
      });

      return true;

    } catch (error) {
      logger.warn('恢复状态失败', { error: error.message });
      return false;
    }
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  addListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  removeListener(event, callback) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * 通知监听器
   * @param {string} event - 事件名称
   * @param {Object} data - 事件数据
   */
  notifyListeners(event, data) {
    const callbacks = this.listeners.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          logger.error('事件监听器执行失败', { event, error: error.message });
        }
      });
    }
  }

  /**
   * 触发自动保存
   * @returns {Promise<boolean>} 是否保存成功
   */
  async triggerAutoSave() {
    try {
      // 检查是否有自动保存管理器
      if (window.autoSaveControllerRef && window.autoSaveControllerRef.current) {
        const autoSaveManager = window.autoSaveControllerRef.current;
        const success = await autoSaveManager.save('mode_switch');
        logger.info('模式切换触发自动保存', { success });
        return success;
      }

      // 如果没有自动保存管理器，尝试触发手动保存事件
      const saveEvent = new CustomEvent('triggerSave', {
        detail: { source: 'mode_switch', timestamp: Date.now() }
      });
      window.dispatchEvent(saveEvent);
      logger.info('模式切换触发保存事件');

      return true;
    } catch (error) {
      logger.warn('模式切换自动保存失败', { error: error.message });
      return false;
    }
  }

  /**
   * 温和地刷新预览内容
   * @param {Object} targetMode - 目标模式
   * @returns {Promise<boolean>} 是否刷新成功
   */
  async refreshPreviewContent(targetMode) {
    try {
      // 查找预览iframe
      const iframe = document.querySelector('.chat-preview-container iframe') ||
                    document.querySelector('[data-preview-iframe="true"]');

      if (!iframe || !iframe.contentDocument) {
        logger.warn('未找到预览iframe，跳过内容刷新');
        return true;
      }

      // 获取当前HTML内容
      const currentHtml = iframe.contentDocument.documentElement.outerHTML;

      // 通知预览区域重新加载内容
      const event = new CustomEvent('modeSwitch', {
        detail: {
          mode: targetMode,
          htmlContent: currentHtml,
          timestamp: Date.now(),
          isModeSwitchEvent: true  // 添加模式切换标志
        }
      });

      // 向iframe发送模式切换事件
      if (iframe.contentWindow) {
        iframe.contentWindow.dispatchEvent(event);
      }

      // 向父窗口发送模式切换事件
      window.dispatchEvent(event);

      logger.info('预览内容刷新完成', { mode: targetMode.id });
      return true;

    } catch (error) {
      logger.error('刷新预览内容失败', { error: error.message });
      return false;
    }
  }

  /**
   * 获取模式切换状态
   * @returns {boolean} 是否正在切换
   */
  isTransitionInProgress() {
    return this.isTransitioning;
  }

  /**
   * 重置控制器
   */
  reset() {
    this.currentMode = PREVIEW_MODES.STANDARD;
    this.isTransitioning = false;
    this.userPreference = null;
    this.listeners.clear();
    localStorage.removeItem('chatPreview_previewMode');
    logger.info('预览模式控制器已重置');
  }
}

// 创建全局单例实例
const previewModeController = new PreviewModeController();

// 导出实例和相关常量
export default previewModeController;

// 便捷方法导出
export const getCurrentMode = () => previewModeController.getCurrentMode();
export const switchToMode = (modeId, options) => previewModeController.switchToMode(modeId, options);
export const getAllModes = () => previewModeController.getAllModes();
export const addModeListener = (event, callback) => previewModeController.addListener(event, callback);
export const removeModeListener = (event, callback) => previewModeController.removeListener(event, callback);

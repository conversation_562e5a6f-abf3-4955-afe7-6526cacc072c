const jwt = require('jsonwebtoken');
require('dotenv').config();

/**
 * 生成JWT令牌
 * @param {Object} user - 用户对象
 * @returns {string} - JWT令牌
 */
const generateToken = (user) => {
  const payload = {
    id: user.id,
    phone: user.phone,
    role: user.role
  };

  return jwt.sign(
    payload,
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );
};

/**
 * 验证JWT令牌
 * @param {string} token - JWT令牌
 * @returns {Object|null} - 解码后的用户信息，如果验证失败则返回null
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    return null;
  }
};

module.exports = {
  generateToken,
  verifyToken
};

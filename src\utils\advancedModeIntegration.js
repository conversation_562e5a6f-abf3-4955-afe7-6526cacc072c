/**
 * 高级模式集成控制器
 * 第四阶段：高级模式改造与服务端渲染集成
 * 
 * 将现有高级模式功能集成到新的HTML安全处理架构中
 * 提供简化的用户选择开关，与后端渲染系统无缝集成
 */

import logger from '../services/logs/frontendLogger.js';
import { RenderingModeSelector } from './renderingModeSelector.js';
import { ServerSideRenderer } from './serverSideRenderer.js';
import { SecurityDetector } from './securityDetector.js';
import systemConfigManager from './systemConfigManager.js';

/**
 * 默认高级模式配置（仅作为回退配置使用）
 * 实际配置从后台动态获取
 */
const DEFAULT_ADVANCED_MODE_CONFIG = {
  // 性能优化功能
  PERFORMANCE_OPTIMIZATION: {
    enabled: true,
    name: '性能优化',
    description: '字体渲染优化、图像质量优化、加载性能提升',
    css: `
      /* 高级模式性能优化 */
      * {
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      img {
        image-rendering: optimizeQuality;
        loading: lazy;
      }

      .img-error {
        display: inline-block;
        padding: 10px;
        background: #f5f5f5;
        border: 1px dashed #ccc;
        color: #666;
        font-size: 12px;
        text-align: center;
        min-width: 100px;
        min-height: 50px;
      }
    `
  },

  // 兼容性增强
  COMPATIBILITY_ENHANCEMENT: {
    enabled: true,
    name: '兼容性增强',
    description: '跨浏览器兼容性增强、CSS前缀自动添加',
    css: `
      /* 兼容性增强 */
      .advanced-compatibility {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
      }
    `
  },

  // 资源优化
  RESOURCE_OPTIMIZATION: {
    enabled: true,
    name: '资源优化',
    description: '自动优化图片加载、处理CORS问题、资源预加载'
  }
};

/**
 * 高级模式控制器类
 */
export class AdvancedModeController {
  constructor() {
    this.isEnabled = false;
    this.config = { ...DEFAULT_ADVANCED_MODE_CONFIG };
    this.backendConfig = null; // 后台配置
    this.listeners = new Set();
    this.initialized = false;
  }

  /**
   * 初始化高级模式控制器
   */
  async initialize() {
    try {
      // 1. 从后台获取渲染方式配置
      await this.loadBackendConfig();

      // 2. 从本地存储加载用户偏好（如果用户有手动设置）
      this.loadUserPreference();

      // 3. 如果用户没有手动设置，使用后台默认配置
      if (!this.hasUserPreference()) {
        this.applyBackendDefaultConfig();
      }

      this.initialized = true;

      logger.info('高级模式控制器初始化成功', {
        enabled: this.isEnabled,
        backendConfig: this.backendConfig,
        hasUserPreference: this.hasUserPreference()
      });

      return true;
    } catch (error) {
      logger.error('高级模式控制器初始化失败', { error: error.message });
      return false;
    }
  }

  /**
   * 检查高级模式状态
   * @returns {boolean} 高级模式是否启用
   */
  isAdvancedModeEnabled() {
    return this.isEnabled;
  }

  /**
   * 切换高级模式
   * @param {boolean} enabled - 是否启用高级模式
   * @returns {boolean} 切换是否成功
   */
  toggleAdvancedMode(enabled = null) {
    try {
      const newState = enabled !== null ? enabled : !this.isEnabled;
      const oldState = this.isEnabled;
      
      this.isEnabled = newState;
      this.saveUserPreference();
      
      // 通知监听器
      this.notifyListeners('modeChanged', {
        oldState,
        newState,
        timestamp: Date.now()
      });
      
      logger.info('高级模式状态切换', {
        from: oldState,
        to: newState
      });
      
      return true;
    } catch (error) {
      logger.error('高级模式切换失败', { error: error.message });
      return false;
    }
  }

  /**
   * 获取高级模式配置
   * @returns {Object} 高级模式配置
   */
  getAdvancedModeConfig() {
    return {
      enabled: this.isEnabled,
      features: this.config,
      metadata: {
        initialized: this.initialized,
        timestamp: Date.now()
      }
    };
  }

  /**
   * 与渲染器集成
   * @param {string} htmlContent - HTML内容
   * @param {Object} securityResult - 安全检测结果
   * @returns {Object} 集成处理结果
   */
  async integrateWithRenderer(htmlContent, securityResult) {
    try {
      // 1. 选择渲染模式
      const renderingMode = RenderingModeSelector.selectMode(
        htmlContent, 
        securityResult, 
        { advancedMode: this.isEnabled }
      );
      
      // 2. 应用高级模式增强（如果启用）
      let processedContent = htmlContent;
      if (this.isEnabled) {
        processedContent = this.applyAdvancedEnhancements(htmlContent);
      }
      
      // 3. 服务端渲染（如果需要）
      let finalContent = processedContent;
      if (renderingMode.useServerSideRendering) {
        finalContent = ServerSideRenderer.generateCompleteHtml(
          processedContent,
          { advancedMode: this.isEnabled }
        );
      }
      
      return {
        success: true,
        renderingMode,
        originalContent: htmlContent,
        processedContent,
        finalContent,
        advancedModeEnabled: this.isEnabled,
        metadata: {
          processingTime: Date.now(),
          contentLength: finalContent.length
        }
      };
      
    } catch (error) {
      logger.error('渲染器集成失败', { error: error.message });
      
      return {
        success: false,
        error: error.message,
        originalContent: htmlContent,
        finalContent: htmlContent,
        advancedModeEnabled: this.isEnabled
      };
    }
  }

  /**
   * 应用高级模式增强
   * @param {string} htmlContent - 原始HTML内容
   * @returns {string} 增强后的HTML内容
   */
  applyAdvancedEnhancements(htmlContent) {
    try {
      let enhancedContent = htmlContent;
      
      // 添加性能优化CSS
      if (this.config.PERFORMANCE_OPTIMIZATION.enabled) {
        const performanceCSS = `<style data-advanced-mode="performance">${this.config.PERFORMANCE_OPTIMIZATION.css}</style>`;
        
        if (enhancedContent.includes('</head>')) {
          enhancedContent = enhancedContent.replace('</head>', `${performanceCSS}\n</head>`);
        } else {
          enhancedContent = `${performanceCSS}\n${enhancedContent}`;
        }
      }
      
      // 添加兼容性增强CSS
      if (this.config.COMPATIBILITY_ENHANCEMENT.enabled) {
        const compatibilityCSS = `<style data-advanced-mode="compatibility">${this.config.COMPATIBILITY_ENHANCEMENT.css}</style>`;
        
        if (enhancedContent.includes('</head>')) {
          enhancedContent = enhancedContent.replace('</head>', `${compatibilityCSS}\n</head>`);
        } else {
          enhancedContent = `${compatibilityCSS}\n${enhancedContent}`;
        }
      }
      
      return enhancedContent;
      
    } catch (error) {
      logger.error('高级模式增强应用失败', { error: error.message });
      return htmlContent;
    }
  }

  /**
   * 添加事件监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    if (typeof listener === 'function') {
      this.listeners.add(listener);
    }
  }

  /**
   * 移除事件监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    this.listeners.delete(listener);
  }

  /**
   * 通知监听器
   * @param {string} event - 事件类型
   * @param {Object} data - 事件数据
   */
  notifyListeners(event, data) {
    this.listeners.forEach(listener => {
      try {
        listener(event, data);
      } catch (error) {
        logger.error('监听器通知失败', { error: error.message });
      }
    });
  }

  /**
   * 从后台加载配置
   */
  async loadBackendConfig() {
    try {
      this.backendConfig = await systemConfigManager.getRenderingModeConfig();

      logger.info('后台配置加载成功', {
        defaultRenderingMode: this.backendConfig.defaultRenderingMode,
        enableAdvancedMode: this.backendConfig.enableAdvancedMode
      });
    } catch (error) {
      logger.error('加载后台配置失败', { error: error.message });
      // 使用默认配置作为回退
      this.backendConfig = {
        defaultRenderingMode: 'SERVER_SIDE_RENDERING',
        enableAdvancedMode: true,
        fallbackMode: 'CLIENT_SIDE_SAFE_LOADING'
      };
    }
  }

  /**
   * 应用后台默认配置
   */
  applyBackendDefaultConfig() {
    if (this.backendConfig) {
      // 根据后台配置决定是否启用高级模式
      const shouldEnableAdvanced = this.backendConfig.enableAdvancedMode &&
                                   this.backendConfig.defaultRenderingMode === 'SERVER_SIDE_RENDERING';

      this.isEnabled = shouldEnableAdvanced;

      logger.info('应用后台默认配置', {
        defaultRenderingMode: this.backendConfig.defaultRenderingMode,
        enableAdvancedMode: this.backendConfig.enableAdvancedMode,
        resultEnabled: this.isEnabled
      });
    }
  }

  /**
   * 检查用户是否有手动偏好设置
   */
  hasUserPreference() {
    try {
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        return localStorage.getItem('fengmian_advanced_mode') !== null;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 加载用户偏好
   */
  loadUserPreference() {
    try {
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        const saved = localStorage.getItem('fengmian_advanced_mode');
        if (saved !== null) {
          this.isEnabled = JSON.parse(saved);
        }
      }
    } catch (error) {
      logger.error('加载用户偏好失败', { error: error.message });
    }
  }

  /**
   * 保存用户偏好
   */
  saveUserPreference() {
    try {
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        localStorage.setItem('fengmian_advanced_mode', JSON.stringify(this.isEnabled));
      }
    } catch (error) {
      logger.error('保存用户偏好失败', { error: error.message });
    }
  }
}

// 创建全局实例
export const advancedModeController = new AdvancedModeController();

// 默认导出
export default {
  AdvancedModeController,
  advancedModeController,
  DEFAULT_ADVANCED_MODE_CONFIG
};

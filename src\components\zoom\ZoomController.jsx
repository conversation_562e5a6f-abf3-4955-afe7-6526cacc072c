import React from 'react';
import { ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';
import './ZoomController.css';

/**
 * 水平缩放控制器组件
 * 
 * @param {Object} props - 组件属性
 * @param {number} props.scale - 当前的缩放比例
 * @param {number} props.minScale - 最小缩放比例
 * @param {number} props.maxScale - 最大缩放比例
 * @param {number} props.step - 缩放步长
 * @param {function} props.onScaleChange - 缩放比例变化时的回调函数
 * @param {function} props.onResetScale - 重置缩放时的回调函数
 * @param {string} props.className - 自定义CSS类名
 */
const ZoomController = ({
  scale = 1.0,
  minScale = 0.05,
  maxScale = 3.0,
  step = 0.05,
  onScaleChange,
  onResetScale,
  className = '',
}) => {

  // 处理滑块值变化
  const handleSliderChange = (e) => {
    const newScale = parseFloat(e.target.value);
    if (onScaleChange) {
      onScaleChange(newScale);
    }
  };

  // 处理缩小按钮点击
  const handleZoomOut = () => {
    const newScale = Math.max(minScale, scale - step);
    if (onScaleChange) {
      onScaleChange(newScale);
    }
  };

  // 处理放大按钮点击
  const handleZoomIn = () => {
    const newScale = Math.min(maxScale, scale + step);
    if (onScaleChange) {
      onScaleChange(newScale);
    }
  };

  // 处理重置按钮点击
  const handleReset = () => {
    if (onResetScale) {
      onResetScale();
    }
  };

  return (
    <div className={`zoom-controller ${className}`}>
      <button 
        className="zoom-controller-button zoom-out"
        onClick={handleZoomOut}
        disabled={scale <= minScale}
        title="缩小"
      >
        <ZoomOut size={16} />
      </button>
      <input
        type="range"
        min={minScale}
        max={maxScale}
        step={step}
        value={scale}
        onChange={handleSliderChange}
        className="zoom-controller-slider"
      />
      <button 
        className="zoom-controller-button zoom-in"
        onClick={handleZoomIn}
        disabled={scale >= maxScale}
        title="放大"
      >
        <ZoomIn size={16} />
      </button>
      <div className="zoom-controller-percentage">
        {Math.round(scale * 100)}%
      </div>
      {onResetScale && (
        <button
          className="zoom-controller-button zoom-reset"
          onClick={handleReset}
          title="重置缩放"
        >
          <RotateCcw size={16} />
        </button>
      )}
    </div>
  );
};

export default ZoomController;

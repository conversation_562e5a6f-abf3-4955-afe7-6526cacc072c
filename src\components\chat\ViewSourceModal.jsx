import React, { memo, useEffect } from 'react';
import { Modal, Tabs } from 'antd';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { docco } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { formatApiResponse, formatHtmlCode } from '../../shared/utils/sourceUtils';

/**
 * 查看源代码模态框组件
 * @param {Object} props - 组件属性
 * @param {boolean} props.visible - 是否显示模态框
 * @param {Function} props.onClose - 关闭模态框的回调函数
 * @param {string} props.htmlContent - HTML源代码
 * @param {string} props.apiResponse - 原始API响应
 * @returns {JSX.Element} - 渲染的组件
 */
const ViewSourceModal = ({ visible, onClose, htmlContent, apiResponse }) => {
  // 准备HTML内容
  const htmlForDisplay = htmlContent ? formatHtmlCode(htmlContent) : '暂无HTML内容';

  // 准备API响应内容
  const jsonContent = formatApiResponse(apiResponse);
  
  // HTML预览标签
  const previewItem = {
    key: '3',
    label: 'HTML预览',
    children: (
      <div style={{ 
        height: '70vh', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        padding: '0'
      }}>
        <iframe
          srcDoc={htmlContent}
          title="HTML预览"
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            display: 'block',
            margin: 'auto'
          }}
          sandbox="allow-same-origin allow-scripts allow-forms"
          referrerPolicy="no-referrer"
          loading="lazy"
          onLoad={(e) => {
            try {
              const frame = e.target;
              const frameDoc = frame.contentDocument || frame.contentWindow.document;
              
              if (frameDoc) {
                // 添加样式到iframe内部，防止双滚动条
                const style = frameDoc.createElement('style');
                style.textContent = `
                  html, body {
                    margin: 0;
                    padding: 0;
                    overflow: visible !important;
                    height: 100% !important;
                  }
                  body {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100%;
                  }
                  body > * {
                    max-width: 100%;
                    margin: auto;
                  }
                `;
                frameDoc.head.appendChild(style);
                
                // 设置iframe高度适应内容
                if (frameDoc.body) {
                  const contentHeight = Math.max(600, frameDoc.body.scrollHeight);
                  frame.style.height = `${contentHeight}px`;
                }
              }
            } catch (error) {
              console.error('Frame adjustment error:', error);
            }
          }}
        />
      </div>
    ),
  };

  // 使用新的items属性模式替换TabPane子组件
  const items = [
    {
      key: '1',
      label: 'HTML源码',
      children: (
        <div style={{ maxHeight: '60vh', overflow: 'auto' }}>
          <SyntaxHighlighter language="html" style={docco}>
            {htmlForDisplay}
          </SyntaxHighlighter>
        </div>
      )
    },
    {
      key: '2',
      label: 'API响应',
      children: (
        <div style={{ maxHeight: '60vh', overflow: 'auto' }}>
          <SyntaxHighlighter language="json" style={docco}>
            {jsonContent}
          </SyntaxHighlighter>
        </div>
      )
    }
  ];
  
  // 只有有HTML内容时才添加预览选项卡
  if (htmlContent) {
    items.unshift(previewItem);
  }

  return (
    <Modal
      title="查看源码"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1100}
      styles={{
        body: { padding: '0' },
        content: { padding: '0' }
      }}
    >
      <Tabs defaultActiveKey={htmlContent ? '3' : '1'} items={items} />
    </Modal>
  );
};

// 使用React.memo包装组件以避免不必要的重渲染
export default memo(ViewSourceModal);

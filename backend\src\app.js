const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const path = require('path');
require('dotenv').config();
const { sequelize } = require('./config/database');
const logger = require('./utils/logger');
const errorHandler = require('./middlewares/errorHandler');
const loggerMiddleware = require('./middlewares/loggerMiddleware');
const { registerTask } = require('./utils/cronJobs');
const { checkHealth, startPeriodicHealthCheck } = require('./utils/healthCheck');
const startCleanupSchedule = require('./tasks/taskCleanupScheduler'); // 任务记录清理

// 导入路由
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const coverRoutes = require('./routes/coverRoutes');
const styleRoutes = require('./routes/styleRoutes');
const adminRoutes = require('./routes/adminRoutes');
const paymentRoutes = require('./routes/paymentRoutes');
const adminPaymentRoutes = require('./routes/adminPaymentRoutes');
const smsRoutes = require('./routes/smsRoutes');
const featureRoutes = require('./routes/featureRoutes');
const systemRoutes = require('./routes/systemRoutes');
const uploadRoutes = require('./routes/uploadRoutes'); // 导入上传路由

// 初始化Express应用
const app = express();

// 基础中间件
app.use(helmet({ contentSecurityPolicy: false })); // 设置安全相关HTTP头，禁用CSP以允许加载外部资源
app.use(cors({
  origin: '*', // 允许所有来源访问，开发环境中使用
  credentials: true // 允许携带凭证（如cookies）
})); // 启用CORS并配置

// 添加保存原始请求体的中间件，用于支付回调签名验证
// 注意：此中间件必须在express.json()之前，否则请求体可能已被JSON解析修改
app.use('/api/payment/callback', (req, res, next) => {
  let rawData = [];
  
  req.on('data', chunk => {
    rawData.push(chunk);
  });
  
  req.on('end', () => {
    if (rawData.length > 0) {
      // 保存原始请求体以供验签使用
      const rawBody = Buffer.concat(rawData).toString();
      req.rawBody = rawBody;
      
      // 尝试解析为JSON，但不抛出错误
      try {
        if (rawBody.trim() && (rawBody.startsWith('{') || rawBody.startsWith('['))) {
          req.body = JSON.parse(rawBody);
        }
      } catch (err) {
        logger.warn('解析支付回调JSON数据失败:', err.message);
      }
    }
    
    next();
  });
  
  req.on('error', (err) => {
    logger.error('读取请求体时出错:', err);
    next(err);
  });
});

app.use(express.json({ limit: '1mb' })); // 解析JSON请求体，除了支付回调路径
app.use(express.urlencoded({ extended: true, limit: '1mb' })); // 解析URL编码请求体

// 自定义Morgan日志，排除高频请求和不必要的日志记录
app.use(morgan('combined', {
  skip: function (req, res) {
    // 排除以下请求的日志记录：
    // 1. 用户查询封面记录、个人资料和积分记录的请求
    // 2. 基础提示词的请求
    // 3. 静态资源请求（CSS、JS、图片等）
    // 4. 健康检查请求
    // 5. 当前用户信息请求
    // 6. 成功的OPTIONS请求（预检请求）
    return req.url.includes('/api/user/covers') ||
           req.url.includes('/api/user/profile') ||
           req.url.includes('/api/user/point-records') ||
           req.url.includes('/api/cover/base-prompts') ||
           req.url.includes('/api/health') ||
           req.url.includes('/api/auth/me') ||
           req.url.match(/\.(jpg|jpeg|png|gif|svg|ico|css|js|woff|woff2|ttf|eot)$/) ||
           req.url.startsWith('/uploads/') ||
           req.url.startsWith('/admin/css/') ||
           req.url.startsWith('/admin/js/') ||
           req.url.startsWith('/admin/img/') ||
           (req.method === 'OPTIONS' && res.statusCode < 400);
  },
  stream: {
    write: message => logger.info(message.trim())
  }
})); // HTTP请求日志

// 添加系统操作日志中间件
app.use(loggerMiddleware({
  excludePaths: [
    '/api/health',
    '/api/auth/me',
    // 不排除统计和日志API，否则无法记录这些操作
    // '/api/admin/stats',
    // '/api/admin/logs',
    // 排除静态资源和上传路径
    '/uploads',
    '/admin/css',
    '/admin/js',
    '/admin/img',
    /\.(jpg|jpeg|png|gif|svg|ico|css|js)$/
  ]
}));

// 设置静态文件目录
app.use(express.static(path.join(__dirname, '../public')));
// 正确映射uploads路径到public/uploads目录
app.use('/uploads', express.static(path.join(__dirname, '../public/uploads')));

// 添加健康检查端点
app.get('/api/health', async (req, res) => {
  try {
    const healthStatus = await checkHealth();
    res.status(healthStatus.status === 'UP' ? 200 : 503).json(healthStatus);
  } catch (error) {
    logger.error(`健康检查API错误: ${error.message}`);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

// 挂载路由
app.use('/api/auth', authRoutes);
app.use('/api/user', userRoutes);
app.use('/api/cover', coverRoutes);
app.use('/api/style', styleRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/payment', paymentRoutes);
app.use('/api/admin/payment', adminPaymentRoutes);
app.use('/api/sms', smsRoutes);
app.use('/api/features', featureRoutes);
app.use('/api/system', systemRoutes);
app.use('/api/upload', uploadRoutes); // 注册上传路由

// 管理后台入口路由
app.get('/admin', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/admin/index.html'));
});

// 404处理
app.use((req, res, next) => {
  res.status(404).json({ message: '请求的资源不存在' });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
const PORT = process.env.BACKEND_PORT || process.env.PORT || 3002;
app.listen(PORT, async () => {
  logger.info(`服务器运行在端口 ${PORT}`);
  logger.info(`管理后台地址: http://localhost:${PORT}/admin`);
  logger.info(`健康检查地址: http://localhost:${PORT}/api/health`);

  try {
    // 测试数据库连接
    await sequelize.authenticate();
    logger.info('数据库连接成功');

    // 导入定时任务
    const checkVipExpiration = require('./tasks/vipExpirationTask');
    const resetVipPoints = require('./tasks/vipPointsResetTask');
    const notifyVipExpiration = require('./tasks/vipExpirationNotifyTask');
    const cleanupSystemLogs = require('./tasks/logCleanupTask'); // 导入日志清理任务
    const closeTimeoutOrders = require('./tasks/orderTimeoutTask'); // 导入订单超时关闭任务
    const resetDailyPoints = require('./tasks/dailyPointsResetTask'); // 导入每日积分重置任务

    // 注册定时任务
    registerTask('vipExpirationCheck', '0 2 * * *', checkVipExpiration); // 每天凌晨2:00
    registerTask('vipPointsReset', '30 2 * * *', resetVipPoints); // 每天凌晨2:30
    registerTask('vipExpirationNotify', '0 3 * * *', notifyVipExpiration); // 每天凌晨3:00
    registerTask('logCleanup', '0 1 * * *', cleanupSystemLogs); // 每天凌晨1:00清理日志
    registerTask('orderTimeoutCheck', '*/5 * * * *', closeTimeoutOrders); // 每5分钟执行一次订单超时检查
    registerTask('dailyPointsReset', '0 0 * * *', resetDailyPoints); // 每天0点重置每日积分

    logger.info('定时任务注册完成');
    
    // 服务启动时直接执行每日积分重置任务（用于测试）
    try {
      logger.info('服务启动时执行每日积分重置任务');
      const result = await resetDailyPoints();
      if (result) {
        logger.info('服务启动时每日积分重置任务执行成功');
      } else {
        logger.error('服务启动时每日积分重置任务执行失败');
      }
    } catch (taskError) {
      logger.error('执行每日积分重置任务出错:', taskError);
    }

    // 启动定期健康检查
    const healthCheckInterval = parseInt(process.env.HEALTH_CHECK_INTERVAL || '60000');
    if (process.env.HEALTH_CHECK_ENABLED === 'true') {
      startPeriodicHealthCheck(healthCheckInterval);
      logger.info(`已启动定期健康检查，间隔 ${healthCheckInterval}ms`);
    }

    // 启动任务清理服务
    const taskCleanupInterval = parseInt(process.env.TASK_CLEANUP_INTERVAL || '60000'); // 默认1分钟
    startCleanupSchedule(taskCleanupInterval);
    logger.info(`已启动任务清理服务，间隔 ${taskCleanupInterval}ms`);
  } catch (error) {
    logger.error('数据库连接或任务注册失败:', error);
  }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

// 处理未处理的Promise拒绝
process.on('unhandledRejection', (error) => {
  logger.error('未处理的Promise拒绝:', error);
});

// 获取上次重置时间
async function getLastResetDate() {
  try {
    const config = await sequelize.models.SystemConfig.findOne({
      where: { config_key: 'last_daily_points_reset_date' }
    });
    return config ? config.config_value : null;
  } catch (error) {
    logger.error('获取上次积分重置时间失败:', error);
    return null;
  }
}

// 更新上次重置时间
async function updateLastResetDate(date) {
  try {
    await sequelize.models.SystemConfig.upsert({
      config_key: 'last_daily_points_reset_date',
      config_value: date.toISOString(),
      description: '上次执行每日积分重置的日期'
    });
    return true;
  } catch (error) {
    logger.error('更新积分重置时间失败:', error);
    return false;
  }
}
module.exports = app; // 导出app供测试使用


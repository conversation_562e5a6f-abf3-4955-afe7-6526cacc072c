import React, { useState, useEffect, useRef, useCallback, useMemo, useImperativeHandle, forwardRef } from 'react';
import { Download, FileText, Share2, RotateCcw, Image as ImageIcon, Loader2, Code, Eye, Save, Crown } from 'lucide-react';
import { getTextEditorStyles, createTextEditor, getKeyboardNavigationScript, getTextElementInitScript } from './utils/textEditor.js';
import { addEditableAttributeToHtml } from './utils/textEditorCore.js';
import { getCoverSizeConfig } from '../../services/templateService';
import { handleOpenPreview, cleanupEditingAttributes } from './utils/previewUtils';
import { handleScreenshotDownload } from './utils/downloadImageUtils'; // 导入handleScreenshotDownload函数
import { detectSpecialTemplate, applySpecialTemplateHandling } from './utils/specialTemplateUtils'; // 导入特殊模板处理函数
import { setupIframeContentWithStructureDetection } from './utils/htmlStructureUtils'; // 导入HTML结构检测和处理函数
import { advancedModeController } from '../../utils/advancedModeIntegration.js'; // 导入高级模式控制器
import { 
  applyFontSize, 
  applyFontSizeToElement, 
  clearFontSizeStyles, 
  applyColor, 
  applyBackColor, 
  applyFontFamily, 
  clearColorStyles, 
  applyBold, 
  applyItalic, 
  applyUnderline,
  applyBorderStyle,
  applyBorderWidth,
  applyBorderColor,
  // 添加新的样式函数
  applyLineHeight,
  applyLetterSpacing,
  applyParagraphSpacing,
  applyVerticalAlign
} from './utils/styleUtils'; // 导入样式应用函数
import {
  calculateOptimalScale,
  handleScaleChange as handleScaleChangeUtil,
  resetScale as resetScaleUtil,
  getPreviewContainerStyle,
  getOuterPreviewAreaStyle
} from './utils/zoomUtils'; // 导入缩放控制函数
import { 
  createShowPermissionError, 
  createHandleActionWithPermission, 
  createHandleElementClickWithPermission,
  checkViewSourcePermission,
  checkCommandPermission
} from './utils/permissionUtils'; // 导入权限检查函数
import {
  getCurrentEditorState,
  applyEditorState,
  createDebouncedRecordState
} from './utils/historyUtils'; // 导入历史记录管理函数
import { createExecuteCommand } from './utils/commandUtils'; // 导入命令执行工具
import autoSaveManager from './utils/autoSaveManager'; // 导入自动保存管理模块
import logger from '../../services/logs/frontendLogger';
import { startSystemMonitoring, getSystemHealthReport } from './utils/systemMonitor';
import {
  startLoading,
  setLoadingPhase,
  setLoadingSuccess,
  setLoadingError,
  LOADING_PHASES
} from './utils/loadingStateManager';
import adminCommunicationHandler from './utils/adminCommunicationHandler';
import html2canvas from 'html2canvas';
import { message } from 'antd';
import axios from 'axios';
import { checkFeatureAvailability } from '../../services/featureService';
import PermissionControl from '@/components/common/PermissionControl.jsx'; // 权限控制组件
import PreviewEditorToolbar from './PreviewEditorToolbar';
import ViewSourceModal from './ViewSourceModal';
import PermissionDialog from '../../components/common/PermissionDialog';
import { useFeatures } from '../../contexts/FeatureContext';
import ZoomController from '../zoom/ZoomController';
import EditorHistory from './utils/EditorHistory';
import { moveUp, moveDown } from './utils/layerUtils';
import PreviewArea from './PreviewArea'; // 导入新的PreviewArea组件
import featureConfig, { isFeatureEnabled, getDebugInfo } from './utils/featureConfig';
import PreviewModeSwitch from './PreviewModeSwitch';

// 注意：LoadingSpinner组件已移至PreviewArea.jsx中，这里保留引用以供其他地方使用
const LoadingSpinner = ({ className }) => {
  return <div className={`animate-spin rounded-full h-8 w-8 border-b-2 border-primary ${className || ''}`}></div>;
};

const ChatPreview = forwardRef((props, ref) => {
  const {
    htmlContent = null,
    isPreviewActive = false,
    onDownloadCover,
    onDownloadHtml,
    onPreview,
    onShare,
    onRestore,
    onSave, // 修复：从 props 中解构 onSave，解决 onSave is not defined 错误
    onViewSource,
    onHtmlContentChange,
    onElementClick,
    coverCode = null,
    coverId = null,
    progress = 0,
    feedbackText = '',
    showSourceModal = false,
    setShowSourceModal = () => {},
    debugMode = false,
    availableActions = {},
    onAction = () => {},
    onImageUpload = () => {},
    templateInfo = null,
    onLayoutChange = () => {},
    selectedSizeType = null, // 添加封面尺寸类型属性
    isGenerating = false, // 添加生成状态属性
    onLoadComplete = null, // 添加预览加载完成回调
    activeEditorElement,
    setActiveEditorElement,
  } = props;

  const iframeRef = useRef(null);
  const containerRef = useRef(null); // This is the div that will be scaled
  const outerPreviewAreaRef = useRef(null); // Parent of containerRef, used to determine available space
  const lastUsedHtmlContentForSrcdocRef = useRef(Symbol('initial')); // Ref to track last HTML written to srcdoc

  const [currentEditingContent, setCurrentEditingContent] = useState(htmlContent);
  const [loadError, setLoadError] = useState(null); // 添加加载错误状态
  const [isLoading, setIsLoading] = useState(false); // 添加加载状态
  const [isEditorInitialized, setIsEditorInitialized] = useState(false); // 添加编辑器初始化状态

  // 添加状态变量来跟踪HTML是否已加载和初始尺寸类型
  const [initialSizeType, setInitialSizeType] = useState(null);
  const [htmlLoaded, setHtmlLoaded] = useState(false);
  const initialSizeTypeRef = useRef(selectedSizeType);

  // 修改 HTML 内容尺寸的初始状态，提供默认值而非 null
  const [htmlContentSize, setHtmlContentSize] = useState({ width: 750, height: 1000 }); // 默认提供尺寸

  // State for natural dimensions of the HTML content and the scale factor
  const [naturalDimensions, setNaturalDimensions] = useState({ width: 750, height: 1000 }); // Default e.g. Xiaohongshu
  const [scale, setScale] = useState(1);
  const [iframeKey, setIframeKey] = useState(Date.now()); // 新增：为iframe提供一个唯一的key

  // 保存上一次的previewHtml引用，用于比较内容是否变化
  const previousPreviewHtmlRef = useRef(htmlContent);
  const textEditorRef = useRef(null);
  
  // 添加自动保存定时器引用
  const autoSaveTimerRef = useRef(null);
  
  // 添加选择变更定时器引用
  const selectionChangeTimerRef = useRef(null);
  
  // 创建编辑历史记录管理实例
  const editorHistoryRef = useRef(null);
  
  // 添加自动保存控制器引用
  const autoSaveControllerRef = useRef(null);
  
  // 关键新增：添加一个专门的 Effect 来处理 scale 值的更新
  useEffect(() => {
    if (textEditorRef.current && typeof textEditorRef.current.updateValue === 'function') {
      textEditorRef.current.updateValue('scale', scale);
    }
  }, [scale]);

  // 重新引入/确保 latestEditorHtmlRef 存在，并用 prop 初始化
  const latestEditorHtmlRef = useRef(htmlContent);

  // 添加用户自定义缩放状态
  const [userScale, setUserScale] = useState(null);
  const [showZoomControl, setShowZoomControl] = useState(true); // 默认显示缩放控制器
  // 添加初始缩放设置状态
  const [isInitialScaleSet, setIsInitialScaleSet] = useState(false);

  // 权限弹出层状态 - 需要在早期定义，避免初始化错误
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [permissionDialogInfo, setPermissionDialogInfo] = useState({ featureName: '', reason: '' });

  // 移除编辑工具栏位置状态和引用
  // const [editorToolbarPosition, setEditorToolbarPosition] = useState({ left: '50%', transform: 'translateX(-50%)' });
  // const editorToolbarRef = useRef(null);

  // 初始化功能配置管理器
  useEffect(() => {
    featureConfig.init();

    // 在调试模式下输出功能配置信息
    if (debugMode) {
      console.log('功能配置调试信息:', getDebugInfo());
    }
  }, [debugMode]);

  /**
   * 检查是否可以跳过预览设置
   * @returns {boolean} 如果可以跳过设置，则返回true
   */
  const shouldSkipSetup = () => {
    // 如果htmlContent与上次使用的内容相同，且编辑器已存在，则可以跳过
    return htmlContent === lastUsedHtmlContentForSrcdocRef.current &&
        lastUsedHtmlContentForSrcdocRef.current !== null &&
        lastUsedHtmlContentForSrcdocRef.current !== '' &&
        textEditorRef.current;
  };

  /**
   * 准备HTML内容，添加必要的脚本和样式
   * @param {string} content 原始HTML内容
   * @returns {string} 处理后的HTML内容
   */
  const prepareHtmlContent = async (content) => {
    if (!content) return '';

    // 主动清理无效的图片链接，防止加载失败和卡顿
    let sanitizedHtmlContent = content;
    if (typeof sanitizedHtmlContent === 'string') {
      // 更换为更强大的正则表达式，以处理单引号、双引号和无协议的URL
      // 包含三种情况: src="http://via.placeholder.com/100", src='https://via.placeholder.com/100', src=//via.placeholder.com/100
      const placeholderRegex = /src=(["'])?(https?:)?\/\/via\.placeholder\.com\/[^'" >]*(?:\1)?/g;
      sanitizedHtmlContent = sanitizedHtmlContent.replace(placeholderRegex, 'src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="');

      // 清理可能的console.log语句
      sanitizedHtmlContent = sanitizedHtmlContent.replace(/console\.log\([^)]*\)/g, debugMode ? '$&' : '');
    }

    // 检查是否需要高级模式处理
    const isAdvancedMode = advancedModeController.isAdvancedModeEnabled();
    if (isAdvancedMode) {
      try {
        // 初始化高级模式控制器
        const initSuccess = await advancedModeController.initialize();
        if (initSuccess) {
          // 使用服务端渲染处理
          const renderResult = await advancedModeController.integrateWithRenderer(
            sanitizedHtmlContent,
            { passed: true, riskLevel: 'SAFE' } // 简化的安全检测结果
          );

          if (renderResult.success && renderResult.finalContent) {
            sanitizedHtmlContent = renderResult.finalContent.html || renderResult.finalContent;
            logger.info('高级模式处理完成', {
              originalLength: content.length,
              finalLength: sanitizedHtmlContent.length,
              isAdvancedMode: true
            });
          } else {
            logger.warn('高级模式渲染失败，使用标准模式', { renderResult });
            sanitizedHtmlContent = addEditableAttributeToHtml(sanitizedHtmlContent);
          }
        } else {
          logger.warn('高级模式初始化失败，使用标准模式');
          sanitizedHtmlContent = addEditableAttributeToHtml(sanitizedHtmlContent);
        }
      } catch (error) {
        logger.error('高级模式处理失败，回退到标准模式', { error: error.message });
        // 回退到标准模式处理
        sanitizedHtmlContent = addEditableAttributeToHtml(sanitizedHtmlContent);
      }
    } else {
      // 标准模式处理 - 直接添加可编辑标记
      sanitizedHtmlContent = addEditableAttributeToHtml(sanitizedHtmlContent);
      logger.info('使用标准模式处理', { isAdvancedMode: false });
    }

    return sanitizedHtmlContent;
  };

  /**
   * 设置iframe内容
   * @param {HTMLIFrameElement} iframe - iframe元素
   * @param {string} content - HTML内容
   */
  const setupIframeContent = (iframe, content) => {
    try {
      // 清除加载错误状态，避免显示加载失败的效果
      setLoadError(null);

      if (!iframe) {
        console.error('iframe元素不存在');
        return;
      }

      // 获取当前页面URL作为基础URL，用于解析相对路径
      const baseUrl = window.location.href;

      // 设置HTML解析阶段
      setLoadingPhase(LOADING_PHASES.PARSING_HTML, '解析HTML结构...');

      // 清理HTML内容中的编辑状态标记
      let cleanedContent = content;
      try {
        if (content && typeof content === 'string' && content.length > 0) {
          cleanedContent = cleanupEditingAttributes(content);
          logger.info('在设置iframe内容前已清理编辑状态标记');
        }
      } catch (cleanError) {
        logger.warn('清理HTML内容失败', { error: cleanError.message });
        // 继续使用原始内容
      }

      // 使用htmlStructureUtils中的函数处理HTML内容
      const success = setupIframeContentWithStructureDetection(
        iframe,
        cleanedContent, // 使用已清理的内容
        getTextEditorStyles,
        getKeyboardNavigationScript,
        getTextElementInitScript,
        baseUrl
      );

      if (!success) {
        console.error('设置iframe内容失败');
        setLoadError('加载内容失败，请刷新页面重试');
      }

      // 在iframe加载完成后设置事件监听
      iframe.onload = async () => {
        try {
          // 清除加载错误状态，避免显示加载失败的效果
          setLoadError(null);

          // 创建文本编辑器并初始化
          if (textEditorRef.current) {
            textEditorRef.current.cleanup();
          }

          // 确保文本编辑器创建函数可用
          if (typeof createTextEditor === 'function') {
          // 使用之前定义的handleElementClickWithPermission函数
            textEditorRef.current = createTextEditor(iframe.contentDocument || iframe.contentWindow?.document, memoizedOnHtmlContentChange, handleElementClickWithPermission, debugMode, false, scale);
            textEditorRef.current.init();

            // --- History Management Integration ---
            if (editorHistoryRef.current) {
            // 安全地获取初始状态并设置
            try {
              const initialState = getCurrentEditorStateWrapper();
              if (initialState) {
                editorHistoryRef.current.setInitialState(initialState);
              }
            } catch (error) {
              // 静默处理错误
              }

              const contentContainer = (iframe.contentDocument || iframe.contentWindow?.document).querySelector('.content-container');
              if (contentContainer) {
                if(textEditorRef.current.mutationObserver) {
                   textEditorRef.current.mutationObserver.disconnect();
                }
                const observer = new MutationObserver(() => {
                    if (editorHistoryRef.current && !editorHistoryRef.current.isApplyingState) {
                        debouncedRecordState();
                    }
                });
                observer.observe(contentContainer, { attributes: true, childList: true, subtree: true, characterData: true });
                textEditorRef.current.mutationObserver = observer;
              }

              if (textEditorRef.current?.getMiddleware?.()?.dragMode) {
                  const dragMode = textEditorRef.current.getMiddleware().dragMode;
                  // 避免重复修补
                  if (!dragMode.endDrag.isPatched) {
                    const originalEndDrag = dragMode.endDrag;
                    dragMode.endDrag = function(...args) {
                        const result = originalEndDrag.apply(this, args);
                        debouncedRecordState();
                        return result;
                    };
                    dragMode.endDrag.isPatched = true;
                  }
              }
            }
                          // --- End History Management ---

            } else {
              // 如果创建函数不可用，延迟重试
            setTimeout(() => {
              try {
                if (typeof createTextEditor === 'function') {
                    // 使用上面已定义的handleElementClickWithPermission函数，避免重复定义
                  // 关键修改：在初始化时传递初始的 scale 值
                  textEditorRef.current = createTextEditor(iframe.contentDocument || iframe.contentWindow?.document, memoizedOnHtmlContentChange, handleElementClickWithPermission, debugMode, false, scale);
                  textEditorRef.current.init();
                }
              } catch (retryError) {
                console.error('文本编辑器初始化失败:', retryError);
                // 不设置loadError，避免显示加载失败的效果
              }
            }, 100);
          }

          // 设置编辑器初始化阶段
          setLoadingPhase(LOADING_PHASES.SETTING_UP_EDITOR, '初始化编辑器...');

          // 内容加载完成后立即隐藏加载状态
          setIsLoading(false);

          // 设置加载成功状态
          setLoadingSuccess('内容加载完成');

          // 通知父组件预览加载完成，这将触发输入框自动折叠
          if (onLoadComplete && typeof onLoadComplete === 'function') {
            onLoadComplete();
          }
        } catch (error) {
          console.error('iframe加载完成后处理错误:', error);
          // 不设置loadError，避免显示加载失败的效果
          setIsLoading(false);
        }
    };
    } catch (error) {
      console.error('设置iframe内容时出错:', error);

      // 设置加载错误状态
      setLoadingError('加载预览内容失败', error);

      // 只有在htmlContent存在时才设置loadError，避免在不必要的情况下显示错误信息
      if (htmlContent) {
        setLoadError('加载预览内容失败');
      }

      // 如果出错，尝试显示一个简单的空白页面
      try {
        if (iframe && iframe.contentDocument) {
          const iframeDoc = iframe.contentDocument;
          iframeDoc.open();
          iframeDoc.write(`
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <style>
                body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; height: 100vh; }
              </style>
            </head>
            <body>
              <div style="text-align: center;">
                <p>内容加载中...</p>
              </div>
            </body>
            </html>
          `);
          iframeDoc.close();
        }
      } catch (fallbackError) {
        console.error('显示备用内容时出错:', fallbackError);
        // 只有在备用内容也加载失败且htmlContent存在时，才设置loadError状态
        if (htmlContent) {
          setLoadError('加载预览内容失败');
        }
      }
    }
  };

  /**
   * 重置状态标志，清理编辑器实例和iframe内容
   */
  const resetStateFlags = () => {
    // 重置所有状态标志
      contentLoadedRef.current = false;
      setContentLoaded(false);
      if (typeof setIsEditorInitialized === 'function') {
        setIsEditorInitialized(false); // 重置编辑器初始化状态
      }
      setLoadError(null); // 重置加载错误状态
      setIsInitialScaleSet(false); // 重置智能缩放状态，允许重新计算最佳缩放比例

      // 重置用户编辑状态标志，确保不会跳过更新
      if (typeof isUserEditingRef !== 'undefined' && isUserEditingRef) {
        isUserEditingRef.current = false;
      }

      // 取消可能正在进行的自动保存操作，避免加载新内容后被旧的自动保存覆盖
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
        autoSaveTimerRef.current = null;
      }

      // 清理所有引用，确保下次加载时能够正确触发setupPreview
      previousPreviewHtmlRef.current = '';
      lastUsedHtmlContentForSrcdocRef.current = '';
  };

  /**
   * 清理编辑器实例
   */
  const cleanupEditorInstance = () => {
      // 清理编辑器实例
      if (textEditorRef.current) {
        try {
        // 尝试使用dragMode清理所有拖拽状态
        if (textEditorRef.current.getMiddleware?.()?.dragMode?.clearAllDragModes) {
          textEditorRef.current.getMiddleware().dragMode.clearAllDragModes();
        }

          textEditorRef.current.cleanup();
        } catch (editorCleanupError) {
          // 静默处理错误
        }
        textEditorRef.current = null;
      }

      // 重置editorInstanceRef
      if (typeof editorInstanceRef !== 'undefined' && editorInstanceRef.current) {
        editorInstanceRef.current = null;
      }

    // 通过中间件重置编辑状态
    if (textEditorRef.current && textEditorRef.current.getMiddleware) {
      const middleware = textEditorRef.current.getMiddleware();
      if (middleware) {
        middleware.setIsEditing(false);
        middleware.clearSelection();
      }
    }
  };

  /**
   * 清理iframe内容
   */
  const cleanupIframeContent = () => {
      try {
        const iframe = iframeRef.current;
        if (iframe) {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
          if (iframeDoc) {
          // 查找所有可能与编辑相关的元素
          const elementsToClean = iframeDoc.querySelectorAll('[contenteditable="true"], [data-editing="true"], [data-editable-fengmian]');

            if (elementsToClean.length > 0) {
              elementsToClean.forEach(el => {
                // 移除所有编辑相关属性
                el.removeAttribute('contenteditable');
                el.removeAttribute('data-editing');
                el.removeAttribute('data-field');
                el.removeAttribute('data-editable-fengmian');
                el.removeAttribute('tabindex');
                el.removeAttribute('title');
              });
            }

            // 移除所有拖拽句柄元素
            const resizeHandles = iframeDoc.querySelectorAll('.resize-handle');
            resizeHandles.forEach(handle => {
              handle.remove();
            });

            // 清除所有选中状态
            if (iframeDoc.getSelection) {
              iframeDoc.getSelection().removeAllRanges();
            }

            // 尝试移除任何编辑器特定的事件监听器
            if (iframeDoc.body) {
              const oldBody = iframeDoc.body.cloneNode(true);
              iframeDoc.body.parentNode.replaceChild(oldBody, iframeDoc.body);
      }

      // 清空iframe内容
            iframeDoc.open();
            iframeDoc.write('');
            iframeDoc.close();
        }
      }

      // 触发自定义事件通知MainLayout，HTML已卸载
      setHtmlLoaded(false);
      const htmlLoadedEvent = new CustomEvent('htmlLoaded', {
        detail: { isLoaded: false }
      });
      window.dispatchEvent(htmlLoadedEvent);
    } catch (error) {
      // 静默处理错误
    }
  };

  // 使用permissionUtils中的函数创建showPermissionError
  const showPermissionError = useMemo(() => {
    return createShowPermissionError(setPermissionDialogInfo, setShowPermissionDialog);
  }, []);

  /**
   * 重置加载状态，清理编辑器实例和iframe内容
   */
  const resetLoadState = () => {
    try {
      // 重置状态标志
      resetStateFlags();

      // 清理编辑器实例
      cleanupEditorInstance();

      // 清理iframe内容
      cleanupIframeContent();

      // 重置showHomePreview状态
      setShowHomePreview(true);
    } catch (error) {
      // 静默处理错误
    }
  };

  // 在适当的位置使用createHandleElementClickWithPermission函数
  const handleElementClickWithPermission = useMemo(() => {
    return createHandleElementClickWithPermission(showPermissionError, onElementClick);
  }, [showPermissionError, onElementClick]);

  const memoizedOnHtmlContentChange = useMemo(() => {
    let debounceTimer;
    return (newHtml) => {
      clearTimeout(debounceTimer);
      // 当编辑器内部内容变化时，立即更新 latestEditorHtmlRef
      latestEditorHtmlRef.current = newHtml;

      debounceTimer = setTimeout(() => {
        setCurrentEditingContent(newHtml);
        if (onHtmlContentChange) {
          onHtmlContentChange(newHtml);
        }
      }, 300); // 300ms debounce time
    };
  }, [onHtmlContentChange]);

  // 先定义setupPreview函数，避免初始化错误
  const setupPreview = useCallback(async () => {
    try {
      if (!htmlContent) {
        // 如果没有生成的HTML，显示提示信息
        setContentLoaded(false);
        // 确保清除加载错误状态
        setLoadError(null);

        // 确保清理之前可能存在的文本编辑器
        if (textEditorRef.current) {
          textEditorRef.current.cleanup();
          textEditorRef.current = null;
        }

        // 处理空内容的情况
        if (iframeRef.current) {
          handleEmptyContent(iframeRef.current);
        } else {
          logger.error('iframe元素不存在，无法处理空内容');
        }
        return;
      }

      // 只在首次加载或内容变化时显示加载状态
      if (htmlContent !== previousPreviewHtmlRef.current) {
        // 启动加载状态管理
        startLoading('正在加载HTML内容...');
        setLoadingPhase(LOADING_PHASES.INITIALIZING, '初始化预览环境...');

        // 短暂显示加载状态，提供视觉反馈
        setIsLoading(true);
        setContentLoaded(false);

        // 在加载新内容前清理之前的文本编辑器实例
        if (textEditorRef.current) {
          textEditorRef.current.cleanup();
          textEditorRef.current = null;
        }
      }

      // 更新当前内容引用
      previousPreviewHtmlRef.current = htmlContent;

      // 检查iframe元素是否存在
      const iframe = iframeRef.current;
      if (!iframe) {
        logger.warn('iframe元素不存在，可能是组件尚未完成渲染，稍后将重试');
        // 设置一个延迟，稍后再次尝试
        const retryTimeout = setTimeout(() => {
          logger.info('重试设置预览...');
          setupPreview();
        }, 100);

        // 返回清理函数，以便在组件卸载时清除超时
        return () => clearTimeout(retryTimeout);
      }

      // 如果内容与上次使用的完全相同，且编辑器已存在，则跳过设置
      if (shouldSkipSetup()) {
        logger.info('内容未变化，跳过设置预览');
        return () => {};
      }

      // 准备HTML内容
      try {
        const sanitizedHtmlContent = await prepareHtmlContent(htmlContent);

        // 设置iframe内容
        setupIframeContent(iframe, sanitizedHtmlContent);
        // 更新lastUsedHtmlContentForSrcdocRef
        lastUsedHtmlContentForSrcdocRef.current = htmlContent;
      } catch (setupError) {
        logger.error('设置iframe内容失败', { error: setupError.message });
        // 显示错误提示
        setLoadError('加载内容失败，请刷新页面重试');
        // 移除模式切换标记（如果存在）
        document.body.classList.remove('mode-switching');
      }

      // 不设置超时处理，因为内容是固定的
      return () => {};
    } catch (error) {
      logger.error('设置预览时出错', { error: error.message });
      // 显示错误提示
      setLoadError(`加载出错: ${error.message}`);
      // 移除模式切换标记（如果存在）
      document.body.classList.remove('mode-switching');
      return () => {};
    }
  }, [
    htmlContent,
    startLoading,
    shouldSkipSetup,
    prepareHtmlContent,
    setupIframeContent
  ]);

  // 监听高级模式切换，自动重新加载预览
  useEffect(() => {
    const handleAdvancedModeChange = (event, data) => {
      if (event === 'modeChanged' && data) {
        logger.info('检测到高级模式切换', {
          from: data.oldState,
          to: data.newState
        });

        // 模式切换后强制重新加载预览
        if (htmlContent && isPreviewActive) {
          logger.info('模式切换后重新加载预览');

          // 清除之前的状态，强制重新加载
          previousPreviewHtmlRef.current = '';
          lastUsedHtmlContentForSrcdocRef.current = '';

          // 重置加载状态
          resetLoadState();

          // 延迟一点时间确保状态重置完成
          setTimeout(() => {
            setupPreview();
          }, 100);
        }
      }
    };

    // 添加监听器
    advancedModeController.addListener(handleAdvancedModeChange);

    // 清理监听器
    return () => {
      advancedModeController.removeListener(handleAdvancedModeChange);
    };
  }, [htmlContent, isPreviewActive, setupPreview, resetLoadState]);

  // Effect to determine natural dimensions of the content
  useEffect(() => {
    let w, h;
    const sizeConfig = getCoverSizeConfig(selectedSizeType);

    if (sizeConfig) {
        // 如果选择了特定尺寸，则始终使用该尺寸，这是最高优先级
        w = sizeConfig.width;
        h = sizeConfig.height;
    } else if (htmlContent && htmlContentSize && htmlContentSize.width > 0 && htmlContentSize.height > 0) {
        // 如果未选择特定尺寸（例如自定义尺寸），则使用HTML内容本身的尺寸
        w = htmlContentSize.width;
        h = htmlContentSize.height;
    } else {
        // 如果没有其他可用信息，则回退到默认尺寸
        w = 750;
        h = 1000;
    }
    
    // 仅当尺寸实际更改时才更新状态，以避免不必要的重新渲染
    if (!naturalDimensions || naturalDimensions.width !== w || naturalDimensions.height !== h) {
        setNaturalDimensions({ width: w, height: h });
    }
}, [selectedSizeType, htmlContentSize, htmlContent, naturalDimensions]);

  // 计算最佳缩放比例的函数 - 使用从zoomUtils导入的函数
  const calculateOptimalScaleCallback = useCallback((contentWidth, contentHeight) => {
    return calculateOptimalScale(contentWidth, contentHeight, outerPreviewAreaRef.current);
  }, []);

  // 处理用户缩放变更 - 使用从zoomUtils导入的函数
  const handleScaleChange = useCallback((newScale) => {
    handleScaleChangeUtil(newScale, setScale, setUserScale);
  }, []);

  // 当scale变化时向iframe发送缩放消息
  useEffect(() => {
    const iframe = iframeRef.current;
    if (iframe && iframe.contentWindow && iframe.contentDocument) {
      iframe.contentWindow.postMessage({
        type: 'scale-content',
        scale: scale
      }, '*');
    }
  }, [scale]);

  // 切换显示缩放控制器
  const toggleZoomControl = useCallback(() => {
    setShowZoomControl(prev => !prev);
  }, []);

  // 重置用户缩放 - 使用从zoomUtils导入的函数
  const resetScale = useCallback(() => {
    resetScaleUtil(htmlContentSize, calculateOptimalScaleCallback, setScale, setUserScale);
  }, [htmlContentSize, calculateOptimalScaleCallback]);

  // 根据封面类型计算预览区域样式，使用原始尺寸显示 - 使用从zoomUtils导入的函数
  const previewContainerStyle = useMemo(() => {
    return getPreviewContainerStyle(scale);
  }, [scale]);

  // 修改预览区域样式，允许内容溢出以便完整显示 - 使用从zoomUtils导入的函数
  const outerPreviewAreaStyle = useMemo(() => {
    return getOuterPreviewAreaStyle(isPreviewActive);
  }, [isPreviewActive]);

  // 暴露组件方法给父组件
  useImperativeHandle(ref, () => ({
    resetLoadState,
    setupPreview,
    getCurrentEditingContent: () => {
      return currentEditingContent;
    },
    // 添加 getIframe 方法，返回 iframeRef.current
    getIframe: () => {
      return iframeRef.current;
    },
    // 添加 getScale 方法，返回当前的缩放比例
    getScale: () => {
      return scale;
    },
    // 新增方法：检查是否可以安全地保存
    canSave: () => {
      // 检查保存所需的所有条件是否满足
      return !!coverId && !!iframeRef.current && contentLoaded;
    },
    handleSave: (saveType = 'manual') => {
      // 先检查是否满足保存条件
      if (!coverId || !iframeRef.current || !contentLoaded) {
        if (saveType === 'manual') {
          message.error('无法保存：预览内容尚未加载完成');
        }
        return false;
      }
      
      if (autoSaveControllerRef.current) {
        return autoSaveControllerRef.current.save(saveType);
      } else {
        // 如果自动保存控制器未初始化，则直接调用manualSave函数
        return autoSaveManager.manualSave(iframeRef, coverId, setIsSaving);
      }
    },
    cancelAutoSave: () => {
      if (autoSaveControllerRef.current) {
        autoSaveControllerRef.current.stop();
      }
    },
    
    // 添加历史记录管理方法
    undoEdit: () => {
      if (editorHistoryRef.current) {
        const previousState = editorHistoryRef.current.undo();
        if (previousState) {
          applyEditorState(previousState, iframeRef.current);
          return true;
        }
      }
      return false;
    },
    redoEdit: () => {
      if (editorHistoryRef.current) {
        const nextState = editorHistoryRef.current.redo();
        if (nextState) {
          applyEditorState(nextState, iframeRef.current);
          return true;
        }
      }
      return false;
    },
    recordCurrentState: () => {
      if (editorHistoryRef.current && iframeRef.current) {
        const currentState = getCurrentEditorState(iframeRef.current);
        if (currentState) {
          editorHistoryRef.current.push(currentState);
          return true;
        }
      }
      return false;
    },
    
    // 命令执行方法
    executeCommand,
    
    // 下载方法
    downloadCurrentView: (filename) => {
      if (!iframeRef.current) {
        message.error('预览区域不可用，无法下载。');
        return;
      }

      try {
        // 直接调用downloadImageUtils.js中的handleScreenshotDownload函数
        handleScreenshotDownload(
          iframeRef,
          setIsDownloading,
          `${filename || '封面截图'}_${Date.now()}.png`,
          debugMode,
          htmlContentSize, // 使用htmlContentSize而不是naturalDimensions，确保尺寸一致性
          scale
        );
      } catch (error) {
        message.error(`下载失败: ${error.message}`);
      }
    },
    
    // 其他方法
    cleanHtmlContent: () => {
      if (iframeRef.current) {
        const content = autoSaveManager.getCurrentHtmlContent(iframeRef);
        return content ? content.htmlContent : null;
      }
      return null;
    }
  }));

  // Memoized callbacks for useEffect dependencies
  const memoizedOnElementClick = useCallback((elementInfo) => {
    if (onElementClick) {
      onElementClick(elementInfo);
    }
  }, [onElementClick]);



  // 监听内容加载完成的消息
  const [contentLoaded, setContentLoaded] = useState(false);
  const contentLoadedRef = useRef(false);


  
  // 添加查看源码权限状态
  const [hasViewSourcePermission, setHasViewSourcePermission] = useState(false);
  const [viewSourcePermissionChecked, setViewSourcePermissionChecked] = useState(false);
  
  // 使用useFeatures hook获取权限信息
  const { features, loading } = useFeatures();
  
  // 替换原有的权限检查useEffect
  useEffect(() => {
    // 当权限信息加载完成后，设置查看源码权限
    if (!loading && features['查看源码']) {
      setHasViewSourcePermission(features['查看源码'].available === true);
          setViewSourcePermissionChecked(true);
    } else if (!loading) {
      // 权限信息加载完成但没有查看源码权限
      setHasViewSourcePermission(false);
      setViewSourcePermissionChecked(true);
    }
  }, [features, loading]);



  // 添加获取尺寸的超时机制
  useEffect(() => {
    if (iframeRef.current && htmlContent) {
      // 如果5秒内没有收到有效的尺寸消息，使用默认尺寸
      const timeoutId = setTimeout(() => {
        if (!htmlContentSize || (htmlContentSize.width <= 100 || htmlContentSize.height <= 100)) {
          console.warn('尺寸消息超时或无效，使用默认尺寸');
          setHtmlContentSize({ width: 750, height: 1000 });
        }
      }, 5000);
      
      return () => clearTimeout(timeoutId);
    }
  }, [iframeRef.current, htmlContent, htmlContentSize]);

  // 增强 handleContentLoadedMessage 处理
  const handleContentLoadedMessage = (event) => {
    if (event.data && event.data.type === 'iframe-content-loaded') {
      setContentLoaded(true);
      setLoadError(null); // 清除加载错误状态，避免显示加载失败的效果

      // 如果事件中包含尺寸信息，更新htmlContentSize
      if (event.data.height && event.data.width) {
        // 验证尺寸是否有效
        const width = Math.max(100, event.data.width); // 确保不小于100px
        const height = Math.max(100, event.data.height); // 确保不小于100px
        
        setHtmlContentSize({
          width: width,
          height: height
        });
        
              if (!htmlLoaded) {
        setHtmlLoaded(true);
        
        // 触发自定义事件通知MainLayout
        const htmlLoadedEvent = new CustomEvent('htmlLoaded', { 
          detail: { isLoaded: true } 
        });
        window.dispatchEvent(htmlLoadedEvent);
      }
      
      // 调试日志
      if (debugMode) {
          // 使用debugMode控制，而不是直接输出
      }
      }
    } else if (event.data && event.data.type === 'iframe-images-loaded') {
      // 图片加载完成后，可能需要更新尺寸
      if (event.data.height && event.data.width) {
        const width = Math.max(100, event.data.width); // 确保不小于100px
        const height = Math.max(100, event.data.height); // 确保不小于100px
        
        setHtmlContentSize({
          width: width,
          height: height
        });
        
        // 调试日志
        if (debugMode) {
          // 使用debugMode控制，而不是直接输出
        }
      }
    }
    
    // 确保iframe内容加载成功
    if (event.data && event.data.type === 'content-loaded') {
      setContentLoaded(true);
      setIsLoading(false);
      setLoadError(null); // 清除加载错误状态，避免显示加载失败的效果
      
      // 记录HTML内容已加载
      setHtmlLoaded(true);
      
      // 添加额外检查，确保iframe内容正确显示
      try {
        const iframe = iframeRef.current;
        if (iframe) {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
          if (iframeDoc && iframeDoc.body) {
            // 确保内容有合理的样式
            if (!iframeDoc.body.style.margin) {
              iframeDoc.body.style.margin = '0';
            }
            if (!iframeDoc.body.style.padding) {
              iframeDoc.body.style.padding = '0';
            }
            
            // 确保内容元素有合理的宽度
            const contentElements = iframeDoc.body.children;
            for (let i = 0; i < contentElements.length; i++) {
              const el = contentElements[i];
              if (!el.style.width && !el.style.minWidth) {
                el.style.minWidth = '100px';
              }
            }
          }
        }
      } catch (error) {
        console.error('处理iframe内容时出错:', error);
      }
      
      // 调用加载完成回调
      if (onLoadComplete) {
        onLoadComplete();
      }
    }
  };

  useEffect(() => {
    window.addEventListener('message', handleContentLoadedMessage);
    return () => {
      window.removeEventListener('message', handleContentLoadedMessage);
    };
  }, [htmlLoaded, debugMode]);

  // 使用ref来跟踪上一次的HTML内容，避免不必要的重新渲染
  const lastHtmlContentRef = useRef('');
  const lastPreviewActiveRef = useRef(false);
  const lastLoadingRef = useRef(false);

  // 定义resetLoadState函数，以便在组件内部使用


  /**
   * 处理空内容的情况
   * @param {HTMLIFrameElement} iframe iframe元素
   */
  const handleEmptyContent = (iframe) => {
    // 检查iframe元素是否存在
    if (!iframe) {
      console.warn('iframe元素不存在，无法处理空内容');
      return;
    }

    try {
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (iframeDoc) {
        iframeDoc.open();
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              body {
                margin: 0;
                padding: 40px 20px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-align: center;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
              }
              .empty-content {
                max-width: 400px;
                opacity: 0.8;
              }
              .empty-icon {
                font-size: 48px;
                margin-bottom: 20px;
              }
              .empty-title {
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 12px;
              }
              .empty-description {
                font-size: 16px;
                line-height: 1.5;
                opacity: 0.9;
              }
            </style>
          </head>
          <body>
            <div class="empty-content">
              <div class="empty-icon">🎨</div>
              <div class="empty-title">等待生成封面</div>
              <div class="empty-description">请在左侧输入您的封面需求，我们将为您生成精美的封面设计</div>
            </div>
          </body>
          </html>
        `);
        iframeDoc.close();
      }
    } catch (error) {
      console.error('处理空内容失败:', error);
    }
  };



  // =================================================================
  // History Management (Undo/Redo) - NEW IMPLEMENTATION
  // =================================================================
  const getCurrentEditorStateWrapper = useCallback(() => {
    return getCurrentEditorState(iframeRef.current);
  }, []);
  
  const applyEditorStateWrapper = useCallback((state) => {
    applyEditorState(state, iframeRef.current, textEditorRef.current, onHtmlContentChange);
  }, [onHtmlContentChange]);

  const debouncedRecordState = useMemo(() => {
    return createDebouncedRecordState(getCurrentEditorStateWrapper, editorHistoryRef);
  }, [getCurrentEditorStateWrapper]);

  // 修改iframe内容设置函数，允许加载外部字体









  // 添加一个标志来跟踪是否是用户编辑操作导致的内容变化
  const isUserEditingRef = useRef(false);
  
  // 添加一个ref来保存文本选区，解决点击工具栏时选区丢失的问题
  const savedSelectionRef = useRef(null);

  // 当HTML内容变化时更新预览
  useEffect(() => {
    // 如果不是预览活动状态，不更新预览
    if (!isPreviewActive) {
      return;
    }

    // 如果内容没有变化且编辑器已初始化，不需要更新
    if (htmlContent === previousPreviewHtmlRef.current && contentLoaded && textEditorRef.current) {
      return;
    }

    // 如果是用户编辑操作导致的变化，不重新设置预览
    if (isUserEditingRef.current) {
      previousPreviewHtmlRef.current = htmlContent; // 更新引用
      // 延迟重置标志，确保后续的变化能够正常处理
      setTimeout(() => {
        isUserEditingRef.current = false;
      }, 500);
      return;
    }

    // 使用防抖处理，避免频繁更新导致的闪烁
    const timer = setTimeout(() => {
      setupPreview();
    }, 200); // 增加延迟时间，减少频繁更新

    return () => clearTimeout(timer);
  }, [htmlContent, isPreviewActive, contentLoaded, setupPreview]);

  // 移除全局变量初始化，改为使用中间件API
  // 注释：我们不再需要初始化全局变量，因为现在使用中间件API来管理状态

  // 移除自动缩放的adjustHeight函数

  // 移除自动调整高度的useEffect

  // 当HTML内容变化时，更新内部状态
  useEffect(() => {
    if (htmlContent) {
      setCurrentEditingContent(htmlContent);

      // 如果htmlContent不为空，标记已经加载了HTML，并保存当前的尺寸类型
      setHtmlLoaded(true);

      // 只在首次加载HTML时保存初始尺寸类型
      if (!htmlLoaded) {
        initialSizeTypeRef.current = selectedSizeType;
        setInitialSizeType(selectedSizeType);
      }
    }
  }, [htmlContent, selectedSizeType, htmlLoaded, debugMode]);

  // 移除旧的编辑器初始化逻辑，因为我们现在使用iframe和setupPreview

  // 添加预览加载状态
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  // 添加下载加载状态
  const [isDownloading, setIsDownloading] = useState(false);
  // 添加保存加载状态
  const [isSaving, setIsSaving] = useState(false);

  // Buttons are active if a preview is active and not loading or previewing
  // 确保 isPreviewActive 也是已定义的，或者如果它不再需要，则移除它
  // 假设 isPreviewActive 是父组件传递的 prop 或者是在这个组件内定义的其他状态
  // 如果 isPreviewActive 也是被错误删除的，需要一并恢复或调整逻辑
  // 暂时假设 isPreviewActive 仍然存在或可访问
  // 移除 !isDownloading 条件，避免下载状态影响其他按钮
  const areButtonsEnabled = isPreviewActive && !isLoading && !isPreviewLoading && !isGenerating;



  // 初始化编辑器
  useEffect(() => {
    // 初始化编辑历史记录管理
    if (!editorHistoryRef.current) {
      editorHistoryRef.current = new EditorHistory({
        maxHistoryLength: 50,
        applyStateFn: (state) => {
            if (editorHistoryRef.current) editorHistoryRef.current.isApplyingState = true;
            
            // 保存MutationObserver状态
            const tempObserver = textEditorRef.current?.mutationObserver;
            if (tempObserver) {
              tempObserver.disconnect();
            }
            
            // 应用编辑器状态
            applyEditorStateWrapper(state);
            
            // 在状态恢复后重置isApplyingState标志
            setTimeout(() => {
                if (editorHistoryRef.current) editorHistoryRef.current.isApplyingState = false;
            }, 100);
        }
      });
      
      // 立即记录初始状态（如果可能）
      setTimeout(() => {
        if (editorHistoryRef.current && iframeRef.current) {
          const initialState = getCurrentEditorStateWrapper();
          if (initialState) {
            editorHistoryRef.current.setInitialState(initialState);
          }
        }
      }, 500);
    }
    
    // 清理函数
    return () => {
      if (editorHistoryRef.current) {
        editorHistoryRef.current.clear();
      }
      if (textEditorRef.current?.mutationObserver) {
          textEditorRef.current.mutationObserver.disconnect();
      }
    };
  }, [applyEditorStateWrapper, getCurrentEditorStateWrapper]);

  // 创建executeCommand函数
  const executeCommand = useMemo(() => {
    return createExecuteCommand({
      iframeRef,
      textEditorRef,
      editorHistoryRef,
      isUserEditingRef,
      onHtmlContentChange,
      debouncedRecordState,
      getTextEditorInstance: () => textEditorRef.current,
      showPermissionError,
      checkCommandPermission,
      checkFeatureAvailability,
      styleUtils: {
        applyFontSize,
        applyColor,
        applyBackColor,
        applyFontFamily,
        applyBold,
        applyItalic,
        applyUnderline,
        applyBorderStyle,
        applyBorderWidth,
        applyBorderColor,
        // 添加新的样式函数
        applyLineHeight,
        applyLetterSpacing,
        applyParagraphSpacing,
        applyVerticalAlign
      },
      detectSpecialTemplate,
      applySpecialTemplateHandling
    });
  }, [
    iframeRef,
    textEditorRef,
    editorHistoryRef,
    isUserEditingRef,
    onHtmlContentChange,
    debouncedRecordState,
    showPermissionError,
    checkCommandPermission
  ]);

  // 样式应用函数已迁移到 ./utils/styleUtils.js

  // 监听模式切换事件 - 移到useEffect外部
  const handleModeSwitch = useCallback((event) => {
      // 检查是否确实包含detail
      if (!event || !event.detail) {
        logger.warn('模式切换事件缺少detail');
        return;
      }

      // 从event.detail中获取模式切换标志
      const isModeSwitchEvent = event.detail.isModeSwitchEvent === true;
      logger.info('收到模式切换事件', { isModeSwitchEvent });

      // 延迟处理，避免与其他组件的事件冲突
      setTimeout(async () => {
        try {
          // 如果iframe存在，处理它的内容
          if (iframeRef.current) {
            // 添加模式切换标记，禁用虚线框样式
            document.body.classList.add('mode-switching');
            
            // 确保iframe的contentDocument存在
            const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
            if (iframeDoc && iframeDoc.body) {
              try {
                // 在iframe的body上也添加模式切换标记
                iframeDoc.body.classList.add('mode-switching');
                logger.info('在iframe内添加模式切换标记');
              } catch (iframeError) {
                logger.warn('无法在iframe内添加模式切换标记', { error: iframeError.message });
              }
            }

            try {
              // 清理当前内容中的编辑状态标记
              if (iframeDoc && iframeDoc.querySelector('.content-container')) {
                const container = iframeDoc.querySelector('.content-container');
                const cleanedHtml = cleanupEditingAttributes(container.innerHTML);
                
                if (cleanedHtml !== container.innerHTML) {
                  container.innerHTML = cleanedHtml;
                  logger.info('已清理模式切换前的编辑状态标记');
                }
              }
            } catch (cleanupError) {
              logger.warn('清理编辑状态标记失败', { error: cleanupError.message });
            }

            // 不要重置iframe key，这会导致预览内容丢失
            // setIframeKey(prev => prev + 1); // 注释掉这行，避免清空预览内容
            
            // 确保在适当的时候移除模式切换标记，即使出现错误
            setTimeout(() => {
              document.body.classList.remove('mode-switching');
              
              // 尝试在iframe内也移除模式切换标记
              try {
                const updatedIframeDoc = iframeRef.current?.contentDocument || iframeRef.current?.contentWindow?.document;
                if (updatedIframeDoc && updatedIframeDoc.body) {
                  updatedIframeDoc.body.classList.remove('mode-switching');
                }
              } catch (removeClassError) {
                // 忽略iframe内移除类的错误
              }
              
              logger.info('模式切换完成，已移除模式切换标记');
            }, 500);
          } else {
            // 如果iframe不存在，只在document.body上添加/移除类
            document.body.classList.add('mode-switching');
            
            // 确保即使没有iframe也能移除模式切换标记
            setTimeout(() => {
              document.body.classList.remove('mode-switching');
              logger.info('无iframe模式切换完成，已移除模式切换标记');
            }, 500);
          }
        } catch (error) {
          logger.error('模式切换处理失败', { error: error.message });
          // 不要重置iframe key，这会导致预览内容丢失
          // setIframeKey(prev => prev + 1); // 注释掉，避免清空预览内容

          // 确保即使出错也清理模式切换标记
          setTimeout(() => {
            document.body.classList.remove('mode-switching');
            
            // 尝试在iframe内也移除模式切换标记
            try {
              const updatedIframeDoc = iframeRef.current?.contentDocument || iframeRef.current?.contentWindow?.document;
              if (updatedIframeDoc && updatedIframeDoc.body) {
                updatedIframeDoc.body.classList.remove('mode-switching');
              }
            } catch (removeClassError) {
              // 忽略iframe内移除类的错误
            }
            
            logger.info('模式切换处理失败，已移除模式切换标记');
          }, 500);
        }
      }, 300); // 给模式切换一些时间完成
    }, [iframeRef]);

  // 初始化系统监控和后台通信
  useEffect(() => {
    // 启动系统监控
    startSystemMonitoring();

    // 初始化后台管理界面通信处理器
    adminCommunicationHandler.init();

    window.addEventListener('modeSwitch', handleModeSwitch);

    // 组件卸载时清理资源
    return () => {
      adminCommunicationHandler.destroy();
      window.removeEventListener('modeSwitch', handleModeSwitch);
    };
  }, [handleModeSwitch]);

  // 初始化预览
  useEffect(() => {
    if (isPreviewActive) {
      setupPreview();
    }
  }, [isPreviewActive, setupPreview]);



  // 定义一个通用的权限检查和错误处理函数
  // ... existing code ...

  const handleMessage = useCallback((event) => {
    // 处理iframe发送的消息
    const { data } = event;

    if (!data || typeof data !== 'object') return;

    if (data.type === 'iframe-content-loaded') {
      // 内容加载完成
      setContentLoaded(true);
      contentLoadedRef.current = true;
      setIsLoading(false);

      // 如果存在自定义尺寸，设置到状态中
      if (data.width && data.height) {
        const contentWidth = data.width;
        const contentHeight = data.height;
        
        setHtmlContentSize({
          width: contentWidth,
          height: contentHeight
        });
        
        // 如果用户没有手动设置缩放比例，且尚未应用初始智能缩放，则应用智能缩放
        if (userScale === null && !isInitialScaleSet) {
          const optimalScale = calculateOptimalScaleCallback(contentWidth, contentHeight);
          setScale(optimalScale);
          setIsInitialScaleSet(true); // 标记已应用初始智能缩放
        }
      }

      // 触发自定义加载完成回调
      if (onLoadComplete && typeof onLoadComplete === 'function') {
        setTimeout(() => onLoadComplete(), 100);
      }
      
      // 触发自定义事件通知MainLayout
      const htmlLoadedEvent = new CustomEvent('htmlLoaded', { 
        detail: { isLoaded: true } 
      });
      window.dispatchEvent(htmlLoadedEvent);
    } else if (data.type === 'iframe-images-loaded') {
      // 所有图片加载完成后更新尺寸
      if (data.width && data.height) {
        const contentWidth = data.width;
        const contentHeight = data.height;
        
        setHtmlContentSize({
          width: contentWidth,
          height: contentHeight
        });
        
        // 如果用户没有手动设置缩放比例，且尚未应用初始智能缩放，则应用智能缩放
        if (userScale === null && !isInitialScaleSet) {
          const optimalScale = calculateOptimalScaleCallback(contentWidth, contentHeight);
          setScale(optimalScale);
          setIsInitialScaleSet(true); // 标记已应用初始智能缩放
        }
      }
    }
  }, [onLoadComplete, userScale, isInitialScaleSet, calculateOptimalScaleCallback]);

  useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [handleMessage]);

  // 添加手动重新加载功能
  const handleReload = useCallback(() => {
    // 重置状态
    resetLoadState();
    // 短暂延迟后重新加载
    setTimeout(() => {
      setupPreview();
    }, 100);
  }, [resetLoadState, setupPreview]);


  
  // 重写"还原"按钮的事件处理器
  const handleRestore = () => {
    // 1. 取消可能进行中的自动保存
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
      autoSaveTimerRef.current = null;
    }
    
    // 2. 重置用户编辑状态标志
    if (isUserEditingRef) {
      isUserEditingRef.current = false;
    }

    // 3. 调用父组件的onRestore方法来更新HTML内容
    if (onRestore) {
      onRestore();
    }
    
    // 4. 更新当前编辑内容和最新HTML引用
    if (htmlContent) {
      setCurrentEditingContent(htmlContent);
      if (latestEditorHtmlRef) {
        latestEditorHtmlRef.current = htmlContent;
      }
    }
    
    // 5. 确保setupPreview不会跳过更新
    lastUsedHtmlContentForSrcdocRef.current = '';
    previousPreviewHtmlRef.current = '';
    
    // 6. 完全重置所有状态
    resetLoadState();
    
    // 7. 直接调用setupPreview，而不是依赖iframe重新挂载
    setupPreview();
  };

  // 使用permissionUtils中的函数创建handleActionWithPermission
  const handleActionWithPermission = useMemo(() => {
    return createHandleActionWithPermission(showPermissionError);
  }, [showPermissionError]);

  const handleLayerMoveUp = useCallback(() => {
    if (iframeRef.current) {
      const success = moveUp(iframeRef.current);
      
      if (success) {
        // 延迟一点再触发 HTML 内容更新，确保视觉效果先显示
        setTimeout(() => {
          const iframeDoc = iframeRef.current.contentDocument;
          if (iframeDoc) {
            const container = iframeDoc.querySelector('.content-container');
            if (container && onHtmlContentChange) {
              onHtmlContentChange(container.innerHTML);
            }
          }
        }, 100);
      }
    }
  }, [onHtmlContentChange]);

  const handleLayerMoveDown = useCallback(() => {
    if (iframeRef.current) {
      const success = moveDown(iframeRef.current);
      
      if (success) {
        // 延迟一点再触发 HTML 内容更新，确保视觉效果先显示
        setTimeout(() => {
          const iframeDoc = iframeRef.current.contentDocument;
          if (iframeDoc) {
            const container = iframeDoc.querySelector('.content-container');
            if (container && onHtmlContentChange) {
              onHtmlContentChange(container.innerHTML);
            }
          }
        }, 100);
      }
    }
  }, [onHtmlContentChange]);

  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    const handleLoad = () => {
      // 清除加载错误状态，避免显示加载失败的效果
      setLoadError(null);
      
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        console.error('无法访问 iframe 文档');
        return;
      }

      // 使用之前定义的handleElementClickWithPermission函数
      // Initialize the editor, passing the unified state setter as the callback
      textEditorRef.current = createTextEditor(
        iframeDoc,
        handleElementClickWithPermission,
        memoizedOnHtmlContentChange,
        debugMode,
        false,
        scale,
        setActiveEditorElement // Pass the unified state setter
      );
      
      // 初始化编辑器
      textEditorRef.current.init();
      
      // 确保初始化后立即添加事件监听器
      if (iframeDoc) {
        // 添加selectionchange事件监听器
        const handleSelectionChange = () => {
          const selection = iframeDoc.getSelection();
          if (!selection || selection.rangeCount === 0) {
            if (debugMode) console.log('selectionchange: 没有有效选区');
            return;
          }
          
          // 使用debounce避免频繁处理
          // 由于selectionchange触发频率高，只在真正需要更新状态时处理
          clearTimeout(selectionChangeTimerRef.current);
          selectionChangeTimerRef.current = setTimeout(() => {
            try {
              // 获取选区的公共祖先节点
              let parentElement = selection.getRangeAt(0).commonAncestorContainer;
              if (parentElement.nodeType === Node.TEXT_NODE) {
                parentElement = parentElement.parentNode;
              }
              
              // 如果找到有效元素，更新activeEditorElement
              if (parentElement && parentElement instanceof HTMLElement && parentElement.isConnected) {
                // 检查元素是否可编辑
                const isEditableElement = 
                  parentElement.getAttribute('contenteditable') === 'true' || 
                  parentElement.hasAttribute('data-editable-fengmian') ||
                  parentElement.closest('[contenteditable="true"]') ||
                  parentElement.closest('[data-editable-fengmian]');
                
                if (isEditableElement) {
                  if (debugMode) console.log('selectionchange: 选中的元素是可编辑元素', parentElement.tagName, parentElement.className);
                  
                  // 找到最近的可编辑父元素
                  let editableElement = parentElement;
                  if (parentElement.getAttribute('contenteditable') !== 'true' && !parentElement.hasAttribute('data-editable-fengmian')) {
                    const closestEditable = parentElement.closest('[contenteditable="true"], [data-editable-fengmian]');
                    if (closestEditable) {
                      editableElement = closestEditable;
                    }
                  }
                  
                  // 如果新选择的元素与当前活动元素不同，先触发blur事件
                  if (activeEditorElement && activeEditorElement !== editableElement && activeEditorElement.isConnected) {
                    if (debugMode) console.log('selectionchange: 切换到新的可编辑元素，触发blur事件');
                    
                    // 清除旧元素的编辑状态标记
                    activeEditorElement.removeAttribute('data-editing');
                    activeEditorElement.classList.remove('editing-active-outline');
                    
                    // 触发自定义事件，通知工具栏重置状态并准备更新
                    const blurEvent = new CustomEvent('editor-element-blur', {
                      bubbles: true,
                      detail: { target: activeEditorElement }
                    });
                    iframeDoc.dispatchEvent(blurEvent);
                  }
                  
                  // 确保元素有正确的编辑状态标记
                  if (!editableElement.hasAttribute('data-editing')) {
                    editableElement.setAttribute('data-editing', 'true');
                    if (debugMode) console.log('selectionchange: 为元素添加编辑状态标记');
                  }
                  
                  // 添加编辑状态样式类
                  if (!editableElement.classList.contains('editing-active-outline')) {
                    editableElement.classList.add('editing-active-outline');
                  }
                  
                  // 移除拖拽模式相关样式类
                  editableElement.classList.remove('drag-mode', 'selected-for-drag');
                  
                  // 设置活动元素
                  if (activeEditorElement !== editableElement) {
                    setActiveEditorElement(editableElement);
                  }
                  
                  // 如果iframe中有textEditor对象，通知它选择变化
                  if (iframeDoc.defaultView && iframeDoc.defaultView.textEditor) {
                    if (debugMode) console.log('selectionchange: 通知textEditor选择变化');
                    if (typeof iframeDoc.defaultView.textEditor.handleSelectionChange === 'function') {
                      iframeDoc.defaultView.textEditor.handleSelectionChange(editableElement);
                    }
                  }
                }
                // 注意：这里不处理非可编辑元素的情况，避免与点击事件处理冲突
              }
            } catch (error) {
              console.error('selectionchange: 处理选择变化错误', error);
            }
          }, 50); // 短暂延迟，避免频繁触发
        };
        
        // 添加click事件监听器
        const handleClick = (e) => {
          const target = e.target;
          if (!target || !(target instanceof HTMLElement)) return;
          
          if (debugMode) console.log('iframe click: 检查元素', target.tagName, target.className);
          
          // 检查是否点击了特殊控件元素
          const isSpecialControlClick = target.closest('.resize-handle, .editor-control, .color-picker-popup, button, select, input');
          if (isSpecialControlClick) {
            if (debugMode) console.log('iframe click: 点击了特殊控件，不改变编辑状态');
            return; // 点击特殊控件不改变编辑状态
          }
          
          // 检查是否点击了可编辑元素
          const clickedEditableElement = 
            target.getAttribute('contenteditable') === 'true' || 
            target.hasAttribute('data-editable-fengmian') ||
            target.closest('[contenteditable="true"]') ||
            target.closest('[data-editable-fengmian]');
          
          if (clickedEditableElement) {
            if (debugMode) console.log('iframe click: 点击了可编辑元素');
            
            // 获取实际的可编辑元素（可能是父元素）
            let editableElement = target;
            if (target.getAttribute('contenteditable') !== 'true' && !target.hasAttribute('data-editable-fengmian')) {
              const closestEditable = target.closest('[contenteditable="true"], [data-editable-fengmian]');
              if (closestEditable) {
                editableElement = closestEditable;
                if (debugMode) console.log('iframe click: 找到最近的可编辑父元素:', editableElement.tagName);
              }
            }
            
            // 如果点击了与当前活动元素不同的元素，先触发blur事件
            if (activeEditorElement && activeEditorElement !== editableElement) {
              if (debugMode) console.log('iframe click: 点击了不同的可编辑元素，触发blur事件');
              
              // 清除旧元素的编辑状态标记
              if (activeEditorElement.isConnected) {
                activeEditorElement.removeAttribute('data-editing');
                activeEditorElement.classList.remove('editing-active-outline');
                
                // 确保旧元素失去焦点
                if (typeof activeEditorElement.blur === 'function' && 
                    iframeDoc.activeElement === activeEditorElement) {
                  activeEditorElement.blur();
                }
                
                // 确保旧元素不再处于编辑模式
                if (iframeDoc.defaultView && iframeDoc.defaultView.textEditor) {
                  const textEditor = iframeDoc.defaultView.textEditor;
                  if (textEditor.editMode && typeof textEditor.editMode.exitEditingMode === 'function') {
                    textEditor.editMode.exitEditingMode();
                  }
                  
                  // 确保全局编辑状态被重置
                  if (typeof textEditor.setIsEditing === 'function') {
                    textEditor.setIsEditing(false);
                  }
                }
                
                // 临时重置全局状态，确保在设置新元素之前，旧元素的状态被完全清除
                if (window.currentActiveTextEditor) {
                  window.currentActiveTextEditor.activeElement = null;
                  window.currentActiveTextEditor.isEditing = false;
                }
                
                // 确保iframe内的全局状态也被临时重置
                if (iframeDoc.defaultView && iframeDoc.defaultView.currentActiveTextEditor) {
                  iframeDoc.defaultView.currentActiveTextEditor.activeElement = null;
                  iframeDoc.defaultView.currentActiveTextEditor.isEditing = false;
                }
              }
              
              // 触发自定义事件，通知工具栏重置状态并准备更新
              const blurEvent = new CustomEvent('editor-element-blur', {
                bubbles: true,
                detail: { target: activeEditorElement }
              });
              iframeDoc.dispatchEvent(blurEvent);
            }
            
            // 添加新元素的编辑状态标记
            editableElement.setAttribute('data-editing', 'true');
            if (!editableElement.classList.contains('editing-active-outline')) {
              editableElement.classList.add('editing-active-outline');
            }
            
            // 如果是拖拽模式，清除拖拽相关样式
            if (editableElement.classList.contains('drag-mode') || editableElement.classList.contains('selected-for-drag')) {
              editableElement.classList.remove('drag-mode', 'selected-for-drag');
            }
            
            // 设置新的活动元素
            setActiveEditorElement(editableElement);
            
            // 直接更新全局状态，确保它与React组件的状态保持一致
            if (window.currentActiveTextEditor) {
              window.currentActiveTextEditor.activeElement = editableElement;
              window.currentActiveTextEditor.isEditing = true;
            }
            
            // 确保iframe内的全局状态也被更新
            if (iframeDoc.defaultView && iframeDoc.defaultView.currentActiveTextEditor) {
              iframeDoc.defaultView.currentActiveTextEditor.activeElement = editableElement;
              iframeDoc.defaultView.currentActiveTextEditor.isEditing = true;
            }
            
            // 如果有textEditor实例，也更新它的状态
            if (iframeDoc.defaultView && iframeDoc.defaultView.textEditor) {
              const textEditor = iframeDoc.defaultView.textEditor;
              if (typeof textEditor.setActiveElement === 'function') {
                textEditor.setActiveElement(editableElement);
              }
              if (typeof textEditor.setIsEditing === 'function') {
                textEditor.setIsEditing(true);
              }
            }
          } else {
            if (debugMode) console.log('iframe click: 点击了非可编辑元素');
            // 如果当前有活动编辑元素，需要触发blur事件
            if (activeEditorElement) {
              if (debugMode) console.log('iframe click: 点击了非可编辑元素，清除活动元素');
              
              // 清除编辑状态标记
              if (activeEditorElement.isConnected) {
                activeEditorElement.removeAttribute('data-editing');
                activeEditorElement.classList.remove('editing-active-outline');
                
                // 确保元素失去焦点
                if (typeof activeEditorElement.blur === 'function' && 
                    iframeDoc.activeElement === activeEditorElement) {
                  activeEditorElement.blur();
                }
                
                // 确保元素不再处于编辑模式
                if (iframeDoc.defaultView && iframeDoc.defaultView.textEditor) {
                  const textEditor = iframeDoc.defaultView.textEditor;
                  if (textEditor.editMode && typeof textEditor.editMode.exitEditingMode === 'function') {
                    textEditor.editMode.exitEditingMode();
                  }
                  
                  // 确保全局编辑状态被重置
                  if (typeof textEditor.setIsEditing === 'function') {
                    textEditor.setIsEditing(false);
                  }
                }
              }
              
              // 触发自定义事件，通知工具栏重置状态
              const blurEvent = new CustomEvent('editor-element-blur', {
                bubbles: true,
                detail: { target: activeEditorElement }
              });
              iframeDoc.dispatchEvent(blurEvent);
              setActiveEditorElement(null);
              
              // 直接重置全局状态
              if (window.currentActiveTextEditor) {
                window.currentActiveTextEditor.activeElement = null;
                window.currentActiveTextEditor.isEditing = false;
              }
              
              // 确保iframe内的全局状态也被重置
              if (iframeDoc.defaultView && iframeDoc.defaultView.currentActiveTextEditor) {
                iframeDoc.defaultView.currentActiveTextEditor.activeElement = null;
                iframeDoc.defaultView.currentActiveTextEditor.isEditing = false;
              }
            }
          }
        };
        
        iframeDoc.addEventListener('selectionchange', handleSelectionChange);
        iframeDoc.addEventListener('click', handleClick);
        
        return () => {
          iframeDoc.removeEventListener('selectionchange', handleSelectionChange);
          iframeDoc.removeEventListener('click', handleClick);
        };
      }
    };

    if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
      handleLoad();
    } else {
      iframe.addEventListener('load', handleLoad);
    }

    return () => {
      iframe.removeEventListener('load', handleLoad);
      if (textEditorRef.current && textEditorRef.current.cleanup) {
        textEditorRef.current.cleanup();
      }
    };
  }, [memoizedOnHtmlContentChange, debugMode, scale, onElementClick, setActiveEditorElement]);

  // 文本样式函数已迁移到 ./utils/styleUtils.js

  // 辅助函数：获取当前文本编辑器实例
  const getTextEditorInstance = () => {
    if (textEditorRef.current) {
      return textEditorRef.current;
    }
    
    if (iframeRef.current) {
      const iframeDoc = iframeRef.current.contentDocument;
      if (iframeDoc && iframeDoc.defaultView && iframeDoc.defaultView.textEditor) {
        return iframeDoc.defaultView.textEditor;
      }
    }
    
    if (window.currentActiveTextEditor && window.currentActiveTextEditor.textEditorRef) {
      return window.currentActiveTextEditor.textEditorRef;
    }
    
    return null;
  };



  // 初始化自动保存功能
  useEffect(() => {
    // 只有当coverId存在且内容已加载时才初始化自动保存
    if (coverId && contentLoaded) {
      if (debugMode) console.log('初始化自动保存功能, coverId:', coverId);
      
      const manager = autoSaveManager.createAutoSaveManager({
        iframeRef,
        coverId,
        debugMode,
        interval: 10000, // 10秒自动保存一次
        onSaveStatusChange: setIsSaving,
        onSaveComplete: (success, errorMessage) => {
          // 可以在这里添加保存完成后的回调逻辑
          if (debugMode) {
            console.log(`自动保存完成，结果: ${success ? '成功' : '失败'}${errorMessage ? ', 错误: ' + errorMessage : ''}`);
          }
        }
      });
      
      autoSaveControllerRef.current = manager;
      manager.start();
      
      return () => {
        if (debugMode) console.log('停止自动保存功能, coverId:', coverId);
        manager.cleanup();
        autoSaveControllerRef.current = null;
      };
    }
  }, [coverId, contentLoaded, debugMode, iframeRef]);

  useEffect(() => {
    return () => {
      // 清理所有定时器，避免内存泄露
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
        autoSaveTimerRef.current = null;
      }
      
      if (selectionChangeTimerRef.current) {
        clearTimeout(selectionChangeTimerRef.current);
        selectionChangeTimerRef.current = null;
      }
    };
  }, []);

  // 在组件内部添加showHomePreview状态变量
  const [showHomePreview, setShowHomePreview] = useState(() => {
    // 直接检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const hasCode = urlParams.has('code');
    const hasEditId = urlParams.has('edit') || urlParams.has('coverId');
    
    // 如果URL中有code或edit/coverId参数，或者isPreviewActive为true，则不显示首页展示内容
    if (hasCode || hasEditId || isPreviewActive) {
      return false;
    }
    
    // 否则，显示首页展示内容
    return true;
  });

  // 添加useEffect监听htmlContent变化
  useEffect(() => {
    if (htmlContent) {
      setShowHomePreview(false);
    }
  }, [htmlContent]);

  // 修改handleStartCreate函数，触发showInputArea事件
  const handleStartCreate = () => {
    setShowHomePreview(false);
    // 触发自定义事件，通知ChatGenerate.jsx显示输入框区域
    window.dispatchEvent(new CustomEvent('showInputArea'));
    // 触发自定义事件，通知ChatGenerate.jsx展开输入框区域
    window.dispatchEvent(new CustomEvent('expandInputArea'));
    // 聚焦到输入框
    const inputElement = document.querySelector('.chat-input textarea');
    if (inputElement) {
      inputElement.focus();
    }
  };

  return (
    <div className="absolute inset-0 flex flex-row bg-white chat-preview-container">
      {/* 预览区域 - 使用新的PreviewArea组件 */}
      <div className="flex-grow h-full flex flex-col">
        <PreviewArea
          iframeRef={iframeRef}
          containerRef={containerRef}
          outerPreviewAreaRef={outerPreviewAreaRef}
          previewContainerStyle={previewContainerStyle}
          outerPreviewAreaStyle={outerPreviewAreaStyle}
          isLoading={isLoading}
          isGenerating={isGenerating}
          loadError={loadError}
          htmlContentSize={htmlContentSize} // 改回使用htmlContentSize，确保iframe尺寸与实际内容尺寸匹配
          iframeKey={iframeKey}
          htmlContent={htmlContent}
          showHomePreview={showHomePreview}
          onStartCreate={handleStartCreate}
          onRetry={() => {
            // 清除加载错误状态
            setLoadError(null);
            // 根据htmlContent是否存在来决定是否显示HomePreview
            // 如果htmlContent存在，则设置showHomePreview为false，否则设置为true
            setShowHomePreview(!htmlContent);
            // 调用setupPreview函数
            setupPreview();
          }}
        />
      </div>

      {/* 右侧编辑面板 - 仅在预览激活时显示，使用固定定位确保不受输入框影响 */}
      {isPreviewActive && (
        <div className="w-80 h-full bg-white border-l border-gray-200 flex flex-col shadow-lg overflow-y-auto pt-2 pb-4 fixed top-0 right-0 bottom-0" style={{ zIndex: 100 }}>
          {/* 预览模式控制器 - 顶部显眼位置 */}
          <div className="px-3 py-2 border-b border-gray-100 bg-gray-50">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-gray-600">加载模式</span>
              <PreviewModeSwitch />
            </div>
          </div>

          {/* 顶部全局按钮 */}
          <div className="py-2 px-3 border-b border-gray-200 flex gap-2">
            {onShare && (
              <button
                onClick={() => handleActionWithPermission('分享链接', onShare)}
                title="分享"
                className="flex-grow px-3 py-1.5 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded-md shadow-sm disabled:opacity-50 relative"
                disabled={!areButtonsEnabled}
              >
                分享
                <Crown size={10} className="absolute -top-1 -right-1 text-orange-500" />
              </button>
            )}
            {onDownloadCover && (
              <button
                onClick={() => onDownloadCover()}
                title="下载为图片"
                className="flex-grow px-3 py-1.5 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md shadow-sm disabled:opacity-50"
                disabled={!areButtonsEnabled}
              >
                下载为图片
              </button>
            )}
            {onDownloadHtml && (
              <button
                onClick={() => onDownloadHtml()}
                title="下载为Html"
                className="flex-grow px-3 py-1.5 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md shadow-sm disabled:opacity-50 relative"
                disabled={!areButtonsEnabled}
              >
                下载为Html
                <Crown size={10} className="absolute -top-1 -right-1 text-orange-500" />
              </button>
            )}
          </div>

          {/* 编辑功能区域 */}
          <div className="flex-grow pt-2 px-4 pb-4 overflow-y-auto">
            {contentLoaded && (
              <div className="flex flex-col space-y-4">
                <PreviewEditorToolbar
                  iframeRef={iframeRef}
                  executeCommandInternal={executeCommand}
                  areButtonsEnabled={areButtonsEnabled}
                  onMoveUp={handleLayerMoveUp}
                  onMoveDown={handleLayerMoveDown}
                  activeEditorElement={activeEditorElement}
                />
              </div>
            )}
          </div>
          
          {/* 底部操作区 - 使用sticky定位确保始终可见 */}
          <div className="p-3 border-t border-gray-200 bg-white sticky bottom-0">
            {/* 预览模式控制器已移至顶部 */}

            {/* 缩放控制器 - 修改为水平布局 */}
            <div className="mb-4">
              <ZoomController
                scale={scale}
                minScale={0.1}
                maxScale={2.5}
                onScaleChange={handleScaleChange}
                onResetScale={resetScale}
              />
            </div>
            {/* 底部按钮组 - 修改为流式布局，确保三个按钮并排显示 */}
            <div className="flex flex-wrap gap-2">
              {onPreview && (
                <button
                  onClick={onPreview}
                  title="全屏预览"
                  className="flex-grow px-3 py-2 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded-md shadow-sm disabled:opacity-50"
                  disabled={!areButtonsEnabled}
                >
                  全屏预览
                </button>
              )}
              {coverId && onSave && (
                <button
                  onClick={() => onSave('manual')}
                  title="保存"
                  className="flex-grow px-3 py-2 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded-md shadow-sm disabled:opacity-50"
                  disabled={!areButtonsEnabled || isSaving}
                >
                  {isSaving ? '保存中...' : '保存'}
                </button>
              )}
              {onRestore && (
                <button
                  onClick={handleRestore}
                  title="还原Html为最初状态"
                  className="flex-grow px-3 py-2 text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 rounded-md shadow-sm disabled:opacity-50"
                  disabled={!areButtonsEnabled}
                >
                  还原Html为最初状态
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 权限提示和加载动画等模态层 */}
      <PermissionDialog
        open={showPermissionDialog}
        onClose={() => setShowPermissionDialog(false)}
        featureName={permissionDialogInfo.featureName}
        reason={permissionDialogInfo.reason}
      />

      {/* 生成封面的加载动画 - 相对于预览区域居中显示 */}
      {isGenerating && (
        <div className="absolute inset-0 flex items-center justify-center z-[9999] bg-black bg-opacity-5 backdrop-blur-[2px]">
          <div className="flex flex-col items-center p-6 rounded-2xl shadow-xl max-w-sm mx-4" style={{background: 'rgba(255,255,255,0.95)'}}>
            {/* 主要动画容器 */}
            <div className="relative w-20 h-20 mb-4">
              {/* 外层脉动环 */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 opacity-20 animate-pulse"></div>

              {/* 中层旋转环 */}
              <div className="absolute inset-2 rounded-full border-4 border-transparent bg-gradient-to-r from-purple-500 to-pink-500 animate-spin"
                   style={{
                     background: 'conic-gradient(from 0deg, #8b5cf6, #ec4899, #8b5cf6)',
                     WebkitMask: 'radial-gradient(circle at center, transparent 60%, black 61%)',
                     mask: 'radial-gradient(circle at center, transparent 60%, black 61%)'
                   }}>
              </div>

              {/* 内层进度圆 */}
              <div className="absolute inset-4 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-semibold text-sm">
                {Math.round(progress)}%
              </div>

              {/* 装饰性粒子 - 增加更多动态元素 */}
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-purple-400 rounded-full animate-bounce opacity-60"></div>
              <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-pink-400 rounded-full animate-bounce opacity-60" style={{animationDelay: '0.5s'}}></div>
              
              {/* 新增装饰性粒子 */}
              <div className="absolute top-1/2 -right-3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-ping opacity-40" style={{animationDuration: '1.5s'}}></div>
              <div className="absolute -top-3 left-1/2 w-1.5 h-1.5 bg-pink-300 rounded-full animate-ping opacity-40" style={{animationDuration: '2s'}}></div>
            </div>

            {/* 背景装饰元素 */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden rounded-2xl pointer-events-none z-[-1]">
              <div className="absolute top-[-10%] right-[-10%] w-40 h-40 bg-gradient-to-br from-purple-200 to-transparent rounded-full opacity-20 animate-pulse" style={{animationDuration: '3s'}}></div>
              <div className="absolute bottom-[-15%] left-[-15%] w-48 h-48 bg-gradient-to-tr from-pink-200 to-transparent rounded-full opacity-20 animate-pulse" style={{animationDuration: '4s'}}></div>
            </div>

            {/* 进度条 */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-3 overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>

            {/* 描述性文本 */}
            <p className="text-gray-700 font-medium text-sm mb-1 text-center">
              {feedbackText || "AI正在为您生成封面..."}
            </p>

            {/* 辅助文本 */}
            <p className="text-gray-500 text-xs text-center">
              请稍候，精彩即将呈现
            </p>
          </div>
        </div>
      )}

      {/* 查看源码模态框 */}
      <ViewSourceModal
        visible={showSourceModal}
        onClose={() => setShowSourceModal(false)}
        htmlContent={latestEditorHtmlRef.current || htmlContent}
      />
    </div>
  );
});

export default ChatPreview;




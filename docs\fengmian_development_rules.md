# 封面生成项目开发规则

## 1. 项目概述

封面生成系统是一个集成AI技术的自媒体封面生成平台，主要功能包括：

- 允许用户选择不同的封面尺寸（如小红书、微信等）
- 提供多种风格选择（如流动科技蓝、活力渐变、极简主义等）
- 用户可自定义填写文案、账号名称和副标题
- 系统构建提示词发送给AI服务，生成HTML代码
- 以动态封面形式预览生成结果
- 支持保存和下载生成的封面
- 集成积分系统控制用户使用频率

## 2. 技术栈

### 2.1 前端技术
- Next.js 14 (App Router) - 前端框架
- shadcn/ui - UI组件库(基于Radix UI和Tailwind CSS)
- Tailwind CSS - 样式工具
- TypeScript - 类型检查
- lucide-react - 图标库
- Axios 或 Next.js fetch API - HTTP请求库
- Day.js 或 date-fns - 日期处理

### 2.2 后端技术
- Node.js - 运行环境
- Express - Web框架
- Sequelize - ORM框架
- MySQL - 数据库
- JWT - 身份验证
- bcrypt - 密码加密

### 2.3 AI服务
- 通用API接口设计，支持对接多种AI服务提供商（DeepSeek、豆包等）
- 分层架构设计，支持多服务提供商和多模型管理
- 支持加密的API密钥管理和负载均衡
- 自定义提示词模板系统

## 3. 目录结构

### 3.1 前端目录结构
- `src/app` - Next.js 14 App Router页面
  - `(auth)` - 认证相关页面
  - `(dashboard)` - 用户仪表盘页面
  - `profile` - 个人中心页面
  - `generate` - 封面生成页面
- `src/components` - 组件目录
  - `admin/` - 管理员组件
  - `auth/` - 认证组件
  - `common/` - 通用组件
  - `core/` - 核心组件
  - `cover/` - 封面生成相关组件
    - `components/` - 子组件
    - `hooks/` - 自定义钩子
    - `utils/` - 工具函数
  - `ui/` - UI基础组件
  - `user/` - 用户相关组件
- `src/contexts` - 上下文管理
- `src/hooks` - 全局钩子
- `src/lib` - 工具库
- `src/services` - API服务调用
- `src/styles` - 全局样式
- `src/utils` - 工具函数

### 3.2 后端目录结构
- `backend/src/controllers` - API控制器
- `backend/src/models` - 数据模型
- `backend/src/routes` - 路由定义
- `backend/src/services` - 业务服务
- `backend/src/utils` - 工具函数
- `backend/src/middlewares` - 中间件
- `backend/src/tasks` - 定时任务
- `backend/src/config` - 系统配置

## 4. 开发规范

### 4.1 前端开发规范
- 使用Next.js 14的App Router路由系统
- 优先使用Server Components，仅在必要时使用Client Components
- 使用React函数组件和Hooks，避免类组件
- 使用模块化设计，拆分业务逻辑到单独的hook中
- 封装API调用到service层
- 使用统一的错误处理机制
- 遵循组件命名规范：[功能][组件类型]
- 表单验证必须在前端和后端同时实现
- 使用防抖和节流优化用户交互体验
- 使用Tailwind CSS进行样式管理
- 实现"时尚炫紫"(Stylish Purple Gradient)的全局视觉风格

### 4.2 后端开发规范
- 使用MVC架构
- API遵循RESTful设计原则
- 所有数据库操作必须使用ORM模型
- 敏感配置使用环境变量或加密存储
- 所有用户输入必须经过验证和净化
- 积分消费和AI调用需要进行事务管理
- 实现API访问频率限制

### 4.3 代码风格
- 使用ESLint和Prettier保持代码风格一致
- 代码注释必须说明功能、参数和返回值
- 变量和函数使用小驼峰命名法
- 组件使用大驼峰命名法
- 常量使用全大写下划线命名法
- 确保所有异步函数使用try/catch处理异常
- TypeScript文件使用.tsx或.ts扩展名
- 使用type和interface定义类型

## 5. 数据库规范

### 5.1 表结构规范
- 表名使用下划线命名法，复数形式
- 主键统一使用`id`字段，自增整数
- 使用`created_at`和`updated_at`记录时间戳
- 外键命名格式：`{关联表名}_id`
- 必须定义字段注释，说明字段用途
- 所有表必须包含软删除字段`is_deleted`

### 5.2 核心表说明
- `users` - 用户信息表
- `cover_records` - 封面生成记录表
- `style_prompts` - 风格模板表
- `base_prompts` - 基础提示词模板表
- `point_records` - 积分变动记录表
- `generation_tasks` - 生成任务状态表
- `ai_service_providers` - AI服务提供商表
- `ai_service_models` - AI模型表
- `ai_service_api_keys` - API密钥表

### 5.3 数据操作规范
- 查询必须使用索引字段
- 批量操作使用事务
- 敏感数据必须加密存储
- 避免级联删除，使用软删除
- 重要操作必须记录数据库审计日志

## 6. API规范

### 6.1 API设计原则
- 使用RESTful风格设计API
- URI使用资源名词，不使用动词
- 使用HTTP方法表示操作类型（GET, POST, PUT, DELETE）
- 使用HTTP状态码表示操作结果
- 所有API返回统一格式的JSON响应
- 分页查询必须提供总数和分页信息

### 6.2 API响应格式
```json
{
  "success": true/false,
  "message": "操作结果描述",
  "data": {}, // 响应数据
  "timestamp": "2023-05-30T12:00:00Z" // 响应时间戳
}
```

### 6.3 API鉴权
- 使用JWT进行API鉴权
- Token在Header中通过Authorization字段传递
- 需要权限的API必须经过鉴权中间件
- API访问日志必须记录用户ID和操作类型
- 实现API访问频率限制，防止滥用

## 7. 安全规范

### 7.1 用户认证
- 密码必须加密存储
- 登录失败次数限制
- 敏感操作需要二次验证
- 定期刷新Token
- 支持多种登录方式（密码、短信验证码等）

### 7.2 AI服务安全
- API密钥必须加密存储
- 轮换使用多个API密钥
- 提示词必须过滤用户输入
- 限制单用户AI调用频率
- 实现AI服务故障转移机制

### 7.3 积分系统安全
- 积分变动必须使用事务
- 记录完整的积分变动日志
- 防止重复扣除积分
- 积分操作必须有唯一标识
- VIP用户积分定期重置机制

### 7.4 日志和监控
- 记录敏感操作日志
- 监控异常API调用
- 监控API调用性能
- 敏感信息脱敏处理
- 实时告警机制

### 7.5 前端安全
- 实现CSP（内容安全策略）
- 防止XSS攻击
- CSRF保护
- 输入数据验证和过滤
- 安全的HTML渲染机制

## 8. 部署规范

### 8.1 环境配置
- 开发、测试、生产环境配置分离
- 使用环境变量管理配置
- 敏感配置加密存储
- 各环境数据库必须隔离
- 生产环境禁用调试功能

### 8.2 部署流程
- 代码合并前必须通过测试
- 部署前必须备份数据库
- 使用版本标签管理发布
- 支持回滚机制
- 部署后进行基本功能验证
- 采用蓝绿部署或灰度发布策略

## 9. 测试规范

### 9.1 单元测试
- 核心业务逻辑必须有单元测试
- 测试覆盖率要求达到70%以上
- 使用模拟对象替代外部依赖
- 每次代码提交前运行单元测试

### 9.2 集成测试
- API接口必须有集成测试
- 测试数据库与开发数据库分离
- 测试前需要准备测试数据
- 定期运行集成测试套件

### 9.3 性能测试
- 在正式发布前进行性能测试
- 监控API响应时间
- 测试系统在高负载下的表现
- 确保AI服务调用的稳定性

## 10. AI服务分层架构

### 10.1 架构设计
- 提供商层：管理不同的AI服务提供商
- 模型层：为每个提供商配置不同模型
- 密钥层：管理每个提供商的多个API密钥
- 适配器层：处理不同服务的请求和响应格式差异
- 负载均衡：基于权重和优先级分配请求

### 10.2 容错机制
- 自动故障检测与切换
- 请求重试机制
- 超时控制
- 并发请求限制
- 监控与告警

### 10.3 任务管理
- 生成任务状态追踪
- 支持任务取消功能
- 资源使用监控
- 用户使用限制

## 11. UI/UX设计规范

### 11.1 视觉设计
- 使用"时尚炫紫"(Stylish Purple Gradient)作为全局视觉风格
- 主色调为优雅且现代的紫色系
- 在关键UI元素上应用紫色渐变效果
- 使用浅色背景突出紫色渐变的视觉焦点
- 深灰或黑色用于主要文本，中灰色用于次要文本

### 11.2 组件设计
- 使用shadcn/ui组件库保持UI一致性
- 确保组件在不同设备上的响应式表现
- 统一的按钮、表单、表格等UI元素样式
- 操作按钮使用图标+工具提示(Tooltip)设计
- 表格内容统一居中对齐，行高一致

### 11.3 交互设计
- 提供清晰的用户反馈和状态指示
- 优化加载状态和空状态的视觉呈现
- 为所有操作按钮添加悬停效果
- 实现数据缓存策略，减少不必要的重复请求
- 提供手动刷新数据的功能

### 11.4 弹窗设计
- 确保预览窗口尺寸适合内容显示
- 使用DialogHeader/DialogClose组件避免重复的关闭图标
- 重要操作（如删除）必须有确认对话框
- 确保弹窗内容居中且完整显示 
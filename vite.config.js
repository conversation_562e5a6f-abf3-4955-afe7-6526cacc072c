import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig(({ mode }) => {
  // 加载环境变量，默认从.env文件
  const env = loadEnv(mode, process.cwd(), '')
  // 获取环境变量，如果不存在则使用默认值
  const frontendPort = parseInt(env.VITE_PORT || '3000')
  const backendUrl = env.VITE_API_URL || 'http://localhost:3002'

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '/src': path.resolve(__dirname, 'src')
      }
    },

    server: {
      port: frontendPort,
      host: true,
      proxy: {
        '/api': {
          target: backendUrl,
          changeOrigin: true,
          secure: false
        },
        '/uploads': {
          target: backendUrl,
          changeOrigin: true,
          secure: false
        }
      }
    },
    build: {
      outDir: 'dist',
      sourcemap: false,
      minify: 'terser',
      chunkSizeWarningLimit: 1500
    }
  }
})
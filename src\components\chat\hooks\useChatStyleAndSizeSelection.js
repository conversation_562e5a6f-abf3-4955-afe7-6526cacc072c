/**
 * 聊天页面风格和尺寸选择自定义Hook
 * 管理风格和尺寸选择的状态和逻辑
 */
import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';
import { getCurrentSizeType, fetchSizeTypesFromBackend, saveSizeTypes, getCoverSizeConfig } from '../../../services/templateService';
import { cachedRequest } from '../../../services/apiCacheService'; // 导入缓存服务
import logger from '../../../utils/logger';

/**
 * 聊天页面风格和尺寸选择Hook
 * @returns {Object} 包含风格和尺寸选择状态和处理函数的对象
 */
const useChatStyleAndSizeSelection = () => {
  // 风格和尺寸状态
  const [selectedStyle, setSelectedStyle] = useState('');
  const [selectedSizeType, setSelectedSizeType] = useState(getCurrentSizeType());
  const [styleImages, setStyleImages] = useState({});
  const [justGenerated, setJustGenerated] = useState(false);
  const [availableStyles, setAvailableStyles] = useState([]);
  const [availableSizeTypes, setAvailableSizeTypes] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  // 添加一个状态，用于管理当前选中的风格示例
  const [selectedExampleId, setSelectedExampleId] = useState(null);

  // 处理风格变更
  const handleStyleChange = useCallback((styleId, skipStyleExample = false) => {
    if (!styleId) {
      console.error('无效的风格ID');
      message.error('选择的风格无效，请重新选择');
      return;
    }

    setSelectedStyle(styleId);

    if (justGenerated) {
      setJustGenerated(false);
    }
  }, [justGenerated]);

  // 处理尺寸类型变更
  const handleSizeTypeChange = useCallback((sizeType, loading = false) => {
    if (loading) {
      message.info('正在生成中，无法切换尺寸');
      return;
    }

    if (sizeType !== selectedSizeType) {
      setSelectedSizeType(sizeType);
    }
  }, [selectedSizeType]);

  // 尺寸类型变更时重置生成状态
  useEffect(() => {
    if (justGenerated) {
      setJustGenerated(false);
    }
  }, [selectedSizeType, justGenerated]);

  // 从后端获取封面类型数据
  const fetchSizeTypes = useCallback(async (forceRefresh = false) => {
    try {
      setIsLoading(true);

      // 从后端获取封面类型数据，直接使用templateService的函数，它已经修改为不需要token
      const backendSizeTypes = await fetchSizeTypesFromBackend();

      if (backendSizeTypes && typeof backendSizeTypes === 'object' && Object.keys(backendSizeTypes).length > 0) {
        // 如果成功从后端获取到封面类型，将其保存到本地存储和状态中
        saveSizeTypes(backendSizeTypes);
        setAvailableSizeTypes(backendSizeTypes);

        // 如果当前选中的类型不在新的类型列表中，自动选择第一个类型
        if (!backendSizeTypes[selectedSizeType] && Object.keys(backendSizeTypes).length > 0) {
          const firstType = Object.keys(backendSizeTypes)[0];
          setSelectedSizeType(firstType);
        }

        return true;
      } else {
        return false;
      }
    } catch (error) {
      logger.error('从后端获取封面类型数据失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [selectedSizeType]);

  // 从后端获取风格数据
  const fetchStyles = useCallback(async (forceRefresh = false) => {
    try {
      setIsLoading(true);

      // 使用缓存服务获取风格列表
      const response = await cachedRequest('/api/style', {}, { 
        forceRefresh 
      });

      // 检查响应数据结构
      if (response.success && Array.isArray(response.data)) {
        const apiStyles = response.data;

        // 格式化风格数据
        const formattedStyles = apiStyles.map(style => ({
          id: style.id_code || style.id.toString(),
          name: style.style_name || style.display_name,
          thumbnailUrl: style.thumbnail_url || null,
          previewHtml: style.preview_html || null
        }));

        if (formattedStyles.length > 0) {
          setAvailableStyles(formattedStyles);

          // 如果当前选中的风格不在新的风格列表中，自动选择第一个风格
          if (!selectedStyle || !formattedStyles.some(style => style.id === selectedStyle)) {
            const firstStyle = formattedStyles[0].id;
            setSelectedStyle(firstStyle);
          }

          // 处理风格图片
          try {
            const apiImages = {};
            apiStyles.forEach(style => {
              if (style.StyleExamples && style.StyleExamples.length > 0 && style.StyleExamples[0].image_url) {
                apiImages[style.id_code || style.id.toString()] = style.StyleExamples[0].image_url;
              }
            });

            // 合并本地存储和API返回的图片
            const savedImages = localStorage.getItem('fengmian_style_images');
            const parsedImages = savedImages ? JSON.parse(savedImages) : {};
            const mergedImages = { ...parsedImages, ...apiImages };

            setStyleImages(mergedImages);

            // 保存到本地存储
            if (Object.keys(mergedImages).length > 0) {
              try {
                localStorage.setItem('fengmian_style_images', JSON.stringify(mergedImages));
              } catch (storageErr) {
                console.error('保存风格图片到本地存储失败');
              }
            }
          } catch (err) {
            console.error('处理API返回的风格图片失败');
          }

          return true;
        }
      }

      // 如果没有获取到有效的风格数据，显示错误信息并返回false
      logger.error('从API获取风格数据失败或返回格式不正确');
      message.error('获取风格列表失败，请刷新页面重试');
      return false;
    } catch (error) {
      logger.error('从后端获取风格数据失败', { error: error.message });
      message.error('获取风格列表失败，请检查网络连接后重试');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [selectedStyle]);

  // 刷新数据
  const refreshData = useCallback(async () => {
    setIsLoading(true);
    message.loading('正在刷新数据...', 1);

    const sizeSuccess = await fetchSizeTypes(true);
    const styleSuccess = await fetchStyles(true);

    if (sizeSuccess && styleSuccess) {
      message.success('数据已成功刷新');
    } else {
      message.error('部分数据刷新失败，请重试');
    }

    setIsLoading(false);
  }, [fetchSizeTypes, fetchStyles]);

  // 在组件挂载时加载数据
  useEffect(() => {
    // 加载风格数据
    fetchStyles();

    // 加载封面类型数据
    fetchSizeTypes();
  }, []); // 空依赖数组确保只在挂载时执行一次

  return {
    selectedStyle,
    selectedSizeType,
    styleImages,
    justGenerated,
    availableStyles,
    availableSizeTypes,
    isLoading,
    setSelectedStyle,
    setSelectedSizeType,
    setStyleImages,
    setJustGenerated,
    handleStyleChange,
    handleSizeTypeChange,
    fetchSizeTypes,
    fetchStyles,
    refreshData,
    selectedExampleId,
    setSelectedExampleId,
  };
};

export default useChatStyleAndSizeSelection;

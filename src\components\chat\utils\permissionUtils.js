/**
 * 权限检查工具模块
 * 提供权限检查相关的通用函数
 */
import { checkFeatureAvailability } from '../../../services/featureService';

/**
 * 创建显示权限错误的函数
 * @param {Function} setPermissionDialogInfo - 设置权限对话框信息的函数
 * @param {Function} setShowPermissionDialog - 设置显示权限对话框的函数
 * @returns {Function} 显示权限错误的函数
 */
export const createShowPermissionError = (setPermissionDialogInfo, setShowPermissionDialog) => {
  /**
   * 显示权限错误对话框
   * @param {string} featureName - 功能名称
   * @param {string} reason - 错误原因
   */
  return (featureName, reason) => {
    setPermissionDialogInfo({ featureName, reason });
    setShowPermissionDialog(true);
  };
};

/**
 * 创建带权限检查的操作处理函数
 * @param {Function} showPermissionError - 显示权限错误的函数
 * @returns {Function} 带权限检查的操作处理函数
 */
export const createHandleActionWithPermission = (showPermissionError) => {
  /**
   * 带权限检查的操作处理函数
   * @param {string} actionName - 操作名称
   * @param {Function} actionFn - 操作函数
   * @returns {Promise<void>}
   */
  return async (actionName, actionFn) => {
    if (!actionFn) return;
    
    try {
      const result = await checkFeatureAvailability(actionName);
      if (!result.available) {
        showPermissionError(actionName, result.reason || '您的账号权限不足，无法使用此功能');
        return;
      }
      actionFn();
    } catch (error) {
      showPermissionError(actionName, '权限检查失败，请重试');
    }
  };
};

/**
 * 创建带权限检查的元素点击处理函数
 * @param {Function} showPermissionError - 显示权限错误的函数
 * @param {Function} onElementClick - 元素点击回调函数
 * @returns {Function} 带权限检查的元素点击处理函数
 */
export const createHandleElementClickWithPermission = (showPermissionError, onElementClick) => {
  /**
   * 带权限检查的元素点击处理函数
   * @param {HTMLElement} element - 被点击的元素
   * @param {string} action - 操作类型
   * @returns {Promise<void>}
   */
  return async (element, action) => {
    // 如果是编辑相关操作，检查权限（包含单击进入拖拽模式）
    if (action === 'dblclick-to-edit' || action === 'click-to-edit' || action === 'click-to-drag') {
      const result = await checkFeatureAvailability('文本编辑');
      if (!result.available) {
        // 显示权限弹窗
        showPermissionError('文本编辑', result.reason || '您的账号权限不足，无法使用此功能');

        // 清除文本框的选中状态，防止用户关闭弹窗后继续操作
        if (element) {
          // 移除拖拽模式类
          element.classList.remove('drag-mode', 'selected-for-drag', 'dragging');
          // 移除编辑状态属性
          element.removeAttribute('data-editing');
          element.classList.remove('editing-active-outline');
          // 失去焦点
          if (document.activeElement === element) {
            element.blur();
          }
        }

        return;
      }
    }

    // 如果有权限或不是编辑操作，继续执行原有逻辑
    if (onElementClick) {
      onElementClick(element, action);
    }
  };
};

/**
 * 检查查看源码权限
 * @param {Function} showPermissionError - 显示权限错误的函数
 * @param {Function} onViewSource - 查看源码回调函数
 * @param {string} currentContent - 当前内容
 * @param {string} defaultContent - 默认内容
 * @returns {Promise<void>}
 */
export const checkViewSourcePermission = async (showPermissionError, onViewSource, currentContent, defaultContent) => {
  if (!onViewSource) return;
  
  try {
    // 检查功能是否可用
    const result = await checkFeatureAvailability('查看源码');
    if (!result || !result.available) {
      showPermissionError('查看源码', result?.reason || '您的账号权限不足，无法使用此功能');
      return;
    }
    
    // 有权限才调用原函数
    onViewSource(currentContent || defaultContent || '');
  } catch (error) {
    showPermissionError('查看源码', '权限检查失败，请重试');
  }
};

/**
 * 检查命令执行权限
 * @param {string} command - 命令名称
 * @param {Function} showPermissionError - 显示权限错误的函数
 * @returns {Promise<boolean>} 是否有权限
 */
export const checkCommandPermission = async (command, showPermissionError) => {
  // 对于撤销/重做命令，检查文本编辑权限
  if (command === 'undo' || command === 'redo') {
    const permResult = await checkFeatureAvailability('文本编辑');
    if (!permResult.available) {
      showPermissionError('文本编辑', permResult.reason || '您的账号权限不足，无法使用此功能');
      return false;
    }
    return true;
  }
  
  // 对于其他命令，默认返回true
  return true;
}; 
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Smartphone, KeyRound, Lock, Loader2, ArrowLeft, CheckCircle2 } from 'lucide-react';
import axios from 'axios';
import { useToast } from '@/components/ui/use-toast';

// API调用函数
const sendSmsCode = async (phone, purpose) => {
  try {
    const response = await axios.post('/api/sms/send', { phone, purpose });
    return response.data;
  } catch (error) {
    console.error('发送验证码失败:', error);
    throw error;
  }
};

const resetPassword = async (data) => {
  try {
    const response = await axios.post('/api/auth/reset-password', data);
    return response.data;
  } catch (error) {
    console.error('重置密码失败:', error);
    if (error.response) {
      const errorData = {
        status: error.response.status,
        message: error.response.data?.message || '重置密码失败，请重试'
      };
      throw errorData;
    }
    throw error;
  }
};

const ForgotPassword = () => {
  const [phone, setPhone] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [sendingCode, setSendingCode] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();

  // Countdown timer effect
  useEffect(() => {
    let timerId;
    if (countdown > 0) {
      timerId = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    } else if (countdown === 0 && timerId) {
      clearInterval(timerId);
    }
    return () => clearInterval(timerId);
  }, [countdown]);

  // Handle Send SMS Code
  const handleSendCode = async () => {
    if (!phone) {
      setErrorMessage('请输入手机号');
      return;
    }
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      setErrorMessage('请输入有效的手机号');
      return;
    }
    
    setErrorMessage('');
    setSendingCode(true);
    
    try {
      const response = await sendSmsCode(phone, 'reset-password');
      if (response.success) {
        setErrorMessage('');
        setCountdown(60);
      } else {
        setErrorMessage(response.message || '验证码发送失败');
      }
    } catch (error) {
      setErrorMessage(error.response?.data?.message || error.message || '发送验证码失败，请检查网络连接');
    } finally {
      setSendingCode(false);
    }
  };

  // Handle Form Submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');
    
    // 验证手机号
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      setErrorMessage('请输入有效的手机号');
      return;
    }
    
    // 验证验证码
    if (!verifyCode || !/^\d{6}$/.test(verifyCode)) {
      setErrorMessage('请输入有效的6位验证码');
      return;
    }
    
    // 验证密码
    if (!password) {
      setErrorMessage('请输入新密码');
      return;
    }
    
    if (!/^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/.test(password)) {
      setErrorMessage('密码必须包含字母和数字，长度必须在8-32个字符之间');
      return;
    }
    
    // 验证确认密码
    if (password !== confirmPassword) {
      setErrorMessage('两次输入的密码不一致');
      return;
    }
    
    setLoading(true);
    
    try {
      const response = await resetPassword({
        phone,
        verifyCode,
        newPassword: password
      });

      if (response.success) {
        toast({
          title: '密码重置成功',
          description: '即将跳转到登录页面',
          variant: 'default'
        });
        
        setTimeout(() => {
          navigate('/auth');
        }, 1500);
      } else {
        setErrorMessage(response.message || '重置密码失败，请重试');
      }
    } catch (error) {
      setErrorMessage(error.message || '重置密码请求失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="w-full flex flex-col items-center">
      <div className="w-full bg-card shadow-lg border border-border/20 rounded-xl">
        <div className="text-center pt-8 pb-4 sm:pt-6 sm:pb-4">
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
            找回密码
          </h1>
        </div>

        <div className="space-y-6 px-6 sm:px-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="forgot_phone" className="text-sm font-medium text-muted-foreground">手机号码</label>
              <div className="relative">
                <Smartphone className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <input
                  id="forgot_phone"
                  type="tel"
                  placeholder="请输入您的手机号"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  required
                  className="pl-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="forgot_verifyCode" className="text-sm font-medium text-muted-foreground">短信验证码</label>
              <div className="flex space-x-2">
                <div className="relative flex-grow">
                  <KeyRound className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <input
                    id="forgot_verifyCode"
                    type="text"
                    placeholder="请输入6位验证码"
                    value={verifyCode}
                    onChange={(e) => setVerifyCode(e.target.value)}
                    required
                    className="pl-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  />
                </div>
                <button
                  type="button"
                  onClick={handleSendCode}
                  disabled={sendingCode || countdown > 0}
                  className="shrink-0 h-10 sm:h-11 border-primary text-primary hover:bg-primary/10 disabled:opacity-70 rounded-md border px-3 py-2 text-sm font-medium"
                >
                  {sendingCode ? <Loader2 className="h-4 w-4 animate-spin"/> : (countdown > 0 ? `${countdown}秒` : '获取验证码')}
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="forgot_password" className="text-sm font-medium text-muted-foreground">新密码</label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <input
                  id="forgot_password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="请输入新密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="pl-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                />
              </div>
              {password && password.length > 0 && !/^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/.test(password) && (
                <p className="text-xs text-red-500">密码必须包含字母和数字，长度必须在8-32个字符之间</p>
              )}
            </div>

            <div className="space-y-2">
              <label htmlFor="forgot_confirmPassword" className="text-sm font-medium text-muted-foreground">确认新密码</label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <input
                  id="forgot_confirmPassword"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="请再次输入新密码"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  className="pl-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                />
              </div>
              {confirmPassword && confirmPassword.length > 0 && password !== confirmPassword && (
                <p className="text-xs text-red-500">两次输入的密码不一致</p>
              )}
            </div>
            
            {errorMessage && (
              <div className="text-sm text-red-500 text-center">{errorMessage}</div>
            )}
            
            <button 
              type="submit" 
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90 text-base py-2.5 sm:py-3 h-auto shadow-md hover:shadow-lg transition-all duration-300 rounded-md flex items-center justify-center" 
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              ) : (
                <Lock className="mr-2 h-5 w-5" />
              )}
              {loading ? '提交中...' : '重置密码'}
            </button>
          </form>
        </div>

        <div className="text-center pb-8 sm:pb-10 px-6 sm:px-8 mt-6">
          <Link to="/auth" className="inline-flex items-center text-sm text-primary hover:underline hover:text-primary/80 font-medium">
            <ArrowLeft className="h-4 w-4 mr-1" /> 返回登录
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;

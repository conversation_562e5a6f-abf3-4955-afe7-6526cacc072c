import { message } from 'antd';

/**
 * 处理查看源代码功能
 * @param {Object} previewAreaRef - 预览区域的引用
 * @param {Function} setShowSourceModal - 设置显示源代码模态框的状态函数
 * @param {String} generatedHTML - 生成的HTML内容
 * @param {Object} originalResponse - 原始API响应
 */
export const handleViewSource = (previewAreaRef, setShowSourceModal, generatedHTML, originalResponse) => {
  try {
    // 先检查预览区域是否存在
    if (!previewAreaRef?.current) {
      message.warning('预览区域未加载完成，请稍后再试');
      return;
    }

    // 尝试从iframe中获取最新的HTML内容（可能有编辑）
    let htmlContent = generatedHTML;
    try {
      const iframeDoc = previewAreaRef.current.getIframeDocument?.();
      if (iframeDoc) {
        const contentContainer = iframeDoc.querySelector('.content-container');
        if (contentContainer) {
          // 如果存在内容容器，优先使用iframe中的内容（可能包含了用户的编辑）
          const wechatCover = contentContainer.querySelector('.wechat-cover');
          if (wechatCover) {
            htmlContent = wechatCover.outerHTML;
          }
        }
      }
    } catch (error) {
      // 如果获取失败，继续使用generatedHTML
    }

    // 检查是否有可查看的HTML内容
    if (!htmlContent && !originalResponse) {
      message.warning('暂无可查看的HTML内容，请先生成封面');
      return;
    }

    // 直接设置状态显示模态框
    setShowSourceModal(true);
  } catch (error) {
    message.error('查看源代码失败，请重试');
  }
};

/**
 * 格式化API响应数据，使其更易于阅读
 * @param {any} response - 原始响应数据
 * @returns {String} 格式化后的字符串
 */
export const formatApiResponse = (response) => {
  if (!response) {
    return '暂无原始响应';
  }

  try {
    if (typeof response === 'string') {
      // 尝试解析JSON字符串，使其格式化更美观
      try {
        const parsedJson = JSON.parse(response);
        return JSON.stringify(parsedJson, null, 2);
      } catch {
        // 如果无法解析为JSON，则直接返回原始字符串
        return response;
      }
    } else {
      // 对象转为格式化的JSON字符串
      return JSON.stringify(response, null, 2);
    }
  } catch (error) {
    return String(response) || '无法格式化的响应数据';
  }
};

/**
 * 提取和格式化HTML代码，使其更易于阅读
 * @param {String} html - 原始HTML字符串
 * @returns {String} 格式化后的HTML字符串
 */
export const formatHtmlCode = (html) => {
  if (!html) {
    return '暂无HTML代码';
  }

  try {
    // 简单的HTML格式化，在开始标签后和结束标签前添加换行和缩进
    // 这里使用一个简单的方法，实际中可能需要更复杂的HTML解析和格式化
    let formattedHtml = html;
    
    // 如果内容太长，可以对内容进行一些基本的格式整理
    formattedHtml = formattedHtml
      .replace(/></g, '>\n<')  // 在标签之间添加换行
      .replace(/(\s*<)(?!\/)([^>]*>)/g, '\n$1$2')  // 在开始标签前添加换行
      .replace(/(<\/[^>]*>)(\s*)</g, '$1\n$2');  // 在结束标签后添加换行

    return formattedHtml;
  } catch (error) {
    return html || '暂无HTML代码';
  }
}; 
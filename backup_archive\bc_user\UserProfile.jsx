import React, { useState, useEffect, useContext, useRef } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom'; // Use useSearchParams for tab state and useNavigate
import axios from 'axios';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

// Theme Context
import ThemeContext from '@/contexts/ThemeContext'; // Import ThemeContext

// Shadcn/ui components & Lucide icons
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogClose, DialogFooter, DialogDescription } from "@/components/ui/dialog"; // Replacing Modal and adding DialogFooter, DialogDescription
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"; // Replacing AntD Avatar
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"; // Replacing AntD Tooltip
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"; // For table pagination
import { User, CreditCard, History, Edit, Save, X, Image as ImageIcon, Eye, Trash2, Palette, MoreHorizontal, ExternalLink, RefreshCw, Briefcase, Wand2, Link as LinkIcon, Upload, Loader2, Lock, Camera, Smartphone, Mail, UserCircle2, CircleDollarSign, Award, Crown, LogOut } from 'lucide-react'; // Lucide Icons
import { cn, maskPhoneNumber } from "@/lib/utils"; // Import cn utility and maskPhoneNumber
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Add dropdown components
import WorkCard from '../myworks/WorkCard'; // 修正 WorkCard 的导入路径
// 导入正确的消息组件
import { message } from 'antd';
// 导入正确的模态框组件
import { Modal } from 'antd';
// 导入密码设置对话框组件
import PasswordDialog from './PasswordDialog';
import { useUserProfile } from '../../contexts/UserContext';

// Helper (already defined, keep it)
const classNames = (...classes) => classes.filter(Boolean).join(' ');

dayjs.locale('zh-cn');

// --- Placeholder components for missing shadcn/ui parts (add later if needed) ---
// We might need Tabs if we stick to tabbed layout on the right side, or just render conditionally
// const Tabs = ({ children }) => <div>{children}</div>; // Placeholder
// const TabsList = ({ children }) => <div>{children}</div>; // Placeholder
// const TabsTrigger = ({ children }) => <button>{children}</button>; // Placeholder
// const TabsContent = ({ children }) => <div>{children}</div>; // Placeholder
// Need a loading indicator, e.g., a simple spinner or skeleton
const LoadingSpinner = ({ className }) => <div className={cn("animate-spin rounded-full h-8 w-8 border-b-2 border-primary", className)}></div>;


const UserProfile = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const initialTab = searchParams.get('tab') || 'profile';
  const initialPage = parseInt(searchParams.get('page') || '1', 10);

  const [activeTab, setActiveTab] = useState(initialTab);
  // Access theme context
  const { theme, setTheme } = useContext(ThemeContext);

  const [loading, setLoading] = useState(false);
  const [userProfile, setUserProfile] = useState(null);
  const [editing, setEditing] = useState(false);
  const [editNickname, setEditNickname] = useState('');
  const [editPhone, setEditPhone] = useState('');
  const [editEmail, setEditEmail] = useState('');

  // 添加数据加载状态追踪
  const [profileDataLoaded, setProfileDataLoaded] = useState(false);
  const [pointsDataLoaded, setPointsDataLoaded] = useState(false);
  const [coversDataLoaded, setCoversDataLoaded] = useState(false);

  const [pointRecords, setPointRecords] = useState([]);
  const [pointsLoading, setPointsLoading] = useState(false);
  const [pointsCurrentPage, setPointsCurrentPage] = useState(initialPage);
  const [pointsTotalPages, setPointsTotalPages] = useState(0);

  const [covers, setCovers] = useState([]);
  const [coversLoading, setCoversLoading] = useState(false);
  const [coversCurrentPage, setCoversCurrentPage] = useState(initialPage);
  const [coversTotalPages, setCoversTotalPages] = useState(0);

  // Preview states (keep, but Dialog will replace Modal)
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false);
  const [currentPreviewRecordId, setCurrentPreviewRecordId] = useState(null);

  // -- START: States migrated from ChatSidebar for unified HTML preview --
  const [cs_isPreviewOpen, cs_setIsPreviewOpen] = useState(false);
  const [cs_currentCover, cs_setCurrentCover] = useState(null); // 用于存储从API获取的完整封面数据
  const [cs_previewHtml, cs_setPreviewHtml] = useState(''); // 用于存储预览用的HTML
  // -- END: States migrated from ChatSidebar --

  // 添加头像上传状态
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarUploading, setAvatarUploading] = useState(false);
  const fileInputRef = useRef(null);

  const navigate = useNavigate(); // Add useNavigate

  // 新增：引导升级弹窗状态
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);

  // Navigation items for the sidebar
  const profileNavItems = [
    { key: 'profile', label: '个人资料', icon: User, href: '/profile?tab=profile' },
    { key: 'points', label: '积分记录', icon: CreditCard, href: '/profile?tab=points' },
    { key: 'orders', label: '订单记录', icon: History, href: '/profile?tab=orders' },
    { key: 'covers', label: '封面记录', icon: ImageIcon, href: '/profile?tab=covers' },
    { key: 'my-creations', label: '我的作品', icon: Briefcase, href: '/profile?tab=my-creations' },
    // Placeholder for theme settings - will be implemented properly later
    // { key: 'theme-settings', label: '主题设置', icon: Palette, href: '/profile?tab=theme-settings' },
  ];

  // 操作类型映射表
  const operationTypeMap = {
    'register': '注册奖励',
    'daily_reward': '每日奖励',
    'generate': '生成封面',
    'admin_adjust': '管理员调整',
    'vip_upgrade': 'VIP升级',
    'daily_points_normal': '平台赠送',
    'daily_points_advanced': '会员赠送'
  };

  // 描述文本映射
  const descriptionMap = {
    '每日积分（普通）重置': '平台赠送积分',
    '每日积分（普通）手动更新': '平台赠送积分',
    '每日积分（高级）重置': '每日会员赠送',
    '每日积分（高级）手动更新': '每日会员赠送'
  };

  // 订单状态映射表
  const orderStatusMap = {
    'pending': '待支付',
    'success': '支付成功',
    'failed': '支付失败',
    'refunded': '已退款'
  };

  // 产品类型映射表
  const productTypeMap = {
    'vip': 'VIP会员',
    'points': '积分充值'
  };

  // 支付方式映射表
  const paymentTypeMap = {
    'wechat': '微信支付',
    'alipay': '支付宝'
  };

  // Add states for delete confirmation
  const [deletingCoverId, setDeletingCoverId] = useState(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 订单记录状态
  const [orders, setOrders] = useState([]);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [ordersCurrentPage, setOrdersCurrentPage] = useState(initialPage);
  const [ordersTotalPages, setOrdersTotalPages] = useState(0);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [isOrderDetailOpen, setIsOrderDetailOpen] = useState(false);

  // 新增分享链接对话框的状态
  const [shareUrl, setShareUrl] = useState('');
  const [shareLinkDialogOpen, setShareLinkDialogOpen] = useState(false);

  // 密码设置对话框状态
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);

  // 使用UserContext获取和更新用户资料
  const { 
    userProfile: contextUserProfile, 
    loading: profileLoading, 
    fetchUserProfile: contextFetchUserProfile, 
    updateUserProfile: contextUpdateUserProfile 
  } = useUserProfile();

  // 初始化编辑状态
  useEffect(() => {
    if (contextUserProfile) {
      setEditNickname(contextUserProfile.nickname || '');
      setEditPhone(contextUserProfile.phone || '');
      setEditEmail(contextUserProfile.email || '');
      setProfileDataLoaded(true);
    }
  }, [contextUserProfile]);

  // -- START: Functions migrated from ChatSidebar for unified HTML preview & edit --

  const cs_formatTitle = (title, maxLength = 30) => {
    if (!title) return '图片预览';
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength) + '...';
  };

  const cs_handleLoadWork = async (record) => {
    const coverCode = record?.cover_code;
    const coverId = record?.id;

    if (!coverCode && !coverId) {
      message.error('封面记录无效，无法查看');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      // 直接设置为null，显示加载状态
      cs_setPreviewHtml(null);
      cs_setCurrentCover(record);
      cs_setIsPreviewOpen(true);

      if (coverId) setCurrentPreviewRecordId(coverId);

      let response;
      if (coverCode) {
        response = await axios.get(`/api/cover/code/${coverCode}/edit`, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } else if (coverId) {
        response = await axios.get(`/api/cover/${coverId}/edit`, { 
          headers: { Authorization: `Bearer ${token}` }
        });
      }

      if (response?.data?.success) {
        const coverData = response.data.data;
        cs_setCurrentCover(coverData);
        const htmlContent = coverData.edited_html_content || coverData.html_content;
        cs_setPreviewHtml(htmlContent || '');
      } else {
        cs_setPreviewHtml(`<div style="color:#666; text-align:center; padding:20px;">
          <p>获取封面详情失败</p>
          <p>${response?.data?.message || '未知错误'}</p>
        </div>`);
      }
    } catch (error) {
      console.error('加载封面数据失败:', error);
      cs_setPreviewHtml(`<div style="color:#666; text-align:center; padding:20px;">
        <p>获取封面详情失败</p>
        <p>${error.message || '未知错误'}</p>
      </div>`);
    }
  };

  const cs_handleEditCover = () => {
    cs_setIsPreviewOpen(false); 

    if (cs_currentCover) {
      try {
        localStorage.setItem('pendingCoverData', JSON.stringify(cs_currentCover));
        message.success('正在准备编辑器...');
        
        // 优先使用 cover_code 进行跳转
        if (cs_currentCover.cover_code) {
          window.location.replace(`/chat-generate?mode=edit&code=${cs_currentCover.cover_code}`);
        } else if (cs_currentCover.id) {
          // 如果没有 cover_code，则使用 id
          window.location.replace(`/chat-generate?mode=edit&edit=${cs_currentCover.id}`);
        } else {
          message.error('封面数据无效，无法跳转到编辑器');
          return;
        }
      } catch (error) {
        console.error('存储或跳转到编辑器失败 (cs_handleEditCover):', error);
        message.error('加载编辑器失败，请稍后再试');
      }
    } else {
      message.error('没有可编辑的封面数据 (cs_handleEditCover)');
    }
  };

  const cs_handleShareCover = async () => {
    try {
      if (!cs_currentCover || !cs_currentCover.id) {
        message.error('无法获取封面信息，无法分享 (cs_handleShareCover)');
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      // 注意: ChatSidebar.jsx 中是直接用 currentCover.id 获取 cover_code
      // 但 currentCover 可能没有直接包含 cover_code，或者说即使有，也应该以API为准确保是最新的
      // 此处我们用 cs_currentCover.id 去查其对应的 cover_code，确保分享的是正确的 code
      const response = await axios.get(`/api/cover/${cs_currentCover.id}`, { // API 获取最新的 cover_code
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success && response.data.data) {
        const coverCode = response.data.data.cover_code;

        if (!coverCode) {
          message.error('此封面不支持分享');
          return;
        }
        
        const newShareUrl = `${window.location.origin}/share/${coverCode}`;

        await navigator.clipboard.writeText(newShareUrl);
        message.success('分享链接已复制到剪贴板');

        // 使用 UserProfile 已有的状态来控制分享对话框
        setShareUrl(newShareUrl); 
        setShareLinkDialogOpen(true); 
      } else {
        message.error(response.data.message || '获取封面信息失败 (cs_handleShareCover)');
      }
    } catch (error) {
      console.error('生成分享链接失败 (cs_handleShareCover):', error);
      message.error('生成分享链接失败，请重试');
    }
  };

  // -- END: Functions migrated from ChatSidebar --

  // --- Data Fetching Logic (largely unchanged, adjust pagination) ---
  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        return;
      }
      const response = await axios.get('/api/user/profile', {
        headers: { Authorization: `Bearer ${token}` }
      });
      if (response.data.success) {
        setUserProfile(response.data.data);
        // Initialize edit state when data loads
        setEditNickname(response.data.data.nickname);
        setEditPhone(response.data.data.phone);
        setEditEmail(response.data.data.email || '');
        setProfileDataLoaded(true); // 标记数据已加载
        
        // 获取每日积分信息
        fetchDailyPoints();
      }
    } catch (error) {
      console.error('获取个人资料失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取每日积分信息
  const fetchDailyPoints = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) { return; }
      
      const response = await axios.get('/api/user/daily-points', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        // 更新用户资料中的每日积分信息
        setUserProfile(prev => ({
          ...prev,
          daily_points: response.data.data.daily_points || 0,
          last_daily_points_update: response.data.data.last_update
        }));
      }
    } catch (error) {
      console.error('获取每日积分信息失败:', error);
    }
  };

  const fetchPointRecords = async (page = 1, limit = 10) => {
    try {
      setPointsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { return; }
      const response = await axios.get(`/api/user/point-records?page=${page}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      if (response.data.success) {
        setPointRecords(response.data.data);
        const total = response.data.pagination.total;
        const calculatedTotalPages = Math.ceil(total / limit);
        setPointsTotalPages(response.data.pagination.totalPages || calculatedTotalPages);
        setPointsCurrentPage(page);
        setPointsDataLoaded(true); // 标记数据已加载
      } else {
        setPointRecords([]);
        setPointsTotalPages(0);
        setPointsCurrentPage(1);
      }
    } catch (error) {
      console.error('获取积分记录失败:', error);
      setPointRecords([]);
      setPointsTotalPages(0);
      setPointsCurrentPage(1);
    } finally { setPointsLoading(false); }
  };

  const fetchCoverRecords = async (page = 1, limit = 12) => {
    try {
      setCoversLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { return; }
      const timestamp = new Date().getTime();
      const response = await axios.get(`/api/user/covers?page=${page}&limit=${limit}&_t=${timestamp}`, {
        headers: { 'Authorization': `Bearer ${token}`, 'Cache-Control': 'no-cache' }
      });
      if (response.data.success) {
        setCovers(response.data.data);
        const total = response.data.pagination.total;
        const calculatedTotalPages = Math.ceil(total / limit);
        setCoversTotalPages(response.data.pagination.totalPages || calculatedTotalPages);
        setCoversCurrentPage(page);
        setCoversDataLoaded(true); // 标记数据已加载
      } else {
        setCovers([]);
        setCoversTotalPages(0);
        setCoversCurrentPage(1);
      }
    } catch (error) {
      console.error('获取封面记录失败:', error);
      setCovers([]);
      setCoversTotalPages(0);
      setCoversCurrentPage(1);
    } finally { setCoversLoading(false); }
  };

  const fetchUserOrders = async (page = 1, limit = 10) => {
    try {
      setOrdersLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { return; }
      
      const response = await axios.get(`/api/payment/orders?page=${page}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        setOrders(response.data.data.orders);
        const total = response.data.pagination.total;
        const calculatedTotalPages = Math.ceil(total / limit);
        setOrdersTotalPages(response.data.pagination.total_pages || calculatedTotalPages);
        setOrdersCurrentPage(page);
      } else {
        setOrders([]);
        setOrdersTotalPages(0);
        setOrdersCurrentPage(1);
      }
    } catch (error) {
      console.error('获取订单记录失败:', error);
      setOrders([]);
      setOrdersTotalPages(0);
      setOrdersCurrentPage(1);
    } finally { 
      setOrdersLoading(false); 
    }
  };

  const handleViewOrderDetail = async (orderNo) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) { 
        message.error('未登录或会话已过期，请先登录');
        return; 
      }
      
      const response = await axios.get(`/api/payment/order/${orderNo}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        setCurrentOrder(response.data.data);
        setIsOrderDetailOpen(true);
      } else {
        message.error(response.data.message || '获取订单详情失败');
      }
    } catch (error) {
      console.error('获取订单详情失败:', error);
      message.error('获取订单详情失败，请稍后再试');
    }
  };

  useEffect(() => {
    const currentTab = searchParams.get('tab') || 'profile';
    const currentPage = parseInt(searchParams.get('page') || '1', 10);
    const forceRefresh = searchParams.get('refresh') === 'true';

    setActiveTab(currentTab);

    // 首次加载或强制刷新时加载用户资料 (针对所有标签页，而不仅仅是个人资料tab)
    if (!profileDataLoaded || forceRefresh) {
      fetchUserProfile();
    }

    // 针对不同标签，只在首次访问、强制刷新、或页码变化时加载数据
    if (currentTab === 'points') {
      setPointsCurrentPage(currentPage);
      if (!pointsDataLoaded || forceRefresh || currentPage !== pointsCurrentPage) {
        fetchPointRecords(currentPage);
      }
    } else if (currentTab === 'covers' || currentTab === 'my-creations') { // 修改：让 my-creations 也触发封面记录加载
      setCoversCurrentPage(currentPage);
      // 如果封面数据未加载、强制刷新或页码变化，则加载数据
      // 注意: coversDataLoaded 状态由两个tab共享，刷新一个会影响另一个，这是预期的，因为它们用同一份数据
      if (!coversDataLoaded || forceRefresh || currentPage !== coversCurrentPage) {
        fetchCoverRecords(currentPage);
      }
    } else if (currentTab === 'orders') {
      fetchUserOrders(currentPage);
    }

    // 如果URL中有refresh参数，移除它，避免持续刷新
    if (forceRefresh) {
      const newParams = new URLSearchParams(searchParams);
      newParams.delete('refresh');
      setSearchParams(newParams, { replace: true }); // 使用 replace: true 避免增加历史记录
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams, profileDataLoaded, pointsDataLoaded, coversDataLoaded]); // 将 dataLoaded 状态也加入依赖，确保刷新后能正确重置和触发加载

  // --- Event Handlers (update signatures/logic for new components) ---
  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const values = { nickname: editNickname, email: editEmail };

      if (!values.nickname) {
        message.error('昵称不能为空');
        setLoading(false);
        return;
      }

      // 使用Context的updateUserProfile方法
      const success = await contextUpdateUserProfile(values);

      if (success) {
        setEditing(false);
        message.success('个人资料更新成功');
      } else {
        message.error('更新个人资料失败');
      }
    } catch (error) {
      console.error('更新个人资料失败:', error);
      message.error('更新个人资料失败，请检查网络或联系管理员');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setEditing(false);
    // Reset edit state to original values
    setEditNickname(userProfile?.nickname || '');
    setEditPhone(userProfile?.phone || '');
    setEditEmail(userProfile?.email || ''); // Reset editEmail
  };

  const handlePreviewImage = (imageUrl) => {
    setPreviewImageUrl(imageUrl);
    setIsImagePreviewOpen(true);
    setCurrentPreviewRecordId(null); // Reset/ensure no record ID for simple image preview
  };

  // 处理头像选择
  const handleAvatarSelect = (e) => {
    if (e.target.files && e.target.files[0]) {
      setAvatarFile(e.target.files[0]);
      handleAvatarUpload(e.target.files[0]);
    }
  };

  // 处理头像上传
  const handleAvatarUpload = async (file) => {
    if (!file) return;

    setAvatarUploading(true);
    const formData = new FormData();
    formData.append('avatar', file);

    try {
      const token = localStorage.getItem('token');
      const response = await axios.post('/api/user/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${token}`
        }
      });

      if (response.data.success) {
        // 更新用户头像
        setUserProfile({
          ...userProfile,
          avatar: response.data.data.avatar
        });

        // 更新本地存储的用户信息
        const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
        userInfo.avatar = response.data.data.avatar;
        localStorage.setItem('user', JSON.stringify(userInfo));

        message.success('头像上传成功');
      }
    } catch (error) {
      console.error('上传头像失败:', error);
      message.error('上传头像失败，请重试');
    } finally {
      setAvatarUploading(false);
    }
  };

  const handleDeleteCover = async (coverId) => {
    try {
      setDeleteLoading(true);
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await axios.put(`/api/user/covers/${coverId}/hide`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        // 成功隐藏记录后更新列表，但保持标记数据为已加载状态
        fetchCoverRecords(coversCurrentPage);
        setIsDeleteConfirmOpen(false);
        setDeletingCoverId(null);
      }
    } catch (error) {
      console.error('隐藏封面记录失败:', error);
    } finally {
      setDeleteLoading(false);
    }
  };

  const openDeleteConfirm = (coverId) => {
    setDeletingCoverId(coverId);
    setIsDeleteConfirmOpen(true);
  };

  const handlePageChange = (newPage, type, e) => {
    if (e) e.preventDefault(); // 阻止默认行为，防止链接跳转
    const newParams = new URLSearchParams(searchParams);
    newParams.set('tab', type);
    newParams.set('page', newPage.toString());
    setSearchParams(newParams);
  };

  // 添加手动刷新数据的函数
  const handleRefreshData = () => {
    fetchUserProfile();
    if (activeTab === 'points') fetchPointRecords();
    if (activeTab === 'covers') fetchCoverRecords();
    if (activeTab === 'my-creations') fetchMyCoverBases();
    // 刷新每日积分信息
    fetchDailyPoints();
  }
  
  // 退出登录处理函数
  const handleLogout = () => {
    try {
      // 清除本地存储的用户信息和登录凭证
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // 触发自定义事件，通知组件用户已退出登录
      window.dispatchEvent(new CustomEvent('userLogout'));
      
      // 显示成功提示
      message.success('退出登录成功');
      
      // 跳转到登录页面
      navigate('/login');
    } catch (error) {
      console.error('退出登录失败:', error);
      message.error('退出登录失败，请重试');
    }
  };

  // Helper function to generate page numbers for pagination
  const generatePageNumbers = (currentPage, totalPages, pageNeighbours = 1) => {
    const totalNumbers = (pageNeighbours * 2) + 3; // pageNeighbours on each side + currentPage + firstPage + lastPage
    const totalBlocks = totalNumbers + 2; // Including ellipsis

    if (totalPages <= totalBlocks) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const pages = [];
    const leftBound = Math.max(2, currentPage - pageNeighbours);
    const rightBound = Math.min(totalPages - 1, currentPage + pageNeighbours);

    pages.push(1); // Always show the first page

    if (leftBound > 2) {
      pages.push('...'); // Ellipsis before the main block
    }

    for (let i = leftBound; i <= rightBound; i++) {
      pages.push(i);
    }

    if (rightBound < totalPages - 1) {
      pages.push('...'); // Ellipsis after the main block
    }

    pages.push(totalPages); // Always show the last page

    // Remove duplicate ellipsis if bounds are too close to start/end
    // e.g. [1, '...', 2, 3, 4, '...', 5] should be [1, 2, 3, 4, 5]
    // or [1, '...', 2, 3, '...', 6] where current is 2
    if (pages.length >= 3 && pages[1] === '...' && pages[2] === 2) {
        pages.splice(1, 1);
    }
    if (pages.length >= 3 && pages[pages.length - 2] === '...' && pages[pages.length - 3] === totalPages - 1) {
        pages.splice(pages.length - 2, 1);
    }


    return pages;
  };

  // 处理分享链接功能
  const handleShareCover = async (recordId) => {
    try {
      if (!recordId) {
        return;
      }

      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      // 获取封面记录以获取cover_code
      const response = await axios.get(`/api/cover/${recordId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success && response.data.data) {
        const coverCode = response.data.data.cover_code;

        if (!coverCode) {
          message.error('此封面不支持分享');
          return;
        }

        // 生成分享链接
        const shareUrl = `${window.location.origin}/share/${coverCode}`;

        // 复制链接到剪贴板
        await navigator.clipboard.writeText(shareUrl);
        message.success('分享链接已复制到剪贴板');

        // 显示分享链接对话框 - 使用shadcn/ui的Dialog组件
        setShareLinkDialogOpen(true);
        setShareUrl(shareUrl);
      } else {
        message.error(response.data.message || '获取封面信息失败');
      }
    } catch (error) {
      console.error('生成分享链接失败:', error);
      message.error('生成分享链接失败，请重试');
    }
  };

  // --- Rendering Logic ---
  if (loading && !userProfile) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner />
        </div>
    );
  }

  if (!userProfile) {
    return <div className="p-6 text-center text-destructive">无法加载用户资料。</div>;
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'profile':
  return (
          <Card className="bg-card text-card-foreground shadow-lg w-full">
            <CardHeader className="pb-5 flex flex-row items-center justify-between">
                <div>
                <CardTitle className="text-2xl font-semibold text-foreground">个人资料</CardTitle>
                  <CardDescription>管理您的账户信息</CardDescription>
                </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleRefreshData}
                  className="h-8 w-8 hover:bg-accent/80"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span className="sr-only">刷新</span>
                </Button>
                {!editing && userProfile && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditing(true)}
                      className="text-muted-foreground border-border hover:bg-accent/50 hover:text-accent-foreground"
                    >
                      <Edit className="mr-2 h-4 w-4" /> 编辑资料
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPasswordDialogOpen(true)}
                      className="text-muted-foreground border-border hover:bg-accent/50 hover:text-accent-foreground ml-2"
                    >
                      <Lock className="mr-2 h-4 w-4" /> 修改密码
                    </Button>
                  </>
                )}
              </div>
            </CardHeader>

            <CardContent className="pt-6 space-y-8">
              <div className="flex flex-col items-center space-y-2">
                <div
                  className="relative group cursor-pointer"
                  onClick={() => !avatarUploading && fileInputRef.current && fileInputRef.current.click()}
                >
                  <Avatar className="h-28 w-28 text-4xl">
                    <AvatarImage src={userProfile.avatar || ''} alt={userProfile.nickname} />
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {editing ? (editNickname ? editNickname[0].toUpperCase() : (userProfile.nickname ? userProfile.nickname[0].toUpperCase() : 'U')) : (userProfile.nickname ? userProfile.nickname[0].toUpperCase() : 'U')}
                        </AvatarFallback>
                      </Avatar>
                  {userProfile.is_vip && (
                    <Crown
                      className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-7 w-7 text-yellow-500 fill-yellow-400 bg-background p-0.5 rounded-full shadow-md"
                    />
                  )}
                  {!avatarUploading && (
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 flex flex-col items-center justify-center transition-opacity duration-300 rounded-full">
                      <Camera className="w-7 h-7 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <span className="text-xs text-white mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        更换头像
                      </span>
                    </div>
                  )}
                  {avatarUploading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-background/80 rounded-full">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    </div>
                  )}
                </div>
                        <input
                          id="avatar-upload"
                          type="file"
                  ref={fileInputRef}
                          accept="image/*"
                          className="hidden"
                          onChange={handleAvatarSelect}
                  disabled={avatarUploading || (editing && loading)}
                />
                {editing && (
                    <div className="text-center">
                        <h3 className="text-xl font-medium text-foreground">{editNickname || userProfile.nickname}</h3>
                        </div>
                      )}
                    </div>

              <Separator className="bg-border" />

              {!editing ? (
                <div className="space-y-6">
                  {[
                    { label: "昵称", value: userProfile.nickname, icon: UserCircle2 },
                    { label: "手机号", value: userProfile.phone ? maskPhoneNumber(userProfile.phone) : '未设置', icon: Smartphone },
                    { label: "邮箱", value: userProfile.email || '未设置', icon: Mail },
                    { label: "可用积分", value: <span className="font-semibold text-primary">{userProfile.points}</span>, icon: CircleDollarSign },
                    { label: "每日积分", value: <span className="font-semibold text-green-500">{userProfile.daily_points || 0}</span>, icon: Award },
                    { label: "会员状态", value: userProfile.is_vip ? `高级会员 (到期时间: ${dayjs(userProfile.vip_expire_date).format('YYYY-MM-DD')})` : '普通会员', icon: Award },
                  ].map((item, index) => (
                    <div key={index} className="grid grid-cols-3 md:grid-cols-4 gap-4 items-center">
                      <div className="flex items-center space-x-3 col-span-1 md:col-span-1">
                        {item.icon && React.createElement(item.icon, {
                          className: cn(
                            "h-5 w-5 text-muted-foreground flex-shrink-0",
                            item.label === "会员状态" && userProfile.is_vip && "text-yellow-500",
                            item.label === "每日积分" && "text-green-500"
                          )
                        })}
                        <Label className="text-base text-muted-foreground text-left">{item.label}</Label>
                    </div>
                      <div className="text-base text-foreground col-span-2 md:col-span-3">{item.value}</div>
                  </div>
                  ))}
                  <div className="flex justify-start pt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleLogout}
                      className="text-muted-foreground hover:bg-destructive/10 hover:text-destructive"
                    >
                      <LogOut className="mr-2 h-4 w-4" /> 退出登录
                    </Button>
                        </div>
                    </div>
              ) : (
                <form onSubmit={handleUpdateProfile} className="space-y-6">
                  {[
                    { id: "nickname", label: "昵称", value: editNickname, onChange: (e) => setEditNickname(e.target.value), type: "text", disabled: loading, icon: UserCircle2 },
                    { id: "email", label: "邮箱", value: editEmail, onChange: (e) => setEditEmail(e.target.value), type: "email", disabled: loading, icon: Mail },
                    { id: "phone", label: "手机号", value: editPhone, onChange: () => {}, type: "tel", disabled: true, icon: Smartphone },
                  ].map((item) => (
                    <div key={item.id} className="grid grid-cols-3 md:grid-cols-4 gap-4 items-center">
                      <div className="flex items-center space-x-3 col-span-1 md:col-span-1">
                        {item.icon && <item.icon className="h-5 w-5 text-muted-foreground flex-shrink-0" />}
                        <Label htmlFor={item.id} className="text-base text-muted-foreground text-left">{item.label}</Label>
                    </div>
                                <Input
                        id={item.id}
                        type={item.type}
                        value={item.value}
                        onChange={item.onChange}
                        disabled={item.disabled}
                        className="col-span-2 md:col-span-3 focus:ring-primary focus:border-primary text-base"
                    />
                  </div>
                  ))}
                  <div className="grid grid-cols-3 md:grid-cols-4 gap-4 items-center">
                      <div className="flex items-center space-x-3 col-span-1 md:col-span-1">
                        <CircleDollarSign className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                        <Label className="text-base text-muted-foreground text-left">可用积分</Label>
                        </div>
                      <div className="text-base text-foreground col-span-2 md:col-span-3"><span className="font-semibold text-primary">{userProfile.points}</span></div>
                  </div>
                  <div className="grid grid-cols-3 md:grid-cols-4 gap-4 items-center">
                      <div className="flex items-center space-x-3 col-span-1 md:col-span-1">
                        <Award className="h-5 w-5 text-green-500 flex-shrink-0" />
                        <Label className="text-base text-muted-foreground text-left">每日积分</Label>
                        </div>
                      <div className="text-base text-foreground col-span-2 md:col-span-3"><span className="font-semibold text-green-500">{userProfile.daily_points || 0}</span></div>
                  </div>
                  <div className="grid grid-cols-3 md:grid-cols-4 gap-4 items-center">
                      <div className="flex items-center space-x-3 col-span-1 md:col-span-1">
                        <Award className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                        <Label className="text-base text-muted-foreground text-left">会员状态</Label>
                        </div>
                      <div className="text-base text-foreground col-span-2 md:col-span-3">{userProfile.is_vip ? `高级会员 (到期时间: ${dayjs(userProfile.vip_expire_date).format('YYYY-MM-DD')})` : '普通会员'}</div>
                    </div>
                   <CardFooter className="px-0 pt-6 flex justify-end space-x-3">
                    <Button type="button" variant="outline" onClick={handleCancelEdit} disabled={loading || avatarUploading} className="hover:border-muted-foreground">
                    <X className="mr-2 h-4 w-4" /> 取消
                  </Button>
                    <Button type="submit" className="bg-gradient-primary text-primary-foreground hover:opacity-90 min-w-[100px]" disabled={loading || avatarUploading}>
                      { (loading || avatarUploading) ?
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> :
                          <Save className="mr-2 h-4 w-4" />
                      }
                      { (loading || avatarUploading) ? '处理中...' : '保存更改' }
                  </Button>
                </CardFooter>
                </form>
            )}
            </CardContent>
          </Card>
        );
      case 'points':
        return (
          <Card className="bg-card text-card-foreground shadow-lg">
            <CardHeader className="pb-5">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-2xl font-semibold text-card-foreground">积分记录</CardTitle>
                  <CardDescription>查看您的积分获取与使用情况</CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefreshData}
                  className="h-8 w-8 p-0 hover:bg-accent/50"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span className="sr-only">刷新</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {pointsLoading ? (
                <div className="flex justify-center items-center h-32"><LoadingSpinner /></div>
              ) : pointRecords.length > 0 ? (
                <>
                  <Table className="border-border">
                    <TableHeader>
                      <TableRow className="border-border hover:bg-muted/50">
                        <TableHead className="px-3 py-3.5 text-muted-foreground min-w-[60px] text-center">序号</TableHead>
                        <TableHead className="px-3 py-3.5 text-muted-foreground text-center">日期</TableHead>
                        <TableHead className="px-3 py-3.5 text-muted-foreground text-center">类型</TableHead>
                        <TableHead className="px-3 py-3.5 text-muted-foreground text-center">积分变化</TableHead>
                        <TableHead className="px-3 py-3.5 text-muted-foreground text-center">描述</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {pointRecords.map((record, idx) => (
                        <TableRow key={record.id} className="border-border hover:bg-muted/50 h-14">
                          <TableCell className="px-3 py-3 text-center">{(pointsCurrentPage - 1) * 10 + idx + 1}</TableCell>
                          <TableCell className="px-3 py-3 text-center">{dayjs(record.operation_time || record.created_at).format('YYYY-MM-DD HH:mm')}</TableCell>
                          <TableCell className="px-3 py-3 text-center">{operationTypeMap[record.operation_type] || record.operation_type || '未知类型'}</TableCell>
                          <TableCell className={cn(
                            "px-3 py-3 font-medium text-center",
                            record.points_change > 0 ? 'text-green-600' : record.points_change < 0 ? 'text-red-600' : 'text-muted-foreground'
                            )}>
                            {record.points_change != null ? (record.points_change > 0 ? `+${record.points_change}` : record.points_change) : 'N/A'}
                          </TableCell>
                          <TableCell className="px-3 py-3 text-center">{record.description ? descriptionMap[record.description] || record.description : '-'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {pointsTotalPages > 1 && (
                    <Pagination className="mt-6">
                      <PaginationContent>
                        <PaginationPrevious
                          onClick={(e) => handlePageChange(Math.max(1, pointsCurrentPage - 1), 'points', e)}
                          className={pointsCurrentPage === 1 ? "pointer-events-none opacity-50" : undefined}
                        />
                        {generatePageNumbers(pointsCurrentPage, pointsTotalPages).map((page, index) =>
                          page === '...' ? (
                            <PaginationEllipsis key={`${page}-${index}`} />
                          ) : (
                            <PaginationLink
                              key={page}
                              isActive={pointsCurrentPage === page}
                              onClick={(e) => handlePageChange(page, 'points', e)}
                              className={cn(
                                pointsCurrentPage === page && "bg-gradient-primary text-primary-foreground hover:opacity-90",
                                pointsCurrentPage !== page && "hover:bg-accent/20"
                              )}
                            >
                              {page}
                            </PaginationLink>
                          )
                        )}
                        <PaginationNext
                          onClick={(e) => handlePageChange(Math.min(pointsTotalPages, pointsCurrentPage + 1), 'points', e)}
                          className={pointsCurrentPage === pointsTotalPages ? "pointer-events-none opacity-50" : undefined}
                        />
                      </PaginationContent>
                    </Pagination>
                  )}
                </>
              ) : (
                <p className="text-center text-muted-foreground py-8">暂无积分记录。</p>
              )}
            </CardContent>
          </Card>
        );
      case 'covers':
        return (
          <Card className="bg-card text-card-foreground shadow-lg">
            <CardHeader className="pb-5">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-2xl font-semibold text-card-foreground">封面记录</CardTitle>
                  <CardDescription>查看和管理您生成的封面</CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefreshData}
                  className="h-8 w-8 p-0 hover:bg-accent/50"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span className="sr-only">刷新</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {coversLoading ? (
                <div className="flex justify-center items-center h-32"><LoadingSpinner /></div>
              ) : covers.length > 0 ? (
                <>
                  <Table className="border-border">
                    <TableHeader>
                      <TableRow className="border-border hover:bg-muted/50">
                        <TableHead className="px-3 py-3.5 text-muted-foreground min-w-[60px] text-center">序号</TableHead>
                        <TableHead className="px-3 py-3.5 text-muted-foreground text-center min-w-[150px]">封面编码</TableHead>
                        <TableHead className="px-3 py-3.5 text-muted-foreground text-center min-w-[100px]">类型</TableHead>
                        <TableHead className="px-3 py-3.5 text-muted-foreground text-center min-w-[120px]">风格</TableHead>
                        <TableHead className="px-3 py-3.5 text-muted-foreground text-center min-w-[160px]">生成时间</TableHead>
                        <TableHead className="px-3 py-3.5 text-muted-foreground text-center min-w-[180px]">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {covers.map((record, idx) => (
                        <TableRow key={record.id} className="border-border hover:bg-muted/50 h-14">
                          <TableCell className="px-3 py-3 text-center">{(coversCurrentPage - 1) * 12 + idx + 1}</TableCell>
                          <TableCell className="px-3 py-3 text-center">{record.cover_code}</TableCell>
                          <TableCell className="px-3 py-3 text-center">{record.cover_type_display || record.cover_type}</TableCell>
                          <TableCell className="px-3 py-3 text-center">{record.style_display_name || record.cover_style}</TableCell>
                          <TableCell className="px-3 py-3 text-center">{dayjs(record.created_at).format('YYYY-MM-DD HH:mm')}</TableCell>
                          <TableCell className="px-3 py-3 space-x-1 whitespace-nowrap text-center">
                            <div className="flex items-center justify-center space-x-2">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => cs_handleLoadWork(record)}
                                      className="w-9 h-9 p-0 hover:bg-accent/50"
                                    >
                                      <Eye className="h-4 w-4 text-sky-600" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>预览和操作</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>

                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => cs_handleLoadWork(record)}
                                      className="w-9 h-9 p-0 hover:bg-accent/50"
                                    >
                                      <Edit className="h-4 w-4 text-amber-600" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>编辑 (通过预览)</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>

                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => openDeleteConfirm(record.id)}
                                      className="w-9 h-9 p-0 hover:bg-accent/50"
                                    >
                                      <Trash2 className="h-4 w-4 text-destructive" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>删除封面</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  {coversTotalPages > 1 && (
                   <Pagination className="mt-6">
                      <PaginationContent>
                        <PaginationPrevious
                          onClick={(e) => handlePageChange(Math.max(1, coversCurrentPage - 1), 'covers', e)}
                          className={coversCurrentPage === 1 ? "pointer-events-none opacity-50" : undefined}
                        />
                        {generatePageNumbers(coversCurrentPage, coversTotalPages).map((page, index) =>
                          page === '...' ? (
                            <PaginationEllipsis key={`${page}-${index}`} />
                          ) : (
                            <PaginationLink
                              key={page}
                              isActive={coversCurrentPage === page}
                              onClick={(e) => handlePageChange(page, 'covers', e)}
                              className={cn(
                                coversCurrentPage === page && "bg-gradient-primary text-primary-foreground hover:opacity-90",
                                coversCurrentPage !== page && "hover:bg-accent/20"
                              )}
                            >
                              {page}
                            </PaginationLink>
                          )
                        )}
                        <PaginationNext
                          onClick={(e) => handlePageChange(Math.min(coversTotalPages, coversCurrentPage + 1), 'covers', e)}
                          className={coversCurrentPage === coversTotalPages ? "pointer-events-none opacity-50" : undefined}
                        />
                      </PaginationContent>
                    </Pagination>
                  )}
                </>
              ) : (
                <p className="text-center text-muted-foreground py-8">暂无封面记录。</p>
              )}
            </CardContent>
          </Card>
        );
      case 'orders':
        return (
          <Card className="bg-card text-card-foreground shadow-lg">
            <CardHeader className="pb-5">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-2xl font-semibold text-card-foreground">订单记录</CardTitle>
                  <CardDescription>查看您的订单历史和支付记录</CardDescription>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => fetchUserOrders(ordersCurrentPage)}
                  className="h-8 w-8 hover:bg-accent/80"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span className="sr-only">刷新</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {ordersLoading ? (
                <div className="flex justify-center items-center py-8">
                  <LoadingSpinner />
                </div>
              ) : orders.length > 0 ? (
                <>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>订单号</TableHead>
                          <TableHead>商品类型</TableHead>
                          <TableHead>金额(元)</TableHead>
                          <TableHead>支付方式</TableHead>
                          <TableHead>状态</TableHead>
                          <TableHead>创建时间</TableHead>
                          <TableHead>操作</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {orders.map((order) => (
                          <TableRow key={order.id}>
                            <TableCell className="font-medium">{order.order_no}</TableCell>
                            <TableCell>{productTypeMap[order.product_type] || order.product_type}</TableCell>
                            <TableCell>¥{parseFloat(order.amount).toFixed(2)}</TableCell>
                            <TableCell>{paymentTypeMap[order.payment_type] || order.payment_type}</TableCell>
                            <TableCell>
                              <span className={cn(
                                "px-2 py-1 rounded-full text-xs font-medium",
                                order.payment_status === 'success' && "bg-green-100 text-green-800",
                                order.payment_status === 'pending' && "bg-yellow-100 text-yellow-800",
                                order.payment_status === 'failed' && "bg-red-100 text-red-800",
                                order.payment_status === 'refunded' && "bg-gray-100 text-gray-800"
                              )}>
                                {orderStatusMap[order.payment_status] || order.payment_status}
                              </span>
                            </TableCell>
                            <TableCell>{formatDate(order.createdAt)}</TableCell>
                            <TableCell>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => handleViewOrderDetail(order.order_no)}
                              >
                                <Eye className="h-4 w-4" />
                                <span className="sr-only">查看详情</span>
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                  
                  {ordersTotalPages > 1 && (
                    <div className="flex justify-center mt-4">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious 
                              onClick={(e) => handlePageChange(ordersCurrentPage - 1, 'orders', e)}
                              className={ordersCurrentPage === 1 ? "pointer-events-none opacity-50" : ""}
                            />
                          </PaginationItem>
                          
                          {generatePageNumbers(ordersCurrentPage, ordersTotalPages).map((pageNum, index) => (
                            pageNum === '...' ? (
                              <PaginationItem key={`ellipsis-${index}`}>
                                <PaginationEllipsis />
                              </PaginationItem>
                            ) : (
                              <PaginationItem key={`page-${pageNum}`}>
                                <PaginationLink
                                  onClick={(e) => handlePageChange(pageNum, 'orders', e)}
                                  isActive={pageNum === ordersCurrentPage}
                                >
                                  {pageNum}
                                </PaginationLink>
                              </PaginationItem>
                            )
                          ))}
                          
                          <PaginationItem>
                            <PaginationNext 
                              onClick={(e) => handlePageChange(ordersCurrentPage + 1, 'orders', e)}
                              className={ordersCurrentPage === ordersTotalPages ? "pointer-events-none opacity-50" : ""}
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>暂无订单记录</p>
                </div>
              )}
            </CardContent>
          </Card>
        );
      case 'my-creations': // "我的作品"的简化渲染逻辑
        return (
          <Card className="bg-card text-card-foreground shadow-lg">
            <CardHeader className="pb-5">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-2xl font-semibold text-card-foreground">我的作品集</CardTitle>
                  <CardDescription>在这里浏览和欣赏您所有精彩的AI生成封面。</CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefreshData} // 复用刷新逻辑
                  className="h-9 w-9 p-0 hover:bg-accent/50"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span className="sr-only">刷新作品</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {coversLoading && (
                <div className="flex justify-center items-center py-10">
                  <LoadingSpinner /> <span className='ml-2 text-muted-foreground'>加载作品中...</span>
                </div>
              )}
              {!coversLoading && covers.length === 0 && (
                <div className="text-center py-12">
                  <Briefcase size={48} className="mx-auto text-muted-foreground/70 mb-4" />
                  <h3 className="text-xl font-semibold text-foreground mb-2">您的作品集还是空的</h3>
                  <p className="text-muted-foreground mb-6">立即去创作一些精美的AI封面，它们会在这里展示。</p>
                  <Button onClick={() => navigate('/generate')} className="bg-gradient-primary text-primary-foreground hover:opacity-90">
                    <Wand2 size={16} className="mr-2" />
                    去创作封面
                  </Button>
                </div>
              )}
              {!coversLoading && covers.length > 0 && (
                <>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
                    {covers.map((record) => (
                      <WorkCard
                        key={record.id}
                        record={record}
                        onClick={(clickedRecord) => {
                          cs_handleLoadWork(clickedRecord);
                        }}
                        isVip={userProfile && userProfile.is_vip}
                      />
                    ))}
                  </div>
                  {coversTotalPages > 1 && (
                    <div className="mt-8 flex justify-center">
                      <Pagination>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious
                              href="#"
                              onClick={(e) => handlePageChange(coversCurrentPage - 1, 'my-creations', e)}
                              disabled={coversCurrentPage === 1}
                              className={coversCurrentPage === 1 ? "pointer-events-none opacity-50" : ""}
                            />
                          </PaginationItem>
                          {generatePageNumbers(coversCurrentPage, coversTotalPages).map((page, index) => (
                            <PaginationItem key={index}>
                              {page === '...' ? (
                                <PaginationEllipsis />
                              ) : (
                                <PaginationLink
                                  href="#"
                                  onClick={(e) => handlePageChange(page, 'my-creations', e)}
                                  isActive={coversCurrentPage === page}
                                >
                                  {page}
                                </PaginationLink>
                              )}
                            </PaginationItem>
                          ))}
                          <PaginationItem>
                            <PaginationNext
                              href="#"
                              onClick={(e) => handlePageChange(coversCurrentPage + 1, 'my-creations', e)}
                              disabled={coversCurrentPage === coversTotalPages}
                              className={coversCurrentPage === coversTotalPages ? "pointer-events-none opacity-50" : ""}
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        );
      default:
        return <div>请选择一个选项</div>;
    }
  };

  return (
    // Main layout: flex container with sidebar and content area
    // Using theme-aware classes for background and text.
    <div className="flex flex-col md:flex-row gap-6 lg:gap-8 p-1 bg-background text-foreground min-h-[calc(100vh-var(--header-height,10rem))]">
      {/* Sidebar Navigation */}
      <aside className="md:w-64 lg:w-72 flex-shrink-0">
        <div className="sticky top-20 space-y-2 p-4 bg-card text-card-foreground rounded-lg shadow-sm">
          {profileNavItems.map((item) => {
            const IconComponent = item.icon;
            const isActive = activeTab === item.key;
            return (
              <Link
                key={item.key}
                to={`${item.href}${item.key === activeTab && searchParams.get('page') ? `&page=${searchParams.get('page')}` : ''}`}
                onClick={() => {
                  // Reset page to 1 when changing tabs, unless it's the same tab (e.g., for pagination)
                  if (item.key !== activeTab) {
                    const newParams = new URLSearchParams(searchParams);
                    newParams.set('tab', item.key);
                    newParams.set('page', '1'); // 重置页码为1
                    setSearchParams(newParams);
                  } else {
                    // If it's the same tab, ensure the URL reflects the current internal page state if needed, or let pagination handle it
                    // This part might need refinement if direct page setting on same tab click is desired
                  }
                }}
                className={cn(
                  "group flex items-center rounded-md px-3 py-2.5 text-sm font-medium transition-colors",
                  isActive
                    ? "bg-gradient-primary text-primary-foreground shadow-sm"
                    : "text-muted-foreground hover:bg-accent hover:text-accent-foreground hover:shadow-sm"
                )}
              >
                <IconComponent className={cn("mr-3 h-5 w-5 flex-shrink-0", isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-accent-foreground")} />
                {item.label}
              </Link>
            );
          })}
        </div>
      </aside>

      {/* Content Area */}
      <main className="flex-1 min-w-0">
        {renderContent()}
      </main>

      {/* Image Preview Dialog (already uses shadcn Dialog, ensure styling is theme-aware) */}
      {isImagePreviewOpen && (
        <Dialog open={isImagePreviewOpen} onOpenChange={setIsImagePreviewOpen}>
          <DialogContent className="max-w-3xl bg-card text-card-foreground p-0" hideDefaultCloseButton={true}>
            <DialogHeader className="p-4 border-b border-border flex-row items-center justify-between">
              <DialogTitle>图片预览</DialogTitle>
              <DialogClose asChild>
                <Button variant="ghost" className="h-8 w-8 p-0 text-muted-foreground hover:bg-accent/50 focus:ring-0 rounded-sm">
                  <X className="h-4 w-4" />
                  <span className="sr-only">关闭</span>
                </Button>
              </DialogClose>
            </DialogHeader>
            <div className="w-full p-4 flex justify-center items-center">
              <img
                src={previewImageUrl}
                alt="封面预览"
                className="w-full h-auto object-contain max-h-[80vh]"
                onError={(e) => {
                  e.target.src = 'https://placehold.co/600x400/eee/ccc?text=图片加载失败';
                }}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* 图片预览对话框 */}
      {previewImageUrl && (
        <Dialog open={!!previewImageUrl} onOpenChange={() => setPreviewImageUrl(null)}>
          <DialogContent className="max-w-3xl p-0">
            <div className="p-2">
              <img
                src={previewImageUrl}
                alt="预览图片"
                className="max-w-full max-h-[85vh] mx-auto"
                onError={(e) => {
                  e.target.src = 'https://placehold.co/600x400/eee/ccc?text=图片加载失败';
                }}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* -- Dialog migrated from ChatSidebar for unified HTML preview & edit -- */}
      <Dialog open={cs_isPreviewOpen} onOpenChange={cs_setIsPreviewOpen}>
        <DialogContent 
          className="bg-card text-card-foreground p-0 data-[state=open]:sm:rounded-lg"
          style={{
            maxWidth: '95vw',
            maxHeight: '90vh',
            width: '1200px',
            minWidth: '1100px',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: '#ffffff'
          }}
        >
          <DialogHeader className="p-4 border-b flex-shrink-0 flex-row items-center justify-between">
            <DialogTitle className="text-lg font-semibold text-foreground truncate max-w-md pr-4">
              {cs_formatTitle(cs_currentCover?.cover_text || '')}
            </DialogTitle>
            <DialogDescription className="sr-only">
              封面预览弹出层，显示封面的详细内容和编辑选项
            </DialogDescription>
          </DialogHeader>
          
          <div className="relative flex-1 p-0 flex justify-center items-center" style={{ minHeight: '600px' }}>
            {cs_previewHtml ? (
              <div className="w-full h-full flex items-center justify-center">
                <iframe
                  srcDoc={cs_previewHtml}
                  title="封面预览"
                  className="border-0"
                  style={{
                    width: '100%',
                    height: '100%',
                    margin: 'auto',
                    display: 'block'
                  }}
                  sandbox="allow-same-origin allow-scripts allow-forms" 
                  referrerPolicy="no-referrer"
                  loading="lazy"
                  onLoad={(e) => {
                    try {
                      const frame = e.target;
                      const frameDoc = frame.contentDocument || frame.contentWindow.document;
                      
                      if (frameDoc) {
                        // 添加样式到iframe内部
                        const style = frameDoc.createElement('style');
                        style.textContent = `
                          html, body {
                            margin: 0;
                            padding: 0;
                            overflow: visible !important;
                            height: 100% !important;
                          }
                          body {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            min-height: 100%;
                          }
                          body > * {
                            max-width: 100%;
                            margin: auto;
                          }
                        `;
                        frameDoc.head.appendChild(style);
                        
                        // 设置iframe高度适应内容
                        if (frameDoc.body) {
                          // 获取实际内容高度并设置iframe高度
                          const contentHeight = Math.max(600, frameDoc.body.scrollHeight);
                          frame.style.height = `${contentHeight}px`;
                        }
                      }
                    } catch (error) {
                      console.error('Frame adjustment error:', error);
                    }
                  }}
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="h-10 w-10 animate-spin text-primary" /> 
                <span className="ml-3 text-muted-foreground">加载中...</span>
              </div>
            )}
          </div>
          
          <div className="p-4 border-t flex justify-between items-center">
            <Button variant="outline" onClick={() => cs_setIsPreviewOpen(false)}>
              关闭
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={cs_handleShareCover}>
                <LinkIcon className="mr-2 h-4 w-4" />
                分享
              </Button>
              <Button onClick={cs_handleEditCover}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 添加删除确认对话框 */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent className="bg-card text-card-foreground sm:max-w-[425px]" hideDefaultCloseButton={true}>
          <DialogHeader className="flex flex-row justify-between items-center">
            <DialogTitle>确认删除</DialogTitle>
            <DialogClose asChild>
              <Button variant="ghost" className="h-8 w-8 p-0 text-muted-foreground hover:bg-accent/50 focus:ring-0 rounded-sm">
                <X className="h-4 w-4" />
                <span className="sr-only">关闭</span>
              </Button>
            </DialogClose>
          </DialogHeader>
          <DialogDescription className="text-center py-4 text-base text-muted-foreground pt-2">
            您确定要删除此封面记录吗？
          </DialogDescription>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteConfirmOpen(false)}
              disabled={deleteLoading}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleDeleteCover(deletingCoverId)}
              disabled={deleteLoading}
              className="ml-2"
            >
              {deleteLoading ? <LoadingSpinner className="h-4 w-4 border-destructive-foreground" /> : '确认删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加分享链接对话框 */}
      <Dialog open={shareLinkDialogOpen} onOpenChange={setShareLinkDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>分享链接已生成</DialogTitle>
            <DialogDescription>
              任何人都可以通过以下链接访问您的封面
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4 py-4">
            <textarea
              value={shareUrl}
              readOnly
              className="flex h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              rows={2}
            />
            <div className="text-sm text-muted-foreground">
              链接已复制到剪贴板，可直接分享给他人。
            </div>
          </div>
          <DialogFooter className="sm:justify-center">
            <Button type="button" variant="default" onClick={() => setShareLinkDialogOpen(false)}>
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 引导升级弹窗 */}
      <Dialog open={showUpgradeDialog} onOpenChange={setShowUpgradeDialog}>
        <DialogContent className="sm:max-w-md bg-card text-card-foreground">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-foreground">升级到高级会员</DialogTitle>
            <DialogDescription className="text-muted-foreground pt-2">
              此功能为高级会员专属。升级后即可畅享HTML下载、作品分享及自定义图片等全部特权！
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="pt-4">
            <Button variant="outline" onClick={() => setShowUpgradeDialog(false)} className="mr-2">
              稍后再说
            </Button>
            <Button onClick={() => {
              // navigate('/pricing'); // 跳转到会员升级页面 - 暂时注释，因为页面未开发
              setShowUpgradeDialog(false);
               message.info('将跳转到升级页面（开发中）');
            }}
            className="bg-gradient-primary text-primary-foreground hover:opacity-90"
            >
              立即升级
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 密码设置/修改对话框 */}
      <PasswordDialog
        open={passwordDialogOpen}
        onOpenChange={setPasswordDialogOpen}
        hasPassword={userProfile?.has_set_password}
        currentUserPhone={userProfile?.phone}
      />

      {/* 订单详情对话框 */}
      {isOrderDetailOpen && currentOrder && (
        <Dialog open={isOrderDetailOpen} onOpenChange={setIsOrderDetailOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>订单详情</DialogTitle>
              <DialogDescription>
                订单号: {currentOrder.order?.order_no}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">商品类型</p>
                  <p className="font-medium">{productTypeMap[currentOrder.order?.product_type] || currentOrder.order?.product_type}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">商品详情</p>
                  <p className="font-medium">{currentOrder.order?.product_detail || '-'}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">支付金额</p>
                  <p className="font-medium text-lg">¥{parseFloat(currentOrder.order?.amount).toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">支付方式</p>
                  <p className="font-medium">{paymentTypeMap[currentOrder.order?.payment_type] || currentOrder.order?.payment_type}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">支付状态</p>
                  <p className={cn(
                    "inline-block px-2 py-1 rounded-full text-xs font-medium",
                    currentOrder.order?.payment_status === 'success' && "bg-green-100 text-green-800",
                    currentOrder.order?.payment_status === 'pending' && "bg-yellow-100 text-yellow-800",
                    currentOrder.order?.payment_status === 'failed' && "bg-red-100 text-red-800",
                    currentOrder.order?.payment_status === 'refunded' && "bg-gray-100 text-gray-800"
                  )}>
                    {orderStatusMap[currentOrder.order?.payment_status] || currentOrder.order?.payment_status}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">创建时间</p>
                  <p className="font-medium">{formatDate(currentOrder.order?.createdAt)}</p>
                </div>
              </div>
              
              {currentOrder.packageInfo && (
                <div className="border rounded p-3 bg-accent/10">
                  <h4 className="font-medium mb-2">套餐信息</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <p className="text-muted-foreground">套餐名称</p>
                      <p>{currentOrder.packageInfo.name}</p>
                    </div>
                    {currentOrder.order?.product_type === 'vip' && (
                      <>
                        <div>
                          <p className="text-muted-foreground">有效期</p>
                          <p>{currentOrder.packageInfo.duration}天</p>
                        </div>
                      </>
                    )}
                    {currentOrder.order?.product_type === 'points' && (
                      <>
                        <div>
                          <p className="text-muted-foreground">积分数量</p>
                          <p>{currentOrder.packageInfo.points}点</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">赠送积分</p>
                          <p>{currentOrder.packageInfo.bonus_points || 0}点</p>
                        </div>
                      </>
                    )}
                    <div>
                      <p className="text-muted-foreground">价格</p>
                      <p>¥{parseFloat(currentOrder.packageInfo.price).toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              )}
              
              {currentOrder.order?.payment_status === 'pending' && currentOrder.paymentParams && (
                <div className="border rounded p-3 bg-accent/10">
                  <h4 className="font-medium mb-2">支付信息</h4>
                  <p className="text-sm text-muted-foreground mb-2">请扫描下方二维码完成支付</p>
                  <div className="flex justify-center">
                    <img 
                      src={currentOrder.paymentParams.qrCodeUrl} 
                      alt="支付二维码" 
                      className="w-40 h-40 border"
                    />
                  </div>
                  <p className="text-xs text-center mt-2 text-muted-foreground">
                    二维码有效期至: {formatDate(currentOrder.paymentParams.paymentExpireTime)}
                  </p>
                </div>
              )}
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsOrderDetailOpen(false)}>
                关闭
              </Button>
              {currentOrder.order?.payment_status === 'pending' && (
                <Button onClick={() => fetchUserOrders(ordersCurrentPage)}>
                  刷新订单状态
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default UserProfile;

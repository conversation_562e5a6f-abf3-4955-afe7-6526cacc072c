/**
 * 支付设置模块
 * 功能：管理支付接口配置，包括微信支付和支付宝支付
 */

// 模块变量
let paymentConfigs = [];
let currentEditId = null;

// DOM元素
const configElements = {
  wechatConfigForm: document.getElementById('wechatConfigForm'),
  alipayConfigForm: document.getElementById('alipayConfigForm'),
  saveWechatConfigBtn: document.getElementById('saveWechatConfigBtn'),
  saveAlipayConfigBtn: document.getElementById('saveAlipayConfigBtn'),
  wechatConfigStatus: document.getElementById('wechatConfigStatus'),
  alipayConfigStatus: document.getElementById('alipayConfigStatus')
};

/**
 * 初始化支付设置模块
 */
function init() {
  // 绑定事件
  if (configElements.saveWechatConfigBtn) {
    configElements.saveWechatConfigBtn.addEventListener('click', saveWechatConfig);
  }
  
  if (configElements.saveAlipayConfigBtn) {
    configElements.saveAlipayConfigBtn.addEventListener('click', saveAlipayConfig);
  }

  // 初始加载数据
  loadPaymentConfigs();

  // 监听DOM变化，当页面显示时重新加载数据
  observePageVisibility('payment-settings', () => {
    loadPaymentConfigs();
  });
}

/**
 * 加载支付配置
 */
async function loadPaymentConfigs() {
  try {
    showLoading();
    
    // 发送API请求
    const response = await fetch('/api/admin/payment/payment-configs', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      }
    });

    if (!response.ok) {
      throw new Error('加载支付配置失败');
    }

    const data = await response.json();
    
    if (data.success) {
      paymentConfigs = data.data.configs || [];
      
      // 更新UI
      updateConfigForms();
      updateConfigStatus();
    } else {
      showToast('error', data.message || '加载支付配置失败');
    }
  } catch (error) {
    console.error('加载支付配置失败:', error);
    showToast('error', '加载支付配置失败，请重试');
  } finally {
    hideLoading();
  }
}

/**
 * 更新配置表单
 */
function updateConfigForms() {
  // 微信支付配置
  const wechatConfigs = paymentConfigs.filter(config => config.payment_type === 'wechat');
  if (wechatConfigs.length > 0) {
    wechatConfigs.forEach(config => {
      const inputElement = document.getElementById(`wechat_${config.config_key}`);
      if (inputElement) {
        // 对于加密的配置，不显示实际值
        if (config.is_encrypted) {
          inputElement.value = config.config_value ? '**********' : '';
          inputElement.setAttribute('data-encrypted', 'true');
          inputElement.setAttribute('data-id', config.id);
          inputElement.setAttribute('placeholder', '留空表示不修改');
        } else {
          inputElement.value = config.config_value || '';
          inputElement.setAttribute('data-id', config.id);
        }
      }
    });
  }

  // 支付宝配置
  const alipayConfigs = paymentConfigs.filter(config => config.payment_type === 'alipay');
  if (alipayConfigs.length > 0) {
    alipayConfigs.forEach(config => {
      const inputElement = document.getElementById(`alipay_${config.config_key}`);
      if (inputElement) {
        // 对于加密的配置，不显示实际值
        if (config.is_encrypted) {
          inputElement.value = config.config_value ? '**********' : '';
          inputElement.setAttribute('data-encrypted', 'true');
          inputElement.setAttribute('data-id', config.id);
          inputElement.setAttribute('placeholder', '留空表示不修改');
        } else {
          inputElement.value = config.config_value || '';
          inputElement.setAttribute('data-id', config.id);
        }
      }
    });
  }
}

/**
 * 更新配置状态
 */
function updateConfigStatus() {
  // 检查微信支付配置是否完整
  const wechatConfigs = paymentConfigs.filter(config => config.payment_type === 'wechat');
  const wechatComplete = wechatConfigs.length > 0 && wechatConfigs.every(config => config.config_value);
  
  // 检查支付宝配置是否完整
  const alipayConfigs = paymentConfigs.filter(config => config.payment_type === 'alipay');
  const alipayComplete = alipayConfigs.length > 0 && alipayConfigs.every(config => config.config_value);
  
  // 更新状态显示
  if (configElements.wechatConfigStatus) {
    configElements.wechatConfigStatus.innerHTML = wechatComplete 
      ? '<span class="badge bg-success">已配置</span>' 
      : '<span class="badge bg-warning">未完成</span>';
  }
  
  if (configElements.alipayConfigStatus) {
    configElements.alipayConfigStatus.innerHTML = alipayComplete 
      ? '<span class="badge bg-success">已配置</span>' 
      : '<span class="badge bg-warning">未完成</span>';
  }
}

/**
 * 保存微信支付配置
 */
async function saveWechatConfig() {
  try {
    if (!configElements.wechatConfigForm) return;
    
    // 检查表单有效性
    if (!configElements.wechatConfigForm.checkValidity()) {
      configElements.wechatConfigForm.reportValidity();
      return;
    }
    
    showLoading();
    
    // 收集表单数据
    const formData = {};
    const formElements = configElements.wechatConfigForm.elements;
    
    for (let i = 0; i < formElements.length; i++) {
      const element = formElements[i];
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        const key = element.id.replace('wechat_', '');
        const isEncrypted = element.getAttribute('data-encrypted') === 'true';
        const id = element.getAttribute('data-id');
        
        // 对于加密字段，只有在值不是占位符且不为空时才发送
        if (isEncrypted) {
          if (element.value && element.value !== '**********') {
            formData[key] = {
              value: element.value,
              encrypted: true,
              id: id
            };
          }
        } else {
          formData[key] = {
            value: element.value,
            encrypted: false,
            id: id
          };
        }
      }
    }
    
    // 保存每个配置项
    const results = await Promise.all(
      Object.keys(formData).map(key => {
        const config = formData[key];
        
        if (config.id) {
          // 更新现有配置
          return updatePaymentConfig(config.id, {
            config_value: config.value,
            is_encrypted: config.encrypted
          });
        } else {
          // 创建新配置
          return createPaymentConfig({
            payment_type: 'wechat',
            config_key: key,
            config_name: getConfigName(key),
            config_value: config.value,
            is_encrypted: config.encrypted,
            is_active: true
          });
        }
      })
    );
    
    // 检查结果
    if (results.every(result => result)) {
      showToast('success', '微信支付配置已保存');
      loadPaymentConfigs();
    } else {
      showToast('error', '部分配置保存失败，请检查并重试');
    }
  } catch (error) {
    console.error('保存微信支付配置失败:', error);
    showToast('error', '保存微信支付配置失败，请重试');
  } finally {
    hideLoading();
  }
}

/**
 * 保存支付宝配置
 */
async function saveAlipayConfig() {
  try {
    if (!configElements.alipayConfigForm) return;
    
    // 检查表单有效性
    if (!configElements.alipayConfigForm.checkValidity()) {
      configElements.alipayConfigForm.reportValidity();
      return;
    }
    
    showLoading();
    
    // 收集表单数据
    const formData = {};
    const formElements = configElements.alipayConfigForm.elements;
    
    for (let i = 0; i < formElements.length; i++) {
      const element = formElements[i];
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        const key = element.id.replace('alipay_', '');
        const isEncrypted = element.getAttribute('data-encrypted') === 'true';
        const id = element.getAttribute('data-id');
        
        // 对于加密字段，只有在值不是占位符且不为空时才发送
        if (isEncrypted) {
          if (element.value && element.value !== '**********') {
            formData[key] = {
              value: element.value,
              encrypted: true,
              id: id
            };
          }
        } else {
          formData[key] = {
            value: element.value,
            encrypted: false,
            id: id
          };
        }
      }
    }
    
    // 保存每个配置项
    const results = await Promise.all(
      Object.keys(formData).map(key => {
        const config = formData[key];
        
        if (config.id) {
          // 更新现有配置
          return updatePaymentConfig(config.id, {
            config_value: config.value,
            is_encrypted: config.encrypted
          });
        } else {
          // 创建新配置
          return createPaymentConfig({
            payment_type: 'alipay',
            config_key: key,
            config_name: getConfigName(key),
            config_value: config.value,
            is_encrypted: config.encrypted,
            is_active: true
          });
        }
      })
    );
    
    // 检查结果
    if (results.every(result => result)) {
      showToast('success', '支付宝配置已保存');
      loadPaymentConfigs();
    } else {
      showToast('error', '部分配置保存失败，请检查并重试');
    }
  } catch (error) {
    console.error('保存支付宝配置失败:', error);
    showToast('error', '保存支付宝配置失败，请重试');
  } finally {
    hideLoading();
  }
}

/**
 * 更新支付配置
 * @param {string} id 配置ID
 * @param {Object} data 配置数据
 * @returns {boolean} 是否成功
 */
async function updatePaymentConfig(id, data) {
  try {
    const response = await fetch(`/api/admin/payment/payment-configs/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error('更新支付配置失败');
    }

    const responseData = await response.json();
    return responseData.success;
  } catch (error) {
    console.error('更新支付配置失败:', error);
    return false;
  }
}

/**
 * 创建支付配置
 * @param {Object} data 配置数据
 * @returns {boolean} 是否成功
 */
async function createPaymentConfig(data) {
  try {
    const response = await fetch('/api/admin/payment/payment-configs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error('创建支付配置失败');
    }

    const responseData = await response.json();
    return responseData.success;
  } catch (error) {
    console.error('创建支付配置失败:', error);
    return false;
  }
}

/**
 * 获取配置名称
 * @param {string} key 配置键
 * @returns {string} 配置名称
 */
function getConfigName(key) {
  const configNames = {
    // 微信支付
    'app_id': '应用ID',
    'mch_id': '商户号',
    'api_key': 'API密钥',
    'app_secret': '应用密钥',
    'notify_url': '回调地址',
    'cert_path': '证书路径',
    'key_path': '密钥路径',
    
    // 支付宝
    'app_id_alipay': '应用ID',
    'private_key': '应用私钥',
    'public_key': '支付宝公钥',
    'notify_url_alipay': '回调地址',
    'return_url': '返回地址',
    'gateway_url': '网关地址'
  };
  
  return configNames[key] || key;
}

// 辅助函数：获取Token
function getToken() {
  return localStorage.getItem('token') || '';
}

// 辅助函数：显示Loading
function showLoading() {
  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    loadingElement.style.display = 'flex';
  }
}

// 辅助函数：隐藏Loading
function hideLoading() {
  const loadingElement = document.getElementById('loading');
  if (loadingElement) {
    loadingElement.style.display = 'none';
  }
}

// 辅助函数：显示提示消息
function showToast(type, message) {
  if (window.utils && window.utils.showToast) {
    window.utils.showToast(type, message);
  } else {
    alert(message);
  }
}

// 辅助函数：监听页面可见性变化
function observePageVisibility(pageId, callback) {
  // 创建一个观察器实例
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const page = document.getElementById(pageId);
        if (page && !page.classList.contains('d-none')) {
          callback();
        }
      }
    });
  });

  // 配置观察选项
  const config = { attributes: true };

  // 开始观察目标节点
  const page = document.getElementById(pageId);
  if (page) {
    observer.observe(page, config);
  }
}

// 初始化模块
document.addEventListener('DOMContentLoaded', init);

// 导出模块
window.paymentSettingsModule = {
  loadPaymentConfigs,
  saveWechatConfig,
  saveAlipayConfig
}; 
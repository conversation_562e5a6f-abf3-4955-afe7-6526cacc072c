const logger = require('../utils/logger');

/**
 * 全局错误处理中间件
 * 统一处理应用中的错误，格式化错误响应
 */
const errorHandler = (err, req, res, next) => {
  // 获取错误状态码，默认为500
  const statusCode = err.statusCode || 500;
  
  // 记录错误日志
  logger.error(`${statusCode} - ${err.message} - ${req.originalUrl} - ${req.method} - ${req.ip}`);
  
  // 开发环境下返回完整错误信息，生产环境下隐藏错误详情
  const isDev = process.env.NODE_ENV === 'development';
  
  res.status(statusCode).json({
    success: false,
    message: err.message,
    stack: isDev ? err.stack : undefined,
    timestamp: new Date().toISOString()
  });
};

module.exports = errorHandler;

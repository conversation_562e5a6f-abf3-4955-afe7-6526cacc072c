-- 创建会员套餐表
CREATE TABLE IF NOT EXISTS `member_packages` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '套餐名称',
  `duration` int NOT NULL COMMENT '有效期(天)',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `discount_price` decimal(10,2) DEFAULT NULL COMMENT '折扣价格',
  `description` text COMMENT '套餐描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员套餐表';

-- 创建退款记录表
CREATE TABLE IF NOT EXISTS `refund_records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `payment_id` int NOT NULL COMMENT '关联支付记录ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `refund_no` varchar(50) NOT NULL COMMENT '退款单号',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `refund_status` enum('pending','success','failed') NOT NULL DEFAULT 'pending' COMMENT '退款状态',
  `transaction_id` varchar(100) DEFAULT NULL COMMENT '第三方交易ID',
  `refund_time` datetime DEFAULT NULL COMMENT '退款完成时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_refund_no` (`refund_no`),
  KEY `idx_payment_id` (`payment_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='退款记录表';

-- 创建支付安全日志表
CREATE TABLE IF NOT EXISTS `payment_security_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL COMMENT '用户ID',
  `order_no` varchar(50) DEFAULT NULL COMMENT '订单号',
  `ip_address` varchar(50) NOT NULL COMMENT 'IP地址',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `status` enum('success','failed','suspicious') NOT NULL COMMENT '状态',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `risk_level` enum('low','medium','high') DEFAULT 'low' COMMENT '风险等级',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='支付安全日志表';

-- 创建支付配置表
CREATE TABLE IF NOT EXISTS `payment_configs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `payment_type` varchar(20) NOT NULL COMMENT '支付类型(wechat/alipay)',
  `config_name` varchar(50) NOT NULL COMMENT '配置名称',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `is_encrypted` tinyint(1) DEFAULT '0' COMMENT '是否加密',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_payment_config` (`payment_type`,`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='支付配置表';

-- 创建订单状态日志表
CREATE TABLE IF NOT EXISTS `order_status_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `previous_status` varchar(20) DEFAULT NULL COMMENT '之前状态',
  `current_status` varchar(20) NOT NULL COMMENT '当前状态',
  `operator_id` int DEFAULT NULL COMMENT '操作者ID',
  `operator_type` enum('user','system','admin') NOT NULL DEFAULT 'system' COMMENT '操作者类型',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单状态日志表';

-- 创建会员事件记录表
CREATE TABLE IF NOT EXISTS `member_events` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户ID',
  `event_type` enum('upgrade','renew','downgrade','expire','benefit_use') NOT NULL COMMENT '事件类型',
  `description` varchar(255) DEFAULT NULL COMMENT '事件描述',
  `related_id` int DEFAULT NULL COMMENT '关联ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员事件记录表';

-- 创建会员权益表
CREATE TABLE IF NOT EXISTS `member_benefits` (
  `id` int NOT NULL AUTO_INCREMENT,
  `benefit_code` varchar(50) NOT NULL COMMENT '权益代码',
  `benefit_name` varchar(100) NOT NULL COMMENT '权益名称',
  `benefit_desc` text COMMENT '权益描述',
  `benefit_icon` varchar(255) DEFAULT NULL COMMENT '权益图标',
  `apply_role` enum('user','vip','admin') NOT NULL DEFAULT 'vip' COMMENT '适用角色',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_benefit_code` (`benefit_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员权益表'; 
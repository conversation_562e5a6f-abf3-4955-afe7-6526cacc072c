/**
 * HTML结构检测和处理工具
 * 用于检测HTML内容是否包含完整的HTML结构，并提取head和body内容
 * 主要解决背景图片无法显示的问题
 */

import logger from '../../../services/logs/frontendLogger';
import featureConfig, { isFeatureEnabled } from './featureConfig';
import { setupAdvancedIframeContent } from './advancedHtmlLoader';
import functionalPageProcessor, { detectPageType, processFunctionalPage, setupFunctionalPageEnvironment } from './functionalPageProcessor';

/**
 * 检测HTML内容是否包含完整的HTML结构
 * @param {string} htmlContent - HTML内容
 * @returns {boolean} - 是否包含完整的HTML结构
 */
export const isCompleteHtmlStructure = (htmlContent) => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return false;
  }

  // 检查是否包含<!DOCTYPE>、<html>、<head>和<body>标签
  const docTypeRegex = /<!DOCTYPE\s+html>/i;
  const htmlTagRegex = /<html[^>]*>/i;
  const headTagRegex = /<head[^>]*>/i;
  const bodyTagRegex = /<body[^>]*>/i;
  const headCloseTagRegex = /<\/head>/i;
  const bodyCloseTagRegex = /<\/body>/i;
  const htmlCloseTagRegex = /<\/html>/i;

  return (
    docTypeRegex.test(htmlContent) &&
    htmlTagRegex.test(htmlContent) &&
    headTagRegex.test(htmlContent) &&
    bodyTagRegex.test(htmlContent) &&
    headCloseTagRegex.test(htmlContent) &&
    bodyCloseTagRegex.test(htmlContent) &&
    htmlCloseTagRegex.test(htmlContent)
  );
};

/**
 * 从完整的HTML结构中提取head和body内容
 * @param {string} htmlContent - 完整的HTML内容
 * @returns {Object} - 包含head和body内容的对象
 */
export const extractHeadAndBodyContent = (htmlContent) => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return { 
      headContent: '', 
      bodyContent: '', 
      bodyAttributes: '',
      baseUrl: ''
    };
  }

  try {
    // 提取head内容
    const headMatch = htmlContent.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
    const headContent = headMatch ? headMatch[1] : '';

    // 提取body内容
    const bodyMatch = htmlContent.match(/<body([^>]*?)>([\s\S]*?)<\/body>/i);
    const bodyContent = bodyMatch ? bodyMatch[2] : '';
    const bodyAttributes = bodyMatch ? bodyMatch[1] : '';

    // 提取base URL
    const baseMatch = headContent.match(/<base[^>]*href=["']([^"']+)["'][^>]*>/i);
    const baseUrl = baseMatch ? baseMatch[1] : '';

    return { 
      headContent, 
      bodyContent, 
      bodyAttributes,
      baseUrl
    };
  } catch (error) {
    logger.error('提取HTML结构内容失败', { error: error.message });
    return { 
      headContent: '', 
      bodyContent: '', 
      bodyAttributes: '',
      baseUrl: ''
    };
  }
};

/**
 * 处理完整HTML结构的内容
 * 保留原始的head内容，并将body内容包装在content-container中
 * @param {string} htmlContent - 完整的HTML内容
 * @returns {Object} - 处理后的HTML内容对象，包含doctype、head和body
 */
export const processCompleteHtmlContent = (htmlContent) => {
  try {
    // 提取DOCTYPE
    const doctypeMatch = htmlContent.match(/<!DOCTYPE[^>]*>/i);
    const doctype = doctypeMatch ? doctypeMatch[0] : '<!DOCTYPE html>';
    
    // 提取head和body内容
    const { headContent, bodyContent, bodyAttributes, baseUrl } = extractHeadAndBodyContent(htmlContent);
    
    return {
      doctype,
      headContent,
      bodyContent,
      bodyAttributes,
      baseUrl
    };
  } catch (error) {
    logger.error('处理完整HTML结构失败', { error: error.message });
    return {
      doctype: '<!DOCTYPE html>',
      headContent: '',
      bodyContent: htmlContent,
      bodyAttributes: '',
      baseUrl: ''
    };
  }
};

/**
 * 生成用于计算尺寸和处理图片的脚本
 * @param {boolean} hasBodyBackground - 是否检查body背景
 * @returns {string} - 脚本内容
 */
export const generateSizeCalculationScript = (hasBodyBackground = true) => {
  return `
    // 阻止右键菜单
    document.body.oncontextmenu = function(e) { e.preventDefault(); return false; };
    document.addEventListener('contextmenu', function(e) { e.preventDefault(); return false; });
    
    // 增强型尺寸计算和消息发送
    function calculateAndSendSize(type = 'iframe-content-loaded') {
      try {
        const container = document.querySelector('.content-container');
        if (!container) {
          sendDefaultSize(type);
          return;
        }
        
        ${hasBodyBackground ? `
        // 检查body是否有背景图片
        const bodyStyle = window.getComputedStyle(document.body);
        const hasBodyBackground = bodyStyle.backgroundImage && bodyStyle.backgroundImage !== 'none';
        
        // 尝试多种选择器查找可能的封面元素
        let coverElement = null;
        
        if (hasBodyBackground) {
          // 如果body有背景，优先使用body作为封面元素
          coverElement = document.body;
        } else {
          const selectors = [
            '.wechat-cover', '.weibo-cover', '.xiaohongshu-cover', 
            '.douyin-cover', '.tiktok-cover', '.article-cover', 
            '.cover-element', '[data-role="cover"]',
            'div[style*="background-image"]',
            'div[style*="background"]',
            'div > div',
            'div'
          ];
          
          // 逐个尝试选择器
          for (const selector of selectors) {
            const elements = container.querySelectorAll(selector);
            if (elements.length > 0) {
              coverElement = Array.from(elements).reduce((largest, current) => {
                const currentArea = current.offsetWidth * current.offsetHeight;
                const largestArea = largest ? largest.offsetWidth * largest.offsetHeight : 0;
                return currentArea > largestArea ? current : largest;
              }, null);
              
              if (coverElement) break;
            }
          }
        }
        ` : `
        // 尝试多种选择器查找可能的封面元素
        let coverElement = null;
        const selectors = [
          '.wechat-cover', '.weibo-cover', '.xiaohongshu-cover', 
          '.douyin-cover', '.tiktok-cover', '.article-cover', 
          '.cover-element', '[data-role="cover"]',
          'div[style*="background-image"]',
          'div[style*="background"]',
          'div > div',
          'div'
        ];
        
        // 逐个尝试选择器
        for (const selector of selectors) {
          const elements = container.querySelectorAll(selector);
          if (elements.length > 0) {
            coverElement = Array.from(elements).reduce((largest, current) => {
              const currentArea = current.offsetWidth * current.offsetHeight;
              const largestArea = largest ? largest.offsetWidth * largest.offsetHeight : 0;
              return currentArea > largestArea ? current : largest;
            }, null);
            
            if (coverElement) break;
          }
        }
        `}
        
        // 计算尺寸
        let width, height;
        if (coverElement) {
          width = coverElement.offsetWidth || coverElement.scrollWidth;
          height = coverElement.offsetHeight || coverElement.scrollHeight;
        } else {
          width = container.scrollWidth;
          height = container.scrollHeight;
        }
        
        // 确保尺寸有效
        if (!width || !height || width < 100 || height < 100) {
          width = 750;
          height = 1000;
        }
        
        // 发送尺寸消息
        window.parent.postMessage({
          type: type,
          width: width,
          height: height,
          source: ${hasBodyBackground ? `coverElement === document.body ? 'body-background' : (coverElement ? 'cover-element' : 'container')` : `coverElement ? 'cover-element' : 'container'`}
        }, '*');
        
      } catch (e) {
        sendDefaultSize(type);
      }
    }
    
    // 发送默认尺寸
    function sendDefaultSize(type) {
      window.parent.postMessage({
        type: type,
        width: 750,
        height: 1000,
        source: 'default'
      }, '*');
    }
  `;
};

/**
 * 生成用于处理图片和DOM加载的脚本
 * @param {boolean} checkBodyBackground - 是否检查body背景
 * @param {Function} getTextElementInitScript - 获取文本元素初始化脚本的函数
 * @returns {string} - 脚本内容
 */
export const generateDomLoadedScript = (checkBodyBackground = true, getTextElementInitScript = null) => {
  return `
    // 处理图片加载错误
    function handleImageError(img) {
      img.classList.add('error');
      img.alt = '图片加载失败';
    }
    
    // 监听DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
      try {
        // 立即计算并发送初始尺寸
        calculateAndSendSize('iframe-content-loaded');
        
        // 确保所有文本元素可见
        const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div:not(.content-container)');
        textElements.forEach(el => {
          if (!el.style.color && !window.getComputedStyle(el).color) {
            el.style.color = '#000000';
          }
        });
        
        // 处理图片
        const images = document.querySelectorAll('img');
        if (images.length > 0) {
          let loadedCount = 0;
          const totalImages = images.length;
          
          const checkAllImagesLoaded = () => {
            loadedCount++;
            if (loadedCount === totalImages) {
              // 所有图片加载完成，重新计算尺寸
              setTimeout(() => calculateAndSendSize('iframe-images-loaded'), 100);
            }
          };
          
          images.forEach(img => {
            // 添加错误处理
            img.onerror = function() {
              handleImageError(this);
              checkAllImagesLoaded();
            };
            
            if (img.complete) {
              if (img.naturalWidth === 0) {
                handleImageError(img);
              }
              checkAllImagesLoaded();
            } else {
              img.addEventListener('load', checkAllImagesLoaded);
              img.addEventListener('error', () => {
                handleImageError(img);
                checkAllImagesLoaded();
              });
            }
          });
        }
        
        ${checkBodyBackground ? `
        // 添加背景图片加载监听
        const bodyStyle = window.getComputedStyle(document.body);
        const hasBodyBackground = bodyStyle.backgroundImage && bodyStyle.backgroundImage !== 'none';
        const elementsWithBackground = document.querySelectorAll('[style*="background-image"]');
        
        if (elementsWithBackground.length > 0 || hasBodyBackground) {
          // 等待一段时间后重新计算尺寸，以便捕获背景图片加载完成的情况
          setTimeout(() => calculateAndSendSize('background-images-loaded'), 1000);
        }
        ` : `
        // 添加背景图片加载监听
        const elementsWithBackground = document.querySelectorAll('[style*="background-image"]');
        if (elementsWithBackground.length > 0) {
          // 等待一段时间后重新计算尺寸，以便捕获背景图片加载完成的情况
          setTimeout(() => calculateAndSendSize('background-images-loaded'), 1000);
        }
        `}
        
        // 最后的保障：如果2秒后尺寸仍未正确计算，再次尝试
        setTimeout(() => calculateAndSendSize('delayed-calculation'), 2000);
        
        ${getTextElementInitScript ? getTextElementInitScript() : ''}
      } catch(e) {
        sendDefaultSize('error-recovery');
      }
    });
  `;
};

/**
 * 生成完整HTML结构的内容
 * @param {Object} params - 参数对象
 * @param {string} params.doctype - DOCTYPE声明
 * @param {string} params.headContent - head标签内容
 * @param {string} params.bodyContent - body标签内容
 * @param {string} params.bodyAttributes - body标签属性
 * @param {string} params.baseUrl - 基础URL
 * @param {string} params.editorStyles - 编辑器样式
 * @param {string} params.keyboardScript - 键盘导航脚本
 * @param {string} params.textElementInitScript - 文本元素初始化脚本
 * @returns {string} - 完整的HTML内容
 */
export const generateCompleteHtmlContent = ({
  doctype = '<!DOCTYPE html>',
  headContent = '',
  bodyContent = '',
  bodyAttributes = '',
  baseUrl = '',
  editorStyles = '',
  keyboardScript = '',
  textElementInitScript = null
}) => {
  const baseTagHtml = baseUrl ? `<base href="${baseUrl}">` : '';

  // 检测bodyContent是否已经包含完整的布局结构
  // 如果包含特定的CSS类或复杂结构，则不添加content-container包装
  const hasComplexStructure = bodyContent.includes('wechat-cover') ||
                              bodyContent.includes('background:') ||
                              bodyContent.includes('background-image:') ||
                              bodyContent.match(/<div[^>]*class="[^"]*(?:cover|main|container|wrapper|layout)[^"]*"/i);

  // 根据内容复杂度决定是否添加包装容器
  const finalBodyContent = hasComplexStructure ?
    bodyContent : // 保持原始结构
    `<div class="content-container">${bodyContent}</div>`; // 添加包装容器

  // 只在需要包装容器时添加相关样式
  const containerStyles = hasComplexStructure ? '' : `
        /* 确保内容容器样式 */
        .content-container {
          position: relative;
          width: 100%;
          height: 100%;
          overflow: hidden !important;
        }`;

  return `
    ${doctype}
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="Content-Security-Policy" content="default-src *; img-src * data: blob:; style-src * 'unsafe-inline'; font-src * data:; script-src * 'unsafe-inline' 'unsafe-eval';">
      ${baseTagHtml}
      ${headContent}
      <style>
        ${containerStyles}
        * { box-sizing: border-box; }
        ${editorStyles}
      </style>
    </head>
    <body${bodyAttributes}>
      ${finalBodyContent}
      <script>
        ${generateSizeCalculationScript(true)}
        ${generateDomLoadedScript(true, textElementInitScript)}
        ${keyboardScript}
      </script>
    </body>
    </html>
  `;
};

/**
 * 生成HTML片段的内容
 * @param {Object} params - 参数对象
 * @param {string} params.content - HTML片段内容
 * @param {string} params.baseUrl - 基础URL
 * @param {string} params.editorStyles - 编辑器样式
 * @param {string} params.keyboardScript - 键盘导航脚本
 * @param {string} params.textElementInitScript - 文本元素初始化脚本
 * @returns {string} - 完整的HTML内容
 */
export const generateFragmentHtmlContent = ({
  content = '',
  baseUrl = '',
  editorStyles = '',
  keyboardScript = '',
  textElementInitScript = null
}) => {
  const baseTagHtml = baseUrl ? `<base href="${baseUrl}">` : '';
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="Content-Security-Policy" content="default-src *; img-src * data: blob:; style-src * 'unsafe-inline'; font-src * data:; script-src * 'unsafe-inline' 'unsafe-eval';">
      ${baseTagHtml}
      <style>
        /* 重置样式并确保内容可见 */
        html, body {
          margin: 0 !important;
          padding: 0 !important;
          width: 100%;
          height: 100%;
          overflow: hidden !important;
          font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        body {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .content-container {
          position: relative;
          width: 100%;
          height: 100%;
          overflow: hidden !important;
          min-width: 300px;
          min-height: 300px;
        }
        img { 
          max-width: 100%; 
          height: auto;
        }
        /* 图像加载失败时的样式 */
        img.error {
          min-width: 100px;
          min-height: 100px;
          background: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
        }
        img.error::before {
          content: "图片加载失败";
          position: absolute;
          font-size: 14px;
          color: #888;
        }
        * { box-sizing: border-box; }
        ${editorStyles}
      </style>
    </head>
    <body>
    <div class="content-container">${content}</div>
      <script>
        ${generateSizeCalculationScript(false)}
        ${generateDomLoadedScript(false, textElementInitScript)}
        ${keyboardScript}
      </script>
    </body>
    </html>
  `;
};

/**
 * 生成错误回退的HTML内容
 * @param {string} content - 原始HTML内容
 * @param {string} errorMessage - 错误信息
 * @returns {string} - 包含错误信息的HTML内容
 */
export const generateFallbackHtmlContent = (content, errorMessage = '加载内容时出错，请刷新页面重试。') => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <style>
        body { margin: 0; padding: 10px; font-family: sans-serif; }
        .error { color: red; margin-bottom: 10px; }
      </style>
    </head>
    <body>
      <div class="error">${errorMessage}</div>
      <div class="content-container">${content}</div>
    </body>
    </html>
  `;
};

/**
 * 处理完整HTML结构的内容并注入到iframe
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {string} content - HTML内容
 * @param {Function} getTextEditorStyles - 获取文本编辑器样式的函数
 * @param {Function} getKeyboardNavigationScript - 获取键盘导航脚本的函数
 * @param {Function} getTextElementInitScript - 获取文本元素初始化脚本的函数
 * @param {string} baseUrl - 基础URL，用于解析相对路径
 * @returns {boolean} - 是否成功注入
 */
export const setupCompleteHtmlContent = (
  iframe, 
  content, 
  getTextEditorStyles, 
  getKeyboardNavigationScript, 
  getTextElementInitScript,
  baseUrl = ''
) => {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) {
      throw new Error('无法访问iframe文档');
    }

    // 处理完整HTML结构
    const { doctype, headContent, bodyContent, bodyAttributes, baseUrl: contentBaseUrl } = processCompleteHtmlContent(content);
    
    // 使用base标签处理相对路径
    const effectiveBaseUrl = contentBaseUrl || baseUrl;
    
    // 生成完整的HTML内容
    const htmlContent = generateCompleteHtmlContent({
      doctype,
      headContent,
      bodyContent,
      bodyAttributes,
      baseUrl: effectiveBaseUrl,
      editorStyles: getTextEditorStyles ? getTextEditorStyles() : '',
      keyboardScript: getKeyboardNavigationScript ? getKeyboardNavigationScript() : '',
      textElementInitScript: getTextElementInitScript
    });
    
    // 写入HTML内容
    iframeDoc.write(htmlContent);
    
    return true;
  } catch (error) {
    logger.error('设置完整HTML内容失败', { error: error.message });
    return false;
  }
};

/**
 * 处理HTML片段的内容并注入到iframe
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {string} content - HTML内容
 * @param {Function} getTextEditorStyles - 获取文本编辑器样式的函数
 * @param {Function} getKeyboardNavigationScript - 获取键盘导航脚本的函数
 * @param {Function} getTextElementInitScript - 获取文本元素初始化脚本的函数
 * @param {string} baseUrl - 基础URL，用于解析相对路径
 * @returns {boolean} - 是否成功注入
 */
export const setupFragmentHtmlContent = (
  iframe, 
  content, 
  getTextEditorStyles, 
  getKeyboardNavigationScript, 
  getTextElementInitScript,
  baseUrl = ''
) => {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) {
      throw new Error('无法访问iframe文档');
    }

    // 生成HTML片段的内容
    const htmlContent = generateFragmentHtmlContent({
      content,
      baseUrl,
      editorStyles: getTextEditorStyles ? getTextEditorStyles() : '',
      keyboardScript: getKeyboardNavigationScript ? getKeyboardNavigationScript() : '',
      textElementInitScript: getTextElementInitScript
    });
    
    // 写入HTML内容
    iframeDoc.write(htmlContent);
    
    return true;
  } catch (error) {
    logger.error('设置HTML片段内容失败', { error: error.message });
    return false;
  }
};

/**
 * 处理错误情况下的内容并注入到iframe
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {string} content - HTML内容
 * @param {string} errorMessage - 错误信息
 * @returns {boolean} - 是否成功注入
 */
export const setupFallbackContent = (iframe, content, errorMessage = '加载内容时出错，请刷新页面重试。') => {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) {
      return false;
    }

    // 生成错误回退的HTML内容
    const htmlContent = generateFallbackHtmlContent(content, errorMessage);
    
    // 写入HTML内容
    iframeDoc.write(htmlContent);
    
    return true;
  } catch (error) {
    logger.error('设置回退内容失败', { error: error.message });
    return false;
  }
};

/**
 * 将HTML内容注入到iframe中
 * 根据HTML内容是否包含完整的HTML结构采用不同的注入方式
 * 支持增强型加载器，通过功能配置安全控制
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {string} content - HTML内容
 * @param {Function} getTextEditorStyles - 获取文本编辑器样式的函数
 * @param {Function} getKeyboardNavigationScript - 获取键盘导航脚本的函数
 * @param {Function} getTextElementInitScript - 获取文本元素初始化脚本的函数
 * @param {string} baseUrl - 基础URL，用于解析相对路径
 * @returns {boolean} - 是否成功注入
 */
export const setupIframeContentWithStructureDetection = (
  iframe,
  content,
  getTextEditorStyles,
  getKeyboardNavigationScript,
  getTextElementInitScript,
  baseUrl = ''
) => {
  if (!iframe) {
    logger.error('iframe元素不存在');
    return false;
  }

  const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
  if (!iframeDoc) {
    logger.error('无法访问iframe文档');
    return false;
  }

  try {
    // 初始化功能配置管理器
    featureConfig.init();

    // 检测页面类型和功能特征
    const pageInfo = detectPageType(content);
    logger.info('页面类型检测结果', pageInfo);

    // 处理功能性页面
    let processedContent = content;
    if (pageInfo.type !== 'simple') {
      processedContent = processFunctionalPage(content, pageInfo);
      logger.info('功能性页面处理完成', { originalLength: content.length, processedLength: processedContent.length });
    }

    // 检查是否启用增强型加载器
    const useAdvancedLoader = isFeatureEnabled('ADVANCED_HTML_LOADER');

    if (useAdvancedLoader) {
      logger.info('使用增强型HTML加载器');

      // 尝试使用增强型加载器
      try {
        // 打开iframe文档
        iframeDoc.open();

        const success = setupAdvancedIframeContent(
          iframe,
          processedContent, // 使用处理后的内容
          getTextEditorStyles,
          getKeyboardNavigationScript,
          getTextElementInitScript,
          baseUrl,
          { useAdvancedMode: true, enableFallback: true }
        );

        // 关闭iframe文档
        iframeDoc.close();

        if (success) {
          logger.info('增强型加载器执行成功');
          return true;
        } else {
          logger.warn('增强型加载器执行失败，降级到标准模式');
          // 确保iframe文档已关闭，为标准模式做准备
          try {
            iframeDoc.close();
          } catch (e) {
            // 忽略关闭错误
          }
        }
      } catch (advancedError) {
        logger.error('增强型加载器执行出错，降级到标准模式', { error: advancedError.message });
      }
    }

    // 标准模式处理（默认模式或增强模式失败时的降级）
    logger.info('使用标准HTML加载器');

    // 检测HTML内容是否包含完整的HTML结构
    const isComplete = isCompleteHtmlStructure(processedContent);

    // 打开iframe文档
    iframeDoc.open();

    let success = false;
    if (isComplete) {
      // 如果是完整的HTML结构，使用setupCompleteHtmlContent函数
      success = setupCompleteHtmlContent(
        iframe,
        processedContent, // 使用处理后的内容
        getTextEditorStyles,
        getKeyboardNavigationScript,
        getTextElementInitScript,
        baseUrl
      );
    } else {
      // 如果不是完整的HTML结构，使用setupFragmentHtmlContent函数
      success = setupFragmentHtmlContent(
        iframe,
        processedContent, // 使用处理后的内容
        getTextEditorStyles,
        getKeyboardNavigationScript,
        getTextElementInitScript,
        baseUrl
      );
    }

    // 设置功能性页面环境
    if (success && pageInfo.type !== 'simple') {
      setupFunctionalPageEnvironment(iframe, pageInfo);
    }

    // 关闭iframe文档
    iframeDoc.close();

    return success;
  } catch (error) {
    logger.error('设置iframe内容失败', { error: error.message });
    try {
      // 尝试回退到简单的内容注入
      setupFallbackContent(iframe, content);
      iframeDoc.close();
    } catch (e) {
      logger.error('回退注入也失败了', { error: e.message });
    }
    return false;
  }
};


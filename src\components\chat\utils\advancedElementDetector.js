/**
 * 高级元素检测系统
 * 扩展现有的elementDetection.js功能，支持复杂静态页面中各种元素类型的智能识别
 * 第十六阶段：智能元素检测系统
 */

import logger from '../../../services/logs/frontendLogger';

/**
 * 元素类型枚举
 */
export const ELEMENT_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  NAVIGATION: 'navigation',
  FORM: 'form',
  MEDIA: 'media',
  INTERACTIVE: 'interactive',
  CONTAINER: 'container',
  SVG: 'svg'
};

/**
 * 检测导航元素
 * @param {Element} element - 要检测的元素
 * @returns {Object} 检测结果
 */
export const detectNavigationElements = (element) => {
  if (!element) return { isNavigation: false, score: 0, type: null };

  let score = 0;
  let navType = null;
  const reasons = [];

  const tagName = element.tagName.toLowerCase();
  const className = element.className || '';
  const id = element.id || '';

  // 1. 检查语义化标签
  if (tagName === 'nav') {
    score += 50;
    navType = 'semantic-nav';
    reasons.push('semantic-nav-tag');
  }

  if (tagName === 'header') {
    score += 30;
    navType = 'header-nav';
    reasons.push('header-tag');
  }

  // 2. 检查类名和ID
  const navKeywords = ['nav', 'menu', 'navigation', 'navbar', 'menubar', 'breadcrumb'];
  const classAndId = (className + ' ' + id).toLowerCase();
  
  for (const keyword of navKeywords) {
    if (classAndId.includes(keyword)) {
      score += 25;
      navType = `${keyword}-nav`;
      reasons.push(`nav-keyword-${keyword}`);
      break;
    }
  }

  // 3. 检查结构特征
  const links = element.querySelectorAll('a');
  const listItems = element.querySelectorAll('li');
  
  if (links.length >= 2) {
    score += 20;
    reasons.push('multiple-links');
  }

  if (listItems.length >= 2) {
    score += 15;
    reasons.push('list-structure');
  }

  // 4. 检查位置特征
  try {
    const rect = element.getBoundingClientRect();
    const isTopPosition = rect.top < window.innerHeight * 0.2;
    const isFullWidth = rect.width > window.innerWidth * 0.8;
    
    if (isTopPosition && isFullWidth) {
      score += 15;
      reasons.push('top-full-width');
    }
  } catch (e) {
    // 忽略位置计算错误
  }

  return {
    isNavigation: score >= 25,
    score,
    type: navType,
    reasons: reasons.join(','),
    elementType: ELEMENT_TYPES.NAVIGATION
  };
};

/**
 * 检测表单元素
 * @param {Element} element - 要检测的元素
 * @returns {Object} 检测结果
 */
export const detectFormElements = (element) => {
  if (!element) return { isForm: false, score: 0, type: null };

  let score = 0;
  let formType = null;
  const reasons = [];

  const tagName = element.tagName.toLowerCase();
  const className = element.className || '';

  // 1. 检查表单标签
  if (tagName === 'form') {
    score += 50;
    formType = 'form-container';
    reasons.push('form-tag');
  }

  // 2. 检查表单控件
  const formControls = ['input', 'textarea', 'select', 'button'];
  if (formControls.includes(tagName)) {
    score += 40;
    formType = `form-control-${tagName}`;
    reasons.push(`form-control-${tagName}`);
  }

  // 3. 检查表单相关元素
  if (tagName === 'label') {
    score += 30;
    formType = 'form-label';
    reasons.push('form-label');
  }

  if (tagName === 'fieldset' || tagName === 'legend') {
    score += 35;
    formType = `form-${tagName}`;
    reasons.push(`form-${tagName}`);
  }

  // 4. 检查类名
  const formKeywords = ['form', 'input', 'field', 'control', 'submit', 'search'];
  const lowerClassName = className.toLowerCase();
  
  for (const keyword of formKeywords) {
    if (lowerClassName.includes(keyword)) {
      score += 15;
      formType = `form-${keyword}`;
      reasons.push(`form-keyword-${keyword}`);
      break;
    }
  }

  // 5. 检查容器中的表单元素
  const formElementsInside = element.querySelectorAll('input, textarea, select, button');
  if (formElementsInside.length >= 2) {
    score += 20;
    formType = 'form-container';
    reasons.push('contains-form-elements');
  }

  return {
    isForm: score >= 25,
    score,
    type: formType,
    reasons: reasons.join(','),
    elementType: ELEMENT_TYPES.FORM
  };
};

/**
 * 检测多媒体元素
 * @param {Element} element - 要检测的元素
 * @returns {Object} 检测结果
 */
export const detectMediaElements = (element) => {
  if (!element) return { isMedia: false, score: 0, type: null };

  let score = 0;
  let mediaType = null;
  const reasons = [];

  const tagName = element.tagName.toLowerCase();
  const className = element.className || '';

  // 1. 检查媒体标签
  const mediaTags = {
    'video': 50,
    'audio': 50,
    'img': 45,
    'picture': 40,
    'source': 35,
    'canvas': 40,
    'iframe': 30
  };

  if (mediaTags[tagName]) {
    score += mediaTags[tagName];
    mediaType = `media-${tagName}`;
    reasons.push(`media-tag-${tagName}`);
  }

  // 2. 检查SVG媒体
  if (tagName === 'svg') {
    score += 35;
    mediaType = 'media-svg';
    reasons.push('svg-media');
  }

  // 3. 检查类名
  const mediaKeywords = ['video', 'audio', 'image', 'img', 'media', 'player', 'gallery'];
  const lowerClassName = className.toLowerCase();
  
  for (const keyword of mediaKeywords) {
    if (lowerClassName.includes(keyword)) {
      score += 15;
      mediaType = `media-${keyword}`;
      reasons.push(`media-keyword-${keyword}`);
      break;
    }
  }

  // 4. 检查媒体容器
  const mediaElements = element.querySelectorAll('video, audio, img, svg, canvas, iframe');
  if (mediaElements.length >= 1 && tagName !== 'body') {
    score += 20;
    mediaType = 'media-container';
    reasons.push('contains-media-elements');
  }

  // 5. 检查特殊属性
  if (element.hasAttribute('src') || element.hasAttribute('poster') || element.hasAttribute('controls')) {
    score += 10;
    reasons.push('has-media-attributes');
  }

  return {
    isMedia: score >= 25,
    score,
    type: mediaType,
    reasons: reasons.join(','),
    elementType: ELEMENT_TYPES.MEDIA
  };
};

/**
 * 检测交互元素
 * @param {Element} element - 要检测的元素
 * @returns {Object} 检测结果
 */
export const detectInteractiveElements = (element) => {
  if (!element) return { isInteractive: false, score: 0, type: null };

  let score = 0;
  let interactiveType = null;
  const reasons = [];

  const tagName = element.tagName.toLowerCase();
  const className = element.className || '';

  // 1. 检查交互标签
  const interactiveTags = {
    'button': 50,
    'a': 40,
    'input': 45,
    'select': 45,
    'textarea': 45,
    'details': 35,
    'summary': 35
  };

  if (interactiveTags[tagName]) {
    score += interactiveTags[tagName];
    interactiveType = `interactive-${tagName}`;
    reasons.push(`interactive-tag-${tagName}`);
  }

  // 2. 检查事件属性
  const eventAttributes = ['onclick', 'onmouseover', 'onmouseout', 'onfocus', 'onblur'];
  for (const attr of eventAttributes) {
    if (element.hasAttribute(attr)) {
      score += 15;
      interactiveType = 'interactive-event';
      reasons.push(`has-${attr}`);
      break;
    }
  }

  // 3. 检查类名
  const interactiveKeywords = ['button', 'btn', 'link', 'click', 'hover', 'active', 'toggle', 'dropdown'];
  const lowerClassName = className.toLowerCase();
  
  for (const keyword of interactiveKeywords) {
    if (lowerClassName.includes(keyword)) {
      score += 15;
      interactiveType = `interactive-${keyword}`;
      reasons.push(`interactive-keyword-${keyword}`);
      break;
    }
  }

  // 4. 检查样式特征
  try {
    const style = window.getComputedStyle(element);
    if (style.cursor === 'pointer') {
      score += 20;
      reasons.push('pointer-cursor');
    }
  } catch (e) {
    // 忽略样式获取错误
  }

  // 5. 检查可访问性属性
  const a11yAttributes = ['role', 'tabindex', 'aria-label', 'aria-expanded'];
  for (const attr of a11yAttributes) {
    if (element.hasAttribute(attr)) {
      score += 10;
      reasons.push(`has-${attr}`);
      break;
    }
  }

  return {
    isInteractive: score >= 25,
    score,
    type: interactiveType,
    reasons: reasons.join(','),
    elementType: ELEMENT_TYPES.INTERACTIVE
  };
};

/**
 * 分析元素层次结构
 * @param {Element} container - 容器元素
 * @returns {Object} 层次分析结果
 */
export const analyzeElementHierarchy = (container) => {
  if (!container) return { levels: [], maxDepth: 0, complexity: 'simple' };

  const hierarchy = [];
  let maxDepth = 0;

  const analyzeLevel = (element, depth = 0) => {
    if (!element || depth > 10) return; // 防止无限递归

    maxDepth = Math.max(maxDepth, depth);

    const levelInfo = {
      depth,
      element,
      tagName: element.tagName.toLowerCase(),
      children: element.children.length,
      hasText: element.textContent.trim().length > 0,
      detectionResults: {
        navigation: detectNavigationElements(element),
        form: detectFormElements(element),
        media: detectMediaElements(element),
        interactive: detectInteractiveElements(element)
      }
    };

    hierarchy.push(levelInfo);

    // 递归分析子元素
    Array.from(element.children).forEach(child => {
      analyzeLevel(child, depth + 1);
    });
  };

  analyzeLevel(container);

  // 计算复杂度
  let complexity = 'simple';
  if (maxDepth > 5) complexity = 'complex';
  else if (maxDepth > 3) complexity = 'medium';

  const elementTypes = hierarchy.reduce((types, level) => {
    Object.entries(level.detectionResults).forEach(([type, result]) => {
      if (result.score >= 25) {
        types[type] = (types[type] || 0) + 1;
      }
    });
    return types;
  }, {});

  return {
    levels: hierarchy,
    maxDepth,
    complexity,
    elementTypes,
    totalElements: hierarchy.length
  };
};

/**
 * 综合元素检测器
 * 整合所有检测功能，为元素提供全面的分析
 * @param {Element} element - 要检测的元素
 * @returns {Object} 综合检测结果
 */
export const comprehensiveElementDetection = (element) => {
  if (!element) return null;

  const results = {
    element,
    tagName: element.tagName.toLowerCase(),
    className: element.className || '',
    id: element.id || '',
    detectionResults: {
      navigation: detectNavigationElements(element),
      form: detectFormElements(element),
      media: detectMediaElements(element),
      interactive: detectInteractiveElements(element)
    },
    primaryType: null,
    editingStrategy: null,
    priority: 0
  };

  // 确定主要类型和优先级
  let highestScore = 0;
  let primaryType = ELEMENT_TYPES.CONTAINER;

  Object.entries(results.detectionResults).forEach(([type, result]) => {
    if (result.score > highestScore) {
      highestScore = result.score;
      primaryType = result.elementType;
    }
  });

  results.primaryType = primaryType;
  results.priority = highestScore;

  // 确定编辑策略
  results.editingStrategy = determineEditingStrategy(results);

  return results;
};

/**
 * 确定元素的编辑策略
 * @param {Object} detectionResult - 检测结果
 * @returns {Object} 编辑策略
 */
export const determineEditingStrategy = (detectionResult) => {
  const { primaryType, detectionResults } = detectionResult;

  const strategies = {
    [ELEMENT_TYPES.TEXT]: {
      mode: 'text-edit',
      allowDrag: true,
      allowResize: false,
      editableAttributes: ['textContent'],
      restrictions: []
    },
    [ELEMENT_TYPES.IMAGE]: {
      mode: 'image-edit',
      allowDrag: true,
      allowResize: true,
      editableAttributes: ['src', 'alt'],
      restrictions: []
    },
    [ELEMENT_TYPES.NAVIGATION]: {
      mode: 'structure-edit',
      allowDrag: false,
      allowResize: false,
      editableAttributes: ['textContent'],
      restrictions: ['maintain-structure']
    },
    [ELEMENT_TYPES.FORM]: {
      mode: 'form-edit',
      allowDrag: false,
      allowResize: false,
      editableAttributes: ['value', 'placeholder'],
      restrictions: ['maintain-functionality']
    },
    [ELEMENT_TYPES.MEDIA]: {
      mode: 'media-edit',
      allowDrag: true,
      allowResize: true,
      editableAttributes: ['src', 'controls'],
      restrictions: ['preserve-aspect-ratio']
    },
    [ELEMENT_TYPES.INTERACTIVE]: {
      mode: 'interactive-edit',
      allowDrag: false,
      allowResize: false,
      editableAttributes: ['textContent'],
      restrictions: ['preserve-events']
    },
    [ELEMENT_TYPES.CONTAINER]: {
      mode: 'container-edit',
      allowDrag: true,
      allowResize: true,
      editableAttributes: [],
      restrictions: ['check-children']
    }
  };

  return strategies[primaryType] || strategies[ELEMENT_TYPES.CONTAINER];
};

/**
 * 智能元素推荐
 * 基于页面结构推荐最适合编辑的元素
 * @param {Element} container - 容器元素
 * @returns {Array} 推荐的元素列表
 */
export const recommendEditableElements = (container) => {
  if (!container) return [];

  const allElements = Array.from(container.querySelectorAll('*'));
  const detectionResults = allElements.map(el => comprehensiveElementDetection(el))
    .filter(result => result && result.priority >= 25);

  // 按优先级和类型排序
  const sortedResults = detectionResults.sort((a, b) => {
    // 首先按优先级排序
    if (b.priority !== a.priority) {
      return b.priority - a.priority;
    }

    // 然后按类型优先级排序
    const typePriority = {
      [ELEMENT_TYPES.TEXT]: 5,
      [ELEMENT_TYPES.IMAGE]: 4,
      [ELEMENT_TYPES.MEDIA]: 3,
      [ELEMENT_TYPES.INTERACTIVE]: 2,
      [ELEMENT_TYPES.NAVIGATION]: 1,
      [ELEMENT_TYPES.FORM]: 1,
      [ELEMENT_TYPES.CONTAINER]: 0
    };

    return (typePriority[b.primaryType] || 0) - (typePriority[a.primaryType] || 0);
  });

  // 过滤重复和嵌套元素
  const filteredResults = [];
  const addedElements = new Set();

  for (const result of sortedResults) {
    const element = result.element;

    // 跳过已添加的元素
    if (addedElements.has(element)) continue;

    // 检查是否与已添加的元素有嵌套关系
    let hasNestingConflict = false;
    for (const addedElement of addedElements) {
      if (element.contains(addedElement) || addedElement.contains(element)) {
        hasNestingConflict = true;
        break;
      }
    }

    if (!hasNestingConflict) {
      filteredResults.push(result);
      addedElements.add(element);
    }
  }

  logger.info('智能元素推荐完成', {
    totalAnalyzed: allElements.length,
    qualified: detectionResults.length,
    recommended: filteredResults.length,
    types: filteredResults.reduce((acc, r) => {
      acc[r.primaryType] = (acc[r.primaryType] || 0) + 1;
      return acc;
    }, {})
  });

  return filteredResults;
};

/**
 * 元素组选择功能
 * 选择相关的元素组进行批量编辑
 * @param {Element} element - 基准元素
 * @param {Element} container - 容器元素
 * @returns {Array} 相关元素组
 */
export const selectElementGroup = (element, container) => {
  if (!element || !container) return [element];

  const baseDetection = comprehensiveElementDetection(element);
  if (!baseDetection) return [element];

  const allElements = Array.from(container.querySelectorAll('*'));
  const relatedElements = [];

  for (const el of allElements) {
    if (el === element) continue;

    const detection = comprehensiveElementDetection(el);
    if (!detection) continue;

    // 检查是否为相同类型
    if (detection.primaryType === baseDetection.primaryType) {
      // 检查是否在相同的父容器中
      if (el.parentElement === element.parentElement) {
        relatedElements.push(el);
      }
    }
  }

  return [element, ...relatedElements];
};

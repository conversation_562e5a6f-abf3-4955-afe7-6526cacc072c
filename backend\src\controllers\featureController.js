const { FeatureControl } = require('../models');
const { successResponse, errorResponse, paginationResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');
const { Op } = require('sequelize');

/**
 * 获取功能控制列表
 * @route GET /api/admin/features
 */
const getFeaturesList = async (req, res) => {
  const { page = 1, limit = 10, keyword } = req.query;

  try {
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const where = {};
    if (keyword) {
      where[Op.or] = [
        { feature_name: { [Op.like]: `%${keyword}%` } },
        { feature_description: { [Op.like]: `%${keyword}%` } }
      ];
    }
    
    // 查询功能控制
    const { count, rows } = await FeatureControl.findAndCountAll({
      where,
      order: [['id', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    // 准备分页信息
    const pagination = {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      total_pages: Math.ceil(count / limit)
    };
    
    return paginationResponse(res, rows, pagination, '获取功能控制列表成功');
  } catch (error) {
    logger.error('获取功能控制列表失败:', error);
    return errorResponse(res, '获取功能控制列表失败', 500);
  }
};

/**
 * 获取功能控制详情
 * @route GET /api/admin/features/:id
 */
const getFeatureDetail = async (req, res) => {
  const { id } = req.params;
  
  try {
    const feature = await FeatureControl.findByPk(id);
    
    if (!feature) {
      return errorResponse(res, '功能控制不存在', 404);
    }
    
    return successResponse(res, '获取功能控制详情成功', { feature });
  } catch (error) {
    logger.error('获取功能控制详情失败:', error);
    return errorResponse(res, '获取功能控制详情失败', 500);
  }
};

/**
 * 创建功能控制
 * @route POST /api/admin/features
 */
const createFeature = async (req, res) => {
  const { feature_name, feature_description, user_roles, points_cost, is_active } = req.body;
  
  try {
    // 检查功能名称是否已存在
    const existingFeature = await FeatureControl.findOne({
      where: { feature_name }
    });
    
    if (existingFeature) {
      return errorResponse(res, '功能名称已存在', 400);
    }
    
    // 创建功能控制
    const feature = await FeatureControl.create({
      feature_name,
      feature_description,
      user_roles,
      points_cost: points_cost || 0,
      is_active: is_active !== undefined ? is_active : true
    });
    
    logger.info(`管理员${req.user.id}创建了功能控制: ${feature_name}`);
    
    return successResponse(res, '创建功能控制成功', { feature });
  } catch (error) {
    logger.error('创建功能控制失败:', error);
    return errorResponse(res, '创建功能控制失败', 500);
  }
};

/**
 * 更新功能控制
 * @route PUT /api/admin/features/:id
 */
const updateFeature = async (req, res) => {
  const { id } = req.params;
  const { feature_name, feature_description, user_roles, points_cost, is_active } = req.body;
  
  try {
    const feature = await FeatureControl.findByPk(id);
    
    if (!feature) {
      return errorResponse(res, '功能控制不存在', 404);
    }
    
    // 检查功能名称是否与其他功能重复
    if (feature_name && feature_name !== feature.feature_name) {
      const existingFeature = await FeatureControl.findOne({
        where: { 
          feature_name,
          id: { [Op.ne]: id }
        }
      });
      
      if (existingFeature) {
        return errorResponse(res, '功能名称已存在', 400);
      }
    }
    
    // 更新功能控制
    await feature.update({
      feature_name: feature_name || feature.feature_name,
      feature_description: feature_description !== undefined ? feature_description : feature.feature_description,
      user_roles: user_roles || feature.user_roles,
      points_cost: points_cost !== undefined ? points_cost : feature.points_cost,
      is_active: is_active !== undefined ? is_active : feature.is_active
    });
    
    logger.info(`管理员${req.user.id}更新了功能控制: ${feature.feature_name}`);
    
    return successResponse(res, '更新功能控制成功', { feature });
  } catch (error) {
    logger.error('更新功能控制失败:', error);
    return errorResponse(res, '更新功能控制失败', 500);
  }
};

/**
 * 删除功能控制
 * @route DELETE /api/admin/features/:id
 */
const deleteFeature = async (req, res) => {
  const { id } = req.params;
  
  try {
    const feature = await FeatureControl.findByPk(id);
    
    if (!feature) {
      return errorResponse(res, '功能控制不存在', 404);
    }
    
    await feature.destroy();
    
    logger.info(`管理员${req.user.id}删除了功能控制: ${feature.feature_name}`);
    
    return successResponse(res, '删除功能控制成功');
  } catch (error) {
    logger.error('删除功能控制失败:', error);
    return errorResponse(res, '删除功能控制失败', 500);
  }
};

/**
 * 获取前台可用的功能控制列表
 * @route GET /api/features
 */
const getClientFeatures = async (req, res) => {
  try {
    const userRole = req.user.role;
    
    // 查询所有活跃的功能控制
    const features = await FeatureControl.findAll({
      where: {
        is_active: true
      }
    });
    
    // 过滤出适用于当前用户角色的功能
    const userFeatures = features.filter(feature => {
      return feature.user_roles.includes(userRole);
    });
    
    return successResponse(res, '获取功能控制列表成功', { features: userFeatures });
  } catch (error) {
    logger.error('获取功能控制列表失败:', error);
    return errorResponse(res, '获取功能控制列表失败', 500);
  }
};

/**
 * 检查功能是否可用
 * @route GET /api/features/:featureName/check
 */
const checkFeatureAvailability = async (req, res) => {
  const { featureName } = req.params;
  
  try {
    // 获取当前用户
    const user = req.user;
    
    // 查询指定的功能控制
    const feature = await FeatureControl.findOne({
      where: {
        feature_name: featureName,
        is_active: true
      }
    });
    
    if (!feature) {
      return successResponse(res, '功能检查完成', {
        available: true,
        reason: '功能未受限制'
      });
    }
    
    // 检查用户角色是否在功能的适用角色列表中
    const roleMatched = feature.user_roles.includes(user.role);
    
    // 计算用户总可用积分（常规积分 + 每日积分）
    const totalAvailablePoints = user.points + (user.daily_points || 0);
    
    // 检查用户总积分是否足够
    const pointsSufficient = totalAvailablePoints >= feature.points_cost;
    
    let available = true;
    let reason = '';
    
    if (!roleMatched) {
      available = false;
      reason = '您的账号权限不足，无法使用此功能';
    } else if (!pointsSufficient) {
      available = false;
      reason = `您的积分不足，此功能需要${feature.points_cost}积分`;
    }
    
    return successResponse(res, '功能检查完成', {
      available,
      reason,
      points_cost: feature.points_cost,
      current_points: user.points,
      daily_points: user.daily_points || 0,
      total_available_points: totalAvailablePoints
    });
  } catch (error) {
    logger.error('检查功能可用性失败:', error);
    return errorResponse(res, '检查功能可用性失败', 500);
  }
};

module.exports = {
  getFeaturesList,
  getFeatureDetail,
  createFeature,
  updateFeature,
  deleteFeature,
  getClientFeatures,
  checkFeatureAvailability
}; 
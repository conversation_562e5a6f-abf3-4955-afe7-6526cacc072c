body {
  font-family: 'Microsoft YaHei', sans-serif;
  background-color: #f5f7fb;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  padding: 48px 0 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
  background-color: #1e2a3a;
  color: white;
  min-height: 100vh;
}

.sidebar-sticky {
  position: relative;
  top: 0;
  height: calc(100vh - 48px);
  padding-top: 0.5rem;
  overflow-x: hidden;
  overflow-y: auto;
}

.sidebar .nav-link {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.75);
  padding: 0.75rem 1rem;
}

.sidebar .nav-link:hover {
  color: #fff;
}

.sidebar .nav-link.active {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link i {
  margin-right: 10px;
}

/* 子菜单样式 */
.sidebar .sub-menu {
  padding-left: 1.5rem;
  margin-top: 0.25rem;
}

.sidebar .sub-menu .nav-link {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.sidebar .nav-link[data-bs-toggle="collapse"] {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar .nav-link[data-bs-toggle="collapse"] i.bi-chevron-down {
  font-size: 0.75rem;
  transition: transform 0.3s;
}

.sidebar .nav-link[aria-expanded="true"] i.bi-chevron-down {
  transform: rotate(180deg);
}

.navbar-brand {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 1.1rem;
  background-color: rgba(0, 0, 0, .25);
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
}

.navbar .navbar-toggler {
  right: 1rem;
}

.content {
  padding: 70px 20px 20px;
}

.dashboard-card {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stat-card i {
  font-size: 36px;
  margin-bottom: 10px;
  color: #1890ff;
}

.search-box {
  width: 300px;
}

.content-wrapper {
  padding: 20px;
}

.dashboard-card .card-title {
  margin-bottom: 10px;
  color: #1890ff;
}

.d-block {
  display: block !important;
}

.modal-preview-image {
  max-width: 100%;
  max-height: 300px;
}

.code-preview {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
}

.d-none {
  display: none !important;
}

/* 封面类型下拉菜单样式 */
.cover-type-select {
  width: auto;
}

/* 预览图像样式 */
.img-thumbnail {
  width: 50px;
  height: 50px;
  object-fit: cover;
}

/* HTML预览框架样式 */
.html-preview-frame {
  width: 100%;
  height: 500px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

/* 预览信息样式 */
.preview-info {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  margin-bottom: 15px;
}

/* 日志筛选表单样式 */
.log-filter-form {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 5px;
  margin-bottom: 15px;
}

/* 分页控件样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 预览图像样式 */
.preview-image {
  max-width: 200px;
  max-height: 200px;
}

/* 日志详情样式 */
.log-detail-info {
  max-height: 300px;
  overflow-y: auto;
}

/* 恢复之前为 main 添加的样式，并调整padding */
main.col-md-9.offset-md-3.col-lg-10.offset-lg-2.px-md-4 {
  flex: 1;
  padding-left: 3rem; /* 增加左内边距 */
}

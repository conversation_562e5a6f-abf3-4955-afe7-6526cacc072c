import React, { memo } from 'react';
import HomePreview from './HomePreview';

// 加载旋转动画组件，与ChatPreview.jsx中一致
const LoadingSpinner = ({ className }) => {
  return <div className={`animate-spin rounded-full h-8 w-8 border-b-2 border-primary ${className || ''}`}></div>;
};

/**
 * 预览区域组件，负责渲染预览区域的UI结构
 * 
 * @param {Object} props 组件属性
 * @param {React.RefObject} props.iframeRef iframe元素引用
 * @param {React.RefObject} props.containerRef 预览容器元素引用
 * @param {React.RefObject} props.outerPreviewAreaRef 外部预览区域元素引用
 * @param {Object} props.previewContainerStyle 预览容器样式
 * @param {Object} props.outerPreviewAreaStyle 外部预览区域样式
 * @param {boolean} props.isLoading 是否正在加载
 * @param {boolean} props.isGenerating 是否正在生成内容
 * @param {string|null} props.loadError 加载错误信息
 * @param {Object} props.htmlContentSize HTML内容尺寸
 * @param {number|string} props.iframeKey iframe的key值
 * @param {Function} props.onRetry 重试函数，当加载错误时调用
 * @param {React.ReactNode} props.children 子元素
 * @param {string|null} props.htmlContent HTML内容
 * @param {boolean} props.showHomePreview 是否显示首页预览
 * @param {Function} props.onStartCreate 开始创建的回调函数
 * @returns {React.ReactElement} 预览区域组件
 */
const PreviewArea = memo(({
  iframeRef,
  containerRef,
  outerPreviewAreaRef,
  previewContainerStyle,
  outerPreviewAreaStyle,
  isLoading,
  isGenerating,
  loadError,
  htmlContentSize = { width: 750, height: 1000 },
  iframeKey,
  onRetry,
  children,
  htmlContent,
  showHomePreview,
  onStartCreate
}) => {
  // 判断是否显示首页预览 - 修改逻辑，不再依赖loadError状态
  const shouldShowHomePreview = showHomePreview && !isLoading && !isGenerating && !htmlContent;

  return (
    <div className="editor-content-area flex-grow overflow-hidden" style={{ 
      scrollbarWidth: 'none', 
      msOverflowStyle: 'none', 
      backgroundColor: '#f0f2f5', // 给预览区一个不同的背景色
      height: '100%'
    }}>
      <style>{`
        /* 隐藏滚动条但保持可滚动性 */
        .editor-content-area::-webkit-scrollbar {
          display: none;
          width: 0 !important;
        }
        .chat-preview-container *::-webkit-scrollbar {
          display: none;
          width: 0 !important;
        }
        /* Firefox */
        .chat-preview-container * {
          scrollbar-width: none;
        }
        /* IE/Edge */
        .chat-preview-container * {
          -ms-overflow-style: none;
        }
        /* 确保预览容器正确居中 */
        .preview-outer-container {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        /* 确保预览内容不显示滚动条 */
        .preview-container {
          overflow: visible !important;
        }
      `}</style>
      <div className="relative w-full h-full">
        {shouldShowHomePreview ? (
          <HomePreview onStartCreate={onStartCreate} />
        ) : (
          <div
            ref={outerPreviewAreaRef}
            className="px-4 pt-0 pb-0 flex items-center justify-center bg-transparent preview-outer-container w-full h-full"
            style={outerPreviewAreaStyle}
          >
            <div
              ref={containerRef}
              data-preview-container="true"
              style={previewContainerStyle}
              className="preview-container"
            >
              {isLoading && !isGenerating && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
                  <div className="flex flex-col items-center">
                    <LoadingSpinner className="h-12 w-12" />
                    <span className="ml-3 text-lg">加载中...</span>
                  </div>
                </div>
              )}
              {loadError && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
                  <div className="flex flex-col items-center p-4 bg-red-50 border border-red-200 rounded-md">
                    <span className="text-sm text-red-600 mb-2">{loadError}</span>
                    <button
                      className="px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm"
                      onClick={onRetry}
                    >
                      重试
                    </button>
                  </div>
                </div>
              )}
              <iframe
                key={iframeKey}
                ref={iframeRef}
                data-preview-iframe="true"
                style={{
                  width: htmlContentSize?.width > 0 ? `${htmlContentSize.width}px` : '750px',
                  height: htmlContentSize?.height > 0 ? `${htmlContentSize.height}px` : '1000px',
                  border: 'none', // 确保无边框
                  outline: 'none', // 移除焦点边框
                  display: 'block',
                  background: 'transparent',
                  overflow: 'hidden',
                  flexShrink: 0,
                  boxShadow: 'none', // 移除可能的阴影
                }}
                sandbox="allow-same-origin allow-scripts allow-forms allow-downloads" // 增加下载权限，确保资源加载
                title="封面预览"
                srcDoc="" // 添加空的srcDoc属性，避免使用about:srcdoc
              />
              {children}
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

PreviewArea.displayName = 'PreviewArea';

export default PreviewArea; 
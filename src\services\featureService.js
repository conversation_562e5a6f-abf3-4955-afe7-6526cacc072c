import axios from 'axios';
import { cachedRequest, removeCacheItem } from './apiCacheService';

/**
 * 处理Token过期的情况
 * @param {Error} error - Axios错误对象
 * @returns {boolean} - 如果是Token过期，返回true
 */
const handleTokenExpired = (error) => {
  // 检查是否是401错误
  if (error.response && error.response.status === 401) {
    // 清除已过期的登录信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // 清除相关缓存
    removeCacheItem(/^GET:\/api\/auth\/me/); // 清除认证缓存
    removeCacheItem(/^GET:\/api\/user\/profile/); // 清除用户资料缓存
    removeCacheItem(/^GET:\/api\/features/); // 清除权限缓存
    
    // 显示登录提示
    import('antd').then(({ Modal }) => {
      Modal.error({
        title: '登录已过期',
        content: '您的登录已过期，请重新登录',
        okText: '去登录',
        onOk: () => {
          // 保存当前URL以便登录后跳回
          const currentPath = window.location.pathname + window.location.search;
          window.location.href = `/auth?redirect=${encodeURIComponent(currentPath)}`;
        }
      });
    });
    return true;
  }
  return false;
};

// 权限缓存对象
let featureCache = {};
// 缓存过期时间（默认5分钟）
const CACHE_EXPIRY_TIME = 5 * 60 * 1000; 

/**
 * 检查特定功能是否可用
 * @param {string} feature - 功能标识符
 * @param {boolean} useCache - 是否使用缓存
 * @returns {Promise<{available: boolean, reason: string}>} - 功能可用性结果
 */
export async function checkFeatureAvailability(feature, useCache = true) {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      return {
        available: false,
        reason: '请先登录',
        points_cost: 0,
        current_points: 0
      };
    }

    // 使用apiCacheService进行缓存请求
    const response = await cachedRequest(`/api/features/${feature}/check`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }, {
      // 如果不使用缓存，则强制刷新
      forceRefresh: !useCache
    });

    if (response.success) {
      return response.data;
    } else {
      return {
        available: false,
        reason: response.message || '检查功能可用性失败',
        points_cost: 0,
        current_points: 0
      };
    }
  } catch (error) {
    // 处理Token过期情况
    if (handleTokenExpired(error)) {
      return {
        available: false,
        reason: '登录已过期，请重新登录',
        points_cost: 0,
        current_points: 0,
        token_expired: true
      };
    }
    
    return {
      available: false,
      reason: '检查功能可用性失败，请稍后再试',
      points_cost: 0,
      current_points: 0
    };
  }
}

/**
 * 清除权限缓存
 * @param {string} feature - 可选，指定要清除的功能缓存，不提供则清除所有
 */
export function clearFeatureCache(feature = null) {
  if (feature) {
    // 清除特定功能的缓存
    removeCacheItem(new RegExp(`^GET:\/api\/features\/${feature}\/check`));
  } else {
    // 清除所有功能的缓存
    removeCacheItem(/^GET:\/api\/features/);
  }
}

/**
 * 批量获取多个功能的权限
 * @param {string[]} features - 功能标识符数组
 * @param {boolean} useCache - 是否使用缓存
 * @returns {Promise<Object>} - 包含所有功能权限的对象
 */
export async function batchCheckFeatures(features, useCache = true) {
  const results = {};
  const promises = features.map(feature => 
    checkFeatureAvailability(feature, useCache)
      .then(result => {
        results[feature] = result;
      })
  );
  
  await Promise.all(promises);
  return results;
}

/**
 * 消费积分
 * @param {number} amount - 消费积分数量
 * @param {string} reason - 消费原因
 * @param {string} [operationId] - 操作唯一标识，用于防止重复操作
 * @returns {Promise<Object>} - 消费结果
 */
export const consumePoints = async (amount, reason, operationId) => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      return { success: false, message: '请先登录' };
    }

    const payload = {
      amount,
      reason
    };

    // 如果提供了操作ID，添加到请求中
    if (operationId) {
      payload.operation_id = operationId;
    }

    const response = await axios.post('/api/user/consume-points',
      payload,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );

    if (response.data.success) {
      // 更新本地存储的用户积分
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        user.points = response.data.data.points;
        // 更新每日积分
        if (response.data.data.daily_points !== undefined) {
          user.daily_points = response.data.data.daily_points;
        }
        localStorage.setItem('user', JSON.stringify(user));
      }
      
      // 清除可能受影响的缓存
      removeCacheItem(/^GET:\/api\/user\/profile/);
      // 清除功能检查相关的缓存，确保下次检查功能可用性时获取最新的积分数据
      removeCacheItem(/^GET:\/api\/features/);
      
      return { success: true, data: response.data.data };
    } else {
      return { success: false, message: response.data.message || '积分扣除失败' };
    }
  } catch (error) {
    // 处理Token过期情况
    if (handleTokenExpired(error)) {
      return { success: false, message: '登录已过期，请重新登录', token_expired: true };
    }
    
    return { success: false, message: '积分扣除请求失败，请稍后再试' };
  }
};

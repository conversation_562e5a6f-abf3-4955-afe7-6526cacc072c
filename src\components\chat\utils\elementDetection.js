/**
 * 元素检测模块 - 用于分析HTML元素，识别可编辑文本元素
 * 实现高级文本元素检测，支持多种类型的文本元素识别
 * 集成第十六阶段：智能元素检测系统
 */

import {
  comprehensiveElementDetection,
  recommendEditableElements,
  analyzeElementHierarchy,
  ELEMENT_TYPES
} from './advancedElementDetector';

/**
 * 分析单个元素，判断其是否可能是可编辑文本元素
 * @param {Element} element - 要分析的DOM元素
 * @return {Object} 包含分析结果和评分的对象
 */
export const analyzeElement = (element) => {
  if (!element || !element.nodeType || element.nodeType !== Node.ELEMENT_NODE) {
    return { isEditable: false, score: 0, reason: 'not-element' };
  }

  // 初始化评分
  let score = 0;
  let reason = [];

  // 1. 检查标签类型
  const tagName = element.tagName.toLowerCase();
  
  // 标题元素有很高的可能性是可编辑文本
  if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
    score += 40;
    reason.push('heading-tag');
  }
  
  // 段落和span通常包含文本
  if (tagName === 'p') {
    score += 35;
    reason.push('paragraph-tag');
  }
  
  if (tagName === 'span') {
    score += 30;
    reason.push('span-tag');
  }
  
  // div可能包含文本，但评分较低（除非它直接包含文本）
  if (tagName === 'div') {
    // 首先给较低的基础分
    score += 1;
    reason.push('div-tag');
    
    // 检查是否直接包含文本节点（不包含子元素）
    let hasDirectTextNode = false;
    let hasElementNode = false;
    
    for (const child of element.childNodes) {
      if (child.nodeType === Node.TEXT_NODE && child.textContent.trim().length > 0) {
        hasDirectTextNode = true;
      } else if (child.nodeType === Node.ELEMENT_NODE) {
        hasElementNode = true;
      }
    }
    
    // 如果div直接包含文本节点且不包含其他元素节点，评分大幅提高
    if (hasDirectTextNode && !hasElementNode) {
      score += 25;
      reason.push('div-with-direct-text');
    }
    // 如果同时包含文本节点和元素节点，可能是混合容器，适度提高评分
    else if (hasDirectTextNode && hasElementNode) {
      score += 10;
      reason.push('mixed-content-div');
    }
  }

  // SVG文本元素
  if (tagName === 'text' || (element.namespaceURI && element.namespaceURI.includes('svg'))) {
    score += 35;
    reason.push('svg-text');
    
    // 增强对SVG元素的识别
    if (tagName === 'svg') {
      score += 40; // 大幅增加SVG容器本身的评分
      reason.push('svg-container');
      
      // 检查是否有特定的类名，如paper-plane
      if (element.className && typeof element.className.baseVal === 'string') {
        const className = element.className.baseVal.toLowerCase();
        if (className.includes('paper-plane')) {
          score += 80; // 极高优先级，确保paper-plane图标可编辑
          reason.push('paper-plane-svg');
        } else if (className.includes('icon') || 
                  className.includes('logo') || 
                  className.includes('graphic')) {
          score += 60; // 高优先级识别所有图标/Logo类SVG
          reason.push('icon-logo-svg');
        }
      } else if (element.className && typeof element.className === 'string') {
        // 如果className不是对象（baseVal），直接使用字符串形式
        const className = element.className.toLowerCase();
        if (className.includes('paper-plane')) {
          score += 80; // 极高优先级，确保paper-plane图标可编辑
          reason.push('paper-plane-svg');
        } else if (className.includes('icon') || 
                  className.includes('logo') || 
                  className.includes('graphic')) {
          score += 60; // 高优先级识别所有图标/Logo类SVG
          reason.push('icon-logo-svg');
        }
      }
      
      // 根据SVG的尺寸适当调整评分
      try {
        const rect = element.getBoundingClientRect();
        if (rect.width < 100 && rect.height < 100) {
          score += 20; // 小型SVG更可能是图标，增加评分
          reason.push('small-svg-icon');
        }
      } catch (e) {
        // 忽略尺寸计算错误
      }
    }
    
    // SVG图形元素（path, rect, circle等）
    if (['path', 'rect', 'circle', 'ellipse', 'line', 'polygon', 'polyline'].includes(tagName)) {
      score += 30;
      reason.push('svg-shape');
      
      // 如果是SVG图形元素，但没有父SVG，增加评分使其成为可编辑元素
      if (!element.closest('svg')) {
        score += 20;
        reason.push('standalone-svg-shape');
      }
    }
    
    // SVG分组元素
    if (['g', 'symbol', 'defs', 'marker', 'pattern', 'mask', 'clipPath'].includes(tagName)) {
      score += 25;
      reason.push('svg-group');
    }
  }

  // 2. 检查类名和ID
  const className = element.className;
  const id = element.id;

  // 常见的文本相关类名
  const textRelatedClasses = ['title', 'heading', 'text', 'content', 'description', 'subtitle', 'label', 'caption', 'name', 'info'];
  if (typeof className === 'string') {
    for (const cls of textRelatedClasses) {
      if (className.toLowerCase().includes(cls)) {
        score += 15;
        reason.push(`text-class-${cls}`);
        break;
      }
    }
  }

  // 容器相关类名，降低评分
  const containerClasses = ['container', 'wrapper', 'section', 'main', 'body', 'content-container'];
  if (typeof className === 'string') {
    for (const cls of containerClasses) {
      if (className.toLowerCase().includes(cls)) {
        score -= 20;
        reason.push(`container-class-${cls}`);
        break;
      }
    }
  }

  // 3. 检查元素内容和大小
  // 有文本内容的元素更可能是可编辑文本
  const textContent = element.textContent ? element.textContent.trim() : '';
  if (textContent.length > 0) {
    // 检查是否直接包含文本节点
    let hasDirectTextContent = false;
    for (const child of element.childNodes) {
      if (child.nodeType === Node.TEXT_NODE && child.textContent.trim().length > 0) {
        hasDirectTextContent = true;
        break;
      }
    }
    
    if (hasDirectTextContent) {
      if (textContent.length < 500) {
        score += 25; // 直接包含短文本
        reason.push('has-direct-short-text');
      } else {
        score += 15; // 直接包含长文本
        reason.push('has-direct-long-text');
      }
    } else if (textContent.length < 100) {
      score += 10; // 间接包含短文本
      reason.push('has-indirect-short-text');
    } else {
      score += 5; // 间接包含长文本，可能是容器
      reason.push('has-indirect-long-text');
    }

    // 检查是否只包含文本子节点或简单格式化元素
    let hasOnlyTextNodes = true;
    for (const child of element.childNodes) {
      if (child.nodeType !== Node.TEXT_NODE && 
          child.nodeType !== Node.COMMENT_NODE && 
          !(child.nodeType === Node.ELEMENT_NODE && ['br', 'b', 'i', 'strong', 'em', 'u', 'small', 'span', 'a'].includes(child.tagName.toLowerCase()))) {
        hasOnlyTextNodes = false;
        break;
      }
    }
    
    if (hasOnlyTextNodes && element.childNodes.length > 0) {
      score += 25;
      reason.push('only-text-or-simple-format');
    }
  } else {
    // 没有文本内容，可能不是文本元素
    score -= 25;
    reason.push('no-text-content');
  }

  // 4. 检查元素尺寸和位置
  // 过大的元素可能是容器而非具体文本元素
  try {
    const rect = element.getBoundingClientRect();
    const parentRect = element.parentElement ? element.parentElement.getBoundingClientRect() : null;
    
    // 元素太大（超过页面宽度的80%），可能是容器
    if (rect.width > window.innerWidth * 0.8 && rect.height > 100) {
      score -= 20;
      reason.push('too-large-element');
    }
    
    // 元素几乎与父元素一样大，可能是容器
    if (parentRect && 
        rect.width > parentRect.width * 0.9 && 
        rect.height > parentRect.height * 0.9) {
      score -= 15;
      reason.push('same-size-as-parent');
    }
  } catch (e) {
    // 忽略尺寸计算错误
  }

  // 5. 检查样式
  if (window.getComputedStyle) {
    try {
      const style = window.getComputedStyle(element);
      
      // 检查font相关属性，文本元素通常会设置这些属性
      if (style.fontSize && style.fontSize !== 'inherit') {
        score += 5;
        reason.push('has-font-size');
      }
      
      if (style.fontFamily && style.fontFamily !== 'inherit') {
        score += 5;
        reason.push('has-font-family');
      }
      
      if (style.fontWeight && style.fontWeight !== 'normal' && style.fontWeight !== '400') {
        score += 5;
        reason.push('has-font-weight');
      }
      
      // 检查显示方式，inline和inline-block更可能是文本
      if (style.display === 'inline' || style.display === 'inline-block') {
        score += 15;
        reason.push(`display-${style.display}`);
      }
      
      // 特定的display值可能表示容器
      if (style.display === 'flex' || style.display === 'grid') {
        score -= 15;
        reason.push(`container-display-${style.display}`);
      }
      
      // 检查是否有文本装饰属性
      if (style.textDecoration && style.textDecoration !== 'none') {
        score += 5;
        reason.push('has-text-decoration');
      }
    } catch (e) {
      // 忽略样式获取错误
    }
  }

  // 6. 检查属性
  if (element.hasAttribute('contenteditable')) {
    score += 40; // 已经是contenteditable的元素优先级高
    reason.push('already-contenteditable');
  }
  
  if (element.hasAttribute('data-editable-fengmian')) {
    score += 40; // 已标记为可编辑的元素
    reason.push('already-marked-editable');
  }
  
  // 7. 识别不应该可编辑的元素
  // 表单元素、按钮等不应该是可编辑的
  if (['button', 'input', 'select', 'textarea', 'iframe'].includes(tagName)) {
    score -= 50;
    reason.push('non-editable-tag');
  }
  
  // 检查特定的类名或样式，排除不应编辑的元素
  if (className && (
    typeof className === 'string' && (
      className.includes('button') || 
      className.includes('btn') || 
      className.includes('nav') || 
      className.includes('menu') || 
      className.includes('icon') ||
      className.includes('wrapper') ||
      className.includes('container')
    ))) {
    score -= 25;
    reason.push('non-editable-class');
  }

  // 8. 修正特殊情况
  // 链接元素，看情况决定是否可编辑
  if (tagName === 'a') {
    // 如果链接包含丰富的内容，可能是文本块而非单纯的链接
    if (element.innerHTML.includes('<img') || element.children.length > 1) {
      score -= 15;
      reason.push('complex-link');
    } else {
      // 简单链接，可以作为文本元素
      score += 10;
      reason.push('simple-link');
    }
  }
  
  // 图片元素单独处理
  if (tagName === 'img') {
    score = 40; // 提高图片元素的评分，确保它们可以被选择和拖拽
    reason = ['image-element'];
  }
  
  // 图片容器特殊处理 - 检查是否包含图片的div或span
  if ((tagName === 'div' || tagName === 'span') && element.querySelector('img')) {
    score += 50; // 大幅提高包含图片的容器的评分
    reason.push('image-container');
    
    // 如果容器只包含一个图片元素，进一步提高评分
    const imgElements = element.querySelectorAll('img');
    if (imgElements.length === 1 && element.childElementCount === 1) {
      score += 20; // 单图片容器，几乎肯定是可拖拽元素
      reason.push('single-image-container');
    }
  }
  
  // 如果元素已经有data-image-container属性，确保它被识别为可编辑
  if (element.hasAttribute('data-image-container')) {
    score += 60; // 极高优先级，确保被标记为图片容器的元素可编辑
    reason.push('marked-image-container');
  }

  // 确定是否可能是可编辑元素，提高评分门槛
  const isEditable = score >= 25;

  return {
    element,
    isEditable,
    score,
    reason: reason.join(','),
    tagName,
    hasDirectText: Array.from(element.childNodes).some(n => n.nodeType === Node.TEXT_NODE && n.textContent.trim().length > 0),
    isImageContainer: tagName === 'img' || element.querySelector('img') !== null || element.hasAttribute('data-image-container')
  };
};

/**
 * 查找容器中所有潜在的可编辑元素
 * @param {Element} container - 容器元素
 * @return {Array} 潜在可编辑元素数组
 */
export const findPotentialEditableElements = (container) => {
  if (!container) return [];

  // 获取所有元素
  const allElements = Array.from(container.querySelectorAll('*'));
  
  // 分析每个元素
  const analysisResults = allElements.map(el => ({
    element: el,
    analysis: analyzeElement(el)
  })).filter(result => result.analysis.isEditable);
  
  // 按评分排序，评分高的优先
  analysisResults.sort((a, b) => b.analysis.score - a.analysis.score);
  
  // 智能过滤过程，保留文本元素，但过滤明显的容器
  const textTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'a', 'label'];
  const containerTags = ['div', 'section', 'article', 'main', 'footer', 'header', 'aside'];
  
  // 新的过滤逻辑，智能保留文本元素
  const filteredResults = [];
  const addedElements = new Set(); // 用于跟踪已添加的元素
  
  for (const result of analysisResults) {
    const element = result.element;
    const tagName = element.tagName.toLowerCase();
    
    // 1. 已经在集合中的元素跳过
    if (addedElements.has(element)) continue;
    
    // 2. 对于标题、段落等文本元素，直接添加，即使它们嵌套在其他元素中
    if (textTags.includes(tagName) || result.analysis.hasDirectText) {
      filteredResults.push(result);
      addedElements.add(element);
      continue;
    }
    
    // 3. 对于容器元素，检查它们是否与已添加的文本元素有嵌套关系
    if (containerTags.includes(tagName)) {
      // 检查是否包含已添加的文本元素
      let containsAddedTextElement = false;
      for (const addedElement of addedElements) {
        if (element.contains(addedElement) && element !== addedElement) {
          containsAddedTextElement = true;
          break;
        }
      }
      
      // 如果包含已添加的文本元素，跳过这个容器
      if (containsAddedTextElement) continue;
      
      // 检查是否是另一个容器的子元素
      let isNestedInContainer = false;
      for (const existingResult of filteredResults) {
        const existingElement = existingResult.element;
        const existingTagName = existingElement.tagName.toLowerCase();
        
        // 如果是嵌套在其他容器中的容器，跳过
        if (containerTags.includes(existingTagName) && 
            existingElement.contains(element) && 
            existingElement !== element) {
          isNestedInContainer = true;
          break;
        }
      }
      
      // 如果嵌套在其他容器中，跳过这个容器
      if (isNestedInContainer) continue;
    }
    
    // 4. 其他元素，添加到结果中
    filteredResults.push(result);
    addedElements.add(element);
  }
  
  // 特别处理SVG元素
  const svgElements = Array.from(container.querySelectorAll('svg, svg text, svg path, svg g'));
  const svgResults = svgElements.map(el => ({
    element: el,
    analysis: analyzeElement(el)
  })).filter(result => result.analysis.score >= 25);
  
  // 合并SVG元素结果
  svgResults.forEach(result => {
    if (!addedElements.has(result.element)) {
      filteredResults.push(result);
      addedElements.add(result.element);
    }
  });
  
  return filteredResults.map(result => result.element);
};

/**
 * 增强版查找潜在可编辑元素
 * 集成高级元素检测功能，支持复杂页面结构
 * @param {Element} container - 容器元素
 * @param {Object} options - 检测选项
 * @returns {Array} 潜在可编辑元素数组
 */
export const findPotentialEditableElementsAdvanced = (container, options = {}) => {
  if (!container) return [];

  const {
    includeNavigation = false,
    includeForm = false,
    includeMedia = true,
    includeInteractive = false,
    useIntelligentRecommendation = true
  } = options;

  // 使用智能推荐系统
  if (useIntelligentRecommendation) {
    const recommendations = recommendEditableElements(container);

    // 根据选项过滤结果
    const filteredRecommendations = recommendations.filter(result => {
      const { primaryType } = result;

      if (primaryType === ELEMENT_TYPES.NAVIGATION && !includeNavigation) return false;
      if (primaryType === ELEMENT_TYPES.FORM && !includeForm) return false;
      if (primaryType === ELEMENT_TYPES.MEDIA && !includeMedia) return false;
      if (primaryType === ELEMENT_TYPES.INTERACTIVE && !includeInteractive) return false;

      return true;
    });

    return filteredRecommendations.map(result => result.element);
  }

  // 回退到原有的检测方法
  return findPotentialEditableElements(container);
};

/**
 * 分析页面复杂度
 * 为编辑策略提供页面结构信息
 * @param {Element} container - 容器元素
 * @returns {Object} 页面复杂度分析结果
 */
export const analyzePageComplexity = (container) => {
  if (!container) return { complexity: 'simple', recommendations: [] };

  const hierarchy = analyzeElementHierarchy(container);
  const recommendations = [];

  // 基于复杂度提供建议
  if (hierarchy.complexity === 'complex') {
    recommendations.push('建议使用智能推荐模式');
    recommendations.push('考虑分区域编辑');
  } else if (hierarchy.complexity === 'medium') {
    recommendations.push('可以使用标准编辑模式');
  } else {
    recommendations.push('适合全面编辑');
  }

  // 基于元素类型提供建议
  if (hierarchy.elementTypes.navigation > 0) {
    recommendations.push('检测到导航元素，建议谨慎编辑');
  }

  if (hierarchy.elementTypes.form > 0) {
    recommendations.push('检测到表单元素，建议保持功能性');
  }

  if (hierarchy.elementTypes.media > 0) {
    recommendations.push('检测到媒体元素，支持拖拽和缩放');
  }

  return {
    ...hierarchy,
    recommendations,
    editingMode: hierarchy.complexity === 'complex' ? 'intelligent' : 'standard'
  };
};

/**
 * 检测结果缓存系统
 */
class DetectionCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 100;
    this.ttl = 5 * 60 * 1000; // 5分钟TTL
  }

  /**
   * 生成缓存键
   * @param {Element} element - 元素
   * @returns {string} 缓存键
   */
  generateKey(element) {
    if (!element) return null;

    const tagName = element.tagName;
    const className = element.className || '';
    const id = element.id || '';
    const textContent = element.textContent ? element.textContent.substring(0, 50) : '';

    return `${tagName}-${className}-${id}-${textContent}`.replace(/\s+/g, '-');
  }

  /**
   * 获取缓存结果
   * @param {Element} element - 元素
   * @returns {Object|null} 缓存的检测结果
   */
  get(element) {
    const key = this.generateKey(element);
    if (!key) return null;

    const cached = this.cache.get(key);
    if (!cached) return null;

    // 检查TTL
    if (Date.now() - cached.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.result;
  }

  /**
   * 设置缓存结果
   * @param {Element} element - 元素
   * @param {Object} result - 检测结果
   */
  set(element, result) {
    const key = this.generateKey(element);
    if (!key) return;

    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      result,
      timestamp: Date.now()
    });
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0
    };
  }
}

// 创建全局缓存实例
const detectionCache = new DetectionCache();

/**
 * 带缓存的元素分析
 * @param {Element} element - 要分析的元素
 * @returns {Object} 分析结果
 */
export const analyzeElementWithCache = (element) => {
  if (!element) return null;

  // 尝试从缓存获取
  const cached = detectionCache.get(element);
  if (cached) {
    return cached;
  }

  // 执行分析
  const result = analyzeElement(element);

  // 缓存结果
  detectionCache.set(element, result);

  return result;
};

/**
 * 清空检测缓存
 * 在页面内容发生重大变化时调用
 */
export const clearDetectionCache = () => {
  detectionCache.clear();
};

/**
 * 为元素添加可编辑标记
 * @param {Array|Element} elements - 要标记的元素或元素数组
 */
export const markEditableElements = (elements) => {
  if (!elements) return;
  
  // 转换为数组处理
  const elementsArray = Array.isArray(elements) ? elements : [elements];
  
  elementsArray.forEach(element => {
    if (!element || !element.setAttribute) return;
    
    // 检查是否是图片或图片容器
    const isImg = element.tagName.toLowerCase() === 'img';
    const hasImg = element.querySelector('img') !== null;
    const isImageContainer = isImg || hasImg || element.hasAttribute('data-image-container');
    
    element.setAttribute('data-editable-fengmian', 'true');
    element.setAttribute('contenteditable', 'true');
    
    // 为图片容器添加特殊标记
    if (isImageContainer) {
      element.setAttribute('data-image-container', 'true');
      
      // 确保图片容器可以接收鼠标事件
      element.style.pointerEvents = 'auto';
      
      // 确保图片元素可以接收鼠标事件
      if (hasImg) {
        const imgElement = element.querySelector('img');
        if (imgElement) {
          imgElement.style.pointerEvents = 'auto';
          
          // 确保图片填充容器
          imgElement.style.width = '100%';
          imgElement.style.height = '100%';
          imgElement.style.objectFit = 'contain';
        }
      }
      
      // 设置图片容器的鼠标样式为移动
      element.style.cursor = 'move';
    }
    
    // 添加双击编辑提示
    if (!element.hasAttribute('title')) {
      element.setAttribute('title', isImageContainer ? '点击拖拽图片' : '双击编辑文本');
    }
  });
};

/**
 * 分析HTML内容并标记可编辑元素
 * @param {string} htmlString - HTML字符串
 * @return {string} 处理后的HTML字符串
 */
export const analyzeHtmlContent = (htmlString) => {
  if (!htmlString) return htmlString;

  try {
    // 解析HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    
    // 查找所有可编辑元素，从body的直接子元素开始
    const container = doc.body;
    const editableElements = findPotentialEditableElements(container);
    
    // 标记可编辑元素
    markEditableElements(editableElements);
    
    // 如果没有找到可编辑元素，寻找可包装的文本节点
    if (editableElements.length === 0) {
      // 查找具有意义文本的文本节点
      const wrapTextNodes = (parent) => {
        const childNodes = Array.from(parent.childNodes);
        
        for (let i = 0; i < childNodes.length; i++) {
          const node = childNodes[i];
          
          // 如果是文本节点且包含有意义的文本
          if (node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0) {
            // 创建一个span元素包裹文本
            const span = doc.createElement('span');
            span.setAttribute('data-editable-fengmian', 'true');
            span.setAttribute('contenteditable', 'true');
            span.textContent = node.textContent;
            
            // 替换原始节点
            parent.replaceChild(span, node);
          } 
          // 如果是元素节点，递归处理
          else if (node.nodeType === Node.ELEMENT_NODE) {
            wrapTextNodes(node);
          }
        }
      };
      
      // 从body开始包装文本节点
      wrapTextNodes(container);
    }
    
    return doc.documentElement.outerHTML;
    
  } catch (error) {
    console.error('分析HTML内容失败:', error);
    return htmlString;
  }
}; 
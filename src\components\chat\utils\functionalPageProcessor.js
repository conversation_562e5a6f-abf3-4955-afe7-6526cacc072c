/**
 * 功能性静态页面处理器
 * 专门处理带有功能按钮、交互的、多页面的、多导航的静态网页
 * 确保上传后不会破坏丢失原有功能，同时支持编辑台的所有功能
 */

import logger from '../../../services/logs/frontendLogger';
import { isFeatureEnabled } from './featureConfig';

/**
 * 页面类型检测结果枚举
 */
export const PAGE_TYPES = {
  SIMPLE: 'simple',           // 简单页面，基础HTML片段
  FUNCTIONAL: 'functional',   // 功能性页面，包含交互元素
  MULTI_PAGE: 'multi_page',   // 多页面应用，包含导航
  COMPLEX: 'complex'          // 复杂页面，包含多种功能
};

/**
 * 检测页面类型和功能特征
 * @param {string} htmlContent - HTML内容
 * @returns {Object} 页面类型和特征分析结果
 */
export const detectPageType = (htmlContent) => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return { type: PAGE_TYPES.SIMPLE, features: [], score: 0 };
  }

  const features = [];
  let score = 0;

  try {
    // 检测JavaScript功能
    const scriptMatches = htmlContent.match(/<script[^>]*>[\s\S]*?<\/script>/gi) || [];
    if (scriptMatches.length > 0) {
      score += scriptMatches.length * 3;
      features.push(`${scriptMatches.length}个脚本标签`);
      
      // 检测事件处理器
      const eventHandlers = htmlContent.match(/on\w+\s*=\s*["'][^"']*["']/gi) || [];
      if (eventHandlers.length > 0) {
        score += eventHandlers.length * 2;
        features.push(`${eventHandlers.length}个事件处理器`);
      }
    }

    // 检测交互元素
    const interactiveElements = htmlContent.match(/<(button|input|select|textarea|a)[^>]*>/gi) || [];
    if (interactiveElements.length > 0) {
      score += interactiveElements.length;
      features.push(`${interactiveElements.length}个交互元素`);
    }

    // 检测表单
    const formMatches = htmlContent.match(/<form[^>]*>[\s\S]*?<\/form>/gi) || [];
    if (formMatches.length > 0) {
      score += formMatches.length * 4;
      features.push(`${formMatches.length}个表单`);
    }

    // 检测导航元素
    const navElements = htmlContent.match(/<nav[^>]*>[\s\S]*?<\/nav>/gi) || [];
    const navLinks = htmlContent.match(/<a[^>]*href\s*=\s*["'][^"']*["'][^>]*>/gi) || [];
    if (navElements.length > 0 || navLinks.length > 5) {
      score += Math.max(navElements.length * 3, Math.min(navLinks.length, 10));
      features.push(`导航系统(${navElements.length}个nav标签, ${navLinks.length}个链接)`);
    }

    // 检测多媒体元素
    const mediaElements = htmlContent.match(/<(video|audio|canvas|svg|iframe)[^>]*>/gi) || [];
    if (mediaElements.length > 0) {
      score += mediaElements.length * 2;
      features.push(`${mediaElements.length}个多媒体元素`);
    }

    // 检测CSS框架
    const cssFrameworks = [];
    if (htmlContent.includes('bootstrap')) cssFrameworks.push('Bootstrap');
    if (htmlContent.includes('tailwind')) cssFrameworks.push('Tailwind');
    if (htmlContent.includes('foundation')) cssFrameworks.push('Foundation');
    if (cssFrameworks.length > 0) {
      score += cssFrameworks.length * 2;
      features.push(`CSS框架: ${cssFrameworks.join(', ')}`);
    }

    // 检测单页应用特征
    const spaFeatures = [];
    if (htmlContent.includes('router') || htmlContent.includes('Router')) spaFeatures.push('路由');
    if (htmlContent.includes('vue') || htmlContent.includes('Vue')) spaFeatures.push('Vue.js');
    if (htmlContent.includes('react') || htmlContent.includes('React')) spaFeatures.push('React');
    if (htmlContent.includes('angular') || htmlContent.includes('Angular')) spaFeatures.push('Angular');
    if (spaFeatures.length > 0) {
      score += spaFeatures.length * 5;
      features.push(`SPA特征: ${spaFeatures.join(', ')}`);
    }

    // 根据分数确定页面类型
    let type;
    if (score <= 3) {
      type = PAGE_TYPES.SIMPLE;
    } else if (score <= 10) {
      type = PAGE_TYPES.FUNCTIONAL;
    } else if (score <= 20) {
      type = PAGE_TYPES.MULTI_PAGE;
    } else {
      type = PAGE_TYPES.COMPLEX;
    }

    return { type, features, score };

  } catch (error) {
    logger.error('页面类型检测失败', { error: error.message });
    return { type: PAGE_TYPES.SIMPLE, features: [], score: 0 };
  }
};

/**
 * 处理功能性页面的HTML内容
 * @param {string} htmlContent - 原始HTML内容
 * @param {Object} pageInfo - 页面信息
 * @returns {string} 处理后的HTML内容
 */
export const processFunctionalPage = (htmlContent, pageInfo) => {
  try {
    let processedContent = htmlContent;

    // 1. 保护JavaScript功能
    processedContent = protectJavaScriptFunctionality(processedContent);

    // 2. 处理相对路径
    processedContent = resolveRelativePaths(processedContent);

    // 3. 注入编辑器兼容性代码
    processedContent = injectEditorCompatibility(processedContent, pageInfo);

    // 4. 优化CSS选择器
    processedContent = optimizeCssSelectors(processedContent);

    return processedContent;

  } catch (error) {
    logger.error('处理功能性页面失败', { error: error.message });
    return htmlContent; // 返回原始内容作为降级
  }
};

/**
 * 保护JavaScript功能
 * @param {string} htmlContent - HTML内容
 * @returns {string} 处理后的内容
 */
const protectJavaScriptFunctionality = (htmlContent) => {
  try {
    // 保护事件处理器
    let processedContent = htmlContent.replace(
      /(<[^>]*\s)(on\w+\s*=\s*["'][^"']*["'])([^>]*>)/gi,
      '$1data-original-$2 $2$3'
    );

    // 保护内联脚本
    processedContent = processedContent.replace(
      /<script([^>]*)>([\s\S]*?)<\/script>/gi,
      (match, attrs, content) => {
        // 添加数据属性标记原始脚本
        return `<script${attrs} data-functional-script="true">${content}</script>`;
      }
    );

    return processedContent;
  } catch (error) {
    logger.error('保护JavaScript功能失败', { error: error.message });
    return htmlContent;
  }
};

/**
 * 解析相对路径
 * @param {string} htmlContent - HTML内容
 * @returns {string} 处理后的内容
 */
const resolveRelativePaths = (htmlContent) => {
  try {
    // 处理相对路径的图片
    let resolved = htmlContent.replace(
      /src\s*=\s*["'](?!https?:\/\/|data:|\/\/)([^"']+)["']/gi,
      (match, path) => {
        // 对于相对路径，添加标记以便后续处理
        return `src="${path}" data-original-src="${path}"`;
      }
    );

    // 处理相对路径的CSS
    resolved = resolved.replace(
      /href\s*=\s*["'](?!https?:\/\/|\/\/)([^"']+\.css)["']/gi,
      (match, path) => {
        return `href="${path}" data-original-href="${path}"`;
      }
    );

    return resolved;
  } catch (error) {
    logger.error('解析相对路径失败', { error: error.message });
    return htmlContent;
  }
};

/**
 * 注入编辑器兼容性代码
 * @param {string} htmlContent - HTML内容
 * @param {Object} pageInfo - 页面信息
 * @returns {string} 处理后的内容
 */
const injectEditorCompatibility = (htmlContent, pageInfo) => {
  try {
    // 检查是否需要注入兼容性代码
    if (pageInfo.type === PAGE_TYPES.SIMPLE) {
      return htmlContent;
    }

    // 注入编辑器兼容性脚本
    const compatibilityScript = `
<script data-editor-compatibility="true">
(function() {
  'use strict';
  
  // 编辑器兼容性管理器
  window.EditorCompatibilityManager = {
    isEditorMode: function() {
      return window.parent !== window && window.parent.document.querySelector('.chat-preview-container');
    },
    
    preserveOriginalHandlers: function() {
      // 保存原始事件处理器
      const elements = document.querySelectorAll('[data-original-onclick], [data-original-onchange]');
      elements.forEach(el => {
        Object.keys(el.dataset).forEach(key => {
          if (key.startsWith('original')) {
            const eventName = key.replace('original', '').toLowerCase();
            const handler = el.dataset[key];
            try {
              el[eventName] = new Function('event', handler);
            } catch (e) {
              console.warn('无法恢复事件处理器:', e);
            }
          }
        });
      });
    },
    
    init: function() {
      if (this.isEditorMode()) {
        // 在编辑器模式下，保持功能性但允许编辑
        this.preserveOriginalHandlers();
        
        // 添加编辑器样式兼容性
        const style = document.createElement('style');
        style.textContent = \`
          /* 编辑器兼容性样式 */
          [contenteditable="true"] {
            outline: 2px dashed #007bff !important;
            outline-offset: 2px !important;
          }
          [contenteditable="true"]:focus {
            outline: 2px solid #007bff !important;
          }
        \`;
        document.head.appendChild(style);
      }
    }
  };
  
  // 页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      window.EditorCompatibilityManager.init();
    });
  } else {
    window.EditorCompatibilityManager.init();
  }
})();
</script>`;

    // 在head标签结束前注入脚本
    if (htmlContent.includes('</head>')) {
      return htmlContent.replace('</head>', compatibilityScript + '\n</head>');
    } else {
      // 如果没有head标签，在body开始后注入
      return htmlContent.replace('<body>', '<body>' + compatibilityScript);
    }

  } catch (error) {
    logger.error('注入编辑器兼容性代码失败', { error: error.message });
    return htmlContent;
  }
};

/**
 * 优化CSS选择器
 * @param {string} htmlContent - HTML内容
 * @returns {string} 处理后的内容
 */
const optimizeCssSelectors = (htmlContent) => {
  try {
    // 处理style标签中的CSS
    return htmlContent.replace(
      /<style([^>]*)>([\s\S]*?)<\/style>/gi,
      (match, attrs, css) => {
        // 优化CSS，确保不与编辑器样式冲突
        const optimizedCSS = css
          .replace(/(\s|^)body(\s|{)/g, '$1.functional-page-body$2')
          .replace(/(\s|^)html(\s|{)/g, '$1.functional-page-html$2');
        
        return `<style${attrs}>${optimizedCSS}</style>`;
      }
    );
  } catch (error) {
    logger.error('优化CSS选择器失败', { error: error.message });
    return htmlContent;
  }
};

/**
 * 为功能性页面设置iframe环境
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {Object} pageInfo - 页面信息
 * @returns {boolean} 是否设置成功
 */
export const setupFunctionalPageEnvironment = (iframe, pageInfo) => {
  try {
    if (!iframe || !iframe.contentWindow) {
      return false;
    }

    const iframeWindow = iframe.contentWindow;
    const iframeDoc = iframe.contentDocument;

    // 设置功能性页面的特殊属性
    if (iframeDoc.body) {
      iframeDoc.body.classList.add('functional-page-body');
      iframeDoc.body.setAttribute('data-page-type', pageInfo.type);
    }

    if (iframeDoc.documentElement) {
      iframeDoc.documentElement.classList.add('functional-page-html');
    }

    // 设置安全的sandbox权限
    if (pageInfo.type !== PAGE_TYPES.SIMPLE) {
      iframe.sandbox = 'allow-scripts allow-same-origin allow-forms allow-downloads allow-modals';
    }

    return true;

  } catch (error) {
    logger.error('设置功能性页面环境失败', { error: error.message });
    return false;
  }
};

export default {
  detectPageType,
  processFunctionalPage,
  setupFunctionalPageEnvironment,
  PAGE_TYPES
};

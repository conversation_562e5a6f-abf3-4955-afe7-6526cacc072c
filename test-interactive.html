<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试交互功能</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .result { margin: 20px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>交互功能测试页面</h1>
    <button class="button" onclick="showAlert()">点击测试</button>
    <button class="button" id="eventBtn">事件监听测试</button>
    <div class="result" id="result">等待测试...</div>

    <script>
        function showAlert() {
            document.getElementById("result").innerHTML = "onclick 事件成功执行！";
            alert("JavaScript 交互功能正常！");
        }

        document.getElementById("eventBtn").addEventListener("click", function() {
            document.getElementById("result").innerHTML = "addEventListener 事件成功执行！";
            console.log("事件监听器正常工作");
        });

        document.addEventListener("DOMContentLoaded", function() {
            console.log("页面加载完成，JavaScript正常执行");
        });
    </script>
</body>
</html>

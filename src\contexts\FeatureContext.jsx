import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { checkFeatureAvailability, batchCheckFeatures } from '../services/featureService';

// 创建上下文
const FeatureContext = createContext(null);

// 需要缓存的功能权限列表，扩展为包含所有需要缓存的权限
const FEATURE_NAMES = ['查看源码', 'HTML下载', '分享链接', '文本编辑', '源代码查看'];

// 缓存过期时间（与apiCacheService保持一致）
const CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5分钟

export const FeatureProvider = ({ children }) => {
  // 权限信息的状态
  const [features, setFeatures] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 初始化和刷新权限缓存的函数
  const refreshFeatures = async () => {
    try {
      setLoading(true);
      
      // 使用批量检查功能，提高效率
      const featuresCache = await batchCheckFeatures(FEATURE_NAMES);
      
      setFeatures(featuresCache);
      setError(null);
    } catch (error) {
      console.error('初始化权限缓存失败:', error);
      setError('获取权限信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时初始化权限缓存
  useEffect(() => {
    refreshFeatures();
    
    // 定期刷新权限缓存（5分钟，与其他缓存保持一致）
    const refreshInterval = setInterval(() => {
      refreshFeatures();
    }, CACHE_EXPIRY_TIME);
    
    return () => clearInterval(refreshInterval);
  }, []);

  // 检查权限的函数
  const checkFeature = async (featureName) => {
    // 如果已缓存，直接返回
    if (features[featureName]) {
      return features[featureName];
    }
    
    // 否则重新获取
    try {
      const result = await checkFeatureAvailability(featureName);
      // 更新缓存
      setFeatures(prev => ({
        ...prev,
        [featureName]: result
      }));
      return result;
    } catch (error) {
      console.error(`检查${featureName}权限失败:`, error);
      return { available: false, reason: '检查权限失败' };
    }
  };
  
  // 使用useMemo避免不必要的重新渲染
  const contextValue = useMemo(() => ({
    features,
    checkFeature,
    loading,
    error,
    refreshFeatures
  }), [features, loading, error]);
  
  return (
    <FeatureContext.Provider value={contextValue}>
      {children}
    </FeatureContext.Provider>
  );
};

// 自定义hook便于组件使用
export const useFeatures = () => {
  const context = useContext(FeatureContext);
  if (!context) {
    throw new Error('useFeatures必须在FeatureProvider内使用');
  }
  return context;
};

export default FeatureContext; 
import React, { createContext, useContext } from 'react';
import useAuth from '../hooks/useAuth';
import { clearFeatureCache } from '../services/featureService';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const authValues = useAuth();
  return <AuthContext.Provider value={authValues}>{children}</AuthContext.Provider>;
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (context === undefined || context === null) {
    console.warn('useAuthContext was called outside of AuthProvider or authValues is null. Ensure App is wrapped in AuthProvider and useAuth provides valid initial state.');
    return {
      user: null,
      isLoggedIn: false,
      isLoading: true,
      checkLoginStatus: () => {},
      getOperationCost: async () => 0,
      checkPoints: async () => ({ hasEnough: false, message: 'Auth context not available' }),
      confirmAuthAndPoints: async () => {},
      updateUserPoints: () => {},
      refreshUserInfo: async () => {},
      checkTokenValidity: async () => false,
      logout: () => { console.warn('Logout called on uninitialized AuthContext'); } // Default logout added
    };
  }
  return context;
};

export default AuthContext;

const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const coverController = require('../controllers/coverController');
const aiServiceController = require('../controllers/aiServiceController');
const logController = require('../controllers/logController');
const taskController = require('../controllers/taskController');
const { auth, adminAuth } = require('../middlewares/authMiddleware');
const { validate, schemas } = require('../utils/validator');
const upload = require('../middlewares/uploadMiddleware');

// 所有管理员路由都需要认证和管理员权限
router.use(auth);
router.use(adminAuth);

// 数据统计
router.get('/statistics', adminController.getStatistics);
router.get('/stats', adminController.getDashboardStats);
router.get('/users/recent', adminController.getRecentUsers);
router.get('/covers/recent', adminController.getRecentCovers);

// 用户管理
router.get('/users', adminController.getUsersList);
router.post('/users', adminController.createUser);
router.get('/users/:id', adminController.getUserDetail);
router.put('/users/:id', adminController.updateUser);
router.post('/users/:id/reset-password', adminController.resetUserPassword);
router.put('/users/:id/status', adminController.updateUserStatus);
router.delete('/users/:id', adminController.deleteUser);

// 风格提示词模板管理
router.get('/style-prompts', adminController.getStylePrompts);
router.get('/style-prompts/:id', adminController.getStylePromptById);
router.post('/style-prompts', upload.single('preview_image'), adminController.createStylePrompt);
router.put('/style-prompts/:id', upload.single('preview_image'), adminController.updateStylePrompt);
router.delete('/style-prompts/:id', adminController.deleteStylePrompt);

// 积分记录管理
router.get('/point-records', adminController.getPointRecordsList);

// 封面记录管理
router.get('/cover-records', coverController.getAdminCoverRecords);
router.get('/cover-records/:id', coverController.getCoverDetail);
router.delete('/cover-records/batch', coverController.batchDeleteCoverRecords);
router.patch('/cover-records/batch/status', coverController.batchUpdateCoverStatus);
router.patch('/cover-records/:id/status', coverController.updateCoverStatus);
router.delete('/cover-records/:id', coverController.deleteCoverRecord);

// 封面管理
router.get('/covers', adminController.getCoversList);
router.get('/covers/:id', adminController.getCoverDetail);
router.delete('/covers/:id', adminController.deleteCover);

// 基础提示词模板管理
router.get('/base-prompts', adminController.getBasePrompts);
router.get('/base-prompts/:id', adminController.getBasePromptById);
router.post('/base-prompts', validate(schemas.basePrompt), adminController.createBasePrompt);
router.put('/base-prompts/:id', validate(schemas.basePrompt), adminController.updateBasePrompt);
router.delete('/base-prompts/:id', adminController.deleteBasePrompt);

// 系统配置管理
router.get('/configs', adminController.getSystemConfigs);
router.put('/configs/:key', adminController.updateSystemConfig);
router.delete('/configs/:key', adminController.deleteSystemConfig);

// 政策管理
router.put('/policies/privacy', adminController.updatePrivacyPolicy);
router.put('/policies/agreement', adminController.updateUserAgreement);

// 系统设置路由 - 为前端兼容添加
router.get('/settings', adminController.getSystemConfigs); // 映射到getSystemConfigs
router.post('/settings/system', (req, res) => {
  // 将前端的系统设置请求转换为配置更新
  const { new_user_points, cover_points_cost, verify_code_storage_type } = req.body;

  // 更新new_user_points配置
  if (new_user_points !== undefined) {
    adminController.updateSystemConfig({
      ...req,
      params: { key: 'new_user_points' },
      body: { config_value: String(new_user_points) }
    }, res);
    return; // 由updateSystemConfig处理响应
  }

  // 更新cover_points_cost配置
  if (cover_points_cost !== undefined) {
    adminController.updateSystemConfig({
      ...req,
      params: { key: 'cover_points_cost' },
      body: { config_value: String(cover_points_cost) }
    }, res);
    return; // 由updateSystemConfig处理响应
  }

  // 更新verify_code_storage_type配置
  if (verify_code_storage_type !== undefined) {
    adminController.updateSystemConfig({
      ...req,
      params: { key: 'verify_code_storage_type' },
      body: { config_value: verify_code_storage_type }
    }, res);
    return; // 由updateSystemConfig处理响应
  }

  // 如果没有有效参数
  res.status(400).json({
    success: false,
    message: '请提供有效的系统设置参数'
  });
});

// 网站信息设置
router.post('/settings/site', (req, res) => {
  // 将前端的网站信息设置请求转换为配置更新
  const { site_name, site_description, contact_email, icp } = req.body;
  const updates = [];

  // 收集所有需要更新的配置
  if (site_name !== undefined) {
    updates.push({ key: 'site_name', value: site_name });
  }
  if (site_description !== undefined) {
    updates.push({ key: 'site_description', value: site_description });
  }
  if (contact_email !== undefined) {
    updates.push({ key: 'contact_email', value: contact_email });
  }
  if (icp !== undefined) {
    updates.push({ key: 'icp', value: icp });
  }

  // 如果没有有效参数
  if (updates.length === 0) {
    return res.status(400).json({
      success: false,
      message: '请提供有效的网站信息参数'
    });
  }

  // 执行所有更新
  Promise.all(updates.map(update => {
    return new Promise((resolve, reject) => {
      adminController.updateSystemConfig({
        ...req,
        params: { key: update.key },
        body: { config_value: update.value }
      }, {
        json: (data) => resolve(data),
        status: (code) => ({
          json: (data) => resolve({ ...data, statusCode: code })
        })
      });
    });
  }))
  .then(results => {
    // 检查是否所有更新都成功
    const allSuccess = results.every(result => result.success);

    if (allSuccess) {
      res.json({
        success: true,
        message: '网站信息更新成功'
      });
    } else {
      // 找出失败的更新
      const failedUpdates = results
        .filter(result => !result.success)
        .map(result => result.message)
        .join('; ');

      res.status(500).json({
        success: false,
        message: `部分网站信息更新失败: ${failedUpdates}`
      });
    }
  })
  .catch(error => {
    console.error('网站信息更新失败:', error);
    res.status(500).json({
      success: false,
      message: '网站信息更新失败: ' + error.message
    });
  });
});

// AI服务管理
router.get('/ai-services', aiServiceController.getAllAIServices);

// 获取所有AI服务商名称
router.get('/ai-services/providers', aiServiceController.getServiceProviders);

// AI参数配置接口
router.get('/ai-services/:id/parameters', aiServiceController.getServiceParameters);
router.put('/ai-services/:id/parameters', aiServiceController.updateServiceParameters);

// 获取当前激活的AI服务
router.get('/ai-services/active', aiServiceController.getActiveAIService);
// API密钥池管理
router.get('/ai-services/key-pool', aiServiceController.getKeyPoolStatus);
router.post('/ai-services/refresh-key-pool', aiServiceController.refreshKeyPool);

// 批量更新AI服务状态
router.put('/ai-services/batch-update', aiServiceController.batchUpdateStatus);

// 具体AI服务操作
router.get('/ai-services/:id', aiServiceController.getAIService);
router.post('/ai-services', aiServiceController.createAIService);
router.put('/ai-services/:id', aiServiceController.updateAIService);
router.delete('/ai-services/:id', aiServiceController.deleteAIService);
// 启用/停用AI服务
router.put('/ai-services/:id/activate', aiServiceController.activateAIService);
// 测试AI服务连接
router.post('/ai-services/:id/test', aiServiceController.testAIService);
// AI聊天测试
router.post('/ai-services/:id/chat-test', aiServiceController.chatTestAIService);

// 任务管理
router.get('/tasks', taskController.getTaskList);
router.get('/tasks/:id', taskController.getTaskDetail);
router.post('/tasks/:id/cancel', taskController.cancelTask);
router.delete('/tasks/:id', taskController.deleteTask);
router.post('/tasks/cleanup', taskController.cleanupTasks);

// 系统日志管理
router.get('/logs', logController.getLogs);
router.get('/logs/stats', logController.getLogStats);
router.get('/logs/:id', logController.getLogDetail);
router.delete('/logs/cleanup', logController.cleanupLogs);
router.post('/logs/test', logController.createTestLogs);

// HTML处理配置管理
router.get('/config/rendering-mode', adminController.getRenderingModeConfig);
router.post('/config/rendering-mode', adminController.setRenderingModeConfig);
router.get('/config/security-rules', adminController.getSecurityRulesConfig);
router.post('/config/security-rules', adminController.updateSecurityRulesConfig);

// 安全管理
router.get('/security/violations', adminController.getSecurityViolations);
router.get('/security/statistics', adminController.getSecurityStatistics);
router.delete('/security/violations/:id', adminController.deleteSecurityViolation);
router.post('/security/violations/cleanup', adminController.cleanupSecurityViolations);

// 安全检查记录
router.get('/security/check-records', adminController.getSecurityCheckRecords);
router.get('/security/check-records/:id', adminController.getSecurityCheckRecordDetail);
router.post('/security/check-records/batch-delete', adminController.batchDeleteSecurityCheckRecords);

module.exports = router;

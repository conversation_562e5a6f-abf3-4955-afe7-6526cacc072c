import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Upload, X, Eye, Save, AlertTriangle } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { message, Modal } from 'antd';
import axios from 'axios';
import { getCurrentUser } from '../../services/authService';
import { SecurityDetector } from '../../utils/securityDetector.js';
import logger from '../../services/logs/frontendLogger.js';
import systemConfigManager from '../../utils/systemConfigManager.js';
import RenderingModeSelector, { RENDERING_MODES } from '../../utils/renderingModeSelector.js';
import { ServerSideRenderer } from '../../utils/serverSideRenderer.js';

/**
 * 简单的HTML清理函数，移除可能的危险标签和属性
 * @param {string} html HTML内容
 * @returns {string} 清理后的HTML
 */
const sanitizeHtml = (html) => {
  // 移除script标签
  let sanitized = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // 移除iframe标签
  sanitized = sanitized.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '');
  
  // 移除事件处理属性
  sanitized = sanitized.replace(/\son\w+\s*=\s*(['"]).*?\1/gi, '');
  
  // 移除javascript:协议
  sanitized = sanitized.replace(/href\s*=\s*(['"])javascript:.*?\1/gi, 'href="javascript:void(0)"');
  
  return sanitized;
};

/**
 * 文件上传组件 - 允许用户上传HTML文件并预览
 */
const FileUploadView = () => {
  const navigate = useNavigate();
  const fileInputRef = useRef(null);
  const previewIframeRef = useRef(null);
  
  const [file, setFile] = useState(null);
  const [fileName, setFileName] = useState('');
  const [htmlContent, setHtmlContent] = useState('');
  const [originalHtmlContent, setOriginalHtmlContent] = useState(''); // 存储原始HTML内容
  const [isUploading, setIsUploading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [previewReady, setPreviewReady] = useState(false);
  const [hasFileError, setHasFileError] = useState(false);
  const [fileErrorMessage, setFileErrorMessage] = useState('');
  const [title, setTitle] = useState('');
  const [currentUser, setCurrentUser] = useState(null);
  const [securityResult, setSecurityResult] = useState(null);
  const [renderingMode, setRenderingMode] = useState(null);
  const [renderingConfig, setRenderingConfig] = useState(null);
  
  useEffect(() => {
    const user = getCurrentUser();
    if (user) {
      setCurrentUser(user);
    }
  }, []);
  
  // 清除文件和预览
  const handleClearFile = () => {
    setFile(null);
    setFileName('');
    setHtmlContent('');
    setOriginalHtmlContent(''); // 清理原始HTML内容
    setPreviewReady(false);
    setHasFileError(false);
    setFileErrorMessage('');
    setSecurityResult(null);
    setRenderingMode(null);
    setRenderingConfig(null);
    
    // 重置文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // 处理文件选择
  const handleFileChange = (event) => {
    setIsUploading(true);
    setHasFileError(false);
    
    const selectedFile = event.target.files[0];
    
    if (!selectedFile) {
      setIsUploading(false);
      return;
    }

    // 检查文件类型，只接受HTML文件
    if (!selectedFile.type.match('text/html') && 
        !selectedFile.name.toLowerCase().endsWith('.html') && 
        !selectedFile.name.toLowerCase().endsWith('.htm')) {
      setHasFileError(true);
      setFileErrorMessage('请上传HTML文件');
      setIsUploading(false);
      return;
    }

    // 检查文件大小，限制为5MB
    if (selectedFile.size > 5 * 1024 * 1024) {
      setHasFileError(true);
      setFileErrorMessage('文件大小不能超过5MB');
      setIsUploading(false);
      return;
    }
    
    setFile(selectedFile);
    setFileName(selectedFile.name);
    
    // 读取文件内容
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        let content = e.target.result;

        // 第一阶段：获取安全检测配置
        logger.info('开始HTML文件安全检测', { fileSize: content.length });

        // 获取后台安全检测配置
        const securityConfig = await systemConfigManager.getSecurityConfig();
        logger.info('获取安全检测配置', securityConfig);

        // 检查是否所有安全检测规则都已关闭
        const allRulesDisabled = !securityConfig.enableXssDetection &&
                                !securityConfig.enableMaliciousScriptDetection &&
                                !securityConfig.enableDangerousTagDetection &&
                                !securityConfig.enableExternalResourceValidation;

        let currentSecurityResult;
        if (allRulesDisabled) {
          // 所有规则都关闭，跳过安全检测
          logger.info('所有安全检测规则已关闭，跳过安全检测');
          currentSecurityResult = {
            passed: true,
            riskLevel: 'NONE',
            details: [],
            engineVersion: '2.0.0',
            timestamp: new Date().toISOString(),
            skipped: true
          };
        } else {
          // 根据配置构建启用的规则列表
          const enabledRules = [];
          if (securityConfig.enableXssDetection) enabledRules.push('xss');
          if (securityConfig.enableMaliciousScriptDetection) enabledRules.push('maliciousScript');
          if (securityConfig.enableDangerousTagDetection) enabledRules.push('dangerousTags');
          if (securityConfig.enableExternalResourceValidation) enabledRules.push('externalResources');

          // 执行安全检测
          currentSecurityResult = SecurityDetector.detect(content, { enabledRules });
        }

        // 保存安全检测结果到组件状态
        setSecurityResult(currentSecurityResult);

        // 记录检测结果
        logger.info('安全检测完成', {
          passed: currentSecurityResult.passed,
          riskLevel: currentSecurityResult.riskLevel,
          violationsCount: currentSecurityResult.details.length
        });

        // 根据检测结果处理
        if (!currentSecurityResult.passed) {
          // 安全检测未通过，显示详细信息给用户
          const violationMessages = currentSecurityResult.details.map(detail =>
            `${detail.description}: ${detail.violations.join(', ')}`
          ).join('\n');

          setHasFileError(true);
          setFileErrorMessage(`安全检测未通过，发现以下问题：\n${violationMessages}`);
          setIsUploading(false);

          // 记录安全违规到后台（如果需要）
          try {
            await axios.post('/api/system/security-violation', {
              content: content.substring(0, 1000), // 只发送前1000字符用于分析
              securityResult: currentSecurityResult,
              source: 'file_upload'
            });
          } catch (logError) {
            logger.warn('记录安全违规失败', { error: logError.message });
          }

          return;
        }

        // 安全检测通过，进行渲染方式选择（Step3: 渲染方式决策）
        try {
          // 1. 读取后台配置的默认渲染方式
          const config = await systemConfigManager.getRenderingModeConfig();
          setRenderingConfig(config);
          
          // 2. 基于HTML复杂度和安全结果选择渲染方式
          const selectedMode = RenderingModeSelector.selectMode(content, currentSecurityResult, config);
          setRenderingMode(selectedMode);
          
          logger.info('渲染方式选择完成', { 
            mode: selectedMode.mode,
            reason: selectedMode.reason,
            config: config
          });
          
          // 3. 根据选择的渲染方式处理HTML内容
          let processedContent = content;
          let editableContent = content;
          
          if (selectedMode.mode === RENDERING_MODES.SERVER_SIDE_RENDERING) {
            // 高级模式：使用服务端渲染
            const renderResult = ServerSideRenderer.generateCompleteHtml(content, {
              preserveOriginalFunction: true,
              injectEditingMarkers: true,
              optimizeForEditing: true
            });
            
            if (renderResult.success) {
              processedContent = renderResult.html;
              editableContent = renderResult.editableHtml || renderResult.html;
              
              logger.info('服务端渲染处理完成', { 
                originalLength: content.length,
                processedLength: processedContent.length 
              });
            } else {
              // 服务端渲染失败，降级到客户端安全加载
              logger.warn('服务端渲染失败，降级到安全加载模式', { error: renderResult.error });
              processedContent = sanitizeHtml(content);
              editableContent = processedContent;
            }
          } else {
            // 标准模式：客户端安全加载
            processedContent = sanitizeHtml(content);
            editableContent = processedContent;
          }
          
          // 保存处理结果到不同状态
          setOriginalHtmlContent(content); // 保存原始完整内容
          setHtmlContent(editableContent); // 保存处理后的可编辑内容用于预览
        setPreviewReady(true);
        setIsUploading(false);

          logger.info('HTML文件处理完成', { 
            originalLength: content.length,
            editableLength: editableContent.length,
            renderingMode: selectedMode.mode
          });
          
        } catch (configError) {
          // 如果配置读取失败，使用默认的客户端安全加载
          logger.error('读取渲染配置失败，使用默认安全加载模式', { error: configError.message });
          
          const sanitizedContent = sanitizeHtml(content);
          setOriginalHtmlContent(content);
          setHtmlContent(sanitizedContent);
          setRenderingMode({ mode: RENDERING_MODES.CLIENT_SIDE_SAFE_LOADING, reason: '配置读取失败，使用默认模式' });
          setPreviewReady(true);
          setIsUploading(false);
        }

      } catch (error) {
        logger.error('读取文件内容失败:', error);
        setHasFileError(true);
        setFileErrorMessage('读取文件失败，请重试');
        setIsUploading(false);
      }
    };
    
    reader.onerror = () => {
      setHasFileError(true);
      setFileErrorMessage('读取文件失败，请重试');
      setIsUploading(false);
    };
    
    reader.readAsText(selectedFile);
  };
  
  // 处理保存封面
  const handleSaveCover = async () => {
    if (!htmlContent || !originalHtmlContent) {
      message.error('请先上传HTML文件');
      return;
    }
    
    try {
      setIsSaving(true);
      
      // 从 localStorage 获取 token
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        navigate('/auth');
        return;
      }
      
      // 生成默认标题
      let finalTitle = title;
      if (!finalTitle) {
        const userPhone = currentUser?.phone || '';
        const phoneSuffix = userPhone.slice(-4);
        const timestamp = Date.now();
        finalTitle = `自定义${phoneSuffix}${timestamp}`;
      }
      
      // 使用原始HTML内容生成哈希值（Web Crypto API）
      const encoder = new TextEncoder();
      const data = encoder.encode(originalHtmlContent);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const htmlHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      
      // 准备封面数据（按照第二阶段数据存储策略要求）
      const coverData = {
        cover_text: finalTitle,
        cover_type: 'custom',
        cover_type_name: '自定义封面',
        cover_style: 'custom',
        cover_style_name: '自定义风格',
        // 原始HTML存储（核心修正：使用真正的原始内容）
        original_html_content: originalHtmlContent,
        // 编辑后HTML存储（经过渲染方式处理的内容）
        edited_html_content: htmlContent,
        // 预览用HTML（与edited_html_content相同，保持兼容性）
        html_content: htmlContent,
        original_html_hash: htmlHash,
        // 安全检测结果存储
        security_scan_result: securityResult || { passed: true, riskLevel: 'SAFE', details: [], timestamp: new Date().toISOString() },
        // 渲染方式记录
        rendering_mode: renderingMode?.mode || RENDERING_MODES.CLIENT_SIDE_SAFE_LOADING,
        rendering_reason: renderingMode?.reason || '默认安全加载模式',
        rendering_config: renderingConfig,
        // 正确标记内容来源类型
        content_source_type: 'upload',
        record_type: '文件上传'
      };
      
      // 发送请求保存封面记录
      const response = await axios.post('/api/cover/save-custom', coverData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        message.success('封面保存成功');
        
        // 如果返回了 cover_code，可以跳转到编辑页面
        if (response.data.data?.cover_code) {
          const coverCode = response.data.data.cover_code;
          
          // 直接导航到主页，带上code和source参数
          navigate(`/?code=${coverCode}&source=upload`, { replace: true });
        } else {
          // 如果没有返回cover_code，导航到封面记录页面
          navigate('/covers', { replace: true });
        }
      } else {
        message.error(response.data.message || '保存失败，请重试');
      }
    } catch (error) {
      console.error('保存封面失败:', error);
      message.error('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };
  
  // 更新预览内容
  useEffect(() => {
    if (previewIframeRef.current && htmlContent && previewReady) {
      const iframe = previewIframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      
      if (iframeDoc) {
        iframeDoc.open();
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              html, body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: auto;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
              }
              body {
                display: flex;
                justify-content: center;
                align-items: center;
              }
              * {
                max-width: 100%;
              }
              img, svg, video {
                max-width: 100%;
                height: auto;
                display: block;
                margin: 0 auto;
              }
              /* 使用CSS隐藏可能遗漏的脚本和iframe */
              script, iframe {
                display: none !important;
              }
            </style>
          </head>
          <body>
            ${htmlContent}
          </body>
          </html>
        `);
        iframeDoc.close();
      }
    }
  }, [htmlContent, previewReady]);
  
  return (
    <div className="flex flex-col h-full p-4 md:p-6 lg:p-8">
      <Card className="flex flex-col flex-1 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0">
          <CardTitle className="text-2xl font-bold">上传HTML文件</CardTitle>
          <CardDescription>
            上传HTML文件生成封面，支持.html或.htm格式，最大5MB
          </CardDescription>
        </CardHeader>
        
        <CardContent className="flex-1 flex p-6 gap-6 overflow-hidden">
          {/* 左侧：文件上传区域 */}
          <div className="w-1/3 flex flex-col gap-6">
            {/* 标题输入区域 */}
            <div>
              <label htmlFor="file-title" className="block text-sm font-medium text-slate-700 mb-1">
                标题（可选）
              </label>
              <input
                id="file-title"
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="输入封面标题"
                className="w-full p-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            <div 
              className={`flex-1 flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-8 text-center transition-colors
                ${hasFileError ? 'border-red-300 bg-red-50' : 'border-slate-300 bg-slate-50 hover:bg-slate-100'}
                ${file ? 'border-green-300 bg-green-50' : ''}
              `}
              onClick={() => fileInputRef.current?.click()}
            >
              {!file ? (
                <div className="cursor-pointer">
                  <Upload className="mx-auto h-12 w-12 text-slate-400" />
                  <h3 className="mt-2 text-lg font-semibold text-slate-700">点击或拖放文件</h3>
                  <p className="text-sm text-slate-500 mt-1">
                    支持.html或.htm格式，最大5MB
                  </p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="flex items-center mb-2">
                    <span className="font-medium text-lg text-green-600 break-all">{fileName}</span>
                    <button 
                      onClick={(e) => {
                        e.stopPropagation(); 
                        handleClearFile();
                      }}
                      className="ml-4 p-1 rounded-full hover:bg-slate-200 flex-shrink-0"
                      title="移除文件"
                    >
                      <X size={18} className="text-slate-500" />
                    </button>
                  </div>
                  <span className="text-sm text-slate-500">点击更换文件</span>
                </div>
              )}
              
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept=".html,.htm"
                className="hidden"
                disabled={isUploading}
              />
            </div>
            
            {hasFileError && (
              <div className="mt-2 flex items-center text-red-500">
                <AlertTriangle size={16} className="mr-1" />
                <span className="text-sm">{fileErrorMessage}</span>
              </div>
            )}
          </div>
          
          {/* 右侧：预览区域 */}
          <div className="flex-1 flex flex-col">
            <h3 className="text-lg font-semibold mb-3 flex items-center flex-shrink-0">
              <Eye size={18} className="mr-2" />
              预览
            </h3>
            
            <div className="border rounded-lg overflow-hidden bg-white flex-1">
              {!previewReady ? (
                <div className="h-full flex items-center justify-center text-slate-400">
                  {isUploading ? (
                    <div className="flex flex-col items-center">
                      <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mb-3"></div>
                      <p>正在解析文件...</p>
                    </div>
                  ) : (
                    <p>上传文件后在此处预览</p>
                  )}
                </div>
              ) : (
                <iframe
                  ref={previewIframeRef}
                  className="w-full h-full border-none"
                  title="HTML预览"
                  sandbox="allow-same-origin allow-scripts"
                />
              )}
            </div>
          </div>
        </CardContent>
        
        <CardFooter className="bg-slate-50 p-4 flex justify-between flex-shrink-0">
          <Button variant="outline" onClick={() => {
            navigate('/');
            // 触发自定义事件，通知其他组件视图已更改为chat
            window.dispatchEvent(new CustomEvent('viewChange', { detail: { view: 'chat' } }));
          }}>
            返回
          </Button>
          
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleClearFile} disabled={!file || isUploading || isSaving}>
              清除
            </Button>
            <Button 
              onClick={handleSaveCover} 
              disabled={!previewReady || hasFileError || isSaving}
              className="bg-primary text-white hover:bg-primary/90"
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  保存中...
                </>
              ) : (
                <>
                  <Save size={16} className="mr-1.5" />
                  保存封面
                </>
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default FileUploadView; 
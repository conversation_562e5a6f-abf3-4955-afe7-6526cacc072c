/**
 * 文本编辑器核心模块 - 聊天风格布局版本
 * 包含共享状态和核心功能
 */

// 导入拖拽模式和编辑模式
import { createDragMode } from './dragMode';
import { createEditMode } from './editMode';
import { createTextEditorMiddleware } from './textEditorMiddleware';
// 导入新的元素检测模块
import { analyzeElement, findPotentialEditableElements, markEditableElements, analyzeHtmlContent } from './elementDetection';
import { getActiveMode, isEnabled } from './textEditorMiddleware';

// 导出样式函数
export const getTextEditorStyles = () => {
  // 检查是否处于模式切换状态
  // 首先检查当前窗口
  let isModeSwitching = document.body.classList.contains('mode-switching');
  
  // 如果当前窗口不是模式切换状态，则检查父窗口（iframe情况）
  if (!isModeSwitching && window.parent !== window) {
    try {
      isModeSwitching = window.parent.document.body.classList.contains('mode-switching');
    } catch (e) {
      // 跨域访问可能会导致错误，忽略它
    }
  }
  
  return `
    /* 确保可编辑区域有视觉指示 */
    [contenteditable="true"] {
      outline: none; /* Base outline, specific states will override */
      padding: 2px;
      min-height: 18px; /* Default min height */
      transition: all 0.2s ease; /* 平滑过渡所有属性变化 */
      position: relative; /* For absolute positioning of handles */
      box-sizing: border-box; /* 确保宽高计算包含边框和内边距 */
      min-width: 50px; /* 确保元素有最小宽度，便于调整大小 */
    }

    /* 确保嵌套的contenteditable元素不拦截鼠标事件 */
    [contenteditable="true"] [contenteditable="true"] {
      pointer-events: none !important;
    }
    
    /* 但允许其子元素接收鼠标事件，确保正常的文本编辑功能 */
    [contenteditable="true"] [contenteditable="true"] * {
      pointer-events: auto;
    }

    ${!isModeSwitching ? `
    /* 当鼠标移动到文本框区域内时，在文本框四周浮现一圈虚线 - 修改选择器确保只有最外层元素显示虚线 */
    [contenteditable="true"]:not([contenteditable="true"] [contenteditable="true"]):hover:not(.drag-mode):not(:focus):not([data-editing="true"]) {
      outline: 1px dashed #007bff !important;
      cursor: pointer; /* 使用指针光标，而不是手掌图标 */
    }
    ` : `
    /* 模式切换时禁用虚线框 */
    [contenteditable="true"]:not([contenteditable="true"] [contenteditable="true"]):hover:not(.drag-mode):not(:focus):not([data-editing="true"]) {
      outline: none !important;
      cursor: default;
    }
    `}

    /* 文本编辑工具栏样式 */
    .text-editor-toolbar {
      position: absolute;
      display: flex;
      background: white;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      z-index: 9999;
      flex-wrap: wrap;
      min-width: 200px;
      max-width: 400px;
    }

    /* 拖拽模式下的实线轮廓 - 仅在非编辑模式下应用，确保只有非嵌套元素显示轮廓 */
    [contenteditable="true"]:not([contenteditable="true"] [contenteditable="true"]).drag-mode:not([data-editing="true"]):not(:focus) {
      outline: 2px solid #007bff !important; /* Solid blue line for drag mode */
      cursor: move !important; /* 移动光标（十字星带箭头），表示可拖拽 */
      z-index: 10; /* 确保拖拽元素在上层 */
      box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); /* 添加阴影效果，增强视觉反馈 */
    }

    /* 编辑模式下的实线轮廓 (获取焦点时) - 确保只有非嵌套元素显示轮廓 */
    [contenteditable="true"]:not([contenteditable="true"] [contenteditable="true"]):focus {
      caret-color: auto !important;
      outline: 2px solid #1890ff !important; /* 添加编辑模式下的边框样式 */
      z-index: 11 !important; /* 确保编辑元素在最上层 */
      cursor: text !important; /* 确保编辑模式下鼠标为文本光标 */
    }

    /* 编辑中的元素样式 - 最高优先级 - 确保只有非嵌套元素显示轮廓 */
    [contenteditable="true"]:not([contenteditable="true"] [contenteditable="true"])[data-editing="true"] {
      cursor: text !important; /* 确保编辑中的元素鼠标为文本光标 */
      z-index: 11 !important; /* 确保编辑元素在最上层 */
      outline: 2px solid #1890ff !important; /* 添加编辑模式下的边框样式 */
    }

    /* 确保编辑中的元素内部所有元素都使用文本光标 */
    [contenteditable="true"][data-editing="true"] * {
      cursor: text !important;
    }

    /* 调整大小的控制点样式 */
    .resize-handle {
      position: absolute;
      width: 10px; /* Smaller handles */
      height: 10px;
      background-color: #1890ff;
      border: 1px solid white; /* Make them more visible */
      border-radius: 50%;
      z-index: 10001; /* Higher than toolbar perhaps */
      display: none; /* Hidden by default, shown on hover/focus/drag-mode */
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
      /* 确保控制点不继承父元素的文本光标 */
      cursor: auto !important; /* 重置光标，后续会根据位置设置特定光标 */
    }
    /* 显示控制点: 当父元素 hover (且非编辑焦点), 或父元素 focus (编辑焦点), 或父元素 drag-mode */
    [contenteditable="true"].drag-mode .resize-handle,
    [contenteditable="true"].resize-mode .resize-handle { /* resize-mode to keep them visible during resize */
      display: block !important; /* 使用!important确保显示 */
    }

    /* 边框控制点 */
    .resize-handle.top { top: -5px; left: 50%; transform: translateX(-50%); cursor: ns-resize !important; }
    .resize-handle.right { right: -5px; top: 50%; transform: translateY(-50%); cursor: ew-resize !important; }
    .resize-handle.bottom { bottom: -5px; left: 50%; transform: translateX(-50%); cursor: ns-resize !important; }
    .resize-handle.left { left: -5px; top: 50%; transform: translateY(-50%); cursor: ew-resize !important; }

    /* 四角控制点 - 需求 5 */
    .resize-handle.top-left { top: -5px; left: -5px; cursor: nwse-resize !important; }
    .resize-handle.top-right { top: -5px; right: -5px; cursor: nesw-resize !important; }
    .resize-handle.bottom-left { bottom: -5px; left: -5px; cursor: nesw-resize !important; }
    .resize-handle.bottom-right { bottom: -5px; right: -5px; cursor: nwse-resize !important; }

    /* 拖拽元素时的样式 */
    [contenteditable="true"].dragging {
      opacity: 0.7;
      z-index: 10002; /* Highest during drag */
    }

    /* 确保拖拽过程中不显示调整大小的控制点 */
    [contenteditable="true"].dragging .resize-handle {
      display: none !important;
    }

    /* 调整大小模式下的样式 */
    [contenteditable="true"].resize-mode {
      outline: 2px dashed #1890ff !important;
      z-index: 10003; /* 调整大小时最高层级 */
    }

    /* 添加全局模式切换样式 - 模式切换时禁止所有编辑操作 */
    body.mode-switching [contenteditable="true"] {
      cursor: default !important;
      outline: none !important;
      pointer-events: none !important;
      user-select: none !important;
    }
  `;
};

export const getKeyboardNavigationScript = () => {
  return `
    document.addEventListener('keydown', function(e) {
      // ESC键退出编辑模式
      if (e.key === 'Escape') {
        document.activeElement.blur();
      }
    });
  `;
};

export const getTextElementInitScript = () => {
  return `
    (function() {
      // 禁用ESC键退出编辑
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          if (document.activeElement && document.activeElement.getAttribute('contenteditable') === 'true') {
            e.preventDefault();
            // 不执行退出编辑操作，不修改原始样式
          }
        }
      });
    })();
  `;
};

// 创建文本编辑器
export const createTextEditor = (
  doc,
  onElementClick,
  externalHtmlContentChangeCallback,
  debugMode = false,
  isDivMode = false,
  initialScale = 1,
  onSelectionChange
) => {
  let win;
  let containerEl;
  let activeElement = null;
  let isInitialized = false;
  let editableElementsTracker = new Set();
  let sharedIsEditingState = false; // 用于共享的编辑状态
  let isDeleteModeEnabled = false; // 删除模式状态

  // 根据传入的容器类型，确定document和window对象
  if (isDivMode) {
    // DIV模式：容器应是HTMLElement
    const isValidContainer = doc && doc.nodeType === 1 && doc.ownerDocument;
    if (!isValidContainer) {
      console.error('createTextEditor Critical Error: In DIV mode, a valid HTML element container is required. Received:', doc);
      return { init: () => {}, cleanup: () => {} };
    }
    containerEl = doc;
    win = doc.ownerDocument.defaultView;
    if (debugMode) console.log('createTextEditor: 初始化DIV模式编辑器');
  } else {
    // IFRAME模式：容器应是iframe的document对象
    if (!doc || !doc.defaultView) {
      if (debugMode) console.error('createTextEditor: 在IFRAME模式下需要提供有效的document对象');
      return { init: () => {}, cleanup: () => {} };
    }
    containerEl = doc.body;
    win = doc.defaultView;
    if (debugMode) console.log('createTextEditor: 初始化IFRAME模式编辑器');
  }

  // 如果仍然无法获取document或window，返回空对象
  if (!doc || !win) {
    console.error('createTextEditor: 无法从容器获取有效的document或window对象');
    return { init: () => {}, cleanup: () => {} };
  }

  // 处理回调函数
  const htmlContentChangeCallback = typeof externalHtmlContentChangeCallback === 'function' ? externalHtmlContentChangeCallback : null;

  // 声明中间件实例变量，先不初始化
  let middlewareInstance;

  // 创建上下文对象
  const context = {
    doc,
    win,
    containerEl,
    getActiveElement: () => activeElement,
    setActiveElement: (element) => {
      // 检查元素是否真的变化了
      const hasChanged = activeElement !== element;
      
      if (hasChanged) {
        // 记录之前的活动元素，用于后续比较
        const previousElement = activeElement;
        
        // 更新活动元素
        activeElement = element;

        // 确保onSelectionChange回调被调用，即使element为null
        if (typeof onSelectionChange === 'function') {
          try {
          onSelectionChange(element);
            if (debugMode) {
              if (element) {
                console.log('通知选择变更:', element.tagName, element.className);
              } else {
                console.log('通知选择变更: null');
              }
            }
          } catch (error) {
            if (debugMode) console.error('选择变更回调错误:', error);
          }
        } else if (debugMode) {
          console.warn('没有提供onSelectionChange回调');
        }

      // 设置全局变量，以便其他组件可以访问
      if (!window.currentActiveTextEditor) {
        window.currentActiveTextEditor = {};
      }
      window.currentActiveTextEditor.activeElement = element;
      window.currentActiveTextEditor.isEditing = sharedIsEditingState;
        
        // 清理旧元素的所有状态
        if (previousElement && previousElement.isConnected) {
          // 移除编辑状态标记
          previousElement.removeAttribute('data-editing');
          previousElement.classList.remove('editing-active-outline');
          
          // 移除拖拽模式相关的类
          previousElement.classList.remove('drag-mode', 'selected-for-drag', 'dragging', 'resize-mode');
          
          // 显式调用blur()方法，确保失去焦点
          if (typeof previousElement.blur === 'function' && doc.activeElement === previousElement) {
            previousElement.blur();
          }
          
          // 触发自定义事件，通知工具栏重置状态
          const blurEvent = new CustomEvent('editor-element-blur', {
            bubbles: true,
            detail: { target: previousElement }
          });
          doc.dispatchEvent(blurEvent);
          
          // 恢复子元素的默认光标样式
          const allChildren = previousElement.querySelectorAll('*');
          allChildren.forEach(child => {
            child.style.cursor = '';
            child.style.userSelect = '';
          });
          
          // 恢复元素的默认光标样式
          previousElement.style.cursor = '';
          
          // 重置其他可能的编辑状态标记
          previousElement.removeAttribute('data-initial-styles');
          previousElement.removeAttribute('data-user-direct-color');
          
          // 移除可能的事件监听器
          previousElement.removeEventListener('mousemove', middlewareInstance?.editMode?.handleEditingMouseMove, true);
          
          // 如果有dragMode实例，确保清除该元素的拖拽状态
          if (middlewareInstance && middlewareInstance.dragMode) {
            const dragHandles = previousElement.querySelectorAll('.resize-handle');
            dragHandles.forEach(handle => {
              if (handle.parentNode === previousElement) {
                previousElement.removeChild(handle);
              }
            });
          }
        }
        
        // 更新元素的视觉样式
        if (previousElement && previousElement.classList) {
          previousElement.classList.remove('editing-active-outline');
        }
        
        if (element && element.classList) {
          element.classList.add('editing-active-outline');
        }
      }
    },
    debugMode,
    isDivMode,
    editableElementsTracker,
    onElementClick, // 将 onElementClick 添加到上下文中
    htmlContentChangeCallback, // 将 htmlContentChangeCallback 添加到上下文中
    getIsEditing: () => sharedIsEditingState,
    setIsEditing: (status) => {
      sharedIsEditingState = status;
      // Ensure global state is also updated if needed
      if (window.currentActiveTextEditor) {
        window.currentActiveTextEditor.isEditing = status;
      }
    },
    scale: initialScale, // 在上下文中初始化 scale
    getMiddleware: () => middlewareInstance,
    isDeleteMode: isDeleteModeEnabled,
    setDeleteMode: (mode) => {
      isDeleteModeEnabled = mode;
      // 更新所有元素的删除按钮
      if (editableElementsTracker) {
        editableElementsTracker.forEach(el => {
          if (!el) return;
          updateElementDeleteButton(el);
        });
      }
    }
  };

  // 创建模式和中间件
  const editModeInstance = createEditMode(context);
  const dragModeInstance = createDragMode({...context, editMode: editModeInstance });
  // 初始化中间件实例
  middlewareInstance = createTextEditorMiddleware(context, dragModeInstance, editModeInstance);

  // 实时文本更新处理函数，恢复原有逻辑
  const handleInput = (e) => {
    // 阻止事件冒泡
    e.stopPropagation();

    // 确保全局变量已设置
    if (!window.currentActiveTextEditor) {
      window.currentActiveTextEditor = {};
    }
    window.currentActiveTextEditor.activeElement = activeElement;
    window.currentActiveTextEditor.isEditing = sharedIsEditingState;

    // 检查当前元素是否处于编辑模式
    const currentElement = e.currentTarget || e.target;
    const isCurrentlyEditing = currentElement && currentElement.hasAttribute('data-editing');

    // 只有在非编辑模式下才调用HTML内容变化回调
    // 这确保了在编辑模式下，键盘输入不会对文本框状态产生任何影响
    if (!isCurrentlyEditing && (htmlContentChangeCallback || externalHtmlContentChangeCallback)) {
      // 获取当前HTML内容
      const container = doc.querySelector('.content-container');
      if (container) {
        const html = container.innerHTML;
        if (htmlContentChangeCallback) {
          htmlContentChangeCallback(html);
        }
        if (externalHtmlContentChangeCallback) {
          externalHtmlContentChangeCallback(html);
        }
      }
    }
  };

  // 查找可编辑元素 - 增强版，使用元素检测模块
  const findEditableElements = () => {
    // 使用基本选择器找到可能的元素
    const basicElements = Array.from(containerEl.querySelectorAll('h1, h2, h3, h4, h5, h6, .title, .main-title, .subtitle, .sub-title, .content-text, p.lead, .description, .account-info, .account, .title-text, div.subtitle, div.sub-title, div.account, div.account-info, div.title-text, div.main-title, h1.title, p.subtitle, p.sub-title, span.subtitle, .editable-text'));
    
    // 使用高级元素检测找到更多潜在可编辑元素
    const potentialElements = findPotentialEditableElements(containerEl);
    
    // 明确查找所有SVG元素 - 确保不遗漏任何SVG
    const svgElements = [];
    
    // 1. 查找所有顶级SVG元素
    const topLevelSvgs = Array.from(containerEl.querySelectorAll('svg'));
    svgElements.push(...topLevelSvgs);
    
    // 2. 查找特定类名的SVG元素，尤其是paper-plane
    const paperPlaneSvgs = Array.from(containerEl.querySelectorAll('.paper-plane, .icon svg, svg.icon, [class*="icon"] svg, svg[class*="icon"]'));
    svgElements.push(...paperPlaneSvgs);
    
    // 3. 查找SVG内的重要元素（path, g等）
    const svgInternalElements = Array.from(containerEl.querySelectorAll('svg path, svg g, svg text, svg rect, svg circle, svg ellipse, svg line, svg polygon, svg polyline'));
    
    // 对于每个内部元素，找到其父SVG并添加
    svgInternalElements.forEach(element => {
      const parentSvg = element.closest('svg');
      if (parentSvg && !svgElements.includes(parentSvg)) {
        svgElements.push(parentSvg);
      }
    });
    
    // 合并三个数组，去除重复
    const combinedElements = [...basicElements];
    
    // 添加潜在元素（如果不重复）
    potentialElements.forEach(element => {
      if (!combinedElements.includes(element)) {
        combinedElements.push(element);
      }
    });
    
    // 专门添加SVG元素（无论它们是否被元素检测模块找到）
    svgElements.forEach(element => {
      if (!combinedElements.includes(element)) {
        combinedElements.push(element);
        if (debugMode) console.log('添加SVG元素:', element);
      }
    });
    
    // 如果是DIV模式且容器本身可编辑，将容器添加到元素列表中
    if (isDivMode && containerEl && isEditable(containerEl)) {
      if (!combinedElements.includes(containerEl)) {
        combinedElements.push(containerEl);
      }
    }
    
    return combinedElements;
  };

  // 检查元素是否是可编辑元素
  const isEditable = (element) => {
    if (!element) return false;

    // 检查元素本身是否可编辑
    if (element.getAttribute && element.getAttribute('contenteditable') === 'true') {
      return true;
    }

    // 检查元素是否在可编辑元素内部
    const editableElements = doc.querySelectorAll('[contenteditable="true"]');
    return [...editableElements].some(el => el.contains(element));
  };

  // 创建删除按钮
  const createDeleteButton = (element) => {
    // 如果元素已经有删除按钮，则先移除
    removeDeleteButton(element);
    
    if (!isDeleteModeEnabled) return null;
    
    // 创建删除按钮
    const deleteButton = doc.createElement('div');
    deleteButton.className = 'fengmian-delete-button';
    deleteButton.title = '删除元素';
    
    // 标记为编辑器UI元素，确保不被保存到历史记录中
    deleteButton.setAttribute('data-editor-ui', 'true');
    
    // 获取缩放比例的倒数，用于抵消缩放效果
    const scaleCompensation = initialScale ? (1 / initialScale) : 1;
    
    // 设置样式
    Object.assign(deleteButton.style, {
      position: 'absolute',
      top: '0',
      right: '0',
      width: '20px',
      height: '20px',
      borderRadius: '50%',
      background: 'rgba(255, 0, 0, 0.8)',
      color: 'white',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      fontSize: '14px',
      fontWeight: 'bold',
      zIndex: '20000', // 使用比拖拽手柄(10001)更高的z-index
      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
      border: '1px solid white',
      opacity: '0', // 默认不可见
      transition: 'opacity 0.2s ease',
      pointerEvents: 'auto', // 确保点击事件能被捕获
      transform: `translate(50%, -50%) scale(${scaleCompensation})`, // 精确定位并抵消缩放
      transformOrigin: 'center center', // 确保从中心点缩放
      // 确保按钮不会变形
      minWidth: '20px',
      minHeight: '20px',
      maxWidth: '20px',
      maxHeight: '20px',
      lineHeight: '20px',
      padding: '0',
      margin: '0',
      boxSizing: 'border-box'
    });
    
    // 添加删除图标
    deleteButton.innerHTML = '×';
    
    // 创建事件处理函数，以便之后能够移除它们
    const handleMouseEnter = () => {
      if (isDeleteModeEnabled && deleteButton.parentElement) {
        deleteButton.style.opacity = '1';
      }
    };
    
    const handleMouseLeave = () => {
      if (deleteButton.parentElement) {
        deleteButton.style.opacity = '0';
      }
    };
    
    // 添加鼠标悬停显示效果
    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    // 保存事件监听器引用以便之后清理
    if (!element._deleteButtonEventListeners) {
      element._deleteButtonEventListeners = {};
    }
    element._deleteButtonEventListeners.mouseenter = handleMouseEnter;
    element._deleteButtonEventListeners.mouseleave = handleMouseLeave;
    
    // 添加删除事件
    deleteButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      
      // 从DOM中移除元素
      element.remove();
      
      // 从跟踪器中移除
      if (editableElementsTracker.has(element)) {
        editableElementsTracker.delete(element);
      }
      
      // 确保一次性触发所有DOM更新，避免多次记录历史状态
      setTimeout(() => {
      // 更新HTML内容
      const container = doc.querySelector('.content-container');
      if (container && htmlContentChangeCallback) {
        htmlContentChangeCallback(container.innerHTML);
      }
      }, 0);
    });
    
    // 防止事件冒泡
    deleteButton.addEventListener('mousedown', (e) => {
      e.preventDefault();
      e.stopPropagation();
    });
    
    // 确保删除按钮在所有子元素之前，避免多个删除按钮
    const existingDeleteButtons = element.querySelectorAll('.fengmian-delete-button');
    existingDeleteButtons.forEach(btn => {
      if (btn !== deleteButton) {
        btn.remove();
      }
    });
    
    // 将删除按钮添加到元素中
    element.appendChild(deleteButton);
    
    // 确保元素有position:relative以便正确放置删除按钮
    const computedStyle = window.getComputedStyle(element);
    if (computedStyle.position === 'static') {
      element.style.position = 'relative';
    }
    
    // 添加删除按钮引用到元素的dataset
    element.dataset.hasDeleteButton = 'true';
    
    // 返回创建的删除按钮
    return deleteButton;
  };

  // 移除删除按钮
  const removeDeleteButton = (element) => {
    if (!element) return;
    
    // 移除所有删除按钮，确保没有残留
    const deleteButtons = element.querySelectorAll('.fengmian-delete-button');
    deleteButtons.forEach(button => {
      button.remove();
    });
    
    // 清理事件监听器
    if (element._deleteButtonEventListeners) {
      // 移除鼠标事件监听器
      if (element._deleteButtonEventListeners.mouseenter) {
        element.removeEventListener('mouseenter', element._deleteButtonEventListeners.mouseenter);
      }
      if (element._deleteButtonEventListeners.mouseleave) {
        element.removeEventListener('mouseleave', element._deleteButtonEventListeners.mouseleave);
      }
      // 清空引用
      element._deleteButtonEventListeners = null;
    }
    
    delete element.dataset.hasDeleteButton;
  };

  // 更新元素的删除按钮显示状态
  const updateElementDeleteButton = (element) => {
    if (!element) return;
    
    if (isDeleteModeEnabled) {
      if (!element.dataset.hasDeleteButton) {
        createDeleteButton(element);
      }
    } else {
      removeDeleteButton(element);
    }
  };

  // 初始化元素
  const initializeElements = (elements) => {
    elements.forEach(el => {
      if (!el || editableElementsTracker.has(el)) return; // Skip null elements or already processed ones

      try {
        // 判断元素类型，对SVG元素特殊处理
        const isSvgElement = el.namespaceURI === 'http://www.w3.org/2000/svg' || el.tagName.toLowerCase() === 'svg';

        // 设置初始样式属性以确保元素可以正确调整大小
        initializeElementStyles(el, isSvgElement);

        // 根据元素类型设置不同的属性
        if (isSvgElement) {
          // SVG元素设置为可拖动但不可编辑文本
          el.setAttribute('data-editable-fengmian', 'true');
          
          // 确保SVG元素可以接收鼠标事件
          el.style.pointerEvents = 'auto';
          
          // 确保SVG元素有正确的定位样式
          if (!el.style.position || el.style.position === 'static') {
            el.style.position = 'relative';
          }
          
          // 确保SVG元素在正确的层级
          if (!el.style.zIndex) {
            el.style.zIndex = '1';
          }
          
          // 设置初始transform（如果没有）
          if (!el.style.transform || el.style.transform === 'none') {
            const currentTransform = window.getComputedStyle(el).transform;
            if (currentTransform === 'none') {
              el.style.transform = 'translate(0px, 0px)';
            } else {
              el.style.transform = currentTransform;
            }
          }
          
          if (debugMode) console.log('初始化SVG元素:', el);
        } else {
          // 普通HTML元素设置为可编辑
          el.contentEditable = 'true';
        }

        // 添加事件监听器
        middlewareInstance.attachElementEventListeners(el);

        // 对非SVG元素添加输入事件监听器
        if (!isSvgElement) {
          el.addEventListener('input', handleInput);
        }

        // 将元素添加到跟踪器中
        editableElementsTracker.add(el);
        
        // 如果处于删除模式，添加删除按钮
        if (isDeleteModeEnabled) {
          createDeleteButton(el);
        }

      } catch (err) {
        if (debugMode) console.error('元素初始化错误:', err);
      }
    });
  };

  // 初始化元素样式
  const initializeElementStyles = (el, isSvgElement) => {
    // 如果没有传入isSvgElement参数，则自动检测
    if (isSvgElement === undefined) {
      isSvgElement = el.namespaceURI === 'http://www.w3.org/2000/svg' || el.tagName.toLowerCase() === 'svg';
    }
    
    if (isSvgElement) {
      // SVG元素特殊处理
      // 确保SVG元素有正确的样式
      if (!el.style.position || el.style.position === 'static') {
        el.style.position = 'relative';
      }
      
      // 确保SVG元素可以接收鼠标事件
      el.style.pointerEvents = 'auto';
      
      // 确保SVG元素在正确的层
      if (!el.style.zIndex) {
        el.style.zIndex = '1';
      }
      
      // 设置初始transform（如果没有）
      if (!el.style.transform || el.style.transform === 'none') {
        const currentTransform = window.getComputedStyle(el).transform;
        if (currentTransform === 'none') {
          el.style.transform = 'translate(0px, 0px)';
        } else {
          el.style.transform = currentTransform;
        }
      }
      
      // 检查SVG元素的尺寸，如果太小可能不易于拖拽
      try {
        const rect = el.getBoundingClientRect();
        if (rect.width < 20 || rect.height < 20) {
          // 为小型SVG元素添加一个最小尺寸
          el.style.minWidth = '20px';
          el.style.minHeight = '20px';
        }
      } catch (e) {
        // 忽略尺寸计算错误
      }
    } else {
      // 普通HTML元素
      // 设置高度，并确保元素有合理的最小宽度
      if (!el.style.height) {
        el.style.height = `${el.offsetHeight}px`;
      }
      
      // 添加最小宽度，确保元素能够正确显示
      if (!el.style.width && !el.style.minWidth) {
        // 根据元素类型设置不同的最小宽度
        const tagName = el.tagName.toLowerCase();
        if (tagName === 'div' || tagName === 'section' || tagName === 'article') {
          el.style.minWidth = '100px';
        } else if (tagName === 'span' || tagName === 'p' || tagName === 'h1' || 
                  tagName === 'h2' || tagName === 'h3' || tagName === 'h4' || 
                  tagName === 'h5' || tagName === 'h6') {
          el.style.minWidth = '50px';
        } else {
          // 其他元素使用默认最小宽度
          el.style.minWidth = '30px';
        }
      }
    }
  };

  // 初始化文本编辑器
  const initTextEditor = () => {
    if (!containerEl || !doc) {
      isInitialized = false;
      return;
    }

    // If already initialized, clean up existing event listeners to avoid duplicate bindings
    if (isInitialized) {
      middlewareInstance.cleanupListeners(false);
    }

    try {
      // 查找所有可编辑元素
      const elements = findEditableElements();

      // 初始化每个可编辑元素
      initializeElements(elements);

      // 添加全局事件监听器
      middlewareInstance.addGlobalEventListeners();

      isInitialized = true;
      
      // 将textEditor实例挂载到全局对象上，以便其他组件可以访问
      window.textEditor = publicApi;
      if (win && win !== window) {
        win.textEditor = publicApi;
      }
      
      // 确保currentActiveTextEditor中也有textEditorRef引用
      if (!window.currentActiveTextEditor) {
        window.currentActiveTextEditor = {};
      }
      window.currentActiveTextEditor.textEditorRef = publicApi;

    } catch (error) {
      isInitialized = false;
    }
  };

  // 清理函数
  const cleanupEventListeners = () => {
    // 移除所有元素的input事件监听器
    editableElementsTracker.forEach(el => {
      if (!el) return; // 跳过null元素
      el.removeEventListener('input', handleInput);
    });

    // 清理中间件的监听器
    middlewareInstance.cleanupListeners(true);

    // 清理全局变量
    if (window.currentActiveTextEditor) {
      window.currentActiveTextEditor.activeElement = null;
      window.currentActiveTextEditor.isEditing = false;
      window.currentActiveTextEditor.textEditorRef = null;
    }
    
    // 清理全局对象上的textEditor实例引用
    if (window.textEditor === publicApi) {
      window.textEditor = null;
    }
    
    if (win && win.textEditor === publicApi) {
      win.textEditor = null;
    }
  };

  // 返回公共API
  const publicApi = {
    init: initTextEditor,
    cleanup: cleanupEventListeners,
    
    // 关键新增：添加一个安全更新上下文值的方法
    updateValue: (key, value) => {
      if (key in context) {
        context[key] = value;
        if (debugMode) console.log(`Context updated: ${key} =`, value);
      }
    },

    set onHtmlContentChange(callback) {
      if (typeof callback === 'function') {
        externalHtmlContentChangeCallback = callback;
      }
    },
    get htmlContent() {
      const container = doc && doc.querySelector('.content-container');
      return container ? container.innerHTML : '';
    },
    setContent: (html) => {
      const container = doc && doc.querySelector('.content-container');
      if (container) {
        container.innerHTML = html;
        if (htmlContentChangeCallback) {
          htmlContentChangeCallback(html);
        }
        if (externalHtmlContentChangeCallback) {
          externalHtmlContentChangeCallback(html);
        }
      }
    },
    
    // 添加控制删除模式的方法
    setDeleteMode: (mode) => {
      isDeleteModeEnabled = mode;
      
      // 更新所有可编辑元素的删除按钮
      if (editableElementsTracker) {
        editableElementsTracker.forEach(el => {
          if (!el) return; // 跳过null元素
          updateElementDeleteButton(el);
        });
      }
      
      if (debugMode) console.log('删除模式已设置为:', mode);
    },
    
    getDeleteMode: () => {
      return isDeleteModeEnabled;
    },
    
    // 新增：安全地将一个新元素添加到编辑器实例中进行管理
    addNewElementToEditor: (element) => {
      if (!element || editableElementsTracker.has(element)) {
        return; // 如果元素不存在或已在追踪列表中，则不执行任何操作
      }
      
      try {
        // 检查是否为SVG元素
        const isSvgElement = element.namespaceURI === 'http://www.w3.org/2000/svg' || element.tagName.toLowerCase() === 'svg';
        
        // 保存元素的原始位置和样式
        const originalPosition = element.style.position;
        const originalLeft = element.style.left;
        const originalTop = element.style.top;
        const originalTransform = element.style.transform;
        const originalZIndex = element.style.zIndex; // 保存原始z-index
        
        // 设置初始样式属性以确保元素可以正确调整大小和拖拽
        initializeElementStyles(element, isSvgElement);
        
        // 特殊处理：检查元素是否有可能干扰拖拽的样式
        if (element.style) {
          // 检查并处理overflow-wrap和word-break样式
          const computedStyle = window.getComputedStyle(element);
          if (computedStyle.overflowWrap === 'break-word' && computedStyle.wordBreak === 'break-all') {
            // 存储原始样式以便需要时恢复
            element.dataset.originalOverflowWrap = computedStyle.overflowWrap;
            element.dataset.originalWordBreak = computedStyle.wordBreak;
            
            // 临时移除这些样式，以确保拖拽功能正常工作
            element.style.overflowWrap = '';
            element.style.wordBreak = '';
          }
          
          // 确保元素有正确的position属性
          if (!element.style.position || element.style.position === 'static') {
            element.style.position = 'absolute';
          } else {
            // 如果元素已经有明确的position，保留它
            element.style.position = originalPosition;
          }
          
          // 确保元素有z-index，以便在复杂布局中可见
          // 只有在元素没有z-index时才设置默认值，保留原始z-index
          if (!element.style.zIndex && !originalZIndex) {
            element.style.zIndex = '10';
          } else if (originalZIndex) {
            // 恢复原始z-index
            element.style.zIndex = originalZIndex;
          }
          
          // 增强：处理特殊HTML模板中的动画和过渡效果
          if (computedStyle.animation && computedStyle.animation !== 'none') {
            // 保存原始动画状态
            element.dataset.originalAnimation = computedStyle.animation;
            element.dataset.originalAnimationPlayState = computedStyle.animationPlayState || 'running';
            
            // 暂时暂停动画，以便拖拽时不受干扰
            element.style.animationPlayState = 'paused';
          }
          
          // 处理可能影响拖拽的过渡效果
          if (computedStyle.transition && computedStyle.transition !== 'none') {
            // 保存原始过渡效果
            element.dataset.originalTransition = computedStyle.transition;
            
            // 暂时禁用过渡效果，以确保拖拽时响应迅速
            element.style.transition = 'none';
          }
          
          // 增强：处理可能的transform冲突
          if (computedStyle.transform && computedStyle.transform !== 'none' && !originalLeft && !originalTop) {
            // 保存原始transform
            element.dataset.originalTransform = computedStyle.transform;
            
            // 如果元素使用了transform但没有明确的定位，确保它有明确的定位
            if (computedStyle.position === 'static') {
              element.style.position = 'absolute';
              
              // 计算元素当前位置并设置为top/left值
              const rect = element.getBoundingClientRect();
              const parentRect = element.parentElement.getBoundingClientRect();
              
              element.style.top = `${rect.top - parentRect.top}px`;
              element.style.left = `${rect.left - parentRect.left}px`;
              
              // 清除transform以避免冲突
              element.style.transform = 'none';
            }
          } else if (originalLeft && originalTop) {
            // 如果元素已有明确的left和top值，保留它们
            element.style.left = originalLeft;
            element.style.top = originalTop;
            
            // 如果同时有transform，也保留它
            if (originalTransform && originalTransform !== 'none') {
              element.style.transform = originalTransform;
            }
          }
        }
        
        // 根据元素类型设置不同的属性
        if (isSvgElement) {
          // SVG元素设置为可拖动但不可编辑文本
          element.setAttribute('data-editable-fengmian', 'true');
          
          // 确保SVG元素可以接收鼠标事件
          element.style.pointerEvents = 'auto';
        } else {
          // 普通HTML元素设置为可编辑
          element.contentEditable = 'true';
          
          // 添加input事件监听器
          element.addEventListener('input', handleInput);
        }
        
        // 添加事件监听器
        middlewareInstance.attachElementEventListeners(element);
        
        // 将元素添加到跟踪器中
        editableElementsTracker.add(element);
        
        // 特殊处理：检查并处理元素内部的动画和嵌套元素
        handleElementAnimationsRecursively(element);
        
        // 增强：处理嵌套的contenteditable元素
        const nestedEditableElements = element.querySelectorAll('[contenteditable="true"]');
        nestedEditableElements.forEach(nestedEl => {
          if (nestedEl !== element) {
            // 禁用嵌套contenteditable元素的鼠标事件处理，避免事件冲突
            nestedEl.style.pointerEvents = 'none';
          }
        });
        
        // 增强：延迟一小段时间后重新检查样式，确保所有样式已经正确应用
        setTimeout(() => {
          // 再次确保position和z-index正确设置
          if (element.style && (!element.style.position || element.style.position === 'static')) {
            element.style.position = 'absolute';
          }
          
          // 只有在元素没有z-index时才设置默认值，保留原始z-index
          if (element.style && !element.style.zIndex && !originalZIndex) {
            element.style.zIndex = '10';
          } else if (originalZIndex) {
            // 恢复原始z-index
            element.style.zIndex = originalZIndex;
          }
          
          // 确保拖拽功能正常工作
          if (middlewareInstance && middlewareInstance.dragMode) {
            middlewareInstance.dragMode.updateDragHandlePosition(element);
          }
        }, 50);
        
      } catch (err) {
        if (debugMode) console.error('添加新元素到编辑器时出错:', err);
      }
    },
  };
  
  // 辅助函数：递归处理元素及其子元素的动画
  const handleElementAnimationsRecursively = (element) => {
    if (!element) return;
    
    try {
      // 处理元素本身的动画
      const computedStyle = window.getComputedStyle(element);
      if (computedStyle.animation && computedStyle.animation !== 'none') {
        // 存储原始动画状态
        element.dataset.originalAnimation = computedStyle.animation;
        element.dataset.originalAnimationPlayState = computedStyle.animationPlayState || 'running';
        
        // 暂停动画，以便拖拽时不受干扰
        element.style.animationPlayState = 'paused';
      }
      
      // 递归处理子元素
      Array.from(element.children).forEach(child => {
        handleElementAnimationsRecursively(child);
      });
    } catch (error) {
      // 忽略可能的DOM操作错误
      if (debugMode) console.error('Error in handleElementAnimationsRecursively:', error);
    }
  };

  return publicApi;
};

/**
 * 添加可编辑属性到HTML字符串
 * 用于预处理要加载到编辑器中的HTML，将指定元素标记为可编辑
 * @param {string} htmlString - 要处理的HTML字符串
 * @returns {string} - 处理后的HTML字符串
 */
export const addEditableAttributeToHtml = (htmlString) => {
  if (!htmlString) return htmlString;

  try {
    // 使用新的analyzeHtmlContent函数处理HTML
    return analyzeHtmlContent(htmlString);
  } catch (error) {
    console.error('处理HTML时出错:', error);
    // 发生错误时回退到基本实现
    return basicAddEditableAttributeToHtml(htmlString);
  }
};

// 基础版本的HTML处理函数，作为后备
const basicAddEditableAttributeToHtml = (htmlString) => {
  if (!htmlString) return htmlString;

  try {
    // 创建一个临时的 DOM 解析器来处理 HTML，比正则表达式更可靠
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');

    // 要添加可编辑属性的选择器
    const tagsToMakeEditable = 'h1, h2, h3, h4, h5, h6, p, span, div.title, div.subtitle, div.main-title, div.account-info, div:not([class]):not([id])';

    // 查找所有匹配的元素
    const elements = doc.querySelectorAll(tagsToMakeEditable);

    // 为每个元素添加可编辑属性
    elements.forEach(element => {
      // 只添加给没有明确设置为不可编辑的元素
      if (element.getAttribute('data-editable-fengmian') !== 'false') {
        element.setAttribute('data-editable-fengmian', 'true');
        element.setAttribute('contenteditable', 'true');
      }
    });

    // 如果没有找到可编辑元素，至少创建一个
    if (elements.length === 0) {
      // 找到可能是内容容器的元素
      const containers = doc.querySelectorAll('div, section, article');
      // 如果找到了容器，在第一个容器中添加一个可编辑段落
      if (containers.length > 0) {
        const container = containers[0];
        // 只有容器没有任何子元素时才插入
        if (container.childNodes.length === 0) {
        const paragraph = doc.createElement('p');
        paragraph.setAttribute('data-editable-fengmian', 'true');
        paragraph.setAttribute('contenteditable', 'true');
        paragraph.textContent = container.textContent || '编辑文本';
        container.innerHTML = '';
        container.appendChild(paragraph);
        }
      } else {
        // 如果没有容器，则在主体中添加一个可编辑段落
        const body = doc.body;
        // 只有body没有任何子元素时才插入
        if (body.childNodes.length === 0) {
        const paragraph = doc.createElement('p');
        paragraph.setAttribute('data-editable-fengmian', 'true');
        paragraph.setAttribute('contenteditable', 'true');
        paragraph.textContent = body.textContent || '编辑文本';
        body.innerHTML = '';
        body.appendChild(paragraph);
        }
      }
    }

    // 返回处理后的 HTML 字符串
    return doc.documentElement.outerHTML;
  } catch (error) {
    // 静默处理错误，出错时返回原始字符串
    return htmlString;
  }
};

/**
 * 初始化文本编辑器，设置基本的事件监听和初始状态
 * @param {HTMLElement} container - 编辑器容器元素
 */
export const initializeEditor = (container) => {
  if (!container) return;
  
  // 标记容器为编辑器容器
  container.setAttribute('data-text-editor-container', 'true');
  
  // 查找并标记初始的可编辑元素
  findAndMarkEditableElements(container);
  
  // 设置容器click监听
  setupContainerClickListener(container);
  
  console.log('文本编辑器初始化完成');
};

/**
 * 查找并标记容器中的可编辑元素
 * @param {HTMLElement} container - 编辑器容器元素
 * @return {Array} 标记的可编辑元素数组
 */
export const findAndMarkEditableElements = (container) => {
  if (!container) return [];
  
  // 查找基本的可编辑元素（使用CSS选择器查找）
  const basicEditableSelectors = [
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 
    'p', 'span', 'div.text-content', 
    '[data-editable-fengmian]',
    'text', '.text'
  ];
  
  const basicEditableElements = Array.from(
    container.querySelectorAll(basicEditableSelectors.join(','))
  ).filter(element => {
    // 过滤掉不应被编辑的元素
    const excluded = element.closest('button, input, select, textarea, .non-editable');
    return !excluded;
  });
  
  // 查找所有潜在的可编辑元素（使用高级检测逻辑）
  const potentialElements = findPotentialEditableElements(container);
  
  // 合并基本元素和潜在元素，确保没有重复
  const allEditableElements = [...new Set([...basicEditableElements, ...potentialElements])];
  
  // 为所有可编辑元素设置属性
  allEditableElements.forEach(element => {
    if (!element.hasAttribute('data-editable-fengmian')) {
      element.setAttribute('data-editable-fengmian', 'true');
      
      // 添加双击编辑提示
      if (!element.hasAttribute('title')) {
        element.setAttribute('title', '双击编辑文本');
      }
    }
  });
  
  return allEditableElements;
};

/**
 * 设置容器的点击监听器
 * @param {HTMLElement} container - 编辑器容器元素
 */
export const setupContainerClickListener = (container) => {
  if (!container) return;
  
  // 定义模式模块映射
  const modeModules = {
    'dragMode': () => import('./dragMode.js'),
    'editMode': () => import('./editMode.js')
  };
  
  // 点击容器时检查编辑模式
  container.addEventListener('click', (event) => {
    // 获取当前激活的模式
    const activeMode = getActiveMode();
    
    if (!activeMode) return;
    
    // 如果当前模式启用，则调用其点击处理函数
    if (isEnabled(activeMode)) {
      // 使用映射导入对应模式的处理函数
      const importModule = modeModules[activeMode];
      if (importModule) {
        importModule()
          .then(modeModule => {
            if (modeModule.handleContainerClick) {
              modeModule.handleContainerClick(event, container);
            }
          })
          .catch(error => {
            console.error(`加载模式 ${activeMode} 失败:`, error);
          });
      }
    }
  });
};

/**
 * 获取HTML内容中的所有可编辑元素
 * @param {string} htmlContent - HTML内容
 * @return {Object} 包含解析后的DOM和可编辑元素的对象
 */
export const getEditableElementsFromHtml = (htmlContent) => {
  if (!htmlContent) return { dom: null, elements: [] };
  
  try {
    // 解析HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    
    // 查找所有可编辑元素
    const editableElements = findPotentialEditableElements(doc.body);
    
    return {
      dom: doc,
      elements: editableElements
    };
  } catch (error) {
    console.error('解析HTML失败:', error);
    return { dom: null, elements: [] };
  }
};

/**
 * 处理HTML内容，标记可编辑元素
 * @param {string} htmlContent - 原始HTML内容
 * @return {string} 处理后的HTML内容，带有可编辑元素标记
 */
export const processHtmlForEditing = (htmlContent) => {
  return analyzeHtmlContent(htmlContent);
};

/**
 * 获取元素的准确边界
 * @param {HTMLElement} element - 目标元素
 * @return {DOMRect} 元素边界
 */
export const getElementBounds = (element) => {
  if (!element) return null;
  try {
    // 获取元素边界
    const rect = element.getBoundingClientRect();
    return rect;
  } catch (error) {
    console.error('获取元素边界失败:', error);
    return null;
  }
};

/**
 * 处理文本元素的双击事件，切换到编辑模式
 * @param {Event} event - 双击事件
 */
export const handleTextElementDoubleClick = (event) => {
  if (!event || !event.target) return;
  
  const target = event.target.closest('[data-editable-fengmian]');
  if (!target) return;
  
  // 防止事件冒泡
  event.stopPropagation();
  
  // 使元素可编辑
  target.setAttribute('contenteditable', 'true');
  
  // 检查是否已包含编辑指示类
  if (!target.classList.contains('editing')) {
    target.classList.add('editing');
  }

  // 尝试聚焦元素
  try {
    target.focus();
    
    // 将光标移动到文本末尾
    const range = document.createRange();
    const selection = window.getSelection();
    
    range.selectNodeContents(target);
    range.collapse(false); // 将光标移动到末尾
    
    selection.removeAllRanges();
    selection.addRange(range);
  } catch (error) {
    console.error('聚焦元素失败:', error);
  }
  
  // 阻止默认行为
  event.preventDefault();
};

/**
 * 结束编辑模式
 * @param {HTMLElement} element - 正在编辑的元素
 */
export const finishEditing = (element) => {
  if (!element) return;
  
  // 移除编辑标记
  element.classList.remove('editing');
  
  // 如果需要，设置contenteditable为false
  if (!element.hasAttribute('data-always-editable')) {
    element.setAttribute('contenteditable', 'false');
  }
};

/**
 * 获取所有可编辑文本元素
 * @param {HTMLElement} container - 容器元素
 * @return {Array} 可编辑元素数组
 */
export const getAllEditableElements = (container) => {
  if (!container) return [];
  
  // 查找所有已标记的可编辑元素
  const markedElements = Array.from(container.querySelectorAll('[data-editable-fengmian]'));
  
  // 如果没有找到已标记的元素，尝试查找潜在的可编辑元素
  if (markedElements.length === 0) {
    return findPotentialEditableElements(container);
  }
  
  return markedElements;
};

/**
 * 安全检查记录管理模块
 */
window.securityCheckRecordsModule = (function() {
  // 页面元素
  let $securityCheckRecordsTable, $securityCheckPagination, $securityCheckKeyword;
  let $securityCheckRiskLevel, $securityCheckSourceType, $securityCheckStartDate, $securityCheckEndDate;
  let $searchSecurityCheckBtn, $refreshSecurityCheckRecords, $exportSecurityCheckBtn;
  let $batchDeleteSecurityCheckBtn, $selectAllSecurityCheck;

  // 分页信息
  let currentPage = 1;
  let totalPages = 1;
  let pageSize = 10;
  let totalRecords = 0;
  
  // 批量操作
  let selectedRecords = new Set();

  // 初始化
  function init() {
    console.log("安全检查记录模块初始化");
    
    // 获取页面元素
    $securityCheckRecordsTable = document.getElementById('securityCheckRecordsTable');
    $securityCheckPagination = document.getElementById('securityCheckPagination');
    $securityCheckKeyword = document.getElementById('securityCheckKeyword');
    $securityCheckRiskLevel = document.getElementById('securityCheckRiskLevel');
    $securityCheckSourceType = document.getElementById('securityCheckSourceType');
    $securityCheckStartDate = document.getElementById('securityCheckStartDate');
    $securityCheckEndDate = document.getElementById('securityCheckEndDate');
    $searchSecurityCheckBtn = document.getElementById('searchSecurityCheckBtn');
    $refreshSecurityCheckRecords = document.getElementById('refreshSecurityCheckRecords');
    $exportSecurityCheckBtn = document.getElementById('exportSecurityCheckBtn');
    $batchDeleteSecurityCheckBtn = document.getElementById('batchDeleteSecurityCheckBtn');
    $selectAllSecurityCheck = document.getElementById('selectAllSecurityCheck');

    bindEvents();
    loadSecurityCheckRecords();
  }

  // 绑定事件
  function bindEvents() {
    // 搜索按钮
    if ($searchSecurityCheckBtn) {
      $searchSecurityCheckBtn.addEventListener('click', function() {
        currentPage = 1;
        loadSecurityCheckRecords();
      });
    }

    // 刷新按钮
    if ($refreshSecurityCheckRecords) {
      $refreshSecurityCheckRecords.addEventListener('click', function() {
        loadSecurityCheckRecords();
      });
    }

    // 回车键搜索
    if ($securityCheckKeyword) {
      $securityCheckKeyword.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          currentPage = 1;
          loadSecurityCheckRecords();
        }
      });
    }

    // 全选复选框
    if ($selectAllSecurityCheck) {
      $selectAllSecurityCheck.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.record-checkbox');
        checkboxes.forEach(checkbox => {
          checkbox.checked = this.checked;
          if (this.checked) {
            selectedRecords.add(checkbox.value);
          } else {
            selectedRecords.delete(checkbox.value);
          }
        });
        updateBatchDeleteButton();
      });
    }

    // 批量删除按钮
    if ($batchDeleteSecurityCheckBtn) {
      $batchDeleteSecurityCheckBtn.addEventListener('click', function() {
        if (selectedRecords.size === 0) {
          alert('请选择要删除的记录');
          return;
        }
        
        if (confirm('确定要删除选中的 ' + selectedRecords.size + ' 条记录吗？此操作不可撤销。')) {
          batchDeleteRecords();
        }
      });
    }
  }

  // 加载安全检查记录
  function loadSecurityCheckRecords(page = 1) {
    currentPage = page;
    
    // 构建查询参数
    const params = new URLSearchParams({
      page: currentPage,
      limit: pageSize
    });

    // 添加筛选条件
    if ($securityCheckKeyword && $securityCheckKeyword.value.trim()) {
      params.append('keyword', $securityCheckKeyword.value.trim());
    }
    if ($securityCheckRiskLevel && $securityCheckRiskLevel.value) {
      params.append('riskLevel', $securityCheckRiskLevel.value);
    }
    if ($securityCheckSourceType && $securityCheckSourceType.value) {
      params.append('sourceType', $securityCheckSourceType.value);
    }
    if ($securityCheckStartDate && $securityCheckStartDate.value) {
      params.append('startDate', $securityCheckStartDate.value);
    }
    if ($securityCheckEndDate && $securityCheckEndDate.value) {
      params.append('endDate', $securityCheckEndDate.value);
    }

    fetch('/api/admin/security/check-records?' + params, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('token'),
        'Content-Type': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        renderSecurityCheckRecords(data.data);
        updateStatistics(data.statistics);
        updatePagination(data.pagination);
      } else {
        console.error('加载安全检查记录失败:', data.message);
        showMessage('error', data.message || '加载数据失败');
      }
    })
    .catch(error => {
      console.error('加载安全检查记录失败:', error);
      showMessage('error', '网络错误，请稍后重试');
    });
  }

  // 渲染安全检查记录
  function renderSecurityCheckRecords(records) {
    if (!$securityCheckRecordsTable) return;

    const tbody = $securityCheckRecordsTable.querySelector('tbody');
    tbody.innerHTML = '';

    records.forEach(function(record) {
      const tr = document.createElement('tr');
      
      // 格式化时间
      const createTime = new Date(record.createdAt || record.created_at).toLocaleString('zh-CN');
      
      // 风险等级样式
      const riskLevelClass = {
        'SAFE': 'success',
        'LOW': 'info',
        'MEDIUM': 'warning',
        'HIGH': 'danger',
        'CRITICAL': 'danger'
      }[record.risk_level] || 'secondary';

      // 来源类型显示
      const sourceTypeMap = {
        'upload': '文件上传',
        'ai_generated': 'AI生成',
        'manual': '手动创建'
      };

      // 检测结果简化显示
      let detectionResult = '未检测';
      if (record.security_scan_result) {
        try {
          const scanResult = typeof record.security_scan_result === 'string' 
            ? JSON.parse(record.security_scan_result) 
            : record.security_scan_result;
          detectionResult = scanResult.passed ? '✅ 通过' : '❌ 未通过';
        } catch (e) {
          detectionResult = '解析错误';
        }
      }

      // 哈希值显示（截取前8位）
      const hashDisplay = record.original_html_hash 
        ? record.original_html_hash.substring(0, 8) + '...' 
        : '无';

      tr.innerHTML = '<td>' +
          '<input type="checkbox" class="form-check-input record-checkbox" value="' + record.id + '">' +
        '</td>' +
        '<td>' + record.id + '</td>' +
        '<td>' + createTime + '</td>' +
        '<td>' + (record.user ? record.user.nickname || '未知用户' : '未知用户') + '</td>' +
        '<td>' + (record.user ? record.user.phone || '未知手机' : '未知手机') + '</td>' +
        '<td><code>' + (record.cover_code || '无编码') + '</code></td>' +
        '<td>' + (sourceTypeMap[record.content_source_type] || record.content_source_type || '未知') + '</td>' +
        '<td><span class="badge bg-' + riskLevelClass + '">' + (record.risk_level || 'SAFE') + '</span></td>' +
        '<td>' + detectionResult + '</td>' +
        '<td><code>' + hashDisplay + '</code></td>' +
        '<td>' +
          '<button class="btn btn-sm btn-info me-1" onclick="securityCheckRecordsModule.viewDetails(' + record.id + ')" title="查看详情">' +
            '<i class="bi bi-eye"></i>' +
          '</button>' +
          '<button class="btn btn-sm btn-primary me-1" onclick="securityCheckRecordsModule.previewCover(' + record.id + ')" title="预览封面">' +
            '<i class="bi bi-window"></i>' +
          '</button>' +
        '</td>';

      tbody.appendChild(tr);
    });

    // 为复选框添加事件监听
    const checkboxes = document.querySelectorAll('.record-checkbox');
    checkboxes.forEach(checkbox => {
      checkbox.addEventListener('change', function() {
        if (this.checked) {
          selectedRecords.add(this.value);
        } else {
          selectedRecords.delete(this.value);
        }
        updateBatchDeleteButton();
        updateSelectAllCheckbox();
      });
    });

    // 更新记录总数显示
    const totalRecordsElement = document.getElementById('totalSecurityCheckRecords');
    if (totalRecordsElement) {
      totalRecordsElement.textContent = totalRecords;
    }

    // 重置选择状态
    selectedRecords.clear();
    updateBatchDeleteButton();
    updateSelectAllCheckbox();
  }

  // 更新统计信息
  function updateStatistics(statistics) {
    if (!statistics) return;

    // 更新统计卡片
    const elements = {
      'totalSecurityChecks': statistics.total || 0,
      'uploadSecurityChecks': statistics.uploads || 0,
      'riskSecurityChecks': statistics.risks || 0,
      'safetyRate': (statistics.safetyRate || 0) + '%'
    };

    Object.keys(elements).forEach(function(id) {
      const element = document.getElementById(id);
      if (element) {
        element.textContent = elements[id];
      }
    });
  }

  // 更新分页
  function updatePagination(pagination) {
    if (!pagination || !$securityCheckPagination) return;

    totalPages = pagination.totalPages || 1;
    totalRecords = pagination.totalRecords || 0;
    currentPage = pagination.currentPage || 1;

    // 生成分页HTML
    let paginationHtml = '';
    
    // 上一页
    paginationHtml += '<li class="page-item ' + (currentPage <= 1 ? 'disabled' : '') + '">' +
      '<a class="page-link" href="#" onclick="securityCheckRecordsModule.loadPage(' + (currentPage - 1) + ')">上一页</a>' +
      '</li>';

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
      paginationHtml += '<li class="page-item"><a class="page-link" href="#" onclick="securityCheckRecordsModule.loadPage(1)">1</a></li>';
      if (startPage > 2) {
        paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      paginationHtml += '<li class="page-item ' + (i === currentPage ? 'active' : '') + '">' +
        '<a class="page-link" href="#" onclick="securityCheckRecordsModule.loadPage(' + i + ')">' + i + '</a>' +
        '</li>';
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
      }
      paginationHtml += '<li class="page-item"><a class="page-link" href="#" onclick="securityCheckRecordsModule.loadPage(' + totalPages + ')">' + totalPages + '</a></li>';
    }

    // 下一页
    paginationHtml += '<li class="page-item ' + (currentPage >= totalPages ? 'disabled' : '') + '">' +
      '<a class="page-link" href="#" onclick="securityCheckRecordsModule.loadPage(' + (currentPage + 1) + ')">下一页</a>' +
      '</li>';

    $securityCheckPagination.innerHTML = paginationHtml;
  }

  // 更新批量删除按钮显示状态
  function updateBatchDeleteButton() {
    if ($batchDeleteSecurityCheckBtn) {
      if (selectedRecords.size > 0) {
        $batchDeleteSecurityCheckBtn.style.display = 'inline-block';
      } else {
        $batchDeleteSecurityCheckBtn.style.display = 'none';
      }
    }
  }

  // 更新全选复选框状态
  function updateSelectAllCheckbox() {
    if ($selectAllSecurityCheck) {
      const checkboxes = document.querySelectorAll('.record-checkbox');
      const checkedCount = selectedRecords.size;
      const totalCount = checkboxes.length;
      
      if (checkedCount === 0) {
        $selectAllSecurityCheck.checked = false;
        $selectAllSecurityCheck.indeterminate = false;
      } else if (checkedCount === totalCount) {
        $selectAllSecurityCheck.checked = true;
        $selectAllSecurityCheck.indeterminate = false;
      } else {
        $selectAllSecurityCheck.checked = false;
        $selectAllSecurityCheck.indeterminate = true;
      }
    }
  }

  // 批量删除记录
  function batchDeleteRecords() {
    const recordIds = Array.from(selectedRecords);
    
    fetch('/api/admin/security/check-records/batch-delete', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + localStorage.getItem('token'),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ ids: recordIds })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showMessage('success', '成功删除 ' + recordIds.length + ' 条记录');
        loadSecurityCheckRecords(); // 重新加载数据
      } else {
        showMessage('error', data.message || '批量删除失败');
      }
    })
    .catch(error => {
      console.error('批量删除失败:', error);
      showMessage('error', '网络错误，请稍后重试');
    });
  }

  // 显示消息
  function showMessage(type, message) {
    // 简单的提示实现
    if (type === 'error') {
      alert('错误: ' + message);
    } else {
      alert(message);
    }
  }

  // 公开的API
  return {
    init: init,
    loadPage: function(page) {
      if (page >= 1 && page <= totalPages) {
        loadSecurityCheckRecords(page);
      }
    },
    viewDetails: function(recordId) {
      console.log('查看详情:', recordId);
      alert('查看详情功能开发中...');
    },
    previewCover: function(recordId) {
      console.log('预览封面:', recordId);
      // 调用封面记录模块的预览功能
      if (window.coverRecordsModule && window.coverRecordsModule.previewCover) {
        window.coverRecordsModule.previewCover(recordId);
      } else {
        alert('预览功能不可用');
      }
    }
  };
})();
 
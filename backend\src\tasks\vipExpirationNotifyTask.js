const { User } = require('../models');
const logger = require('../utils/logger');
const { Op } = require('sequelize');

// 检查系统中是否有消息模型
let Notification;
try {
  Notification = require('../models').Notification;
} catch (error) {
  logger.warn('未找到Notification模型，将只记录日志而不发送通知');
}

const notifyVipExpiration = async () => {
  try {
    // 计算3天后的日期
    const threeDaysLater = new Date();
    threeDaysLater.setDate(threeDaysLater.getDate() + 3);
    
    // 设置日期范围（当天到期和3天后到期之间）
    const startDate = new Date();
    startDate.setHours(0, 0, 0, 0);
    
    const endDate = new Date(threeDaysLater);
    endDate.setHours(23, 59, 59, 999);
    
    // 查找即将到期的VIP用户
    const expiringVipUsers = await User.findAll({
      where: {
        role: 'vip',
        vip_expire_date: {
          [Op.between]: [startDate, endDate]
        }
      }
    });
    
    if (expiringVipUsers.length === 0) {
      logger.info('没有发现即将到期的VIP用户');
      return;
    }
    
    logger.info(`发现${expiringVipUsers.length}个即将到期的VIP用户，开始处理通知`);
    
    // 为每个即将到期的用户处理通知
    for (const user of expiringVipUsers) {
      // 计算剩余天数
      const daysLeft = Math.ceil((new Date(user.vip_expire_date) - new Date()) / (1000 * 60 * 60 * 24));
      
      // 创建通知内容
      const message = `您的VIP会员将在${daysLeft}天后到期，为了不影响您的使用体验，请及时续费。`;
      
      // 如果有通知模型，则创建通知
      if (Notification) {
        try {
          await Notification.create({
            user_id: user.id,
            type: 'vip_expiration',
            content: message,
            is_read: false
          });
          logger.info(`已向用户${user.id}(${user.phone})发送VIP到期提醒，剩余天数: ${daysLeft}`);
        } catch (error) {
          logger.error(`向用户${user.id}发送通知失败:`, error);
        }
      } else {
        // 否则只记录日志
        logger.info(`用户${user.id}(${user.phone})的VIP将在${daysLeft}天后到期，但未发送通知（缺少通知模型）`);
      }
    }
    
    logger.info(`成功处理${expiringVipUsers.length}个用户的VIP到期提醒`);
  } catch (error) {
    logger.error('处理VIP到期提醒任务失败:', error);
    throw error;
  }
};

module.exports = notifyVipExpiration;

/**
 * 支付回调IP白名单中间件
 * 限制只接受支付服务商官方IP的回调请求
 */
const { checkIPWhitelist } = require('../utils/paymentSecurityUtils');
const { logPaymentSecurity } = require('../utils/paymentSecurityUtils');
const logger = require('../utils/logger');

/**
 * 创建支付回调IP白名单检查中间件
 * @param {string} paymentType - 支付类型 'wechat'|'alipay'
 * @returns {function} 中间件函数
 */
const createPaymentIPWhitelistMiddleware = (paymentType) => {
  return async (req, res, next) => {
    const clientIP = req.ip || 
                    req.connection.remoteAddress || 
                    req.socket.remoteAddress || 
                    req.connection.socket.remoteAddress;
    
    try {
      // 获取请求方法和路径，用于日志记录
      const method = req.method;
      const path = req.path;
      
      logger.info(`收到${paymentType}支付回调请求 [${method} ${path}]，客户端IP: ${clientIP}`);
      
      // 检查IP是否在白名单中
      const isAllowed = await checkIPWhitelist(clientIP, paymentType);
      
      if (!isAllowed) {
        // IP不在白名单中，拒绝访问
        logger.warn(`拒绝${paymentType}支付回调请求，IP不在白名单: ${clientIP}`);
        
        // 记录安全日志
        await logPaymentSecurity(
          null, // 用户ID
          '未知订单号', // 尚未解析请求体
          clientIP,
          `${paymentType}_ip_check`,
          'failed',
          { method, path },
          { error: 'IP不在白名单' },
          'high', // 高风险
          `拒绝${paymentType}支付回调，IP不在白名单: ${clientIP}`
        );
        
        // 开发环境下可以放行，方便测试
        if (process.env.NODE_ENV === 'development' && process.env.BYPASS_IP_CHECK === 'true') {
          logger.warn('开发环境跳过IP白名单检查');
          return next();
        }
        
        // 生产环境直接拒绝访问
        return res.status(403).json({
          code: 'FORBIDDEN',
          message: '请求的IP地址不在允许列表中'
        });
      }
      
      // IP在白名单中，记录日志并放行
      logger.info(`${paymentType}支付回调IP检查通过: ${clientIP}`);
      
      // 记录安全日志
      await logPaymentSecurity(
        null, // 用户ID
        '未知订单号', // 尚未解析请求体
        clientIP,
        `${paymentType}_ip_check`,
        'success',
        { method, path },
        { allowed: true },
        'low', // 低风险
        `${paymentType}支付回调IP检查通过: ${clientIP}`
      );
      
      next();
    } catch (error) {
      logger.error(`${paymentType}支付回调IP检查错误:`, error);
      
      // 记录安全日志
      try {
        await logPaymentSecurity(
          null, // 用户ID
          '未知订单号', // 尚未解析请求体
          clientIP,
          `${paymentType}_ip_check`,
          'failed',
          { error: error.message },
          { stack: error.stack },
          'high', // 高风险
          `${paymentType}支付回调IP检查出错: ${error.message}`
        );
      } catch (logError) {
        // 忽略日志记录错误
        logger.error('记录安全日志失败:', logError);
      }
      
      // 出错时拒绝访问
      return res.status(500).json({
        code: 'ERROR',
        message: '检查IP白名单时出错'
      });
    }
  };
};

module.exports = { createPaymentIPWhitelistMiddleware }; 
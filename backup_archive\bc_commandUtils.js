/**
 * 命令执行工具模块
 * 提供用于执行各种编辑命令的功能
 */

// 移除对OperationType的导入，因为EditorHistory.js中没有导出这个变量
// import { OperationType } from './EditorHistory';

/**
 * 创建executeCommand函数的工厂函数
 * @param {Object} dependencies - 函数依赖项
 * @param {Object} dependencies.iframeRef - iframe元素的引用
 * @param {Object} dependencies.textEditorRef - 文本编辑器的引用
 * @param {Object} dependencies.editorHistoryRef - 编辑历史记录的引用
 * @param {Object} dependencies.isUserEditingRef - 用户编辑标志的引用
 * @param {Function} dependencies.onHtmlContentChange - HTML内容变更回调
 * @param {Function} dependencies.debouncedRecordState - 防抖记录状态函数
 * @param {Function} dependencies.getTextEditorInstance - 获取文本编辑器实例的函数
 * @param {Function} dependencies.showPermissionError - 显示权限错误的函数
 * @param {Function} dependencies.checkCommandPermission - 检查命令权限的函数
 * @param {Function} dependencies.checkFeatureAvailability - 检查功能可用性的函数
 * @param {Object} dependencies.styleUtils - 样式应用工具对象
 * @param {Function} dependencies.detectSpecialTemplate - 检测特殊模板的函数
 * @param {Function} dependencies.applySpecialTemplateHandling - 应用特殊模板处理的函数
 * @returns {Function} executeCommand函数
 */
export const createExecuteCommand = ({
  iframeRef,
  textEditorRef,
  editorHistoryRef,
  isUserEditingRef,
  onHtmlContentChange,
  debouncedRecordState,
  getTextEditorInstance,
  showPermissionError,
  checkCommandPermission,
  checkFeatureAvailability,
  styleUtils = {},
  detectSpecialTemplate,
  applySpecialTemplateHandling
}) => {
  // 解构样式应用工具
  const {
    applyFontSize,
    applyColor,
    applyBackColor,
    applyFontFamily,
    applyBold,
    applyItalic,
    applyUnderline,
    applyBorderStyle,
    applyBorderWidth,
    applyBorderColor
  } = styleUtils;

  /**
   * 执行编辑命令的函数
   * @param {string} command - 要执行的命令
   * @param {any} value - 命令的值（可选）
   * @returns {Promise<void>}
   */
  const executeCommand = async (command, value = null) => {
    // 统一处理撤销/重做，并进行权限检查
    if (command === 'undo' || command === 'redo') {
      // 使用permissionUtils中的checkCommandPermission函数
      const hasPermission = await checkCommandPermission(command, showPermissionError);
      if (!hasPermission) return;
      
      if (editorHistoryRef.current) {
        // 保存当前删除模式状态
        const isDeleteMode = textEditorRef.current && 
                           typeof textEditorRef.current.getDeleteMode === 'function' ? 
                           textEditorRef.current.getDeleteMode() : false;
                           
        // 执行撤销/前进操作
        if (command === 'undo') {
          editorHistoryRef.current.undo();
        } else {
          editorHistoryRef.current.redo();
        }
        
        // 在状态恢复后，确保删除按钮状态正确
        setTimeout(() => {
          if (textEditorRef.current && typeof textEditorRef.current.setDeleteMode === 'function') {
            // 重新应用删除模式状态
            textEditorRef.current.setDeleteMode(isDeleteMode);
          }
        }, 100);
      }
      return; // 操作后直接返回
    }
    
    // 处理插入文本框命令
    if (command === 'insertTextBox') {
      // 检查权限
      const result = await checkFeatureAvailability('文本编辑');
      if (!result.available) {
        showPermissionError('文本编辑', result.reason || '您的账号权限不足，无法使用此功能');
        return;
      }
      
      // 确保iframe引用有效
      if (!iframeRef.current || !iframeRef.current.contentDocument) {
        return;
      }
      
      const iframeDoc = iframeRef.current.contentDocument;
      
      // 创建一个新的文本框
      const contentContainer = iframeDoc.querySelector('.content-container');
      if (!contentContainer) {
        return;
      }
      
      // 确保容器有相对定位，以便子元素可以绝对定位
      const containerStyle = window.getComputedStyle(contentContainer);
      if (containerStyle.position === 'static') {
        contentContainer.style.position = 'relative';
      }
      
      // 创建文本框元素
      const textBox = iframeDoc.createElement('div');
      textBox.setAttribute('contenteditable', 'true');
      textBox.setAttribute('data-editable-fengmian', 'true');
      
      // 设置初始内容
      textBox.textContent = '新增文本框';
      
      // 先添加到容器
      contentContainer.appendChild(textBox);
      
      // 设置文本框样式 - 完全透明背景，自适应宽度
      Object.assign(textBox.style, {
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)', // 使用transform实现精确居中
        padding: '0', // 移除内边距，使文本框大小精确匹配文本
        border: '1px dashed transparent', // 完全透明的边框
        backgroundColor: 'transparent', // 透明背景
        zIndex: '100',
        borderRadius: '4px',
        fontSize: '16px',
        lineHeight: '1.5',
        color: '#333333',
        width: 'auto', // 自适应宽度
        boxSizing: 'border-box',
        overflow: 'visible',
        margin: '0' // 确保没有外边距
      });
      
      // 同步执行关键操作，确保状态一致性
      // 将新元素添加到文本编辑器中进行管理
      const textEditor = getTextEditorInstance();
      if (textEditor && typeof textEditor.addNewElementToEditor === 'function') {
        textEditor.addNewElementToEditor(textBox);
      }
      
      // 触发内容变更
      if (onHtmlContentChange) {
        onHtmlContentChange(contentContainer.innerHTML);
      }
      
      // 记录新的状态到历史记录
      debouncedRecordState();
      
      return;
    }

    // 对于其他所有命令，检查权限
    const result = await checkFeatureAvailability('文本编辑');
    if (!result.available) {
      showPermissionError('文本编辑', result.reason || '您的账号权限不足，无法使用此功能');
      return;
    }

    // 立即设置用户编辑标志，防止useEffect重新加载内容
    isUserEditingRef.current = true;

    try {
      if (!iframeRef.current) return;
      
      const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
      const iframeWindow = iframeRef.current.contentWindow;
      if (!iframeDoc || !iframeWindow) return;
      
      // 尝试让 iframe 获得焦点，确保选区可用
      if (iframeWindow) {
        iframeWindow.focus();
      }
      
      // 获取内容容器
      const contentContainer = iframeDoc.querySelector('.content-container');
      if (!contentContainer) return;
      
      // 获取当前选区 - 修复：必须使用 let 以允许在需要时重新赋值
      let selection = iframeDoc.getSelection();
      let hasValidSelection = selection && selection.rangeCount > 0 && !selection.getRangeAt(0).collapsed;
      
      // 使用中间件API获取当前活动元素
      let activeElement = null;
      let middleware = null;
      
      if (textEditorRef.current && textEditorRef.current.getMiddleware) {
        middleware = textEditorRef.current.getMiddleware();
        if (middleware) {
          activeElement = middleware.getSelectedElement();
        }
      }
      
      if (!activeElement) {
        activeElement = iframeDoc.querySelector('[contenteditable="true"]:focus') ||
                        iframeDoc.querySelector('[contenteditable="true"].drag-mode') ||
                        iframeDoc.querySelector('[data-editing="true"]');
      }

      // 如果没有有效选区也没有活动元素，尝试恢复焦点
      if (!hasValidSelection && !activeElement) {
        // 尝试再次让 iframe 获得焦点
        if (iframeWindow) {
          iframeWindow.focus();
          
          // 重新检查选区
          selection = iframeDoc.getSelection();
          hasValidSelection = selection && selection.rangeCount > 0 && !selection.getRangeAt(0).collapsed;
          
          if (!hasValidSelection && command !== 'insertImage') { // 如果不是插入图片，则放弃
            return;
          }
        } else if (command !== 'insertImage') { // 如果不是插入图片，则放弃
          return;
        }
      }

      // 检查元素是否已经处于编辑状态
      const isAlreadyEditing = activeElement && activeElement.hasAttribute('data-editing');
      const isEditMode = middleware ? middleware.getActiveMode() === 'editMode' : false;
      
      if (activeElement && !isAlreadyEditing && !isEditMode) {
        if (middleware && middleware.getActiveMode() === 'dragMode') {
          if (middleware.dragMode && middleware.dragMode.clearAllDragModes) {
            middleware.dragMode.clearAllDragModes();
          }
        }
        
        activeElement.setAttribute('contenteditable', 'true');
        activeElement.setAttribute('data-editing', 'true');
        
        if (middleware) {
          middleware.setActiveMode('editMode');
          middleware.selectElement(activeElement);
        }
        
        // 只有在没有有效选区的情况下才设置焦点
        if (!hasValidSelection) {
          activeElement.focus();
        }
      }

      // 确保启用 styleWithCSS 以便正确应用样式
      try {
        if (iframeDoc.queryCommandSupported('styleWithCSS')) {
          iframeDoc.execCommand('styleWithCSS', false, true);
        }
      } catch (e) {
        // 忽略错误
      }

      // 应用相应的命令
      switch (command) {
        case 'bold':
          if(applyBold) applyBold(activeElement);
          break;
        case 'italic':
          if(applyItalic) applyItalic(activeElement);
          break;
        case 'underline':
          if(applyUnderline) applyUnderline(activeElement);
          break;
        case 'foreColor':
          if(applyColor) applyColor(activeElement, value);
          break;
        case 'backColor':
          if(applyBackColor) applyBackColor(activeElement, value);
          break;
        case 'fontName':
          if(applyFontFamily) applyFontFamily(activeElement, value);
          break;
        case 'fontSize':
          if(applyFontSize) applyFontSize(activeElement, value);
          break;
        case 'border-style':
          if (applyBorderStyle) applyBorderStyle(activeElement, value);
          break;
        case 'border-width':
          if (applyBorderWidth) applyBorderWidth(activeElement, value);
          break;
        case 'border-color':
          if (applyBorderColor) applyBorderColor(activeElement, value);
          break;
        case 'justifyLeft':
          if (activeElement) activeElement.style.textAlign = 'left';
          break;
        case 'justifyCenter':
          if (hasValidSelection) {
            iframeDoc.execCommand('justifyCenter', false, null);
          } else if (activeElement) {
            activeElement.style.textAlign = 'center';
          }
          break;
        case 'justifyRight':
          if (hasValidSelection) {
            iframeDoc.execCommand('justifyRight', false, null);
          } else if (activeElement) {
            activeElement.style.textAlign = 'right';
          }
          break;
        case 'insertImage': {
             // 智能查找合适的容器来插入图片
             // 1. 首先尝试找到当前活动的可编辑元素的父容器
             let targetContainer = null;
             
             // 如果有活动元素，优先使用其父容器
             if (activeElement) {
               // 查找最近的定位容器（有position非static的父元素）
               let parent = activeElement.parentElement;
               while (parent && parent !== iframeDoc.body) {
                 const computedStyle = window.getComputedStyle(parent);
                 if (computedStyle.position !== 'static') {
                   targetContainer = parent;
                   break;
                 }
                 parent = parent.parentElement;
               }
             }
             
             // 2. 如果没有找到合适的父容器，尝试查找特定的内容容器
             if (!targetContainer) {
               // 按优先级尝试查找各种可能的内容容器
               const possibleContainers = [
                 '.content', // 优先查找.content（如您提供的模板中的容器）
                 '.wechat-cover > .content', // 更精确的路径
                 '.content-container', // 回退到默认容器
                 '.wechat-cover', // 最外层容器
                 'body' // 最后的回退选项
               ];
               
               for (const selector of possibleContainers) {
                 const container = iframeDoc.querySelector(selector);
                 if (container) {
                   targetContainer = container;
                   break;
                 }
               }
             }
             
             // 3. 如果仍然没有找到容器，使用默认的.content-container
             if (!targetContainer) {
               targetContainer = iframeDoc.querySelector('.content-container');
             }
             
             // 如果没有找到任何容器，报错并退出
             if (!targetContainer) {
                console.error("错误：找不到合适的容器，无法插入图片。");
                break; 
             }
             
             // 确保目标容器有position属性，这对于绝对定位的子元素是必需的
             const containerStyle = window.getComputedStyle(targetContainer);
             if (containerStyle.position === 'static') {
               targetContainer.style.position = 'relative';
             }
 
             // 以编程方式创建图片容器
             const imgWrapper = iframeDoc.createElement('div');
             imgWrapper.setAttribute('contenteditable', 'true');
             imgWrapper.setAttribute('data-editable-fengmian', 'true');
             
             // 设置其为绝对定位
             imgWrapper.style.position = 'absolute';
             
             // 创建图片元素
             const imgElement = iframeDoc.createElement('img');
             
             // 使用 addEventListener 替代内联脚本
             const handleLoad = () => {
               const newWidth = imgElement.naturalWidth > 0 ? imgElement.naturalWidth + 4 : 104;
               const newHeight = imgElement.naturalHeight > 0 ? imgElement.naturalHeight + 4 : 104;
               imgWrapper.style.width = `${newWidth}px`;
               imgWrapper.style.height = `${newHeight}px`;
 
               // 使用目标容器进行定位计算
               const containerRect = targetContainer.getBoundingClientRect();
               const scrollTop = targetContainer.scrollTop;
               const scrollLeft = targetContainer.scrollLeft;
               
               let top = scrollTop + (containerRect.height / 2) - (newHeight / 2);
               let left = scrollLeft + (containerRect.width / 2) - (newWidth / 2);
 
               top = Math.max(scrollTop + 5, top);
               left = Math.max(scrollLeft + 5, left);
 
               imgWrapper.style.top = `${top}px`;
               imgWrapper.style.left = `${left}px`;

               imgElement.removeEventListener('load', handleLoad);
               imgElement.removeEventListener('error', handleError);
               
               // 图片加载完成后，使用新的、安全的方法将图片元素注册到编辑器中
              if (textEditorRef.current && textEditorRef.current.addNewElementToEditor) {
                setTimeout(() => {
                  try {
                    // 为图片容器添加可编辑属性，以符合编辑器的内部逻辑
                    imgWrapper.setAttribute('data-editable-fengmian', 'true');
                    
                    // 调用新的、安全的方法来独立初始化这个新元素，而不影响其他任何元素
                    textEditorRef.current.addNewElementToEditor(imgWrapper);
                    
                    // 处理特殊样式，确保拖拽功能正常工作
                    if (imgWrapper.style) {
                      // 确保图片容器有正确的position属性
                      if (!imgWrapper.style.position || imgWrapper.style.position === 'static') {
                        imgWrapper.style.position = 'absolute';
                      }
                      
                      // 确保图片容器有较高的z-index，以便在复杂布局中可见
                      imgWrapper.style.zIndex = '10';
                      
                      // 移除可能干扰拖拽的样式
                      if (imgWrapper.style.overflowWrap === 'break-word' && imgWrapper.style.wordBreak === 'break-all') {
                        imgWrapper.style.overflowWrap = '';
                        imgWrapper.style.wordBreak = '';
                      }
                    }
                    
                    // 增强：检测特殊模板结构并应用额外处理
                    const isSpecialTemplate = detectSpecialTemplate(iframeDoc);
                    if (isSpecialTemplate) {
                      // 增加额外延迟，确保特殊模板中的样式和动画处理完成
                      setTimeout(() => {
                        // 再次确保图片容器可以正常拖拽
                        applySpecialTemplateHandling(imgWrapper, targetContainer);
                      }, 300);
                    }
                  } catch (error) {
                    console.error("Error adding new image element to the editor:", error);
                  }
                }, 200); // 增加延迟时间，确保DOM完全更新
              }
             };

             const handleError = () => {
                 imgElement.src = 'data:image/svg+xml;charset=utf-8,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%22100%22 height=%22100%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22currentColor%22 stroke-width=%222%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22><rect x=%223%22 y=%223%22 width=%2218%22 height=%2218%22 rx=%222%22 ry=%222%22/><polyline points=%2221 15 16 10 5 21%22/></svg>';
             };

             imgElement.addEventListener('load', handleLoad);
             imgElement.addEventListener('error', handleError);

             imgElement.src = value; 
             imgElement.style.display = 'block';
             imgElement.style.pointerEvents = 'none';
             imgElement.setAttribute('contenteditable', 'false');
 
             // 组合元素并添加到目标容器中
             imgWrapper.appendChild(imgElement);
             targetContainer.appendChild(imgWrapper);
 
             // 移除全局初始化调用，避免与单独初始化冲突
             // 注释掉以下代码，而不是删除，以便于未来调试
             /*
             if (textEditorRef.current && textEditorRef.current.init) {
               setTimeout(() => {
                 textEditorRef.current.init();
               }, 100); 
             }
             */
 
             break;
           }
        default:
          iframeDoc.execCommand(command, false, value);
          break;
      }
      
      // 无需手动记录历史，MutationObserver会自动处理
      // 只需确保父组件状态被更新
      if (onHtmlContentChange) {
        onHtmlContentChange(contentContainer.innerHTML);
      }

    } catch (error) {
      console.error("执行命令出错:", error);
    }
  };

  return executeCommand;
};

export default createExecuteCommand; 
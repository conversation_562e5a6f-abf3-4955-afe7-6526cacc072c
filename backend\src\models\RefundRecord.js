const { DataTypes } = require('sequelize');

/**
 * 退款记录模型
 * 用于记录订单退款信息
 */
module.exports = (sequelize) => {
  const RefundRecord = sequelize.define('RefundRecord', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    payment_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '关联支付记录ID'
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '用户ID'
    },
    refund_no: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '退款单号'
    },
    refund_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      comment: '退款金额'
    },
    refund_reason: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '退款原因'
    },
    refund_status: {
      type: DataTypes.ENUM('pending', 'success', 'failed'),
      allowNull: false,
      defaultValue: 'pending',
      comment: '退款状态'
    },
    transaction_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '第三方交易ID'
    },
    refund_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '退款完成时间'
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '退款备注信息'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'refund_records',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        name: 'idx_payment_id',
        fields: ['payment_id']
      },
      {
        name: 'idx_user_id',
        fields: ['user_id']
      },
      {
        name: 'idx_refund_status',
        fields: ['refund_status']
      }
    ]
  });

  // 定义关联关系
  RefundRecord.associate = (models) => {
    // 退款记录属于支付记录
    RefundRecord.belongsTo(models.PaymentRecord, {
      foreignKey: 'payment_id',
      as: 'payment'
    });

    // 退款记录属于用户
    RefundRecord.belongsTo(models.User, {
      foreignKey: 'user_id',
      as: 'user'
    });
  };

  return RefundRecord;
}; 
const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const coverController = require('../controllers/coverController');
const { auth } = require('../middlewares/authMiddleware');
const { validate, schemas } = require('../utils/validator');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 头像上传配置
const avatarStorage = multer.diskStorage({
  destination: function(req, file, cb) {
    const uploadDir = path.join(__dirname, '../../public/uploads/avatars');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function(req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'avatar-' + req.user.id + '-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  const allowedTypes = /jpeg|jpg|png|gif|webp/;
  const ext = path.extname(file.originalname).toLowerCase();
  if (allowedTypes.test(ext)) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件'));
  }
};

// 创建头像上传中间件
const uploadAvatar = multer({
  storage: avatarStorage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 限制5MB
  fileFilter: fileFilter
}).single('avatar');

// 所有用户路由都需要认证
router.use(auth);

// 获取个人资料
router.get('/profile', userController.getUserProfile);

// 更新个人资料
router.put('/profile', userController.updateUserProfile);

// 获取用户积分
router.get('/points', userController.getUserPoints);

// 获取积分记录
router.get('/point-records', userController.getPointRecords);

// 获取每日积分
router.get('/daily-points', userController.getUserDailyPoints);

// 消费积分
router.post('/consume-points', userController.consumePoints);

// 获取用户封面生成记录
router.get('/covers', userController.getUserCovers);

// 隐藏用户封面记录（用户删除）
router.put('/covers/:id/hide', coverController.hideCoverRecord);

// 上传头像
router.post('/avatar', uploadAvatar, userController.uploadAvatar);

// 设置或修改密码
router.post('/set-password', validate(schemas.setPassword), userController.setPassword);

module.exports = router;

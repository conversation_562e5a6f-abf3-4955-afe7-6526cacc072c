import axios from 'axios';
import { message, Modal } from 'antd';

// 创建全局axios拦截器，处理所有请求的token过期和认证错误
const setupAxiosInterceptors = () => {
  // 请求拦截器：确保每个请求都带上最新的token
  axios.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // 响应拦截器：处理token过期等错误
  axios.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      // 处理401错误（未授权/token过期）
      if (error.response && error.response.status === 401) {
        // 排除登录页面的401错误
        const isAuthPage = window.location.pathname === '/auth' || 
                          (window.location.pathname === '/' && window.location.search.includes('view=auth')) ||
                          window.location.pathname === '/login' || 
                          window.location.pathname === '/register';
        
        // 如果不是登录页面，才显示"登录已过期"提示
        if (!isAuthPage) {
          // 检查是否已显示登录过期提示，避免重复显示
          const tokenErrorKey = 'token_error_shown';
          if (!window[tokenErrorKey]) {
            console.error('Token过期或无效，需要重新登录');
            
            // 清除已过期的登录信息
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            
            // 设置标记避免重复显示
            window[tokenErrorKey] = true;
            
            // 显示友好的错误提示
            Modal.error({
              title: '登录已过期',
              content: '您的登录已过期，请重新登录',
              okText: '去登录',
              onOk: () => {
                // 保存当前URL以便登录后跳回
                const currentPath = window.location.pathname + window.location.search;
                // 设置一个延迟清除标记的定时器
                setTimeout(() => {
                  window[tokenErrorKey] = false;
                }, 3000);
                window.location.href = `/auth?redirect=${encodeURIComponent(currentPath)}`;
              },
              onCancel: () => {
                // 清除标记，允许再次显示错误
                setTimeout(() => {
                  window[tokenErrorKey] = false;
                }, 3000);
              }
            });
          }
        }
      }
      // 处理服务器错误（5xx）
      else if (error.response && error.response.status >= 500) {
        message.error({
          content: '服务器暂时出现问题，请稍后再试',
          key: 'server_error',
          duration: 3
        });
      }
      
      return Promise.reject(error);
    }
  );
};

// 导出初始化函数
export default setupAxiosInterceptors; 
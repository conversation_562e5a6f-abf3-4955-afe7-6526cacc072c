-- 初始化会员套餐数据
INSERT INTO `member_packages` (`name`, `duration`, `price`, `discount_price`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
('月度会员', 30, 29.90, 29.90, '高级会员月卡，享受全部特权', 1, NOW(), NOW()),
('季度会员', 90, 79.90, 69.90, '高级会员季卡，享受全部特权，相比月卡更优惠', 1, NOW(), NOW()),
('年度会员', 365, 299.90, 199.90, '高级会员年卡，享受全部特权，最佳性价比选择', 1, NOW(), NOW());

-- 初始化会员权益数据
INSERT INTO `member_benefits` (`benefit_code`, `benefit_name`, `benefit_desc`, `benefit_icon`, `apply_role`, `sort_order`, `is_active`) VALUES
('custom_image', '自定义图片', '可上传自定义图片作为封面背景', 'image-icon.svg', 'vip', 1, 1),
('edit_cover', '编辑封面', '可以编辑生成的封面，调整布局和样式', 'edit-icon.svg', 'vip', 2, 1),
('download_html', '下载HTML', '可下载封面的HTML源代码，便于网站嵌入', 'html-icon.svg', 'vip', 3, 1),
('share_link', '分享链接', '生成封面分享链接，一键分享给他人', 'share-icon.svg', 'vip', 4, 1),
('daily_points', '每日积分', '每日自动赠送额外积分', 'points-icon.svg', 'vip', 5, 1);

-- 初始化支付配置（这里只添加示例配置，实际配置需要替换为真实值）
-- 支付宝配置
INSERT INTO `payment_configs` (`payment_type`, `config_name`, `config_key`, `config_value`, `is_encrypted`, `is_active`, `description`) VALUES
('alipay', '支付宝应用ID', 'app_id', '2021000000000000', 0, 1, '支付宝应用的唯一标识'),
('alipay', '支付宝应用私钥', 'private_key', '请替换为实际的应用私钥', 1, 1, '用于签名的应用私钥'),
('alipay', '支付宝公钥', 'alipay_public_key', '请替换为实际的支付宝公钥', 1, 1, '用于验证支付宝返回数据的签名'),
('alipay', '支付成功后跳转地址', 'return_url', 'https://www.example.com/payment/callback/alipay-return', 0, 1, '支付成功后跳转页面'),
('alipay', '支付结果异步通知地址', 'notify_url', 'https://www.example.com/api/payment/callback/alipay', 0, 1, '支付结果异步通知接收地址');

-- 微信支付配置
INSERT INTO `payment_configs` (`payment_type`, `config_name`, `config_key`, `config_value`, `is_encrypted`, `is_active`, `description`) VALUES
('wechat', '微信支付商户号', 'mch_id', '1900000000', 0, 1, '微信支付商户号'),
('wechat', '微信支付应用ID', 'app_id', 'wx00000000000000', 0, 1, '微信支付应用ID'),
('wechat', '微信支付API密钥', 'key', '请替换为实际的API密钥', 1, 1, '微信支付API密钥'),
('wechat', '微信支付API v3密钥', 'key_v3', '请替换为实际的API v3密钥', 1, 1, '微信支付API v3密钥'),
('wechat', '微信支付证书序列号', 'serial_no', '请替换为实际的证书序列号', 0, 1, '微信支付平台证书序列号'),
('wechat', '微信支付结果通知地址', 'notify_url', 'https://www.example.com/api/payment/callback/wechat', 0, 1, '支付结果通知接收地址'); 
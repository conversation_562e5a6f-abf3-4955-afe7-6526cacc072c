/**
 * 支付安全日志模型
 * 用于记录支付过程中的安全相关事件，如签名验证、IP白名单检查等
 */
const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const PaymentSecurityLog = sequelize.define('payment_security_logs', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    user_id: {
      type: DataTypes.INTEGER,
      comment: '用户ID',
      allowNull: true
    },
    order_no: {
      type: DataTypes.STRING(50),
      comment: '订单号',
      allowNull: true
    },
    ip_address: {
      type: DataTypes.STRING(50),
      comment: 'IP地址',
      allowNull: true
    },
    action: {
      type: DataTypes.STRING(50),
      comment: '操作类型',
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('success', 'failed', 'suspicious'),
      defaultValue: 'success',
      comment: '状态'
    },
    request_data: {
      type: DataTypes.TEXT,
      comment: '请求数据',
      allowNull: true
    },
    response_data: {
      type: DataTypes.TEXT,
      comment: '响应数据',
      allowNull: true
    },
    risk_level: {
      type: DataTypes.ENUM('low', 'medium', 'high'),
      defaultValue: 'low',
      comment: '风险等级'
    },
    description: {
      type: DataTypes.STRING(255),
      comment: '描述',
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '创建时间'
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      comment: '更新时间'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['order_no']
      },
      {
        fields: ['user_id']
      },
      {
        fields: ['created_at']
      }
    ]
  });

  return PaymentSecurityLog;
}; 
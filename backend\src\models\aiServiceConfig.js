const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AIServiceConfig = sequelize.define('ai_service_configs', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  service: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'AI服务商，如deepseek, 豆包等'
  },
  service_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: 'AI服务名称，如deepseek, xai等'
  },
  base_url: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'API基础URL'
  },
  api_key: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'API密钥，加密存储'
  },
  model_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '模型名称，如deepseek-chat, gpt-4等'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否为当前激活的服务'
  },
  request_format: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '请求格式模板，JSON格式'
  },
  response_format: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '响应格式处理方式，JSON格式'
  },
  service_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    defaultValue: '文本AI',
    comment: 'AI服务类型，如文本AI、图像AI等'
  },
  weight: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '权重，用于负载均衡，值越大分配的请求越多'
  },
  priority: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '优先级，值越大优先级越高，优先使用高优先级的服务'
  },
  max_concurrent_requests: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '最大并发请求数，0表示不限制'
  },
  parameters: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'AI服务参数配置，JSON格式存储'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = AIServiceConfig;

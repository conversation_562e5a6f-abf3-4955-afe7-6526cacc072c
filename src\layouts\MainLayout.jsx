import React, { useState, useEffect, useRef } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { message, Modal } from 'antd';
import ChatSidebar from '../components/chat/ChatSidebar';
import { useAuthContext } from '../contexts/AuthContext';
import { cancelCoverGeneration, getCurrentTaskId } from '../services/aiService';
import axios from 'axios';

/**
 * 主布局组件
 * 包含左侧边栏和内容区域，作为应用的主框架
 */
const MainLayout = () => {
  const { user: userInfo, setUser } = useAuthContext();
  const location = useLocation();
  const navigate = useNavigate();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [activeView, setActiveView] = useState('chat');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isHtmlLoaded, setIsHtmlLoaded] = useState(false);
  const [isHoverEnabled, setIsHoverEnabled] = useState(false);
  const sidebarRef = useRef(null);
  
  // 根据路径确定当前活动视图
  useEffect(() => {
    const pathname = location.pathname;
    
    if (pathname === '/' || pathname === '/home') {
      setActiveView('chat');
    } else if (pathname === '/profile') {
      setActiveView('profile');
    } else if (pathname === '/points') {
      setActiveView('points');
    } else if (pathname === '/orders') {
      setActiveView('orders');
    } else if (pathname === '/covers') {
      setActiveView('covers');
    } else if (pathname === '/my-creations') {
      setActiveView('my-creations');
    } else if (pathname === '/auth') {
      setActiveView('auth');
    } else if (pathname.startsWith('/payment')) {
      setActiveView('payment');
    } else if (pathname.startsWith('/payment-result')) {
      setActiveView('payment-result');
    } else if (pathname === '/membership') {
      setActiveView('membership');
    } else if (pathname === '/file-upload') {
      setActiveView('file-upload');
    } else if (pathname === '/code-paste') {
      setActiveView('code-paste');
    } else {
      setActiveView('chat');
    }
  }, [location.pathname]);

  // 组件挂载时初始化用户资料，不设置额外的定时刷新
  // AuthContext已经有定时刷新机制，避免重复请求
  useEffect(() => {
    // 不需要额外的初始化，AuthContext已经处理
  }, []);

  // 处理侧边栏折叠切换
  const toggleSidebarCollapse = () => {
    // 如果用户手动点击折叠按钮，禁用鼠标悬停功能
    if (isHoverEnabled) {
      setIsHoverEnabled(false);
    }
    setIsSidebarCollapsed(prev => !prev);
  };

  // 处理重置设计
  const handleResetDesign = () => {
    // 如果当前不在主页，先导航到主页
    if (location.pathname !== '/') {
      navigate('/', { replace: true });
      return;
    }
    
    // 如果已经在主页，检查是否有未保存的更改
    if (hasUnsavedChanges) {
      Modal.confirm({
        title: '重置确认',
        content: '您确定要重置当前设计吗？所有未保存的更改将会丢失。',
        okText: '确认重置',
        cancelText: '取消',
        onOk: () => {
          // 触发自定义事件，通知ChatGenerate组件执行重置
          const resetEvent = new CustomEvent('resetDesign');
          window.dispatchEvent(resetEvent);
          
          // 清除未保存更改标记
          setHasUnsavedChanges(false);
        }
      });
    } else {
      // 如果没有未保存的更改，直接触发重置事件
      const resetEvent = new CustomEvent('resetDesign');
      window.dispatchEvent(resetEvent);
    }
  };

  // 处理加载封面数据
  const handleLoadCoverData = async (workId, coverCode) => {
    // 如果没有封面编码，显示错误信息
    if (!coverCode) {
      message.error('封面编码不存在，无法查看');
      return;
    }

    try {
      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      // 如果当前不在主页，先导航到主页
      if (location.pathname !== '/') {
        navigate('/', { replace: true });
      }

      // 通过cover_code获取封面详情，添加缓存破坏参数
      const response = await axios.get(`/api/cover/code/${coverCode}/edit?_=${new Date().getTime()}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        const coverData = response.data.data;
        
        // 将封面数据存储在localStorage中
        try {
          localStorage.setItem('pendingCoverData', JSON.stringify(coverData));
          
          // 触发自定义事件，通知ChatGenerate组件加载封面数据
          const loadCoverEvent = new CustomEvent('loadCoverData', { 
            detail: { coverData, source: 'sidebar' } 
          });
          window.dispatchEvent(loadCoverEvent);
          
          // 如果不在主页，导航到主页
          if (location.pathname !== '/') {
            navigate('/', { replace: true });
          }
        } catch (error) {
          console.error('存储封面数据失败:', error);
          message.error('加载编辑器失败，请稍后再试');
        }
      } else {
        message.error(response.data.message || '获取封面详情失败');
      }
    } catch (error) {
      console.error('加载封面数据失败:', error);
      message.error('加载封面数据失败，请稍后再试');
    }
  };

  // 处理导航项点击
  const handleNavItemClick = (view) => {
    // 定义清除消息的函数
    const clearMessage = () => {
      message.destroy(); // 清除所有消息
    };

    // 执行导航的函数
    const performNavigation = () => {
      if (view !== activeView) {
        // 执行轻量级状态更新
        setActiveView(view);
        
        // 清除可能的消息通知
        clearMessage();
        
        // 使用React Router的navigate函数进行页面导航
        switch(view) {
          case 'chat':
            navigate('/', { replace: true });
            break;
          case 'profile':
            navigate('/profile', { replace: true });
            break;
          case 'points':
            navigate('/points', { replace: true });
            break;
          case 'orders':
            navigate('/orders', { replace: true });
            break;
          case 'covers':
            navigate('/covers', { replace: true });
            break;
          case 'my-creations':
            navigate('/my-creations', { replace: true });
            break;
          case 'membership':
            navigate('/membership', { replace: true });
            break;
          case 'auth':
            navigate('/auth', { replace: true });
            break;
          case 'payment':
            navigate('/payment', { replace: true });
            break;
          case 'payment-result':
            navigate('/payment-result', { replace: true });
            break;
          case 'file-upload':
            navigate('/file-upload', { replace: true });
            break;
          case 'code-paste':
            navigate('/code-paste', { replace: true });
            break;
          default:
            navigate('/', { replace: true });
        }
      }
    };

    // 检查是否有未保存的更改
    if (hasUnsavedChanges && location.pathname === '/' && view !== 'chat') {
      Modal.confirm({
        title: '离开提示',
        content: '您有未保存的封面内容，离开此页面可能会丢失您的编辑。确定离开吗？',
        okText: '继续离开',
        cancelText: '留在此页',
        onOk: () => {
          // 执行导航
          performNavigation();
        },
        onCancel: () => {
          // 用户取消离开，不执行任何操作
        }
      });
    } else {
      // 没有未保存的更改，直接导航
      performNavigation();
    }
  };

  // 监听自定义事件，用于设置未保存更改状态
  useEffect(() => {
    const handleUnsavedChanges = (event) => {
      setHasUnsavedChanges(event.detail.hasUnsavedChanges);
    };
    
    const handleResetDesign = () => {
      // 重置设计时，重置HTML加载状态和悬停功能
      setIsHtmlLoaded(false);
      setIsHoverEnabled(false);
      if (isSidebarCollapsed) {
        setIsSidebarCollapsed(false);
      }
    };

    window.addEventListener('unsavedChanges', handleUnsavedChanges);
    window.addEventListener('resetDesign', handleResetDesign);
    
    return () => {
      window.removeEventListener('unsavedChanges', handleUnsavedChanges);
      window.removeEventListener('resetDesign', handleResetDesign);
    };
  }, [isSidebarCollapsed]);

  // 处理用户信息更新
  const handleUserInfoUpdated = (updatedUser) => {
    if (updatedUser && userInfo && updatedUser.id === userInfo.id) {
      // 更新用户信息
      setUser(updatedUser);
    }
  };
  
  // 监听HTML加载完成事件
  useEffect(() => {
    const mounted = { current: true };
    
    const handleHtmlLoaded = (event) => {
      if (!mounted.current) return;
      setIsHtmlLoaded(event.detail.isLoaded);
      
      // 如果处于主页且HTML加载完成，自动收折侧边栏并启用鼠标悬停功能
      if (event.detail.isLoaded && location.pathname === '/') {
        setIsSidebarCollapsed(true);
        setIsHoverEnabled(true);
      }
    };
    
    window.addEventListener('htmlLoaded', handleHtmlLoaded);
    
    return () => {
      mounted.current = false;
      window.removeEventListener('htmlLoaded', handleHtmlLoaded);
    };
  }, [location.pathname]);
  
  // 监听路由变化，重置状态
  useEffect(() => {
    const mounted = { current: true };
    
    // 如果不在主页或者主页未加载HTML，取消鼠标悬停功能并展开侧边栏
    if (mounted.current && (location.pathname !== '/' || !isHtmlLoaded)) {
      setIsHoverEnabled(false);
      if (isSidebarCollapsed) {
        setIsSidebarCollapsed(false);
      }
    }
    
    return () => {
      mounted.current = false;
    };
  }, [location.pathname, isHtmlLoaded, isSidebarCollapsed]);
  
  // 处理鼠标悬停事件
  const handleMouseEnter = () => {
    if (isHoverEnabled && isSidebarCollapsed) {
      setIsSidebarCollapsed(false);
    }
  };
  
  const handleMouseLeave = () => {
    if (isHoverEnabled && !isSidebarCollapsed) {
      setIsSidebarCollapsed(true);
    }
  };

  return (
    <div className="flex h-screen w-screen bg-gray-100 overflow-hidden">
      {/* 左侧边栏 */}
      <div
        ref={sidebarRef}
        className={`bg-white shadow-lg overflow-hidden flex-none z-20 transition-all duration-300 ease-in-out ${isSidebarCollapsed ? 'w-16' : 'w-64'}`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <ChatSidebar
          isCollapsed={isSidebarCollapsed}
          toggleCollapse={toggleSidebarCollapse}
          userInfo={userInfo}
          onNavItemClick={handleNavItemClick}
          activeView={activeView}
          onResetDesign={handleResetDesign}
          onLoadCoverData={handleLoadCoverData}
        />
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-auto relative bg-gray-100">
        <main className="flex-1 overflow-auto">
          <Outlet context={{ activeView, setHasUnsavedChanges }} />
        </main>
      </div>
    </div>
  );
};

export default MainLayout; 
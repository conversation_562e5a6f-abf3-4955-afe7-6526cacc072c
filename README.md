# 封面生成器项目

## 项目概述

该项目是一个基于Next.js 14的封面生成器应用，使用AI技术生成各种风格和尺寸的封面。

## 技术栈

- React
- Next.js 14 (App Router)
- Tailwind CSS
- Antd
- React Router DOM

## 项目结构

### 主要组件

- **ChatGenerate.jsx**: 主要的生成封面页面组件
- **ChatSidebar.jsx**: 侧边栏组件，包含导航和用户信息
- **ChatMainArea.jsx**: 主要内容区域，显示预览或个人中心
- **ChatInputArea.jsx**: 输入区域，用于填写封面信息
- **ChatPreview.jsx**: 预览组件，显示生成的封面

### 自定义钩子

项目使用了多个自定义钩子来分离关注点：

- **useAutoSave.js**: 自动保存功能
- **useContentScaling.js**: 内容缩放功能
- **useIframeEditor.js**: iframe编辑器功能
- **useUrlStateSync.js**: URL状态同步功能
- **useTextEditor.js**: 文本编辑功能
- **useFormSubmit.js**: 表单提交处理
- **useCoverGeneration.js**: 封面生成状态管理
- **useLoadCoverData.js**: 加载封面数据功能
- **useResetDesign.js**: 重置设计功能
- **useNavigationHandlers.js**: 导航处理功能
- **useShareCover.js**: 封面分享功能
- **useViewSource.js**: 查看源代码功能

## 最近修复的问题

1. **钩子顺序问题**: 修复了钩子调用顺序不正确导致的"setGeneratedHTML is not defined"和"Cannot access 'setShowSourceModal' before initialization"错误。
2. **导航问题**: 修复了加载封面后无法切换导航的问题，通过改进useNavigationHandlers钩子。
3. **预览区域问题**: 修复了预览区域加载尺寸错误和编辑台不显示的问题，通过回退到使用原始的ChatPreview组件。
4. **代码清理**: 删除了所有测试文件和临时开发文档，保持代码库的整洁性。

## 未来计划

1. **重构改进**:
   - 完善ChatPreviewRefactored组件，解决与原始ChatPreview组件的兼容性问题
   - 继续拆分大型组件，提高代码可维护性

2. **功能增强**:
   - 改进编辑体验，添加更多编辑选项
   - 优化移动端适配
   - 添加更多风格和尺寸预设

3. **性能优化**:
   - 使用React.memo和useCallback优化渲染性能
   - 优化大型组件的加载时间
   - 实现更高效的状态管理

4. **文档完善**:
   - 添加详细的API文档
   - 创建组件关系图
   - 编写使用指南

## 使用指南

### 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 生产环境

```bash
# 构建项目
npm run build

# 启动生产服务器
npm start
```

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request 

## 权限管理模块

系统实现了基于React Context API的权限管理模块，用于高效管理功能权限，减少不必要的API请求。

### 主要组件

1. **FeatureContext（`src/contexts/FeatureContext.jsx`）**
   - 提供权限信息缓存和统一管理
   - 实现`useFeatures` hook用于在组件中访问权限信息
   - 定期自动刷新权限信息，确保数据时效性

2. **featureService（`src/services/featureService.js`）**
   - 提供权限检查和缓存管理API
   - 实现权限缓存机制，减少重复API请求
   - 提供批量权限检查功能，进一步优化性能

### 使用方法

在需要权限检查的组件中：

```jsx
import { useFeatures } from '../../contexts/FeatureContext';

const MyComponent = () => {
  const { features, loading } = useFeatures();
  
  // 当权限信息加载完成后，使用权限信息
  useEffect(() => {
    if (!loading && features['查看源码']) {
      // 根据权限状态执行相应逻辑
      const canViewSource = features['查看源码'].available;
      // ...
    }
  }, [features, loading]);
  
  return (
    // 组件内容
  );
};
```

### 权限缓存管理

系统会在以下情况下自动清除权限缓存：

1. 用户登录时
2. 用户退出登录时
3. 定期自动刷新（默认5分钟）

这确保了权限信息的时效性，同时大幅减少了不必要的API请求。

## SQL脚本归档

项目中的SQL脚本文件已归档到`sql_archives`目录中：

- `add_user_email.sql` - 添加用户邮箱字段
- `add_payment_status_closed.sql` - 添加支付状态关闭选项
- `add_display_status.sql` - 添加显示状态字段
- `update_refund_records_add_remark.sql` - 更新退款记录添加备注字段

这些脚本已执行完毕，保留作为数据库结构变更的历史记录。 
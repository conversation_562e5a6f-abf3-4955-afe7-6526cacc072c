// statistics.js - 数据统计模块

// 统计模块命名空间
window.statisticsModule = (function() {
  // 私有变量
  let currentStatsData = [];
  let currentPage = 1;
  let pageSize = 10;
  let totalPages = 1;

  // 初始化函数
  function init() {
    console.log('初始化数据统计模块...');

    // 绑定事件
    bindEvents();

    // 加载初始数据
    fetchStatsData();
  }

  // 绑定事件
  function bindEvents() {
    // 筛选条件变更
    document.getElementById('statsType').addEventListener('change', function() {
      updateFilterOptions();
      fetchStatsData();
    });

    document.getElementById('statsDateRange').addEventListener('change', function() {
      fetchStatsData();
    });

    document.getElementById('userType').addEventListener('change', function() {
      fetchStatsData();
    });

    document.getElementById('statsCoverType').addEventListener('change', function() {
      fetchStatsData();
    });

    document.getElementById('orderStatus').addEventListener('change', function() {
      fetchStatsData();
    });

    // 导出按钮
    document.getElementById('exportStatsBtn').addEventListener('click', function() {
      exportStatsToExcel();
    });

    // 初始状态
    updateFilterOptions();
  }

  // 更新筛选选项
  function updateFilterOptions() {
    const statsType = document.getElementById('statsType').value;

    // 隐藏所有特定筛选器
    document.getElementById('userTypeFilter').classList.add('d-none');
    document.getElementById('coverTypeFilter').classList.add('d-none');
    document.getElementById('orderStatusFilter').classList.add('d-none');

    // 根据统计类型显示相应筛选器
    if (statsType === 'user') {
      document.getElementById('userTypeFilter').classList.remove('d-none');
    } else if (statsType === 'cover') {
      document.getElementById('coverTypeFilter').classList.remove('d-none');
    } else if (statsType === 'order') {
      document.getElementById('orderStatusFilter').classList.remove('d-none');
    }
  }

  // 从选项获取日期范围
  function getDateRangeFromOption(option) {
    const now = new Date();
    let startDate, endDate;

    switch (option) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
        break;
      case 'yesterday':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59);
        break;
      case 'last7days':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 6);
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
        break;
      case 'last30days':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 29);
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
        break;
      case 'thisMonth':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        break;
      case 'lastMonth':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 29);
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
    }

    return {
      startDate: formatDateForAPI(startDate),
      endDate: formatDateForAPI(endDate)
    };
  }

  // 格式化日期为API格式 (YYYY-MM-DD)
  function formatDateForAPI(date) {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }

  // 加载统计数据
  function fetchStatsData() {
    const token = localStorage.getItem('token');
    if (!token) return;

    const statsType = document.getElementById('statsType').value;
    const dateRange = getDateRangeFromOption(document.getElementById('statsDateRange').value);
    const userType = document.getElementById('userType').value;
    const coverType = document.getElementById('statsCoverType').value;
    const orderStatus = document.getElementById('orderStatus').value;

    const params = new URLSearchParams({
      stats_type: statsType,
      start_date: dateRange.startDate,
      end_date: dateRange.endDate,
      page: currentPage,
      limit: pageSize
    });

    if (statsType === 'user' && userType) {
      params.append('user_type', userType);
    } else if (statsType === 'cover' && coverType) {
      params.append('cover_type', coverType);
    } else if (statsType === 'order' && orderStatus) {
      params.append('order_status', orderStatus);
    }

    fetch(`/api/admin/statistics?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // 处理后端返回的数据结构
        console.log('获取到的统计数据:', data);

        // 更新统计摘要
        if (data.summary) {
          updateStatsSummary(data.summary);
        } else if (data.data) {
          // 如果数据在data字段中
          updateStatsSummary(data.data);
        }

        // 更新图表
        if (data.chart) {
          updateStatsChart(data.chart);
        } else if (data.data && (data.data.user?.trend || data.data.cover?.trend)) {
          // 如果图表数据在data.user.trend或data.cover.trend中
          const chartData = {
            labels: [],
            values: []
          };

          // 根据统计类型选择不同的数据源
          const statsType = document.getElementById('statsType').value;
          let trendData = [];

          if (statsType === 'user' && data.data.user?.trend) {
            trendData = data.data.user.trend;
          } else if (statsType === 'cover' && data.data.cover?.trend) {
            trendData = data.data.cover.trend;
          } else if (statsType === 'order' && data.data.payment?.trend) {
            trendData = data.data.payment.trend;
          }

          // 处理趋势数据
          trendData.forEach(item => {
            chartData.labels.push(item.date);
            chartData.values.push(item.count || item.amount || 0);
          });

          updateStatsChart(chartData);
        }

        // 更新表格数据
        let itemsData = [];
        const statsType = document.getElementById('statsType').value;

        if (Array.isArray(data.data)) {
          // 直接使用数组数据
          itemsData = data.data;
        } else if (data.data) {
          // 根据统计类型选择不同的数据源
          if (statsType === 'user' && data.data.active_users) {
            itemsData = data.data.active_users;
          } else if (statsType === 'cover' && data.data.cover?.type_trend) {
            itemsData = data.data.cover.type_trend;
          } else if (statsType === 'order' && data.data.payment?.trend) {
            itemsData = data.data.payment.trend;
          }
        }

        console.log('处理后的表格数据:', itemsData);
        renderStatsTable(itemsData);

        // 更新分页
        totalPages = data.pagination?.total_pages || 1;
        renderStatsPagination();
      } else {
        console.error('获取统计数据失败:', data.message);
        renderStatsTable([]);
      }
    })
    .catch(error => {
      console.error('获取统计数据失败:', error);
      renderStatsTable([]);
    });
  }

  // 更新统计摘要
  function updateStatsSummary(summary) {
    // 更新统计摘要标题和值
    document.getElementById('summaryTitle1').textContent = '总用户数';
    document.getElementById('summaryValue1').textContent = summary.total_users || 0;

    document.getElementById('summaryTitle2').textContent = '总封面数';
    document.getElementById('summaryValue2').textContent = summary.total_covers || 0;

    document.getElementById('summaryTitle3').textContent = '总订单数';
    document.getElementById('summaryValue3').textContent = summary.total_orders || 0;

    document.getElementById('summaryTitle4').textContent = '总收入';
    document.getElementById('summaryValue4').textContent = '¥' + (summary.total_revenue || 0).toFixed(2);
  }

  // 更新统计图表
  function updateStatsChart(chartData) {
    // 假设使用某个图表库，这里需要根据实际使用的图表库进行实现
    console.log('更新图表数据:', chartData);

    // 如果使用Chart.js
    if (window.statsChart && typeof window.statsChart === 'object' && typeof window.statsChart.destroy === 'function') {
      try {
        // 检查图表数据结构是否完整
        if (window.statsChart.data && 
            Array.isArray(window.statsChart.data.datasets) && 
            window.statsChart.data.datasets.length > 0) {
          
          // 尝试更新现有图表
          window.statsChart.data.labels = chartData.labels;
          window.statsChart.data.datasets[0].data = chartData.values;
          window.statsChart.update();
        } else {
          console.error('图表数据结构不完整，需要重新创建');
          try {
            window.statsChart.destroy();
          } catch (e) {
            console.error('销毁图表时出错:', e);
          }
          window.statsChart = null;
        }
      } catch (error) {
        console.error('更新图表失败，尝试重新创建:', error);
        try {
          // 如果更新失败，尝试销毁并重新创建
          window.statsChart.destroy();
        } catch (destroyError) {
          console.error('销毁图表失败:', destroyError);
        }
        window.statsChart = null;
      }
    }
    
    // 如果没有图表实例或之前的实例已被销毁，创建新的
    if (!window.statsChart) {
      try {
        // 确保有图表元素
        const chartElement = document.getElementById('statsChart');
        if (!chartElement) {
          console.error('找不到statsChart元素');
          return;
        }
        
        // 确保元素在DOM中可见
        if (chartElement.offsetParent === null) {
          console.log('statsChart元素不可见，可能在隐藏的页面中');
          return;
        }
        
        // 获取2D上下文
        const ctx = chartElement.getContext('2d');
        if (!ctx) {
          console.error('无法获取图表上下文');
          return;
        }
        
        // 初始化图表
        window.statsChart = new Chart(ctx, {
          type: 'line',
          data: {
            labels: chartData.labels,
            datasets: [{
              label: '数据趋势',
              data: chartData.values,
              backgroundColor: 'rgba(54, 162, 235, 0.2)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      } catch (createError) {
        console.error('创建图表失败:', createError);
      }
    }
  }

  // 渲染统计表格
  function renderStatsTable(data) {
    const tbody = document.querySelector('#statsTable tbody');
    if (!tbody) {
      console.error('找不到统计表格的tbody元素');
      return;
    }

    tbody.innerHTML = '';

    if (!Array.isArray(data) || data.length === 0) {
      const tr = document.createElement('tr');
      tr.innerHTML = '<td colspan="5" class="text-center">暂无数据</td>';
      tbody.appendChild(tr);
      return;
    }

    const statsType = document.getElementById('statsType').value;

    data.forEach(item => {
      const tr = document.createElement('tr');

      if (statsType === 'user') {
        tr.innerHTML = `
          <td>${item.username || '-'}</td>
          <td>${item.email || '-'}</td>
          <td>${item.user_type || '普通用户'}</td>
          <td>${item.total_covers || 0}</td>
          <td>${formatDate(item.created_at)}</td>
        `;
      } else if (statsType === 'cover') {
        tr.innerHTML = `
          <td>${item.title || '无标题'}</td>
          <td>${item.username || '-'}</td>
          <td>${item.cover_type || '-'}</td>
          <td>${item.status || '-'}</td>
          <td>${formatDate(item.created_at)}</td>
        `;
      } else if (statsType === 'order') {
        tr.innerHTML = `
          <td>${item.order_id || '-'}</td>
          <td>${item.username || '-'}</td>
          <td>¥${(item.amount || 0).toFixed(2)}</td>
          <td>${item.status || '-'}</td>
          <td>${formatDate(item.created_at)}</td>
        `;
      }

      tbody.appendChild(tr);
    });
  }

  // 渲染分页
  function renderStatsPagination() {
    const paginationElement = document.getElementById('statsPagination');
    paginationElement.innerHTML = '';

    if (totalPages <= 1) {
      return;
    }

    // 上一页按钮
    const prevPageItem = document.createElement('li');
    prevPageItem.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevPageItem.innerHTML = '<a class="page-link" href="#">上一页</a>';
    prevPageItem.addEventListener('click', function(e) {
      e.preventDefault();
      if (currentPage > 1) {
        currentPage--;
        fetchStatsData();
      }
    });
    paginationElement.appendChild(prevPageItem);

    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);

    for (let i = startPage; i <= endPage; i++) {
      const pageItem = document.createElement('li');
      pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;
      pageItem.innerHTML = `<a class="page-link" href="#">${i}</a>`;
      pageItem.addEventListener('click', function(e) {
        e.preventDefault();
        currentPage = i;
        fetchStatsData();
      });
      paginationElement.appendChild(pageItem);
    }

    // 下一页按钮
    const nextPageItem = document.createElement('li');
    nextPageItem.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextPageItem.innerHTML = '<a class="page-link" href="#">下一页</a>';
    nextPageItem.addEventListener('click', function(e) {
      e.preventDefault();
      if (currentPage < totalPages) {
        currentPage++;
        fetchStatsData();
      }
    });
    paginationElement.appendChild(nextPageItem);
  }

  // 格式化日期
  function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // 导出统计数据到Excel
  function exportStatsToExcel() {
    const token = localStorage.getItem('token');
    if (!token) return;

    const statsType = document.getElementById('statsType').value;
    const dateRange = getDateRangeFromOption(document.getElementById('statsDateRange').value);
    const userType = document.getElementById('userType').value;
    const coverType = document.getElementById('statsCoverType').value;
    const orderStatus = document.getElementById('orderStatus').value;

    const params = new URLSearchParams({
      stats_type: statsType,
      start_date: dateRange.startDate,
      end_date: dateRange.endDate,
    });

    if (statsType === 'user' && userType) {
      params.append('user_type', userType);
    } else if (statsType === 'cover' && coverType) {
      params.append('cover_type', coverType);
    } else if (statsType === 'order' && orderStatus) {
      params.append('order_status', orderStatus);
    }

    fetch(`/api/admin/statistics/export?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.blob())
    .then(blob => {
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;

      // 设置文件名
      const now = new Date();
      const formattedDate = now.toISOString().replace(/[:.]/g, '-').slice(0, 19);
      a.download = `statistics-${statsType}-${formattedDate}.xlsx`;

      // 添加到DOM, 触发点击, 然后移除
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      alert('导出成功');
    })
    .catch(error => {
      console.error('导出统计数据失败:', error);
      alert('导出统计数据失败，请稍后再试');
    });
  }

  // 公开API
  return {
    init: init
  };
})();

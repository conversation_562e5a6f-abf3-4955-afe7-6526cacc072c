import React from 'react';
import { Link } from 'react-router-dom';
import { Crown } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

/**
 * 会员服务按钮组件
 * 
 * 根据用户是否是VIP显示不同样式
 * - 非VIP用户显示普通按钮
 * - VIP用户显示渐变色背景和发光效果
 * - 添加鼠标悬停效果，显示会员权益简介
 * - VIP用户显示剩余天数
 * 
 * @param {Object} props.userInfo 用户信息对象
 * @param {boolean} props.isCollapsed 侧边栏是否折叠
 */
const MembershipButton = ({ userInfo, isCollapsed }) => {
  // 透明度过渡类
  const textOpacityClass = isCollapsed ? "opacity-0" : "opacity-100";
  const textTransition = "transition-opacity duration-150 ease-in-out";
  
  // 判断用户是否是VIP
  const isVip = userInfo?.is_vip || false;
  
  // 计算VIP剩余天数
  const getRemainingDays = () => {
    if (!userInfo?.vip_expire_date) return 0;
    
    const expireDate = new Date(userInfo.vip_expire_date);
    const today = new Date();
    const diffTime = expireDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 ? diffDays : 0;
  };
  
  const remainingDays = getRemainingDays();
  
  // 会员权益简介
  const membershipBenefits = [
    '自定义图片上传',
    '封面编辑功能',
    '下载HTML源码',
    '每日积分赠送'
  ];
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            to="/?view=membership" // 修改为使用查询参数方式导航
            className={cn(
              "w-full flex items-center rounded-md transition-all p-2",
              isVip 
                ? "bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-md hover:shadow-lg hover:from-purple-600 hover:to-pink-600 hover:scale-105 active:scale-95" 
                : "text-slate-600 hover:bg-slate-100",
              isCollapsed ? "justify-center" : ""
            )}
          >
            <Crown 
              size={isCollapsed ? 24 : 18} 
              className={cn(
                "shrink-0",
                isCollapsed ? "" : "mr-3",
                isVip ? "text-yellow-300" : ""
              )} 
            />
            {!isCollapsed && (
              <div className="flex flex-col">
                <span className={`text-sm font-medium ${textOpacityClass} ${textTransition}`}>
                  {isVip ? "会员服务" : "开通会员"}
                </span>
                {isVip && (
                  <span className={`text-xs ${textOpacityClass} ${textTransition}`}>
                    剩余{remainingDays}天
                  </span>
                )}
              </div>
            )}
            
            {/* 会员按钮发光效果 */}
            {isVip && (
              <div className="absolute inset-0 rounded-md bg-gradient-to-r from-purple-500 to-pink-500 opacity-30 blur-sm -z-10"></div>
            )}
          </Link>
        </TooltipTrigger>
        <TooltipContent side="right" className="max-w-xs">
          <div>
            <p className="font-bold mb-1">{isVip ? "您的会员特权" : "开通会员即可享受"}</p>
            <ul className="text-xs space-y-1">
              {membershipBenefits.map((benefit, index) => (
                <li key={index} className="flex items-center">
                  <span className="mr-1">•</span> {benefit}
                </li>
              ))}
            </ul>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default MembershipButton; 
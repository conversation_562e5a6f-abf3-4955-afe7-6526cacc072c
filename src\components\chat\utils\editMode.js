/**
 * 编辑模式模块 - 处理编辑相关功能
 */

export const createEditMode = (context) => {
  const { doc, win, containerEl, getActiveElement, setActiveElement, debugMode, isDivMode, editableElementsTracker, onElementClick, externalHtmlContentChangeCallback, getMiddleware } = context;

  // 编辑相关变量
  let isEditing = false;
  let activeToolbar = null;
  let currentEditingElement = null;

  // 进入编辑模式
  const enterEditMode = (element) => {
    // 检查是否是右键触发的事件
    if (window.event && window.event.button === 2) {
      return; // 不响应右键事件
    }
    
    const middleware = getMiddleware();
    if (!middleware) return;

    // 如果有其他活动元素，先让它失去焦点
    const activeElement = getActiveElement();
    if (activeElement && activeElement !== element && typeof activeElement.blur === 'function') {
      activeElement.blur();
    }

    // 设置当前元素为活动元素
    setActiveElement(element);

    // 移除拖拽模式和相关样式
    element.classList.remove('drag-mode', 'selected-for-drag', 'dragging');
    element.style.cursor = 'text'; // 设置鼠标为文本编辑样式

    // 为所有子元素设置光标样式
    const allChildren = element.querySelectorAll('*');
    allChildren.forEach(child => {
      child.style.cursor = 'text';
    });

    // 添加mousemove事件监听器，确保鼠标在元素内移动时保持文本光标样式
    element.addEventListener('mousemove', handleEditingMouseMove, true);

    // 确保元素是可编辑的
    element.setAttribute('contenteditable', 'true');

    // 添加标记，表示这是一个正在编辑的元素
    element.setAttribute('data-editing', 'true');

    // 设置焦点
    element.focus();

    // 进入编辑模式
    middleware.setIsEditing(true);
    middleware.selectElement(element);

    // 通知父窗口当前正在编辑的元素
    try {
      window.parent.postMessage({
        type: 'text-editing-active',
        elementType: element.tagName.toLowerCase(),
        elementText: element.textContent
      }, '*');
    } catch (error) {
      if (debugMode) console.error('Error notifying parent window of editing state');
    }
  };

  // 退出编辑模式
  const exitEditingMode = () => {
    const middleware = getMiddleware();
    if (!middleware) return;

    const activeElement = getActiveElement();
    if (middleware.isEditing() && activeElement) {
      middleware.setIsEditing(false);

      // 移除编辑相关的类和属性
      activeElement.classList.remove('editing-active-outline');
      activeElement.removeAttribute('data-editing');

      // 移除mousemove事件监听器
      activeElement.removeEventListener('mousemove', handleEditingMouseMove, true);

      // 恢复子元素的默认光标样式
      const allChildren = activeElement.querySelectorAll('*');
      allChildren.forEach(child => {
        child.style.cursor = '';
      });

      // 恢复元素的默认光标样式
      activeElement.style.cursor = '';

      if (activeElement && typeof activeElement.blur === 'function') {
        activeElement.blur();
      }
    }
  };

  // 处理编辑模式下的鼠标移动，确保显示文本光标
  const handleEditingMouseMove = (e) => {
    const middleware = getMiddleware();
    if (!middleware) return;

    const target = e.currentTarget;
    const activeElement = getActiveElement();
    if (target.hasAttribute('data-editing') || (middleware.isEditing() && activeElement === target)) {
      // 设置元素及其所有子元素的光标样式为文本光标
      target.style.cursor = 'text';

      // 确保所有子元素也使用文本光标
      const allChildren = target.querySelectorAll('*');
      allChildren.forEach(child => {
        child.style.cursor = 'text';
      });

      // 阻止事件冒泡，避免其他事件处理函数覆盖光标样式
      e.stopPropagation();
      e.preventDefault();
    }
  };

  // 处理焦点获取
  const handleFocus = (e) => {
    // 检查是否是右键触发的事件
    if (e.button === 2 || (window.event && window.event.button === 2)) {
      return; // 不响应右键事件
    }
    
    const target = e.currentTarget; // 使用 e.currentTarget 以确保是监听器附加的元素

    // 只有在元素已经有data-editing属性时才进入编辑模式
    // 这样可以避免focus事件意外触发编辑模式
    if (!target.hasAttribute('data-editing')) {
      // 如果元素没有编辑标记，不自动进入编辑模式
      return;
    }

    const middleware = getMiddleware();
    if (!middleware) return;

    if (target.classList.contains('drag-mode')) {
      // 如果元素处于拖拽模式，则不进入文本编辑模式
      if (onElementClick) {
        onElementClick(target, 'focus'); // 仍然通知点击事件，以便其他逻辑（如选中）可以处理
      }
      return;
    }

    // 确保在编辑模式开始时，如果元素之前是拖拽模式，则移除拖拽模式的类
    if (target.classList.contains('drag-mode-active')) {
      target.classList.remove('drag-mode-active');
    }

    // 检查并设置编辑状态
    if (!middleware.isEditing() || currentEditingElement !== target) {
      if (currentEditingElement && currentEditingElement !== target) {
        // 如果当前有其他元素正在编辑，则先处理其失焦逻辑
        handleBlur({ currentTarget: currentEditingElement });
      }
      middleware.setIsEditing(true);
      currentEditingElement = target;

      // 只有在元素还不是可编辑状态时才设置contenteditable
      if (target.getAttribute('contenteditable') !== 'true') {
        target.setAttribute('contenteditable', 'true');
      }

      // 只有在元素没有焦点时才调用focus()，避免打断用户输入
      if (doc.activeElement !== target) {
        target.focus();
      }

      // 保存初始样式，用于可能的恢复
      const initialStyles = {};
      const computed = window.getComputedStyle(target);
      const styleAttributesToSave = ['color', 'background', 'webkitBackgroundClip', 'backgroundClip', 'webkitTextFillColor'];
      styleAttributesToSave.forEach(attr => {
        initialStyles[attr] = target.style[attr] || '';
      });
      // 特别处理可能由CSS类应用的样式
      if (computed.webkitBackgroundClip === 'text' || computed.backgroundClip === 'text') {
        initialStyles.webkitBackgroundClip = computed.webkitBackgroundClip;
        initialStyles.backgroundClip = computed.backgroundClip;
        initialStyles.webkitTextFillColor = computed.webkitTextFillColor;
        initialStyles.background = computed.background; // 保存背景，因为它与文字裁剪相关
      } else {
        initialStyles.color = computed.color;
      }
      target.setAttribute('data-initial-styles', JSON.stringify(initialStyles));

      // 简化的颜色处理：不再自动重置特殊样式
      // 颜色调整将由ChatPreview.jsx中的新逻辑统一处理

    } else {
      // 如果已经是当前编辑的元素并且再次获得焦点（例如，用户点击了已聚焦的元素）
      // 不需要特别处理，因为元素已经是 contenteditable
    }

    // 编辑状态已通过isEditing变量跟踪，不需要额外的工具栏状态

    if (onElementClick) {
      onElementClick(target, 'focus');
    }
  };

  // 处理焦点失去
  const handleBlur = (e) => {
    const target = e.currentTarget;
    if (!target) return;

    try {
      const middleware = getMiddleware();
      if (!middleware) return;

      const activeElement = getActiveElement();
      if (target === activeElement) {
        middleware.setIsEditing(false);

        // 工具栏状态由编辑状态控制，不需要单独管理
        target.classList.remove('editing-active-outline');

        // 移除编辑标记
        target.removeAttribute('data-editing');

        // 移除mousemove事件监听器
        target.removeEventListener('mousemove', handleEditingMouseMove, true);

        // 恢复子元素的默认光标样式
        const allChildren = target.querySelectorAll('*');
        allChildren.forEach(child => {
          child.style.cursor = '';
        });

        // 恢复元素的默认光标样式
        target.style.cursor = '';

        // 简化的样式恢复逻辑：不再恢复特殊颜色样式
        // 保持用户通过颜色调整功能设置的颜色

        // 清理临时属性
        target.removeAttribute('data-initial-styles');
        target.removeAttribute('data-user-direct-color');
        
        // 确保元素不处于拖拽模式
        target.classList.remove('drag-mode', 'selected-for-drag', 'dragging', 'resize-mode');
        
        // 移除这行代码，不再将contenteditable设置为false
        // if (target.getAttribute('contenteditable') === 'true') {
        //   target.setAttribute('contenteditable', 'false');
        // }
        
        // 如果元素仍然是document.activeElement，强制调用blur()
        if (doc.activeElement === target && typeof target.blur === 'function') {
          target.blur();
        }
        
        // 如果存在拖拽模式实例，确保清除拖拽状态
        if (middleware.dragMode && typeof middleware.dragMode.clearAllDragModes === 'function') {
          // 只清除当前元素的拖拽状态，而不是所有元素
          const dragHandles = target.querySelectorAll('.resize-handle');
          dragHandles.forEach(handle => {
            if (handle.parentNode === target) {
              target.removeChild(handle);
            }
          });
        }

        const newHtml = target.innerHTML;
        if (externalHtmlContentChangeCallback) {
          externalHtmlContentChangeCallback(newHtml, target);
        }
        
        // 触发自定义事件，通知工具栏重置状态
        const blurEvent = new CustomEvent('editor-element-blur', {
          bubbles: true,
          detail: { target }
        });
        doc.dispatchEvent(blurEvent);
        
        // 设置当前编辑元素为null
        currentEditingElement = null;
        
        // 通知父窗口当前已退出编辑模式
        try {
          window.parent.postMessage({
            type: 'text-editing-inactive',
            elementType: target.tagName.toLowerCase()
          }, '*');
        } catch (error) {
          if (debugMode) console.error('Error notifying parent window of editing state change');
        }
      }
    } catch (err) {
      if (debugMode) console.error('Error in handleBlur', err);
    }
  };

  // 处理粘贴
  const handlePaste = (e) => {
    e.preventDefault();
    const text = e.clipboardData.getData('text/plain');
    doc.execCommand('insertText', false, text);
  };

  // 处理键盘按键
  const handleKeyDown = (e) => {
    const activeElement = getActiveElement();
    if (isEditing && activeElement) {
      if (e.key === 'Escape') {
        activeElement.blur();
      }
    }
  };

  // 返回公共API
  return {
    isEditing: () => isEditing,
    enterEditMode,
    exitEditingMode,
    handleEditingMouseMove,
    handleFocus,
    handleBlur,
    handlePaste,
    handleKeyDown
  };
};

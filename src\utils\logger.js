/**
 * 日志工具
 * 提供统一的日志记录功能
 */

// 日志级别
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

// 当前日志级别，可通过环境变量配置
const currentLevel = process.env.NODE_ENV === 'production' ? LOG_LEVELS.INFO : LOG_LEVELS.DEBUG;

/**
 * 日志工具对象
 */
const logger = {
  /**
   * 调试日志
   * @param {string} message - 日志消息
   * @param {Object} [data] - 附加数据
   */
  debug: (message, data) => {
    if (currentLevel <= LOG_LEVELS.DEBUG) {
      console.debug(`[DEBUG] ${message}`, data || '');
    }
  },
  
  /**
   * 信息日志
   * @param {string} message - 日志消息
   * @param {Object} [data] - 附加数据
   */
  info: (message, data) => {
    if (currentLevel <= LOG_LEVELS.INFO) {
      console.info(`[INFO] ${message}`, data || '');
    }
  },
  
  /**
   * 警告日志
   * @param {string} message - 日志消息
   * @param {Object} [data] - 附加数据
   */
  warn: (message, data) => {
    if (currentLevel <= LOG_LEVELS.WARN) {
      console.warn(`[WARN] ${message}`, data || '');
    }
  },
  
  /**
   * 错误日志
   * @param {string} message - 日志消息
   * @param {Object} [data] - 附加数据
   */
  error: (message, data) => {
    if (currentLevel <= LOG_LEVELS.ERROR) {
      console.error(`[ERROR] ${message}`, data || '');
    }
  }
};

export default logger;

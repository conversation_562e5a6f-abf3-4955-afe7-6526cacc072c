import React, { useState, useEffect, useRef, useCallback, useMemo, useImperativeHandle, forwardRef } from 'react';
import { Download, FileText, Share2, RotateCcw, Image as ImageIcon, Loader2, Code, Eye, Save, Crown } from 'lucide-react';
import { getTextEditorStyles, createTextEditor, getKeyboardNavigationScript, getTextElementInitScript } from './utils/textEditor.js';
import { getCoverSizeConfig } from '../../services/templateService';
import { handleOpenPreview } from './utils/previewUtils';
import { handleScreenshotDownload } from './utils/downloadImageUtils'; // 导入handleScreenshotDownload函数
import logger from '../../services/logs/frontendLogger';
import html2canvas from 'html2canvas';
import { message } from 'antd';
import axios from 'axios';
import { checkFeatureAvailability } from '../../services/featureService';
import PermissionControl from '@/components/common/PermissionControl.jsx'; // 权限控制组件
import PreviewEditorToolbar from './PreviewEditorToolbar';
import ViewSourceModal from './ViewSourceModal';
import PermissionDialog from '../../components/common/PermissionDialog';
import { useFeatures } from '../../contexts/FeatureContext';
import ZoomController from '../zoom/ZoomController';

// 加载旋转动画组件，与分享页面一致
const LoadingSpinner = ({ className }) => {
  return <div className={`animate-spin rounded-full h-8 w-8 border-b-2 border-primary ${className || ''}`}></div>;
};

const ChatPreview = forwardRef((props, ref) => {
  const {
    htmlContent = null,
    isPreviewActive = false,
    onDownloadCover,
    onDownloadHtml,
    onPreview,
    onShare,
    onRestore,
    onSave, // 修复：从 props 中解构 onSave，解决 onSave is not defined 错误
    onViewSource,
    onHtmlContentChange,
    onElementClick,
    coverCode = null,
    coverId = null,
    progress = 0,
    feedbackText = '',
    showSourceModal = false,
    setShowSourceModal = () => {},
    debugMode = false,
    availableActions = {},
    onAction = () => {},
    onImageUpload = () => {},
    templateInfo = null,
    onLayoutChange = () => {},
    selectedSizeType = null, // 添加封面尺寸类型属性
    isGenerating = false, // 添加生成状态属性
    onLoadComplete = null, // 添加预览加载完成回调
  } = props;

  const iframeRef = useRef(null);
  const containerRef = useRef(null); // This is the div that will be scaled
  const outerPreviewAreaRef = useRef(null); // Parent of containerRef, used to determine available space
  const lastUsedHtmlContentForSrcdocRef = useRef(Symbol('initial')); // Ref to track last HTML written to srcdoc

  const [currentEditingContent, setCurrentEditingContent] = useState(htmlContent);
  const [loadError, setLoadError] = useState(null); // 添加加载错误状态
  const [isLoading, setIsLoading] = useState(false); // 添加加载状态
  const [isEditorInitialized, setIsEditorInitialized] = useState(false); // 添加编辑器初始化状态

  // 添加状态变量来跟踪HTML是否已加载和初始尺寸类型
  const [initialSizeType, setInitialSizeType] = useState(null);
  const [htmlLoaded, setHtmlLoaded] = useState(false);
  const initialSizeTypeRef = useRef(selectedSizeType);

  // 修改 HTML 内容尺寸的初始状态，提供默认值而非 null
  const [htmlContentSize, setHtmlContentSize] = useState({ width: 750, height: 1000 }); // 默认提供尺寸

  // State for natural dimensions of the HTML content and the scale factor
  const [naturalDimensions, setNaturalDimensions] = useState({ width: 750, height: 1000 }); // Default e.g. Xiaohongshu
  const [scale, setScale] = useState(1);
  const [iframeKey, setIframeKey] = useState(Date.now()); // 新增：为iframe提供一个唯一的key

  // 保存上一次的previewHtml引用，用于比较内容是否变化
  const previousPreviewHtmlRef = useRef(htmlContent);
  const textEditorRef = useRef(null);
  
  // 添加自动保存定时器引用
  const autoSaveTimerRef = useRef(null);
  
  // 关键新增：添加一个专门的 Effect 来处理 scale 值的更新
  useEffect(() => {
    if (textEditorRef.current) {
      textEditorRef.current.updateValue('scale', scale);
    }
  }, [scale]);

  // 重新引入/确保 latestEditorHtmlRef 存在，并用 prop 初始化
  const latestEditorHtmlRef = useRef(htmlContent);

  // 添加用户自定义缩放状态
  const [userScale, setUserScale] = useState(null);
  const [showZoomControl, setShowZoomControl] = useState(true); // 默认显示缩放控制器
  // 添加初始缩放设置状态
  const [isInitialScaleSet, setIsInitialScaleSet] = useState(false);
  
  // 移除编辑工具栏位置状态和引用
  // const [editorToolbarPosition, setEditorToolbarPosition] = useState({ left: '50%', transform: 'translateX(-50%)' });
  // const editorToolbarRef = useRef(null);

  // Effect to determine natural dimensions of the content
  useEffect(() => {
    let w, h;
    const sizeConfig = getCoverSizeConfig(selectedSizeType);

    if (sizeConfig) {
        // 如果选择了特定尺寸，则始终使用该尺寸，这是最高优先级
        w = sizeConfig.width;
        h = sizeConfig.height;
    } else if (htmlContent && htmlContentSize && htmlContentSize.width > 0 && htmlContentSize.height > 0) {
        // 如果未选择特定尺寸（例如自定义尺寸），则使用HTML内容本身的尺寸
        w = htmlContentSize.width;
        h = htmlContentSize.height;
    } else {
        // 如果没有其他可用信息，则回退到默认尺寸
        w = 750;
        h = 1000;
    }
    
    // 仅当尺寸实际更改时才更新状态，以避免不必要的重新渲染
    if (!naturalDimensions || naturalDimensions.width !== w || naturalDimensions.height !== h) {
        setNaturalDimensions({ width: w, height: h });
    }
}, [selectedSizeType, htmlContentSize, htmlContent, naturalDimensions]);

  // 计算最佳缩放比例的函数
  const calculateOptimalScale = useCallback((contentWidth, contentHeight) => {
    if (!outerPreviewAreaRef.current || contentWidth <= 0 || contentHeight <= 0) {
      return 1; // 默认缩放比例为1
    }

    // 获取预览区域的尺寸
    const previewAreaRect = outerPreviewAreaRef.current.getBoundingClientRect();
    const previewAreaWidth = previewAreaRect.width;
    const previewAreaHeight = previewAreaRect.height;

    // 安全边距，确保内容不会太靠近边缘
    const safePadding = 40;
    const safeAreaWidth = previewAreaWidth - safePadding;
    const safeAreaHeight = previewAreaHeight - safePadding;

    // 计算宽度和高度的缩放比例
    const widthScale = safeAreaWidth / contentWidth;
    const heightScale = safeAreaHeight / contentHeight;

    // 取较小的缩放比例，确保内容完全适应预览区域，允许放大和缩小
    let optimalScale = Math.min(widthScale, heightScale);
    
    // 添加一个最大和最小的缩放限制，避免极端情况
    optimalScale = Math.max(0.1, Math.min(optimalScale, 2.5));

    return optimalScale;
  }, []);

  // 移除自动缩放的ResizeObserver代码
  // 处理用户缩放变更
  const handleScaleChange = useCallback((newScale) => {
    setScale(newScale);
    setUserScale(newScale); // 记录用户手动设置的缩放比例
  }, []);

  // 当scale变化时向iframe发送缩放消息
  useEffect(() => {
    const iframe = iframeRef.current;
    if (iframe && iframe.contentWindow && iframe.contentDocument) {
      iframe.contentWindow.postMessage({
        type: 'scale-content',
        scale: scale
      }, '*');
    }
  }, [scale]);

  // 切换显示缩放控制器
  const toggleZoomControl = useCallback(() => {
    setShowZoomControl(prev => !prev);
  }, []);

  // 重置用户缩放
  const resetScale = useCallback(() => {
    // 如果有HTML内容尺寸信息，计算并应用最佳缩放比例
    if (htmlContentSize && htmlContentSize.width > 0 && htmlContentSize.height > 0) {
      const optimalScale = calculateOptimalScale(htmlContentSize.width, htmlContentSize.height);
      setScale(optimalScale);
      } else {
      setScale(1); // 如果没有内容尺寸信息，则重置为1
    }
    setUserScale(null); // 清除用户手动设置的缩放比例
  }, [htmlContentSize, calculateOptimalScale]);

  // 根据封面类型计算预览区域样式，使用原始尺寸显示
  const previewContainerStyle = useMemo(() => {
    return {
      width: 'auto', // 宽度由其内容（iframe）决定
      height: 'auto', // 高度由其内容（iframe）决定
      transform: `scale(${scale})`,
      transformOrigin: 'center center',
      position: 'relative',
      transition: 'transform 0.3s ease',
      overflow: 'visible', // 确保内容不被裁剪
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    };
  }, [scale]);

  // 修改预览区域样式，允许内容溢出以便完整显示
  const outerPreviewAreaStyle = useMemo(() => ({
    position: 'relative',
    width: isPreviewActive ? 'calc(100% - 320px)' : '100%',  // 根据编辑台是否显示调整宽度
    height: 'calc(100vh - 100px)', // 设置为视窗高度减去顶部区域高度
    minHeight: 'calc(100vh - 100px)', // 确保最小高度与高度一致
    overflow: 'visible', // 修改为visible，确保内容不被裁剪
    backgroundColor: 'transparent', // 完全透明，移除灰色背景
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center', // 垂直居中
    alignItems: 'center' // 水平居中
  }), [isPreviewActive]);

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getIframe: () => {
      return iframeRef.current;
    },
    getIframeDocument: () => {
      if (iframeRef.current) {
        return iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
      }
      return null;
    },
    openSourceModal: async () => {
      // 先检查权限，再调用父组件传入的回调以显示源代码模态框
      if (onViewSource) {
        try {
          // 检查功能是否可用
          const result = await checkFeatureAvailability('查看源码');
          if (!result || !result.available) {
            showPermissionError('查看源码', result?.reason || '您的账号权限不足，无法使用此功能');
            return;
          }
          
          // 有权限才调用原函数
          onViewSource(currentEditingContent || htmlContent || '');
        } catch (error) {
          showPermissionError('查看源码', '权限检查失败，请重试');
        } 
      } else {
        // 静默处理错误
      }
    },
    resetLoadState,
    flushPendingHtmlChanges: () => {
      // latestEditorHtmlRef.current 应该持有编辑器最新的内容
      const currentEditorHtml = latestEditorHtmlRef.current;
      if (currentEditorHtml !== htmlContent && onHtmlContentChange) {
        // 只有当编辑器内容与父组件状态不同时才同步
        // 并且要确保 currentEditorHtml 不是 undefined (尽管不太可能，但作为防御)
        if (typeof currentEditorHtml === 'string') {
          onHtmlContentChange(currentEditorHtml);
        }
      }
    },
    downloadCurrentView: handleCaptureAndDownload,
    updateHtmlContent: (newHtml, newCss = '', newJs = '') => {
      const currentScrollPosition = iframeRef.current?.contentWindow?.pageYOffset || 0;
      const activeElementId = iframeRef.current?.contentDocument?.activeElement?.id;

      const styleTag = `<style id="custom-styles">${newCss || ''}</style>`;
      const scriptTag = `<script id="custom-scripts">${newJs || ''}</script>`;

      let finalHtml = newHtml;
      if (newHtml && !newHtml.includes('<head>')) {
        finalHtml = `<html><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">${styleTag}${scriptTag}</head><body>${newHtml}</body></html>`;
      } else if (newHtml) {
        finalHtml = newHtml.replace('</head>', `${styleTag}${scriptTag}</head>`);
      }

      // 直接更新iframe内容，不使用setPreviewHtml
      try {
        const iframe = iframeRef.current;
        if (iframe) {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
          if (iframeDoc) {
            iframeDoc.open();
            iframeDoc.write(finalHtml);
            iframeDoc.close();
            
            // 更新状态
            setLastAppliedConfig && setLastAppliedConfig({ htmlContent: newHtml, customCss: newCss, customJs: newJs });
            // 保留滚动位置和焦点逻辑 (如果之前有)
            setPendingScrollPosition && setPendingScrollPosition(currentScrollPosition);
            setFocusedElementId && setFocusedElementId(activeElementId);
            
            // 更新引用
            lastUsedHtmlContentForSrcdocRef.current = finalHtml;
          }
        }
      } catch (error) {
        // 静默处理错误
      }
    },
    getCurrentHtmlContent: () => currentEditingContent,
    handleSave: handleSave, // 暴露保存方法给父组件
    cancelAutoSave // 暴露取消自动保存方法给父组件
  }));

  // Memoized callbacks for useEffect dependencies
  const memoizedOnElementClick = useCallback((elementInfo) => {
    if (onElementClick) {
      onElementClick(elementInfo);
    }
  }, [onElementClick]);

  const memoizedOnHtmlContentChange = useMemo(() => {
    let debounceTimer;
    return (newHtml) => {
      clearTimeout(debounceTimer);
      // 当编辑器内部内容变化时，立即更新 latestEditorHtmlRef
      latestEditorHtmlRef.current = newHtml;

      debounceTimer = setTimeout(() => {
        setCurrentEditingContent(newHtml);
        if (onHtmlContentChange) {
          onHtmlContentChange(newHtml);
        }
      }, 300); // 300ms debounce time
    };
  }, [onHtmlContentChange]);

  // 监听内容加载完成的消息
  const [contentLoaded, setContentLoaded] = useState(false);
  const contentLoadedRef = useRef(false);

  // 权限弹出层状态
  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [permissionDialogInfo, setPermissionDialogInfo] = useState({ featureName: '', reason: '' });
  
  // 添加查看源码权限状态
  const [hasViewSourcePermission, setHasViewSourcePermission] = useState(false);
  const [viewSourcePermissionChecked, setViewSourcePermissionChecked] = useState(false);
  
  // 使用useFeatures hook获取权限信息
  const { features, loading } = useFeatures();
  
  // 替换原有的权限检查useEffect
  useEffect(() => {
    // 当权限信息加载完成后，设置查看源码权限
    if (!loading && features['查看源码']) {
      setHasViewSourcePermission(features['查看源码'].available === true);
          setViewSourcePermissionChecked(true);
    } else if (!loading) {
      // 权限信息加载完成但没有查看源码权限
      setHasViewSourcePermission(false);
      setViewSourcePermissionChecked(true);
    }
  }, [features, loading]);

  // 显示权限弹出层的辅助函数
  const showPermissionError = (featureName, reason) => {
    setPermissionDialogInfo({ featureName, reason });
    setShowPermissionDialog(true);
  };

  // 添加获取尺寸的超时机制
  useEffect(() => {
    if (iframeRef.current && htmlContent) {
      // 如果5秒内没有收到有效的尺寸消息，使用默认尺寸
      const timeoutId = setTimeout(() => {
        if (!htmlContentSize || (htmlContentSize.width <= 100 || htmlContentSize.height <= 100)) {
          console.warn('尺寸消息超时或无效，使用默认尺寸');
          setHtmlContentSize({ width: 750, height: 1000 });
        }
      }, 5000);
      
      return () => clearTimeout(timeoutId);
    }
  }, [iframeRef.current, htmlContent, htmlContentSize]);

  // 增强 handleContentLoadedMessage 处理
  const handleContentLoadedMessage = (event) => {
    if (event.data && event.data.type === 'iframe-content-loaded') {
      setContentLoaded(true);

      // 如果事件中包含尺寸信息，更新htmlContentSize
      if (event.data.height && event.data.width) {
        // 验证尺寸是否有效
        const width = Math.max(100, event.data.width); // 确保不小于100px
        const height = Math.max(100, event.data.height); // 确保不小于100px
        
        setHtmlContentSize({
          width: width,
          height: height
        });
        
              if (!htmlLoaded) {
        setHtmlLoaded(true);
        
        // 触发自定义事件通知MainLayout
        const htmlLoadedEvent = new CustomEvent('htmlLoaded', { 
          detail: { isLoaded: true } 
        });
        window.dispatchEvent(htmlLoadedEvent);
      }
      
      // 调试日志
      if (debugMode) {
        console.log(`收到内容尺寸: ${width}x${height}`);
      }
      }
    } else if (event.data && event.data.type === 'iframe-images-loaded') {
      // 图片加载完成后，可能需要更新尺寸
      if (event.data.height && event.data.width) {
        const width = Math.max(100, event.data.width); // 确保不小于100px
        const height = Math.max(100, event.data.height); // 确保不小于100px
        
        setHtmlContentSize({
          width: width,
          height: height
        });
        
        // 调试日志
        if (debugMode) {
          console.log(`图片加载完成，更新尺寸: ${width}x${height}`);
        }
      }
    }
  };

  useEffect(() => {
    window.addEventListener('message', handleContentLoadedMessage);
    return () => {
      window.removeEventListener('message', handleContentLoadedMessage);
    };
  }, [htmlLoaded, debugMode]);

  // 使用ref来跟踪上一次的HTML内容，避免不必要的重新渲染
  const lastHtmlContentRef = useRef('');
  const lastPreviewActiveRef = useRef(false);
  const lastLoadingRef = useRef(false);

  // 定义resetLoadState函数，以便在组件内部使用
  const resetLoadState = () => {
    try {
      // 强制重置所有状态，不检查内容是否变化
      contentLoadedRef.current = false;
      setContentLoaded(false);
      if (typeof setIsEditorInitialized === 'function') {
        setIsEditorInitialized(false); // 重置编辑器初始化状态
      }
      setLoadError(null); // 重置加载错误状态
      setIsInitialScaleSet(false); // 重置智能缩放状态，允许重新计算最佳缩放比例

      // 重置用户编辑状态标志，确保不会跳过更新
      if (typeof isUserEditingRef !== 'undefined' && isUserEditingRef) {
        isUserEditingRef.current = false;
      }

      // 取消可能正在进行的自动保存操作，避免加载新内容后被旧的自动保存覆盖
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
        autoSaveTimerRef.current = null;
      }

      // 清理所有引用，确保下次加载时能够正确触发setupPreview
      previousPreviewHtmlRef.current = '';
      lastUsedHtmlContentForSrcdocRef.current = '';

      // 清理编辑器实例
      if (textEditorRef.current) {
        try {
          textEditorRef.current.cleanup();
        } catch (editorCleanupError) {
          // 静默处理错误
        }
        textEditorRef.current = null;
      }

      // 重置editorInstanceRef
      if (typeof editorInstanceRef !== 'undefined' && editorInstanceRef.current) {
        editorInstanceRef.current = null;
      }

      // 清理iframe中的所有编辑和拖拽状态
      try {
        const iframe = iframeRef.current;
        if (iframe) {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
          if (iframeDoc) {
            // 查找所有可能与编辑或拖拽相关的元素
            const elementsToClean = iframeDoc.querySelectorAll('[contenteditable="true"], [data-editing="true"], .drag-mode, .dragging, .selected-for-drag, .resize-mode, .editing-active-outline, [data-editable-fengmian]');
            
            if (elementsToClean.length > 0) {
              elementsToClean.forEach(el => {
                // 移除所有编辑相关属性
                el.removeAttribute('contenteditable');
                el.removeAttribute('data-editing');
                el.removeAttribute('data-field');
                el.removeAttribute('data-editable-fengmian');
                el.removeAttribute('tabindex');
                el.removeAttribute('title');
                
                // 移除所有拖拽相关类
                el.classList.remove('drag-mode');
                el.classList.remove('dragging');
                el.classList.remove('selected-for-drag');
                el.classList.remove('resize-mode');
                el.classList.remove('editing-active-outline');
                
                // 全面清理所有可能影响拖拽的样式属性
                if (el.style) {
                  // 原有清理
                  el.style.transform = ''; // 重置变换
                  el.style.cursor = ''; // 重置鼠标样式
                  el.style.userSelect = ''; // 重置文本选择状态
                  el.style.webkitUserSelect = '';
                  el.style.msUserSelect = '';
                  el.style.willChange = ''; // 重置优化提示
                  el.style.position = ''; // 如果变化了定位方式，重置它
                  el.style.outline = ''; // 重置轮廓
                  el.style.zIndex = ''; // 重置层级
                  
                  // 增加更全面的样式清理
                  el.style.outlineStyle = '';
                  el.style.outlineWidth = '';
                  el.style.outlineColor = '';
                  el.style.outlineOffset = '';
                  el.style.border = '';
                  el.style.borderStyle = '';
                  el.style.borderWidth = '';
                  el.style.borderColor = '';
                  el.style.boxShadow = '';
                  el.style.pointerEvents = '';
                  el.style.transition = '';
                }
                
                // 对元素的所有子元素也应用相同的清理
                const allChildren = el.querySelectorAll('*');
                allChildren.forEach(child => {
                  // 更全面地清理所有子元素样式
                  child.style.cursor = '';
                  child.style.userSelect = '';
                  child.style.webkitUserSelect = '';
                  child.style.msUserSelect = '';
                  child.style.outline = '';
                  child.style.outlineStyle = '';
                  child.style.outlineWidth = '';
                  child.style.outlineColor = '';
                  child.style.outlineOffset = '';
                  child.style.border = '';
                  child.style.borderStyle = '';
                  child.style.boxShadow = '';
                  
                  // 移除可能的拖拽相关类
                  child.classList.remove('drag-mode');
                  child.classList.remove('dragging');
                  child.classList.remove('resize-mode');
                  child.classList.remove('editing-active-outline');
                });
              });
            }
            
            // 移除所有拖拽句柄元素
            const resizeHandles = iframeDoc.querySelectorAll('.resize-handle');
            resizeHandles.forEach(handle => {
              handle.remove();
            });
            
            // 清除所有选中状态
            if (iframeDoc.getSelection) {
              iframeDoc.getSelection().removeAllRanges();
            }
            
            // 尝试移除任何编辑器特定的事件监听器
            if (iframeDoc.body) {
              const oldBody = iframeDoc.body.cloneNode(true);
              iframeDoc.body.parentNode.replaceChild(oldBody, iframeDoc.body);
            }
          }
        }
      } catch (iframeCleanupError) {
        // 静默处理错误
      }

      // 清空iframe内容
      try {
        const iframe = iframeRef.current;
        if (iframe) {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
          if (iframeDoc) {
            iframeDoc.open();
            iframeDoc.write('');
            iframeDoc.close();
          }
        }
      } catch (error) {
        // 静默处理错误
      }
      
      // 通过中间件重置编辑状态
      if (textEditorRef.current && textEditorRef.current.getMiddleware) {
        const middleware = textEditorRef.current.getMiddleware();
        if (middleware) {
          middleware.setIsEditing(false);
          middleware.clearSelection();
        }
      }
      
      // 触发自定义事件通知MainLayout，HTML已卸载
      setHtmlLoaded(false);
      const htmlLoadedEvent = new CustomEvent('htmlLoaded', { 
        detail: { isLoaded: false } 
      });
      window.dispatchEvent(htmlLoadedEvent);
    } catch (error) {
      // 静默处理错误
    }
  };

  // 修改iframe内容设置函数，允许加载外部字体
  const setupPreview = useCallback(async () => {
    // 取消可能正在进行的自动保存操作，避免加载新内容后被旧的自动保存覆盖
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
      autoSaveTimerRef.current = null;
    }
    
    // 当HTML内容变化时，重置htmlLoaded状态，确保重新确定尺寸
    if (htmlContent !== lastUsedHtmlContentForSrcdocRef.current) {
      setHtmlLoaded(false);
      // 重置htmlContentSize，确保重新提取尺寸
      setHtmlContentSize(null);
      // 重置智能缩放状态，允许重新计算最佳缩放比例
      setIsInitialScaleSet(false);
    }
    
    // 如果htmlContent是identical to what was last written to srcdoc and editor exists, skip rewriting.
    // 但如果lastUsedHtmlContentForSrcdocRef.current为null，则总是重新加载
    if (htmlContent === lastUsedHtmlContentForSrcdocRef.current && 
        lastUsedHtmlContentForSrcdocRef.current !== null && 
        lastUsedHtmlContentForSrcdocRef.current !== '' && 
        textEditorRef.current) {
      return;
    }

    // 重置用户编辑标志，确保处理新内容
    if (typeof isUserEditingRef !== 'undefined' && isUserEditingRef) {
      isUserEditingRef.current = false;
    }

    // 更新lastUsedHtmlContentForSrcdocRef，记录当前正在处理的HTML内容
    lastUsedHtmlContentForSrcdocRef.current = htmlContent;
    
    // 更新当前编辑内容，保持与HTML内容同步
    setCurrentEditingContent(htmlContent);
    
    // 更新latestEditorHtmlRef，确保编辑器使用最新内容
    if (latestEditorHtmlRef) {
      latestEditorHtmlRef.current = htmlContent;
    }

    // 重置错误状态
    setLoadError(null);

    try {
      if (!htmlContent) {
        // 如果没有生成的HTML，显示提示信息
        setContentLoaded(false);

        // 确保清理之前可能存在的文本编辑器
        if (textEditorRef.current) {
          textEditorRef.current.cleanup();
          textEditorRef.current = null;
        }

        // 获取预设尺寸
        let previewWidth = '100%';
        let previewHeight = '100%';

        if (selectedSizeType) {
          const sizeConfig = getCoverSizeConfig(selectedSizeType);
          if (sizeConfig) {
            previewWidth = `${sizeConfig.width}px`;
            previewHeight = `${sizeConfig.height}px`;
          }
        }

        // 处理显示提示信息的逻辑
        const iframe = iframeRef.current;
        if (iframe) {
          try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
            if (iframeDoc) {
              iframeDoc.open();
              iframeDoc.write(`
                <!DOCTYPE html>
                <html>
                <head>
                  <meta charset="utf-8">
                  <meta name="viewport" content="width=device-width, initial-scale=1.0">
                  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; img-src * data:; style-src 'unsafe-inline' 'self' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; script-src 'unsafe-inline';">
                  <style>
                    /* 重置样式并确保内容可见 */
                    html, body {
                      margin: 0 !important;
                      padding: 0 !important;
                      width: 100%;
                      height: 100%;
                      overflow: hidden !important;
                      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; /* 使用系统字体作为回退 */
                    }
                    body {
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    }
                    .content-container {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      overflow: hidden !important;
                      min-width: 300px; /* 添加最小宽度 */
                      min-height: 300px; /* 添加最小高度 */
                    }
                    img { 
                      max-width: 100%; 
                      height: auto;
                    }
                    /* 图像加载失败时的样式 */
                    img.error {
                      min-width: 100px;
                      min-height: 100px;
                      background: #f0f0f0;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      position: relative;
                    }
                    img.error::before {
                      content: "图片加载失败";
                      position: absolute;
                      font-size: 14px;
                      color: #888;
                    }
                    * { box-sizing: border-box; }
                    ${getTextEditorStyles()}
                  </style>
                </head>
                <body>
                  <div class="content-container">${sanitizedHtmlContent}</div>
                  <script>
                    // 阻止右键菜单
                    document.body.oncontextmenu = function(e) { e.preventDefault(); return false; };
                    document.addEventListener('contextmenu', function(e) { e.preventDefault(); return false; });
                    
                    // 增强型尺寸计算和消息发送
                    function calculateAndSendSize(type = 'iframe-content-loaded') {
                      try {
                        const container = document.querySelector('.content-container');
                        if (!container) {
                          console.error('找不到内容容器');
                          sendDefaultSize(type);
                          return;
                        }
                        
                        // 尝试多种选择器查找可能的封面元素
                        let coverElement = null;
                        const selectors = [
                          '.wechat-cover', '.weibo-cover', '.xiaohongshu-cover', 
                          '.douyin-cover', '.tiktok-cover', '.article-cover', 
                          '.cover-element', '[data-role="cover"]',
                          'div[style*="background-image"]', // 带背景图的div
                          'div[style*="background"]', // 任何带背景的div
                          'div > div', // 嵌套的div（常见封面结构）
                          'div' // 最后尝试任何div
                        ];
                        
                        // 逐个尝试选择器
                        for (const selector of selectors) {
                          const elements = container.querySelectorAll(selector);
                          if (elements.length > 0) {
                            // 找到最大的元素作为封面
                            coverElement = Array.from(elements).reduce((largest, current) => {
                              const currentArea = current.offsetWidth * current.offsetHeight;
                              const largestArea = largest ? largest.offsetWidth * largest.offsetHeight : 0;
                              return currentArea > largestArea ? current : largest;
                            }, null);
                            
                            if (coverElement) break;
                          }
                        }
                        
                        // 计算尺寸
                        let width, height;
                        if (coverElement) {
                          width = coverElement.offsetWidth || coverElement.scrollWidth;
                          height = coverElement.offsetHeight || coverElement.scrollHeight;
                          // 移除下面的console.log语句，避免信息泄露
                          // console.log("找到封面元素: " + (coverElement.className || coverElement.tagName) + ", 尺寸: " + width + "x" + height);
                        } else {
                          width = container.scrollWidth;
                          height = container.scrollHeight;
                          // 移除下面的console.log语句，避免信息泄露
                          // console.log("使用容器尺寸: " + width + "x" + height);
                        }
                        
                        // 确保尺寸有效
                        if (!width || !height || width < 100 || height < 100) {
                          if (${debugMode}) {
                          console.warn('计算的尺寸无效，使用默认值');
                          }
                          width = 750;
                          height = 1000;
                        }
                        
                        // 发送尺寸消息
                        window.parent.postMessage({
                          type: type,
                          width: width,
                          height: height,
                          source: coverElement ? 'cover-element' : 'container'
                        }, '*');
                        
                      } catch (e) {
                        if (${debugMode}) {
                        console.error('尺寸计算出错:', e);
                        }
                        sendDefaultSize(type);
                      }
                    }
                    
                    // 发送默认尺寸
                    function sendDefaultSize(type) {
                      window.parent.postMessage({
                        type: type,
                        width: 750,
                        height: 1000,
                        source: 'default'
                      }, '*');
                    }
                    
                    // 处理图片加载错误
                    function handleImageError(img) {
                      img.classList.add('error');
                      img.alt = '图片加载失败';
                      if (${debugMode}) {
                      console.warn('图片加载失败:', img.src);
                      }
                    }
                    
                    // 监听DOM加载完成
                    document.addEventListener('DOMContentLoaded', function() {
                      try {
                        // 立即计算并发送初始尺寸
                        calculateAndSendSize('iframe-content-loaded');
                        
                        // 确保所有文本元素可见
                        const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div:not(.content-container)');
                        textElements.forEach(el => {
                          if (!el.style.color && !window.getComputedStyle(el).color) {
                            el.style.color = '#000000';
                          }
                        });
                        
                        // 处理图片
                        const images = document.querySelectorAll('img');
                        if (images.length > 0) {
                          let loadedCount = 0;
                          const totalImages = images.length;
                          
                          const checkAllImagesLoaded = () => {
                            loadedCount++;
                            if (loadedCount === totalImages) {
                              // 所有图片加载完成，重新计算尺寸
                              setTimeout(() => calculateAndSendSize('iframe-images-loaded'), 100);
                            }
                          };
                          
                          images.forEach(img => {
                            // 添加错误处理
                            img.onerror = function() {
                              handleImageError(this);
                              checkAllImagesLoaded();
                            };
                            
                            if (img.complete) {
                              if (img.naturalWidth === 0) {
                                handleImageError(img);
                              }
                              checkAllImagesLoaded();
                            } else {
                              img.addEventListener('load', checkAllImagesLoaded);
                              img.addEventListener('error', () => {
                                handleImageError(img);
                                checkAllImagesLoaded();
                              });
                            }
                          });
                        }
                        
                        // 添加背景图片加载监听
                        const elementsWithBackground = document.querySelectorAll('[style*="background-image"]');
                        if (elementsWithBackground.length > 0) {
                          // 等待一段时间后重新计算尺寸，以便捕获背景图片加载完成的情况
                          setTimeout(() => calculateAndSendSize('background-images-loaded'), 1000);
                        }
                        
                        // 最后的保障：如果2秒后尺寸仍未正确计算，再次尝试
                        setTimeout(() => calculateAndSendSize('delayed-calculation'), 2000);
                        
                        ${getTextElementInitScript()}
                      } catch(e) {
                        if (${debugMode}) {
                        console.error('内容加载事件处理错误:', e);
                        }
                        sendDefaultSize('error-recovery');
                      }
                    });
                    
                    ${getKeyboardNavigationScript()}
                  </script>
                </body>
                </html>
              `);
              iframeDoc.close();
            }
          } catch (error) {
            console.error('设置预览提示失败:', error);
            setLoadError('加载预览提示失败');
          }
        }

        return;
      }

      // 只在首次加载或内容变化时显示加载状态
      if (htmlContent !== previousPreviewHtmlRef.current) {
        // 短暂显示加载状态，提供视觉反馈
        setIsLoading(true);
        setContentLoaded(false);

        // 在加载新内容前清理之前的文本编辑器实例
        if (textEditorRef.current) {
          textEditorRef.current.cleanup();
          textEditorRef.current = null;
        }
      }

      // 更新当前内容引用
      previousPreviewHtmlRef.current = htmlContent;

      const iframe = iframeRef.current;
      if (!iframe) {
        throw new Error('iframe元素不存在');
      }

      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) {
        throw new Error('无法访问iframe文档');
      }

      // 主动清理无效的图片链接，防止加载失败和卡顿
      let sanitizedHtmlContent = htmlContent;
      if (typeof sanitizedHtmlContent === 'string') {
        // 更换为更强大的正则表达式，以处理单引号、双引号和无协议的URL
        // 包含三种情况: src="http://via.placeholder.com/100", src='https://via.placeholder.com/100', src=//via.placeholder.com/100
        const placeholderRegex = /src=(["'])?(https?:)?\/\/via\.placeholder\.com\/[^'" >]*(?:\1)?/g;
        sanitizedHtmlContent = sanitizedHtmlContent.replace(placeholderRegex, 'src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="');
        
        // 清理可能的console.log语句
        sanitizedHtmlContent = sanitizedHtmlContent.replace(/console\.log\([^)]*\)/g, debugMode ? '$&' : '');
      }

      // 设置iframe内容，更新CSP允许字体加载
      iframeDoc.open();
      iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta http-equiv="Content-Security-Policy" content="default-src 'self'; img-src * data:; style-src 'unsafe-inline' 'self' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; script-src 'unsafe-inline';">
          <style>
            /* 重置样式并确保内容可见 */
            html, body {
              margin: 0 !important;
              padding: 0 !important;
              width: 100%;
              height: 100%;
              overflow: hidden !important;
              font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; /* 使用系统字体作为回退 */
            }
            body {
              display: flex;
              justify-content: center;
              align-items: center;
            }
            .content-container {
              position: relative;
              width: 100%;
              height: 100%;
              overflow: hidden !important;
              min-width: 300px; /* 添加最小宽度 */
              min-height: 300px; /* 添加最小高度 */
            }
            img { 
              max-width: 100%; 
              height: auto;
            }
            /* 图像加载失败时的样式 */
            img.error {
              min-width: 100px;
              min-height: 100px;
              background: #f0f0f0;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
            }
            img.error::before {
              content: "图片加载失败";
              position: absolute;
              font-size: 14px;
              color: #888;
            }
            * { box-sizing: border-box; }
            ${getTextEditorStyles()}
          </style>
        </head>
        <body>
          <div class="content-container">${sanitizedHtmlContent}</div>
          <script>
            // 阻止右键菜单
            document.body.oncontextmenu = function(e) { e.preventDefault(); return false; };
            document.addEventListener('contextmenu', function(e) { e.preventDefault(); return false; });
            
            // 增强型尺寸计算和消息发送
            function calculateAndSendSize(type = 'iframe-content-loaded') {
              try {
                const container = document.querySelector('.content-container');
                if (!container) {
                  console.error('找不到内容容器');
                  sendDefaultSize(type);
                  return;
                }
                
                // 尝试多种选择器查找可能的封面元素
                let coverElement = null;
                const selectors = [
                  '.wechat-cover', '.weibo-cover', '.xiaohongshu-cover', 
                  '.douyin-cover', '.tiktok-cover', '.article-cover', 
                  '.cover-element', '[data-role="cover"]',
                  'div[style*="background-image"]', // 带背景图的div
                  'div[style*="background"]', // 任何带背景的div
                  'div > div', // 嵌套的div（常见封面结构）
                  'div' // 最后尝试任何div
                ];
                
                // 逐个尝试选择器
                for (const selector of selectors) {
                  const elements = container.querySelectorAll(selector);
                  if (elements.length > 0) {
                    // 找到最大的元素作为封面
                    coverElement = Array.from(elements).reduce((largest, current) => {
                      const currentArea = current.offsetWidth * current.offsetHeight;
                      const largestArea = largest ? largest.offsetWidth * largest.offsetHeight : 0;
                      return currentArea > largestArea ? current : largest;
                    }, null);
                    
                    if (coverElement) break;
                  }
                }
                
                // 计算尺寸
                let width, height;
                if (coverElement) {
                  width = coverElement.offsetWidth || coverElement.scrollWidth;
                  height = coverElement.offsetHeight || coverElement.scrollHeight;
                } else {
                  width = container.scrollWidth;
                  height = container.scrollHeight;
                }
                
                // 确保尺寸有效
                if (!width || !height || width < 100 || height < 100) {
                  console.warn('计算的尺寸无效，使用默认值');
                  width = 750;
                  height = 1000;
                }
                
                // 发送尺寸消息
                window.parent.postMessage({
                  type: type,
                  width: width,
                  height: height,
                  source: coverElement ? 'cover-element' : 'container'
                }, '*');
                
              } catch (e) {
                console.error('尺寸计算出错:', e);
                sendDefaultSize(type);
              }
            }
            
            // 发送默认尺寸
            function sendDefaultSize(type) {
              window.parent.postMessage({
                type: type,
                width: 750,
                height: 1000,
                source: 'default'
              }, '*');
            }
            
            // 处理图片加载错误
            function handleImageError(img) {
              img.classList.add('error');
              img.alt = '图片加载失败';
              console.warn('图片加载失败:', img.src);
            }
            
            // 监听DOM加载完成
            document.addEventListener('DOMContentLoaded', function() {
              try {
                // 立即计算并发送初始尺寸
                calculateAndSendSize('iframe-content-loaded');
                
                // 确保所有文本元素可见
                const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div:not(.content-container)');
                textElements.forEach(el => {
                  if (!el.style.color && !window.getComputedStyle(el).color) {
                    el.style.color = '#000000';
                  }
                });
                
                // 处理图片
                const images = document.querySelectorAll('img');
                if (images.length > 0) {
                  let loadedCount = 0;
                  const totalImages = images.length;
                  
                  const checkAllImagesLoaded = () => {
                    loadedCount++;
                    if (loadedCount === totalImages) {
                      // 所有图片加载完成，重新计算尺寸
                      setTimeout(() => calculateAndSendSize('iframe-images-loaded'), 100);
                    }
                  };
                  
                  images.forEach(img => {
                    // 添加错误处理
                    img.onerror = function() {
                      handleImageError(this);
                      checkAllImagesLoaded();
                    };
                    
                    if (img.complete) {
                      if (img.naturalWidth === 0) {
                        handleImageError(img);
                      }
                      checkAllImagesLoaded();
                    } else {
                      img.addEventListener('load', checkAllImagesLoaded);
                      img.addEventListener('error', () => {
                        handleImageError(img);
                        checkAllImagesLoaded();
                      });
                    }
                  });
                }
                
                // 添加背景图片加载监听
                const elementsWithBackground = document.querySelectorAll('[style*="background-image"]');
                if (elementsWithBackground.length > 0) {
                  // 等待一段时间后重新计算尺寸，以便捕获背景图片加载完成的情况
                  setTimeout(() => calculateAndSendSize('background-images-loaded'), 1000);
                }
                
                // 最后的保障：如果2秒后尺寸仍未正确计算，再次尝试
                setTimeout(() => calculateAndSendSize('delayed-calculation'), 2000);
                
                ${getTextElementInitScript()}
              } catch(e) {
                console.error('内容加载事件处理错误:', e);
                sendDefaultSize('error-recovery');
              }
            });
            
            ${getKeyboardNavigationScript()}
          </script>
        </body>
        </html>
      `);
      iframeDoc.close();
      lastUsedHtmlContentForSrcdocRef.current = htmlContent; // Update ref for placeholder

      // 在iframe加载完成后设置事件监听
      iframe.onload = async () => {
        try {
          // 创建文本编辑器并初始化
          if (textEditorRef.current) {
            textEditorRef.current.cleanup();
          }

          // 确保文本编辑器创建函数可用
          if (typeof createTextEditor === 'function') {
            // 创建带权限控制的元素点击处理函数
            const handleElementClickWithPermission = async (element, action) => {
              // 如果是编辑相关操作，检查权限（包含单击进入拖拽模式）
              if (action === 'dblclick-to-edit' || action === 'click-to-edit' || action === 'click-to-drag') {
                const result = await checkFeatureAvailability('文本编辑');
                if (!result.available) {
                  // 显示权限弹窗
                  showPermissionError('文本编辑', result.reason || '您的账号权限不足，无法使用此功能');

                  // 清除文本框的选中状态，防止用户关闭弹窗后继续操作
                  if (element) {
                    // 移除拖拽模式类
                    element.classList.remove('drag-mode', 'selected-for-drag', 'dragging');
                    // 移除编辑状态属性
                    element.removeAttribute('data-editing');
                    element.classList.remove('editing-active-outline');
                    // 失去焦点
                    if (document.activeElement === element) {
                      element.blur();
                    }
                  }

                  return;
                }
              }

              // 如果有权限或不是编辑操作，继续执行原有逻辑
              if (onElementClick) {
                onElementClick(element, action);
              }
            };

            // 创建文本编辑器
            // 关键修改：在初始化时传递初始的 scale 值
            textEditorRef.current = createTextEditor(iframeDoc, memoizedOnHtmlContentChange, handleElementClickWithPermission, debugMode, false, scale);
            textEditorRef.current.init();
          } else {
            // 如果创建函数不可用，延迟重试
            setTimeout(() => {
              try {
                if (typeof createTextEditor === 'function') {
                    // 使用上面已定义的handleElementClickWithPermission函数，避免重复定义
                  // 关键修改：在初始化时传递初始的 scale 值
                  textEditorRef.current = createTextEditor(iframeDoc, memoizedOnHtmlContentChange, handleElementClickWithPermission, debugMode, false, scale);
                  textEditorRef.current.init();
                }
              } catch (retryError) {
                setLoadError('文本编辑器初始化失败');
              }
            }, 100);
          }

          // 内容加载完成后立即隐藏加载状态
          setIsLoading(false);
          
          // 通知父组件预览加载完成，这将触发输入框自动折叠
          if (onLoadComplete && typeof onLoadComplete === 'function') {
            onLoadComplete();
          }
        } catch (error) {
          // 静默处理iframe事件监听错误
          setLoadError('内容加载后处理失败');
          setIsLoading(false);
        }
      };

      // 不设置超时处理，因为内容是固定的
      return () => {};
    } catch (error) {
      console.error('设置预览失败:', error);
      setLoadError(`预览加载失败: ${error.message}`);
      setIsLoading(false);
    }
  }, [htmlContent, debugMode, selectedSizeType, memoizedOnHtmlContentChange, onElementClick, onLoadComplete]);

  // 添加一个标志来跟踪是否是用户编辑操作导致的内容变化
  const isUserEditingRef = useRef(false);

  // 当HTML内容变化时更新预览
  useEffect(() => {
    // 如果不是预览活动状态，不更新预览
    if (!isPreviewActive) {
      return;
    }

    // 如果内容没有变化且编辑器已初始化，不需要更新
    if (htmlContent === previousPreviewHtmlRef.current && contentLoaded && textEditorRef.current) {
      return;
    }

    // 如果是用户编辑操作导致的变化，不重新设置预览
    if (isUserEditingRef.current) {
      previousPreviewHtmlRef.current = htmlContent; // 更新引用
      // 延迟重置标志，确保后续的变化能够正常处理
      setTimeout(() => {
        isUserEditingRef.current = false;
      }, 500);
      return;
    }

    // 使用防抖处理，避免频繁更新导致的闪烁
    const timer = setTimeout(() => {
      setupPreview();
    }, 200); // 增加延迟时间，减少频繁更新

    return () => clearTimeout(timer);
  }, [htmlContent, isPreviewActive, contentLoaded, setupPreview]);

  // 移除全局变量初始化，改为使用中间件API
  // 注释：我们不再需要初始化全局变量，因为现在使用中间件API来管理状态

  // 移除自动缩放的adjustHeight函数

  // 移除自动调整高度的useEffect

  // 当HTML内容变化时，更新内部状态
  useEffect(() => {
    if (htmlContent) {
      setCurrentEditingContent(htmlContent);

      // 如果htmlContent不为空，标记已经加载了HTML，并保存当前的尺寸类型
      setHtmlLoaded(true);

      // 只在首次加载HTML时保存初始尺寸类型
      if (!htmlLoaded) {
        initialSizeTypeRef.current = selectedSizeType;
        setInitialSizeType(selectedSizeType);
      }
    }
  }, [htmlContent, selectedSizeType, htmlLoaded, debugMode]);

  // 移除旧的编辑器初始化逻辑，因为我们现在使用iframe和setupPreview

  // 添加预览加载状态
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  // 添加下载加载状态
  const [isDownloading, setIsDownloading] = useState(false);
  // 添加保存加载状态
  const [isSaving, setIsSaving] = useState(false);

  // Buttons are active if a preview is active and not loading or previewing
  // 确保 isPreviewActive 也是已定义的，或者如果它不再需要，则移除它
  // 假设 isPreviewActive 是父组件传递的 prop 或者是在这个组件内定义的其他状态
  // 如果 isPreviewActive 也是被错误删除的，需要一并恢复或调整逻辑
  // 暂时假设 isPreviewActive 仍然存在或可访问
  // 移除 !isDownloading 条件，避免下载状态影响其他按钮
  const areButtonsEnabled = isPreviewActive && !isLoading && !isPreviewLoading && !isGenerating;

  // 执行编辑命令的函数
  const executeCommand = async (command, value = null) => {
    // 检查用户是否有权限使用文本编辑功能
    const result = await checkFeatureAvailability('文本编辑');
    if (!result.available) {
      showPermissionError('文本编辑', result.reason || '您的账号权限不足，无法使用此功能');
      return;
    }

    // 立即设置用户编辑标志，防止useEffect重新加载内容
    isUserEditingRef.current = true;

    try {
      if (!iframeRef.current) {
        console.warn('iframe元素不存在');
        return;
      }

      const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
      if (!iframeDoc) {
        console.warn('无法访问iframe文档');
        return;
      }

      // 使用中间件API获取当前活动元素
      let activeElement = null;
      let middleware = null;
      
      // 首先尝试通过中间件获取选中元素
      if (textEditorRef.current && textEditorRef.current.getMiddleware) {
        middleware = textEditorRef.current.getMiddleware();
        if (middleware) {
          activeElement = middleware.getSelectedElement();
        }
      }
      
      // 如果通过中间件未获取到元素，则使用传统方式查找
      if (!activeElement) {
        activeElement = iframeDoc.querySelector('[contenteditable="true"]:focus') ||
                        iframeDoc.querySelector('[contenteditable="true"].drag-mode') ||
                        iframeDoc.querySelector('[data-editing="true"]');
      }

      if (!activeElement) {
        return;
      }

      // 检查元素是否已经处于编辑状态
      const isAlreadyEditing = activeElement.hasAttribute('data-editing');
      const isEditMode = middleware ? middleware.getActiveMode() === 'editMode' : false;
      
      // 只有在元素还不是编辑状态且当前不是编辑模式时，才进入编辑模式
      if (!isAlreadyEditing && !isEditMode) {
        // 如果有拖拽模式，则先退出
        if (middleware && middleware.getActiveMode() === 'dragMode') {
          if (middleware.dragMode && middleware.dragMode.clearAllDragModes) {
            middleware.dragMode.clearAllDragModes();
          }
        }
        
        // 确保文本框有焦点并处于编辑模式
        activeElement.setAttribute('contenteditable', 'true');
        activeElement.setAttribute('data-editing', 'true');
        
        // 使用中间件API设置编辑状态
        if (middleware) {
          middleware.setActiveMode('editMode');
          middleware.selectElement(activeElement);
        }
        
        // 最后设置焦点，避免中途触发其他事件
        activeElement.focus();
      }

      // 确保启用 styleWithCSS 以便正确应用样式
      try {
        if (iframeDoc.queryCommandSupported('styleWithCSS')) {
          iframeDoc.execCommand('styleWithCSS', false, true);
        }
      } catch (e) {
        // 忽略错误
      }

      // 应用相应的命令
      switch (command) {
        case 'fontSize':
          applyFontSize(activeElement, value, iframeDoc);
          break;
        case 'foreColor':
          applyColor(activeElement, value);
          break;
        case 'fontName':
          activeElement.style.fontFamily = value;
          break;
        case 'bold':
          activeElement.style.fontWeight = activeElement.style.fontWeight === 'bold' ? 'normal' : 'bold';
          break;
        case 'italic':
          activeElement.style.fontStyle = activeElement.style.fontStyle === 'italic' ? 'normal' : 'italic';
          break;
        case 'underline':
          activeElement.style.textDecoration = activeElement.style.textDecoration === 'underline' ? 'none' : 'underline';
          break;
        case 'justifyLeft':
          activeElement.style.textAlign = 'left';
          break;
        case 'justifyCenter':
          activeElement.style.textAlign = 'center';
          break;
        case 'justifyRight':
          activeElement.style.textAlign = 'right';
          break;
        default:
          // 其他命令使用标准execCommand
          iframeDoc.execCommand(command, false, value);
          break;
      }
      
      // 触发内容变化事件
      if (onHtmlContentChange) {
        const contentContainer = iframeDoc.querySelector('.content-container');
        if (contentContainer) {
          const html = contentContainer.innerHTML;
          onHtmlContentChange(html);
        }
      }
    } catch (error) {
      console.error('执行编辑命令出错:', error);
    }
  };

  // 应用字体大小辅助函数
  const applyFontSize = (targetElement, fontSize, iframeDoc) => {
    try {
      // 获取当前选区
      const selection = iframeDoc.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);

        // 如果有选中文本，只对选中文本应用字号
        if (!range.collapsed) {
          // 创建span包装选中文本
          const span = iframeDoc.createElement('span');

          try {
            // 提取选中的内容
            const selectedContent = range.extractContents();

            // 清除选中内容中的所有字号样式
            clearFontSizeStyles(selectedContent);

            // 将内容添加到span中
            span.appendChild(selectedContent);

            // 应用新字号到span
            span.style.setProperty('font-size', fontSize, 'important');

            // 插入span到原位置
            range.insertNode(span);

            // 重新选中span内容
            selection.removeAllRanges();
            const newRange = iframeDoc.createRange();
            newRange.selectNodeContents(span);
            selection.addRange(newRange);
          } catch (e) {
            // 如果失败，对整个文本框应用字号
            applyFontSizeToElement(targetElement, fontSize);
          }
        } else {
          // 没有选中文本，对整个文本框应用字号
          applyFontSizeToElement(targetElement, fontSize);
        }
      } else {
        // 没有选区，对整个文本框应用字号
        applyFontSizeToElement(targetElement, fontSize);
      }
    } catch (e) {
      // 最后的备用方案
      applyFontSizeToElement(targetElement, fontSize);
    }
  };

  // 应用字体大小到元素辅助函数
  const applyFontSizeToElement = (element, fontSize) => {
    // 清除元素的所有字号样式
    clearFontSizeStyles(element);

    // 强制应用新字号
    element.style.setProperty('font-size', fontSize, 'important');
  };

  // 清除字体大小样式辅助函数
  const clearFontSizeStyles = (element) => {
    if (element.nodeType === Node.ELEMENT_NODE) {
      element.style.removeProperty('font-size');

      // 清除所有子元素的字号样式
      const allChildren = element.querySelectorAll ? element.querySelectorAll('*') : [];
      Array.from(allChildren).forEach(child => {
        child.style.removeProperty('font-size');
      });
    } else if (element.children) {
      // 处理DocumentFragment
      Array.from(element.children).forEach(clearFontSizeStyles);
    }
  };

  // 应用颜色到元素辅助函数 
  const applyColor = (targetElement, color) => {
    // 清除目标元素的所有颜色样式
    clearColorStyles(targetElement);

    // 强制应用新颜色
    targetElement.style.setProperty('color', color, 'important');
    targetElement.style.setProperty('-webkit-text-fill-color', color, 'important');
  };

  // 清除颜色样式辅助函数
  const clearColorStyles = (element) => {
    element.style.removeProperty('color');
    element.style.removeProperty('-webkit-text-fill-color');
    element.style.removeProperty('background');
    element.style.removeProperty('background-image');
    element.style.removeProperty('-webkit-background-clip');
    element.style.removeProperty('background-clip');
    element.style.removeProperty('text-shadow');

    // 清除所有子元素的颜色样式
    const allChildren = element.querySelectorAll('*');
    allChildren.forEach(child => {
      child.style.removeProperty('color');
      child.style.removeProperty('-webkit-text-fill-color');
      child.style.removeProperty('background');
      child.style.removeProperty('background-image');
      child.style.removeProperty('-webkit-background-clip');
      child.style.removeProperty('background-clip');
      child.style.removeProperty('text-shadow');
    });
  };

  // 使用useRef来存储工具栏状态，避免组件重新渲染时状态丢失
  const toolbarStateRef = useRef({
    lastSelectedFont: '',
    lastSelectedSize: '',
    lastSelectedColor: '#000000',
    lastBoldState: false,
    lastItalicState: false,
    lastUnderlineState: false,
    lastAlignmentState: 'left'
  });

  // 初始化预览
  useEffect(() => {
    if (isPreviewActive) {
      setupPreview();
    }
  }, [isPreviewActive, setupPreview]);

  // 防抖函数工具
  const debounced = useCallback((func, delay) => {
    let timeoutId;
    return (...args) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(null, args), delay);
    };
  }, []);

  // 清除交互边框和编辑器相关元素的函数
  const cleanForSave = useCallback((element) => {
    // 创建元素的克隆
    const cleanElement = element.cloneNode(true);

    // 清除iframe中的文本选中状态，避免保存时保存选中效果
    const iframeDoc = iframeRef.current?.contentDocument || iframeRef.current?.contentWindow?.document;
    if (iframeDoc && iframeDoc.getSelection) {
      iframeDoc.getSelection().removeAllRanges();
    }

    // 移除所有contenteditable属性和相关编辑属性
    const editableElements = cleanElement.querySelectorAll('[contenteditable], [data-field], .drag-mode, [data-editing]');
    editableElements.forEach(el => {
      el.removeAttribute('contenteditable');
      el.removeAttribute('data-field');
      el.removeAttribute('data-editing');
      el.removeAttribute('title');
      el.removeAttribute('tabindex');
      el.classList.remove('drag-mode');
      el.classList.remove('dragging');
      el.classList.remove('resize-mode');

      // 清理可能影响显示的内联样式
      if (el.style) {
        el.style.cursor = 'default';
        el.style.outline = 'none';
        // 移除可能的拖拽相关内联样式
        el.style.removeProperty('z-index');
        el.style.removeProperty('opacity');
        el.style.removeProperty('user-select');
        el.style.removeProperty('-webkit-user-select');
        el.style.removeProperty('-moz-user-select');
        el.style.removeProperty('-ms-user-select');
      }
    });

    // 移除所有编辑器UI相关元素
    const elementsToRemove = cleanElement.querySelectorAll(
      '.resize-handle, .drag-handle, .text-editor-toolbar, .text-editor-toolbar-container, ' +
      '.color-picker, .color-picker-wrapper, .color-picker-popup, .editor-control, ' +
      '.text-editor-controls, .text-editor-panel, [data-editor-ui="true"]'
    );
    elementsToRemove.forEach(el => el.remove());

    return cleanElement;
  }, []);

  // 处理保存功能
  const handleSave = useCallback(async (saveType = 'manual') => {
    // 如果已经在保存中或没有coverId，则返回
    if (isSaving || !coverId) {
      return;
    }

    // 取消任何正在进行的自动保存操作
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
      autoSaveTimerRef.current = null;
    }

    // 只有手动保存时才设置保存状态
    if (saveType === 'manual') {
      setIsSaving(true);
    }

    try {
      if (!iframeRef.current) {
        if (saveType === 'manual') {
          message.error('预览区域不可用');
        }
        return;
      }

      // 获取预览内容
      const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;

      if (!iframeDoc) {
        if (saveType === 'manual') {
          message.error('无法获取预览内容');
        }
        return;
      }

      // 获取内容容器
      const contentContainer = iframeDoc.querySelector('.content-container');
      if (!contentContainer) {
        if (saveType === 'manual') {
          message.error('找不到内容容器');
        }
        return;
      }

      // 清除交互边框和编辑器相关元素
      const cleanedContainer = cleanForSave(contentContainer);
      const htmlContent = cleanedContainer.innerHTML;

      // 调用API保存HTML内容
      const token = localStorage.getItem('token');
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

      const response = await axios.post(`/api/cover/${coverId}/save`, {
        edited_html_content: htmlContent,
        save_type: saveType
      }, { headers });

      if (response.data && response.data.success) {
        if (saveType === 'manual') {
          message.success('保存成功');
        }
        // 自动保存不显示成功消息，避免干扰用户
      } else {
        throw new Error(response.data?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      // 手动保存时显示错误，自动保存时不显示
      if (saveType === 'manual') {
        message.error('保存失败：' + (error.message || '未知错误'));
      }
    } finally {
        setIsSaving(false);
    }
  }, [isSaving, coverId, cleanForSave]);

  // 自动保存函数
  const autoSave = useCallback(() => {
    if (coverId && contentLoaded && iframeRef.current) {
        handleSave('auto');
      }
  }, [handleSave, coverId, contentLoaded, iframeRef]);

  // 监听HTML内容变化，触发自动保存
  useEffect(() => {
    // 先清除可能存在的自动保存定时器，确保不会有多个定时器同时运行
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
      autoSaveTimerRef.current = null;
    }
    
    if (htmlContent && coverId) {
      // 当HTML内容变化时，设置一个定时器延迟自动保存
      // 这样可以避免频繁保存，只在用户停止编辑一段时间后触发
      autoSaveTimerRef.current = setTimeout(() => {
        autoSave();
      }, 5000); // 5秒后触发自动保存
    }

      return () => {
        // 组件卸载或依赖项变化时清除定时器
        if (autoSaveTimerRef.current) {
          clearTimeout(autoSaveTimerRef.current);
          autoSaveTimerRef.current = null;
        }
      };
  }, [htmlContent, autoSave, coverId]);
  
  // 添加页面刷新前自动保存功能
  useEffect(() => {
    // 页面刷新或关闭前的处理函数
    const handleBeforeUnload = () => {
      // 只有当满足保存条件时，才触发自动保存
      if (coverId && contentLoaded && iframeRef.current) {
        if (debugMode) {
        console.log('页面刷新前自动保存');
        }
        // 立即执行保存
        handleSave('auto');
      }
    };
    
    // 添加页面刷新前的事件监听器
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // 清理函数
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [coverId, contentLoaded, iframeRef, handleSave, debugMode]);

  // 添加取消自动保存的函数
  const cancelAutoSave = useCallback(() => {
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
      autoSaveTimerRef.current = null;
    }
  }, []);

  // 新的下载处理函数
  const handleCaptureAndDownload = useCallback(async (fileNamePrefix = '封面截图') => {
    if (!iframeRef.current) {
      message.error('预览区域不可用，无法下载。');
      return;
    }

    try {
      // 显示下载开始的消息提示
      // message.success('图片已开始下载');

      // 调用downloadImageUtils.js中的handleScreenshotDownload函数
      // 传递必要的参数：预览区域引用、设置下载状态的函数、文件名、调试模式、naturalDimensions、scale
      await handleScreenshotDownload(
        iframeRef,
        setIsDownloading,
        `${fileNamePrefix}_${Date.now()}.png`,
        debugMode,
        naturalDimensions, // 传递实际自然尺寸
        scale // 传递当前缩放比例
      );
    } catch (error) {
      message.error(`下载失败: ${error.message}`);
    }
  }, [logger, debugMode, naturalDimensions, setIsDownloading, scale]); // 添加scale到依赖数组
  
  // HTML下载功能已迁移到PreviewEditorToolbar.jsx中



  // 定义一个通用的权限检查和错误处理函数
  // ... existing code ...

  const handleMessage = useCallback((event) => {
    // 处理iframe发送的消息
    const { data } = event;

    if (!data || typeof data !== 'object') return;

    if (data.type === 'iframe-content-loaded') {
      // 内容加载完成
      setContentLoaded(true);
      contentLoadedRef.current = true;
      setIsLoading(false);

      // 如果存在自定义尺寸，设置到状态中
      if (data.width && data.height) {
        const contentWidth = data.width;
        const contentHeight = data.height;
        
        setHtmlContentSize({
          width: contentWidth,
          height: contentHeight
        });
        
        // 如果用户没有手动设置缩放比例，且尚未应用初始智能缩放，则应用智能缩放
        if (userScale === null && !isInitialScaleSet) {
          const optimalScale = calculateOptimalScale(contentWidth, contentHeight);
          setScale(optimalScale);
          setIsInitialScaleSet(true); // 标记已应用初始智能缩放
        }
      }

      // 触发自定义加载完成回调
      if (onLoadComplete && typeof onLoadComplete === 'function') {
        setTimeout(() => onLoadComplete(), 100);
      }
      
      // 触发自定义事件通知MainLayout
      const htmlLoadedEvent = new CustomEvent('htmlLoaded', { 
        detail: { isLoaded: true } 
      });
      window.dispatchEvent(htmlLoadedEvent);
    } else if (data.type === 'iframe-images-loaded') {
      // 所有图片加载完成后更新尺寸
      if (data.width && data.height) {
        const contentWidth = data.width;
        const contentHeight = data.height;
        
        setHtmlContentSize({
          width: contentWidth,
          height: contentHeight
        });
        
        // 如果用户没有手动设置缩放比例，且尚未应用初始智能缩放，则应用智能缩放
        if (userScale === null && !isInitialScaleSet) {
          const optimalScale = calculateOptimalScale(contentWidth, contentHeight);
          setScale(optimalScale);
          setIsInitialScaleSet(true); // 标记已应用初始智能缩放
        }
      }
    }
  }, [onLoadComplete, userScale, isInitialScaleSet, calculateOptimalScale]);

  useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [handleMessage]);

  // 添加手动重新加载功能
  const handleReload = useCallback(() => {
    // 重置状态
    resetLoadState();
    // 短暂延迟后重新加载
    setTimeout(() => {
      setupPreview();
    }, 100);
  }, [resetLoadState, setupPreview]);

  // 新增：HTML 下载的本地处理逻辑
  const handleDownloadHtml = useCallback(() => {
    if (onDownloadHtml) {
      onDownloadHtml();
      return;
    }
    if (!htmlContent) {
      message.error('没有可下载的HTML内容');
      return;
    }
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${coverCode || 'cover'}_${new Date().toISOString()}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [htmlContent, onDownloadHtml, coverCode]);
  
  // 重写"还原"按钮的事件处理器
  const handleRestore = () => {
    // 1. 取消可能进行中的自动保存
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
      autoSaveTimerRef.current = null;
    }
    
    // 2. 重置用户编辑状态标志
    if (isUserEditingRef) {
      isUserEditingRef.current = false;
    }

    // 3. 调用父组件的onRestore方法来更新HTML内容
    if (onRestore) {
      onRestore();
    }
    
    // 4. 更新当前编辑内容和最新HTML引用
    if (htmlContent) {
      setCurrentEditingContent(htmlContent);
      if (latestEditorHtmlRef) {
        latestEditorHtmlRef.current = htmlContent;
      }
    }
    
    // 5. 确保setupPreview不会跳过更新
    lastUsedHtmlContentForSrcdocRef.current = '';
    previousPreviewHtmlRef.current = '';
    
    // 6. 完全重置所有状态
    resetLoadState();
    
    // 7. 直接调用setupPreview，而不是依赖iframe重新挂载
    setupPreview();
  };

  // 新增：权限检查的通用包装器
  const handleActionWithPermission = useCallback(async (actionName, actionFn) => {
    if (!actionFn) return;
    // 权限检查逻辑是异步的，必须在用户交互（如 onClick）触发的回调中调用，
    // 而不能在组件渲染期间直接调用。
    try {
      const result = await checkFeatureAvailability(actionName);
      if (!result.available) {
        showPermissionError(actionName, result.reason || '您的账号权限不足，无法使用此功能');
        return;
      }
      actionFn();
    } catch (error) {
      showPermissionError(actionName, '权限检查失败，请重试');
    }
  }, [checkFeatureAvailability, showPermissionError]);

  return (
    <div className="absolute inset-0 flex flex-row bg-white chat-preview-container">
      {/* 预览区域 */}
      <div className="flex-grow h-full flex flex-col">
        <div className="editor-content-area flex-grow overflow-hidden" style={{ 
        scrollbarWidth: 'none', 
        msOverflowStyle: 'none', 
          backgroundColor: '#f0f2f5', // 给预览区一个不同的背景色
          height: '100%'
      }}>
        <style>{`
          /* 隐藏滚动条但保持可滚动性 */
          .editor-content-area::-webkit-scrollbar {
            display: none;
            width: 0 !important;
          }
          .chat-preview-container *::-webkit-scrollbar {
            display: none;
            width: 0 !important;
          }
          /* Firefox */
          .chat-preview-container * {
            scrollbar-width: none;
          }
          /* IE/Edge */
          .chat-preview-container * {
            -ms-overflow-style: none;
          }
          /* 确保预览容器正确居中 */
          .preview-outer-container {
            display: flex;
            justify-content: center;
            align-items: center;
          }
          /* 确保预览内容不显示滚动条 */
          .preview-container {
            overflow: visible !important;
          }
        `}</style>
          <div className="relative w-full h-full">
          <div
            ref={outerPreviewAreaRef}
              className="px-4 pt-0 pb-0 flex items-center justify-center bg-transparent preview-outer-container w-full h-full"
            style={outerPreviewAreaStyle}
          >
            <div
              ref={containerRef}
              style={previewContainerStyle}
              className="preview-container"
            >
              {isLoading && !isGenerating && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
                  <div className="flex flex-col items-center">
                    <LoadingSpinner className="h-12 w-12" />
                    <span className="ml-3 text-lg">加载中...</span>
                  </div>
                </div>
              )}
              {loadError && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
                  <div className="flex flex-col items-center p-4 bg-red-50 border border-red-200 rounded-md">
                    <span className="text-sm text-red-600 mb-2">{loadError}</span>
                    <button
                      className="px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm"
                      onClick={() => {
                        setLoadError(null);
                        setupPreview();
                      }}
                    >
                      重试
                    </button>
                  </div>
                </div>
              )}
                <iframe
                    key={iframeKey} // 绑定key
                  ref={iframeRef}
                  style={{
                      width: htmlContentSize?.width > 0 ? `${htmlContentSize.width}px` : '750px',
                      height: htmlContentSize?.height > 0 ? `${htmlContentSize.height}px` : '1000px',
                    border: 'none',
                    display: 'block',
                    background: 'transparent',
                    overflow: 'hidden',
                      flexShrink: 0,
                  }}
                    sandbox="allow-same-origin allow-scripts" // 修复：添加 allow-scripts 以允许编辑器脚本执行
                  title="封面预览"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧编辑面板 - 仅在预览激活时显示，使用固定定位确保不受输入框影响 */}
      {isPreviewActive && (
        <div className="w-80 h-full bg-white border-l border-gray-200 flex flex-col shadow-lg overflow-y-auto pt-2 pb-4 fixed top-0 right-0 bottom-0" style={{ zIndex: 100 }}>
          {/* 顶部全局按钮 */}
           <div className="py-2 px-3 border-b border-gray-200 flex gap-2">
            {onShare && (
              <button
                onClick={() => handleActionWithPermission('分享', onShare)}
                title="分享"
                className="flex-grow px-3 py-1.5 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded-md shadow-sm disabled:opacity-50 relative"
                disabled={!areButtonsEnabled}
              >
                分享
                <Crown size={10} className="absolute -top-1 -right-1 text-orange-500" />
              </button>
            )}
            {onDownloadCover && (
              <button
                onClick={() => onDownloadCover()}
                title="下载为图片"
                className="flex-grow px-3 py-1.5 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md shadow-sm disabled:opacity-50"
                disabled={!areButtonsEnabled}
              >
                下载为图片
              </button>
            )}
             <button
                onClick={() => handleActionWithPermission('HTML下载', handleDownloadHtml)}
                title="下载为Html"
                className="flex-grow px-3 py-1.5 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md shadow-sm disabled:opacity-50 relative"
                disabled={!areButtonsEnabled}
              >
                下载为Html
                <Crown size={10} className="absolute -top-1 -right-1 text-orange-500" />
              </button>
          </div>

          {/* 编辑功能区域 */}
          <div className="flex-grow pt-2 px-4 pb-4 overflow-y-auto">
            {contentLoaded && (
              <div className="flex flex-col space-y-4">
                <PreviewEditorToolbar
                  iframeRef={iframeRef}
                  executeCommandInternal={executeCommand}
                  areButtonsEnabled={areButtonsEnabled}
                  // 传递空props以禁用不再在此工具栏中的按钮
                  onDownloadCover={null}
                  onDownloadHtml={null}
                  onShare={null}
                  onPreview={null}
                  onSave={null}
                  onRestore={null}
                  onViewSource={null}
                  checkFeatureAvailability={checkFeatureAvailability}
                  showPermissionError={showPermissionError}
                  htmlContent={htmlContent}
                />
              </div>
            )}
          </div>
          
          {/* 底部操作区 - 使用sticky定位确保始终可见 */}
          <div className="p-3 border-t border-gray-200 bg-white sticky bottom-0">
             {/* 缩放控制器 - 修改为水平布局 */}
            <div className="mb-4">
        <ZoomController 
          scale={scale}
          minScale={0.1}
          maxScale={2.5}
          onScaleChange={handleScaleChange}
          onResetScale={resetScale}
        />
            </div>
            {/* 底部按钮组 - 修改为流式布局，确保三个按钮并排显示 */}
            <div className="flex flex-wrap gap-2">
              {onPreview && (
                <button
                  onClick={onPreview}
                  title="全屏预览"
                  className="flex-grow px-3 py-2 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded-md shadow-sm disabled:opacity-50"
                  disabled={!areButtonsEnabled}
                >
                  全屏预览
                </button>
              )}
              {coverId && onSave && (
                <button
                  onClick={() => onSave('manual')}
                  title="保存"
                  className="flex-grow px-3 py-2 text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded-md shadow-sm disabled:opacity-50"
                  disabled={!areButtonsEnabled || isSaving}
                >
                  {isSaving ? '保存中...' : '保存'}
                </button>
              )}
              {onRestore && (
                <button
                  onClick={handleRestore}
                  title="还原Html为最初状态"
                  className="flex-grow px-3 py-2 text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 rounded-md shadow-sm disabled:opacity-50"
                  disabled={!areButtonsEnabled}
                >
                  还原Html为最初状态
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 权限提示和加载动画等模态层 */}
      <PermissionDialog
        open={showPermissionDialog}
        onClose={() => setShowPermissionDialog(false)}
        featureName={permissionDialogInfo.featureName}
        reason={permissionDialogInfo.reason}
      />

      {/* 生成封面的加载动画 - 相对于预览区域居中显示 */}
      {isGenerating && (
        <div className="absolute inset-0 flex items-center justify-center z-[9999] bg-black bg-opacity-5 backdrop-blur-[2px]">
          <div className="flex flex-col items-center p-6 rounded-2xl shadow-xl max-w-sm mx-4" style={{background: 'rgba(255,255,255,0.95)'}}>
            {/* 主要动画容器 */}
            <div className="relative w-20 h-20 mb-4">
              {/* 外层脉动环 */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400 via-pink-400 to-purple-500 opacity-20 animate-pulse"></div>

              {/* 中层旋转环 */}
              <div className="absolute inset-2 rounded-full border-4 border-transparent bg-gradient-to-r from-purple-500 to-pink-500 animate-spin"
                   style={{
                     background: 'conic-gradient(from 0deg, #8b5cf6, #ec4899, #8b5cf6)',
                     WebkitMask: 'radial-gradient(circle at center, transparent 60%, black 61%)',
                     mask: 'radial-gradient(circle at center, transparent 60%, black 61%)'
                   }}>
              </div>

              {/* 内层进度圆 */}
              <div className="absolute inset-4 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-semibold text-sm">
                {Math.round(progress)}%
              </div>

              {/* 装饰性粒子 - 增加更多动态元素 */}
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-purple-400 rounded-full animate-bounce opacity-60"></div>
              <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-pink-400 rounded-full animate-bounce opacity-60" style={{animationDelay: '0.5s'}}></div>
              
              {/* 新增装饰性粒子 */}
              <div className="absolute top-1/2 -right-3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-ping opacity-40" style={{animationDuration: '1.5s'}}></div>
              <div className="absolute -top-3 left-1/2 w-1.5 h-1.5 bg-pink-300 rounded-full animate-ping opacity-40" style={{animationDuration: '2s'}}></div>
            </div>

            {/* 背景装饰元素 */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden rounded-2xl pointer-events-none z-[-1]">
              <div className="absolute top-[-10%] right-[-10%] w-40 h-40 bg-gradient-to-br from-purple-200 to-transparent rounded-full opacity-20 animate-pulse" style={{animationDuration: '3s'}}></div>
              <div className="absolute bottom-[-15%] left-[-15%] w-48 h-48 bg-gradient-to-tr from-pink-200 to-transparent rounded-full opacity-20 animate-pulse" style={{animationDuration: '4s'}}></div>
            </div>

            {/* 进度条 */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-3 overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>

            {/* 描述性文本 */}
            <p className="text-gray-700 font-medium text-sm mb-1 text-center">
              {feedbackText || "AI正在为您生成封面..."}
            </p>

            {/* 辅助文本 */}
            <p className="text-gray-500 text-xs text-center">
              请稍候，精彩即将呈现
            </p>
          </div>
        </div>
      )}

      {(loadError || (contentLoaded && (!htmlContentSize || htmlContentSize.width < 100))) && (
        <div className="absolute bottom-4 right-4 z-50">
          <button
            className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded-md text-sm flex items-center"
            onClick={handleReload}
          >
            <RotateCcw className="w-4 h-4 mr-1" /> 重新加载
          </button>
        </div>
      )}
    </div>
  );
});

export default ChatPreview;
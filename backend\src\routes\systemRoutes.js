const express = require('express');
const router = express.Router();
const systemController = require('../controllers/systemController');
const { auth, adminAuth } = require('../middlewares/authMiddleware');

// 获取操作所需积分
router.get('/operation-cost/:type', systemController.getOperationCost);

// 获取系统配置（仅管理员可用）
router.get('/configs', adminAuth, systemController.getSystemConfigs);

// 获取所有政策内容（隐私政策和用户协议）
router.get('/policies', systemController.getPolicies);

// 获取隐私政策内容
router.get('/policies/privacy', systemController.getPrivacyPolicy);

// 获取用户协议内容
router.get('/policies/agreement', systemController.getUserAgreement);

// 获取渲染方式配置（普通用户可访问）
router.get('/rendering-mode-config', systemController.getRenderingModeConfig);

// 记录安全违规（普通用户可访问）
router.post('/security-violation', systemController.logSecurityViolation);

// 获取安全规则配置（普通用户可访问，只读）
router.get('/security-rules', auth, systemController.getSecurityRulesConfig);

module.exports = router;
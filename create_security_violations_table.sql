-- 创建HTML安全违规记录表
CREATE TABLE IF NOT EXISTS html_security_violations (
  id INT PRIMARY KEY AUTO_INCREMENT COMMENT '违规记录ID，唯一标识',
  user_id INT NOT NULL COMMENT '用户ID',
  user_phone VARCHAR(20) COMMENT '用户手机号',
  file_name VARCHAR(255) COMMENT '原始文件名',
  upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') COMMENT '风险等级',
  violation_reasons JSON COMMENT '违规原因详情',
  detected_threats JSON COMMENT '检测到的威胁列表',
  file_size INT COMMENT '文件大小（字节）',
  file_hash VARCHAR(64) COMMENT '文件内容SHA256哈希',
  detection_engine_version VARCHAR(50) COMMENT '检测引擎版本',
  ip_address VARCHAR(45) COMMENT '上传IP地址',
  user_agent TEXT COMMENT '用户代理信息',
  INDEX idx_user_id (user_id),
  INDEX idx_upload_time (upload_time),
  INDEX idx_risk_level (risk_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='HTML安全违规记录表';

-- 插入测试数据
INSERT INTO html_security_violations 
(user_id, file_name, risk_level, violation_reasons, detected_threats, file_size, ip_address) 
VALUES 
(1, 'test1.html', 'HIGH', 
 JSON_OBJECT('xss_detected', true, 'script_tags', 2), 
 JSON_ARRAY('XSS攻击', '恶意脚本'), 
 1024, '127.0.0.1'),
(1, 'test2.html', 'MEDIUM', 
 JSON_OBJECT('suspicious_code', true, 'eval_calls', 1), 
 JSON_ARRAY('可疑代码', 'eval调用'), 
 2048, '127.0.0.1'),
(1, 'test3.html', 'LOW', 
 JSON_OBJECT('iframe_detected', true), 
 JSON_ARRAY('iframe标签'), 
 512, '127.0.0.1');

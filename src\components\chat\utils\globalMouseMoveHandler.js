/**
 * 全局鼠标移动事件处理工具
 * 用于处理全局鼠标移动事件，确保光标样式和悬停效果正确显示
 */

/**
 * 处理全局鼠标移动事件
 * @param {MouseEvent} e - 鼠标事件对象
 * @param {Document} doc - 文档对象
 * @param {Set} editableElementsTracker - 可编辑元素跟踪器
 * @param {boolean} isDragging - 是否正在拖拽
 * @param {Function} isClickOnSpecialElement - 检查是否点击了特殊元素的函数
 * @param {boolean} debugMode - 是否开启调试模式
 */
export const handleGlobalMouseMove = (e, doc, editableElementsTracker, isDragging, isClickOnSpecialElement, debugMode = false) => {
  // 如果正在拖拽，不处理鼠标移动
  if (isDragging) return;

  // 获取鼠标下方的元素
  const target = e.target;
  
  // 检查是否为可编辑元素
  const isEditableTarget = 
    (editableElementsTracker && editableElementsTracker.has(target)) || 
    target.getAttribute('contenteditable') === 'true' || 
    target.hasAttribute('data-editable-fengmian') ||
    target.closest('[contenteditable="true"]') ||
    target.closest('[data-editable-fengmian]');
  
  // 检查是否为特殊控件
  const isSpecialControl = typeof isClickOnSpecialElement === 'function' ? isClickOnSpecialElement(e) : false;
  
  // 设置光标样式
  if (isEditableTarget) {
    // 如果目标是可编辑元素，显示文本编辑光标
    target.style.cursor = 'text';
    
    // 添加悬停效果
    if (!target.classList.contains('element-hover-outline') && 
        !target.classList.contains('editing-active-outline')) {
      target.classList.add('element-hover-outline');
      
      if (debugMode) console.log('添加悬停效果:', target.tagName);
    }
  } else if (isSpecialControl) {
    // 如果是特殊控件，显示默认光标
    target.style.cursor = 'default';
    
    if (debugMode) console.log('特殊控件:', target.tagName);
  } else {
    // 其他情况，显示默认光标
    target.style.cursor = 'default';
    
    // 移除所有悬停效果
    try {
      const hoverElements = doc.querySelectorAll('.element-hover-outline:not(.editing-active-outline)');
      hoverElements.forEach(el => {
        el.classList.remove('element-hover-outline');
        
        if (debugMode) console.log('移除悬停效果:', el.tagName);
      });
    } catch (error) {
      if (debugMode) console.error('移除悬停效果错误:', error);
    }
  }
};

/**
 * 创建全局鼠标移动处理函数
 * @param {Document} doc - 文档对象
 * @param {Set} editableElementsTracker - 可编辑元素跟踪器
 * @param {Function} isClickOnSpecialElement - 检查是否点击了特殊元素的函数
 * @param {boolean} debugMode - 是否开启调试模式
 * @returns {Function} 全局鼠标移动处理函数
 */
export const createGlobalMouseMoveHandler = (doc, editableElementsTracker, isClickOnSpecialElement, debugMode = false) => {
  // 添加拖拽状态跟踪变量
  let isDragging = false;
  
  // 设置拖拽状态
  const setDragging = (value) => {
    isDragging = value;
  };
  
  // 返回处理函数和设置拖拽状态的函数
  return {
    handler: (e) => handleGlobalMouseMove(e, doc, editableElementsTracker, isDragging, isClickOnSpecialElement, debugMode),
    setDragging
  };
}; 
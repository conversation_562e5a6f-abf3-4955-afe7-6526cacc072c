/**
 * 积分套餐管理模块
 * 功能：加载积分套餐列表、添加、编辑、删除、启用/禁用积分套餐
 */

// 定义积分套餐管理模块命名空间
const pointPackagesModule = (function() {
  // 模块内部变量
  let pointPackages = [];
  let currentEditId = null;

  // DOM元素
  const elements = {
    pointPackagesTable: document.getElementById('pointPackagesTable'),
    addPointPackageBtn: document.getElementById('addPointPackageBtn'),
    pointPackageModal: document.getElementById('pointPackageModal'),
    pointPackageForm: document.getElementById('pointPackageForm'),
    pointPackageModalLabel: document.getElementById('pointPackageModalLabel'),
    savePointPackageBtn: document.getElementById('savePointPackageBtn'),
    pointPackageId: document.getElementById('pointPackageId'),
    pointPackageName: document.getElementById('pointPackageName'),
    pointPackagePoints: document.getElementById('pointPackagePoints'),
    pointPackagePrice: document.getElementById('pointPackagePrice'),
    pointPackageBonusPoints: document.getElementById('pointPackageBonusPoints'),
    pointPackageDescription: document.getElementById('pointPackageDescription'),
    pointPackageIsActive: document.getElementById('pointPackageIsActive')
  };

  /**
   * 初始化积分套餐管理模块
   */
  function init() {
    // 绑定事件
    if (elements.addPointPackageBtn) {
      elements.addPointPackageBtn.addEventListener('click', showAddPointPackageModal);
    }
    
    if (elements.savePointPackageBtn) {
      elements.savePointPackageBtn.addEventListener('click', savePointPackage);
    }

    // 初始加载数据
    loadPointPackages();

    // 监听DOM变化，当页面显示时重新加载数据
    observePageVisibility('point-packages', () => {
      loadPointPackages();
    });
  }

  /**
   * 加载积分套餐列表
   */
  async function loadPointPackages() {
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch('/api/admin/payment/point-packages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('加载积分套餐列表失败');
      }

      const data = await response.json();
      
      if (data.success) {
        pointPackages = data.data.packages || [];
        renderPointPackagesTable(pointPackages);
      } else {
        showToast('error', data.message || '加载积分套餐列表失败');
        renderEmptyTable('加载失败: ' + data.message);
      }
    } catch (error) {
      console.error('加载积分套餐列表失败:', error);
      showToast('error', '加载积分套餐列表失败，请重试');
      renderEmptyTable('加载失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 渲染积分套餐表格
   * @param {Array} packages 积分套餐数组
   */
  function renderPointPackagesTable(packages) {
    if (!elements.pointPackagesTable) return;
    
    if (!packages || packages.length === 0) {
      renderEmptyTable('暂无积分套餐数据');
      return;
    }

    // 表格内容
    const rows = packages.map(pkg => {
      return `
        <tr>
          <td>${pkg.id}</td>
          <td>${pkg.name}</td>
          <td>${pkg.points}</td>
          <td>${pkg.bonus_points || 0}</td>
          <td>¥${pkg.price}</td>
          <td>${pkg.is_active ? '<span class="text-success">启用</span>' : '<span class="text-danger">停用</span>'}</td>
          <td>
            <button class="btn btn-sm btn-primary edit-point-package" data-id="${pkg.id}">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm ${pkg.is_active ? 'btn-warning' : 'btn-success'} toggle-point-package" data-id="${pkg.id}" data-active="${pkg.is_active}">
              <i class="bi ${pkg.is_active ? 'bi-pause' : 'bi-play'}"></i>
            </button>
            <button class="btn btn-sm btn-danger delete-point-package" data-id="${pkg.id}">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    }).join('');

    // 更新表格
    elements.pointPackagesTable.innerHTML = `
      <thead>
        <tr>
          <th>ID</th>
          <th>套餐名称</th>
          <th>积分数量</th>
          <th>赠送积分</th>
          <th>价格(元)</th>
          <th>状态</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        ${rows}
      </tbody>
    `;

    // 绑定事件
    document.querySelectorAll('.edit-point-package').forEach(btn => {
      btn.addEventListener('click', function() {
        const packageId = this.getAttribute('data-id');
        editPointPackage(packageId);
      });
    });
    
    document.querySelectorAll('.toggle-point-package').forEach(btn => {
      btn.addEventListener('click', function() {
        const packageId = this.getAttribute('data-id');
        const isActive = this.getAttribute('data-active') === 'true';
        togglePointPackageStatus(packageId, !isActive);
      });
    });
    
    document.querySelectorAll('.delete-point-package').forEach(btn => {
      btn.addEventListener('click', function() {
        const packageId = this.getAttribute('data-id');
        deletePointPackage(packageId);
      });
    });
  }

  /**
   * 渲染空表格
   * @param {string} message 显示的消息
   */
  function renderEmptyTable(message) {
    if (!elements.pointPackagesTable) return;
    
    elements.pointPackagesTable.innerHTML = `
      <thead>
        <tr>
          <th>ID</th>
          <th>套餐名称</th>
          <th>积分数量</th>
          <th>赠送积分</th>
          <th>价格(元)</th>
          <th>状态</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colspan="7" class="text-center">${message}</td>
        </tr>
      </tbody>
    `;
  }

  /**
   * 显示添加积分套餐模态框
   */
  function showAddPointPackageModal() {
    if (!elements.pointPackageModal || !elements.pointPackageForm) return;
    
    // 重置表单
    elements.pointPackageForm.reset();
    elements.pointPackageId.value = '';
    elements.pointPackageModalLabel.textContent = '添加积分套餐';
    currentEditId = null;
    
    // 显示模态框
    const modal = new bootstrap.Modal(elements.pointPackageModal);
    modal.show();
  }

  /**
   * 编辑积分套餐
   * @param {string} packageId 积分套餐ID
   */
  async function editPointPackage(packageId) {
    if (!elements.pointPackageModal || !elements.pointPackageForm) return;
    
    try {
      showLoading();
      
      // 发送API请求获取套餐详情
      const response = await fetch(`/api/admin/payment/point-packages/${packageId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('获取积分套餐详情失败');
      }

      const data = await response.json();
      
      if (data.success) {
        const packageData = data.data.package;
        
        // 填充表单
        elements.pointPackageId.value = packageData.id;
        elements.pointPackageName.value = packageData.name || '';
        elements.pointPackagePoints.value = packageData.points || '';
        elements.pointPackagePrice.value = packageData.price || '';
        elements.pointPackageBonusPoints.value = packageData.bonus_points || 0;
        elements.pointPackageDescription.value = packageData.description || '';
        if (elements.pointPackageIsActive) {
          elements.pointPackageIsActive.checked = packageData.is_active;
        }
        
        // 更新模态框标题
        elements.pointPackageModalLabel.textContent = '编辑积分套餐';
        currentEditId = packageData.id;
        
        // 显示模态框
        const modal = new bootstrap.Modal(elements.pointPackageModal);
        modal.show();
      } else {
        showToast('error', data.message || '获取积分套餐详情失败');
      }
    } catch (error) {
      console.error('获取积分套餐详情失败:', error);
      showToast('error', '获取积分套餐详情失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 保存积分套餐
   */
  async function savePointPackage() {
    if (!elements.pointPackageForm) return;
    
    // 检查表单有效性
    if (!elements.pointPackageForm.checkValidity()) {
      elements.pointPackageForm.reportValidity();
      return;
    }
    
    try {
      showLoading();
      
      // 收集表单数据
      const formData = {
        name: elements.pointPackageName.value,
        points: parseInt(elements.pointPackagePoints.value),
        price: parseFloat(elements.pointPackagePrice.value),
        bonus_points: parseInt(elements.pointPackageBonusPoints.value || 0),
        description: elements.pointPackageDescription.value,
        is_active: elements.pointPackageIsActive ? elements.pointPackageIsActive.checked : true
      };
      
      let response;
      
      if (currentEditId) {
        // 更新现有套餐
        response = await fetch(`/api/admin/payment/point-packages/${currentEditId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(formData)
        });
      } else {
        // 创建新套餐
        response = await fetch('/api/admin/payment/point-packages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(formData)
        });
      }

      if (!response.ok) {
        throw new Error('保存积分套餐失败');
      }

      const data = await response.json();
      
      if (data.success) {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(elements.pointPackageModal);
        if (modal) {
          modal.hide();
        }
        
        // 重新加载数据
        loadPointPackages();
        
        showToast('success', currentEditId ? '积分套餐更新成功' : '积分套餐创建成功');
      } else {
        showToast('error', data.message || '保存积分套餐失败');
      }
    } catch (error) {
      console.error('保存积分套餐失败:', error);
      showToast('error', '保存积分套餐失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 切换积分套餐状态
   * @param {string} packageId 积分套餐ID
   * @param {boolean} isActive 是否激活
   */
  async function togglePointPackageStatus(packageId, isActive) {
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch(`/api/admin/payment/point-packages/${packageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        },
        body: JSON.stringify({ is_active: isActive })
      });

      if (!response.ok) {
        throw new Error('更新积分套餐状态失败');
      }

      const data = await response.json();
      
      if (data.success) {
        // 重新加载数据
        loadPointPackages();
        
        showToast('success', isActive ? '积分套餐已启用' : '积分套餐已停用');
      } else {
        showToast('error', data.message || '更新积分套餐状态失败');
      }
    } catch (error) {
      console.error('更新积分套餐状态失败:', error);
      showToast('error', '更新积分套餐状态失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 删除积分套餐
   * @param {string} packageId 积分套餐ID
   */
  async function deletePointPackage(packageId) {
    // 确认删除
    if (!confirm('确定要删除这个积分套餐吗？此操作不可恢复。')) {
      return;
    }
    
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch(`/api/admin/payment/point-packages/${packageId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('删除积分套餐失败');
      }

      const data = await response.json();
      
      if (data.success) {
        // 重新加载数据
        loadPointPackages();
        
        showToast('success', data.message || '积分套餐删除成功');
      } else {
        showToast('error', data.message || '删除积分套餐失败');
      }
    } catch (error) {
      console.error('删除积分套餐失败:', error);
      showToast('error', '删除积分套餐失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 获取token
   * @returns {string} token
   */
  function getToken() {
    return localStorage.getItem('token') || '';
  }

  /**
   * 显示加载中
   */
  function showLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.style.display = 'flex';
    }
  }

  /**
   * 隐藏加载中
   */
  function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
  }

  /**
   * 显示提示信息
   * @param {string} type 提示类型
   * @param {string} message 提示信息
   */
  function showToast(type, message) {
    window.showToast(type, message);
  }

  /**
   * 监听页面可见性变化
   * @param {string} pageId 页面ID
   * @param {Function} callback 回调函数
   */
  function observePageVisibility(pageId, callback) {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const page = document.getElementById(pageId);
          if (page && !page.classList.contains('d-none')) {
            callback();
          }
        }
      });
    });
    
    const page = document.getElementById(pageId);
    if (page) {
      observer.observe(page, { attributes: true });
    }
  }

  // 公开API
  return {
    init,
    loadPointPackages,
    showAddPointPackageModal,
    editPointPackage,
    togglePointPackageStatus,
    deletePointPackage
  };
})();

// 初始化模块
pointPackagesModule.init(); 
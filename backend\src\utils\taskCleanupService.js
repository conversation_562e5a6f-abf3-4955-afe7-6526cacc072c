/**
 * 任务清理服务
 * 定期检查并取消长时间处于"processing"状态的任务
 */
const { GenerationTask } = require('../models');
const { Op } = require('sequelize');
const logger = require('./logger');

// 任务超时时间（毫秒）
const TASK_TIMEOUT_MS = 5 * 60 * 1000; // 5分钟

/**
 * 清理长时间处于"processing"状态的任务
 * @returns {Promise<number>} 清理的任务数量
 */
const cleanupStaleTasks = async () => {
  try {
    logger.info('开始清理长时间处于"processing"状态的任务');

    // 计算超时时间点
    const timeoutThreshold = new Date(Date.now() - TASK_TIMEOUT_MS);

    // 查找所有长时间处于"processing"状态的任务
    const staleTasks = await GenerationTask.findAll({
      where: {
        status: 'processing',
        start_time: {
          [Op.lt]: timeoutThreshold
        }
      }
    });

    logger.info(`找到 ${staleTasks.length} 个长时间处于"processing"状态的任务`);

    // 取消每个超时任务
    let canceledCount = 0;
    for (const task of staleTasks) {
      try {
        // 直接更新任务状态为已取消
        task.status = 'canceled';
        task.end_time = new Date();
        if (task.start_time) {
          task.duration_ms = new Date() - new Date(task.start_time);
        }
        task.error_message = '系统自动取消：任务处理时间过长';

        // 更新任务参数
        const existingParams = JSON.parse(task.parameters || '{}');
        task.parameters = JSON.stringify({
          ...existingParams,
          cancelReason: '系统自动取消：任务处理时间过长',
          cancelTime: new Date().toISOString(),
          cancelSource: 'auto_cleanup'
        });

        await task.save();
        canceledCount++;
        logger.info(`成功取消超时任务: ${task.task_id}`);
      } catch (error) {
        logger.error(`取消任务 ${task.task_id} 失败:`, error);
      }
    }

    logger.info(`成功取消 ${canceledCount} 个超时任务`);
    return canceledCount;
  } catch (error) {
    logger.error('清理超时任务失败:', error);
    return 0;
  }
};

/**
 * 启动定时清理任务
 * @param {number} intervalMs - 清理间隔（毫秒）
 * @returns {Object} 定时器对象
 */
const startCleanupSchedule = (intervalMs = 60000) => { // 默认每分钟检查一次
  logger.info(`启动任务清理服务，间隔: ${intervalMs}ms`);

  // 立即执行一次清理
  cleanupStaleTasks().catch(err => logger.error('初始清理失败:', err));

  // 设置定时器定期执行清理
  const timer = setInterval(() => {
    cleanupStaleTasks().catch(err => logger.error('定时清理失败:', err));
  }, intervalMs);

  return {
    timer,
    stop: () => {
      clearInterval(timer);
      logger.info('任务清理服务已停止');
    }
  };
};

module.exports = {
  cleanupStaleTasks,
  startCleanupSchedule
};

import React, { useState, useEffect, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { 
  Bold, Italic, Underline, Strikethrough, AlignLeft, AlignCenter, AlignRight,
  Link2, Image as ImageIcon, Upload, Square, List, ListOrdered,
  ArrowUp, ArrowDown, RotateCcw, RotateCw, CaseSensitive, Palette, Highlighter,
  Type, Baseline, SquareDashedBottom, Frame, 
  AlignStartVertical, AlignCenterVertical, AlignEndVertical,
  MoveHorizontal, MoveVertical, ArrowUpDown
} from 'lucide-react';
import {
  Button,
  Tooltip,
  Space,
  Modal,
  message,
  Spin,
  Tabs,
  Divider,
  Switch,
  Select,
  ColorPicker
} from 'antd';
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  UndoOutlined,
  RedoOutlined
} from '@ant-design/icons';
import debounce from 'lodash/debounce';
import axios from 'axios';
import { getCurrentBorderStyles } from './utils/styleUtils';

// #region Constants
const fontOptions = [
  { value: "inherit", label: "默认字体" },
  { value: "SimSun, serif", label: "宋体" },
  { value: "SimHei, sans-serif", label: "黑体" },
  { value: "KaiTi, serif", label: "楷体" },
  { value: "FangSong, serif", label: "仿宋" },
  { value: "'Source Han Sans CN', 'Noto Sans SC', sans-serif", label: "思源黑体" },
  { value: "'Noto Serif SC', SimSun, serif", label: "思源宋体" },
  { value: "'Microsoft YaHei', sans-serif", label: "微软雅黑" },
  { value: "'PingFang SC', sans-serif", label: "苹方" },
  { value: "'Alibaba PuHuiTi', sans-serif", label: "阿里普惠体" },
  { value: "FZShuSong-Z01S, SimSun, serif", label: "方正书宋" },
  { value: "Arial, sans-serif", label: "Arial" },
  { value: "'Times New Roman', Times, serif", label: "Times New Roman" },
  { value: "Georgia, serif", label: "Georgia" },
  { value: "Verdana, sans-serif", label: "Verdana" },
  { value: "Helvetica, sans-serif", label: "Helvetica" },
  { value: "Courier New, monospace", label: "Courier New" },
];

const fontSizeOptions = [
  '12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px', '36px', '48px', '60px', '72px', '96px'
].map(size => ({ label: size, value: size }));

const borderStyleOptions = [
  { label: '无线框', value: 'none' },
  { label: '实线', value: 'solid' },
  { label: '虚线', value: 'dashed' },
  { label: '点状', value: 'dotted' },
];

const borderWidthOptions = [
  '1px', '2px', '3px', '4px', '5px', '8px', '10px'
].map(size => ({ label: size, value: size }));

// 新增行高选项
const lineHeightOptions = [
  { label: '默认', value: 'normal' },
  { label: '1倍', value: '1' },
  { label: '1.2倍', value: '1.2' },
  { label: '1.5倍', value: '1.5' },
  { label: '1.8倍', value: '1.8' },
  { label: '2倍', value: '2' },
  { label: '2.5倍', value: '2.5' },
  { label: '3倍', value: '3' }
];

// 新增字间距选项
const letterSpacingOptions = [
  { label: '默认', value: 'normal' },
  { label: '紧凑', value: '-0.05em' },
  { label: '适中', value: '0.05em' },
  { label: '宽松', value: '0.1em' },
  { label: '较宽', value: '0.15em' },
  { label: '很宽', value: '0.2em' }
];

// 新增段落间距选项
const paragraphSpacingOptions = [
  { label: '默认', value: '0px' },
  { label: '5px', value: '5px' },
  { label: '10px', value: '10px' },
  { label: '15px', value: '15px' },
  { label: '20px', value: '20px' },
  { label: '25px', value: '25px' },
  { label: '30px', value: '30px' }
];
// #endregion

// #region Link/Image Upload Modal
const LinkInput = ({ onSetLink, onCancel, type = 'link' }) => {
    const [url, setUrl] = useState('https://');
    const [isLoading, setIsLoading] = useState(false);
    const inputRef = useRef(null);
    const fileInputRef = useRef(null);

    useEffect(() => {
        setTimeout(() => {
        if (inputRef.current) {
            inputRef.current.focus();
            inputRef.current.select();
        }
        }, 100);
    }, []);

    const handleSetLink = () => {
        if (url) onSetLink(url);
        else onCancel();
    };

    const handleUploadClick = () => {
      fileInputRef.current?.click();
    };

    const handleFileChange = async (event) => {
      const file = event.target.files[0];
      if (!file) return;

      if (!file.type.startsWith('image/')) {
        message.error('请选择一个图片文件!');
        return;
      }

      if (file.size > 10 * 1024 * 1024) {
        message.error('文件太大，请上传10MB以下的图片。');
        return;
      }
      
      const formData = new FormData();
      formData.append('image', file);
      setIsLoading(true);

      try {
        const token = localStorage.getItem('token');
        const response = await axios.post('/api/upload/image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.data.success) {
          message.success('上传成功!');
          onSetLink(response.data.url);
        } else {
          message.error(response.data.message || '上传失败，请重试。');
        }
      } catch (error) {
        console.error("上传图片失败:", error);
        const errorMessage = error.response?.data?.message || '上传过程中发生错误，请检查网络或联系管理员。';
        message.error(errorMessage);
      } finally {
        setIsLoading(false);
        if(fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    };

    const renderUrlInput = () => (
      <div className="flex items-center space-x-2 pt-4">
        <Link2 size={16} className="text-gray-500 flex-shrink-0" />
        <input
            ref={inputRef}
            type="text"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleSetLink()}
            className="w-full p-1 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            placeholder="输入链接地址"
        />
        <Tooltip title="插入">
            <button onClick={handleSetLink} className="p-1 text-green-600 hover:bg-green-100 rounded-md">✓</button>
        </Tooltip>
      </div>
    );

    const renderLocalUpload = () => (
      <div className="flex flex-col items-center justify-center py-6">
        <Button type="primary" icon={<Upload />} size="large" onClick={handleUploadClick} className="w-full">
          点击选择图片上传
        </Button>
        <p className="text-xs text-gray-400 mt-2">支持jpg, png, gif等格式，大小不超过10MB</p>
        <input type="file" ref={fileInputRef} onChange={handleFileChange} accept="image/*" className="hidden"/>
      </div>
    );

    return (
      <Spin spinning={isLoading} tip="上传中...">
        {type === 'link' ? renderUrlInput() : (
          <div className="relative -mx-6 -mb-5">
            <Tabs defaultActiveKey="1" centered items={[
                { key: "1", label: "本地上传", children: <div className="px-6">{renderLocalUpload()}</div> },
                { key: "2", label: "网络图片", children: <div className="px-6 pb-4">{renderUrlInput()}</div> }
            ]}/>
          </div>
        )}
      </Spin>
    );
};
// #endregion

const PreviewEditorToolbar = React.memo(({
  iframeRef,
  executeCommandInternal,
  areButtonsEnabled = true,
  onMoveUp,
  onMoveDown,
  activeEditorElement
}) => {  
  // #region State
  const [activeFont, setActiveFont] = useState('inherit');
  const [activeSize, setActiveSize] = useState('16px');
  const [activeColor, setActiveColor] = useState('#000000');
  const [activeBackColor, setActiveBackColor] = useState('#000000');
  const [isBold, setBold] = useState(false);
  const [isItalic, setItalic] = useState(false);
  const [isUnderline, setUnderline] = useState(false);
  const [isStrikethrough, setStrikethrough] = useState(false);
  const [activeAlignment, setActiveAlignment] = useState('left');
  const [isDeleteMode, setIsDeleteMode] = useState(false);
  const [activeBorderStyle, setActiveBorderStyle] = useState('');
  const [activeBorderWidth, setActiveBorderWidth] = useState('0px');
  const [activeBorderColor, setActiveBorderColor] = useState('#000000');
  // 新增状态变量
  const [activeVerticalAlign, setActiveVerticalAlign] = useState('auto');
  const [activeLineHeight, setActiveLineHeight] = useState('normal');
  const [activeLetterSpacing, setActiveLetterSpacing] = useState('normal');
  const [activeParagraphSpacing, setActiveParagraphSpacing] = useState('0px');
  const [modalInfo, setModalInfo] = useState({ visible: false, type: 'link', key: 0 });
  const lastSelectionRef = useRef(null);

  const toolbarButtonClass = "flex items-center justify-center p-2 text-gray-600 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed";
  const activeToolbarButtonClass = "bg-blue-100 text-blue-600";
  // #endregion

  // #region State Synchronization
  const updateToolbarState = useCallback(() => {
    const iframe = iframeRef.current;
    if (!iframe?.contentWindow?.document) return;
    
    // 优先使用activeEditorElement属性
    let parentElement = null;
    
    try {
      if (activeEditorElement && activeEditorElement.isConnected) {
        // 确保元素仍然连接到DOM，这是安全访问样式的前提
        // 有时元素可能已被移除但引用仍然存在
        const doc = iframe.contentWindow.document;
        const stillConnected = doc.body.contains(activeEditorElement);
        
        if (stillConnected) {
          parentElement = activeEditorElement;
          // console.log('使用activeEditorElement更新工具栏状态:', parentElement.tagName, parentElement.className);
        } else {
          // console.log('activeEditorElement已不在文档中，将使用选区');
        }
      } else {
        // console.log('无有效的activeEditorElement，将使用选区');
      }
      
      // 如果没有有效的activeEditorElement，尝试使用当前选区
      if (!parentElement) {
    const doc = iframe.contentWindow.document;
    const selection = doc.getSelection();

    if (!selection || selection.rangeCount === 0) return;

    lastSelectionRef.current = selection.getRangeAt(0).cloneRange();

        parentElement = selection.getRangeAt(0).commonAncestorContainer;
    if (parentElement.nodeType === Node.TEXT_NODE) {
      parentElement = parentElement.parentNode;
        }
        
        // console.log('使用选区更新工具栏状态:', parentElement.tagName, parentElement.className);
    }
        
    if (parentElement && parentElement instanceof HTMLElement && parentElement.isConnected) {
        try {
          // console.log('开始计算元素样式...');
      const style = iframe.contentWindow.getComputedStyle(parentElement);
          // console.log('获取到样式对象:', style.fontFamily, style.fontSize, style.color);
      
      const rgbToHex = (rgb) => {
        if (!rgb || rgb.startsWith('rgba(0, 0, 0, 0)') || rgb === 'transparent') return null;
        try {
          const rgbMatch = rgb.match(/\d+/g);
          if (!rgbMatch || rgbMatch.length < 3) return '#000000';
          return '#' + rgbMatch.slice(0, 3).map(x => parseInt(x, 10).toString(16).padStart(2, '0')).join('');
        } catch {
          return '#000000';
        }
      };
      
      const normalizeFontName = (fontName) => fontName.split(',')[0].replace(/['"]/g, '').trim();
      const currentFont = fontOptions.find(f => normalizeFontName(f.value).toLowerCase() === normalizeFontName(style.fontFamily).toLowerCase());
      
          // console.log('计算字体:', style.fontFamily, '匹配结果:', currentFont?.value || 'inherit');
      setActiveFont(currentFont ? currentFont.value : 'inherit');
          
          // console.log('设置字号:', style.fontSize || '16px');
      setActiveSize(style.fontSize || '16px');
          
          const colorHex = rgbToHex(style.color) || '#000000';
          // console.log('设置文本颜色:', style.color, '转换为:', colorHex);
          setActiveColor(colorHex);
      
      const backColor = rgbToHex(style.backgroundColor);
          const finalBackColor = backColor === '#ffffff' ? '#000000' : (backColor || '#000000');
          // console.log('设置背景颜色:', style.backgroundColor, '转换为:', finalBackColor);
          setActiveBackColor(finalBackColor);

          const isBoldText = style.fontWeight === 'bold' || parseInt(style.fontWeight, 10) >= 700;
          // console.log('设置加粗状态:', style.fontWeight, '是否加粗:', isBoldText);
          setBold(isBoldText);
          
          // console.log('设置斜体状态:', style.fontStyle, '是否斜体:', style.fontStyle === 'italic');
      setItalic(style.fontStyle === 'italic');
      
      let node = parentElement;
      let underline = false;
      let strikethrough = false;
      while (node && node.tagName !== 'BODY') {
        const nodeStyle = iframe.contentWindow.getComputedStyle(node);
        if (nodeStyle.textDecorationLine.includes('underline')) underline = true;
        if (nodeStyle.textDecorationLine.includes('line-through')) strikethrough = true;
        if(node.tagName === 'U') underline = true;
        if(node.tagName === 'S' || node.tagName === 'STRIKE') strikethrough = true;
        node = node.parentNode;
      }
          // console.log('设置下划线状态:', underline);
      setUnderline(underline);
          // console.log('设置删除线状态:', strikethrough);
      setStrikethrough(strikethrough);

      const textAlign = style.textAlign;
          let finalAlignment = 'left';
          if (['left', 'center', 'right', 'justify'].includes(textAlign)) finalAlignment = textAlign;
          else if (textAlign === 'start') finalAlignment = 'left';
          else if (textAlign === 'end') finalAlignment = 'right';
          // console.log('设置文本对齐:', style.textAlign, '转换为:', finalAlignment);
          setActiveAlignment(finalAlignment);

      const borderStyles = getCurrentBorderStyles(parentElement);
      if (borderStyles) {
            // console.log('设置边框样式:', borderStyles.style, borderStyles.width, borderStyles.color);
        setActiveBorderStyle(borderStyles.style || '');
        setActiveBorderWidth(borderStyles.width || '0px');
        setActiveBorderColor(rgbToHex(borderStyles.color) || '#000000');
      }

          // 获取垂直对齐信息 - 修改为检测 Flexbox 布局
          let verticalAlign = 'auto';
          if (style.display === 'flex' && style.flexDirection === 'column') {
            // 基于 justify-content 属性检测垂直对齐状态
            if (style.justifyContent === 'flex-start') verticalAlign = 'top';
            else if (style.justifyContent === 'center') verticalAlign = 'middle';
            else if (style.justifyContent === 'flex-end') verticalAlign = 'bottom';
          } else if (style.position === 'absolute') {
            // 保留旧的检测逻辑作为备用，以便兼容旧的实现方式
            if (style.top === '0px' && style.bottom === 'auto') verticalAlign = 'top';
            else if (style.top === '50%' && style.bottom === 'auto') verticalAlign = 'middle';
            else if (style.top === 'auto' && style.bottom === '0px') verticalAlign = 'bottom';
          }
          // console.log('设置垂直对齐:', verticalAlign);
          setActiveVerticalAlign(verticalAlign);

          // 获取行高
          // console.log('设置行高:', style.lineHeight || 'normal');
          setActiveLineHeight(style.lineHeight || 'normal');
          
          // 获取字间距
          // console.log('设置字间距:', style.letterSpacing || 'normal');
          setActiveLetterSpacing(style.letterSpacing || 'normal');
          
          // 获取段落间距
          const paragraphSpacing = style.marginBottom || '0px';
          // console.log('设置段落间距:', paragraphSpacing);
          setActiveParagraphSpacing(paragraphSpacing);
          
          // 移除调试日志
          // console.log('工具栏状态更新完成');
        } catch (error) {
          console.error('获取元素样式时出错:', error);
        }
      } else {
        // 修改警告信息，避免频繁打印警告
        // console.warn('没有找到有效的元素来更新工具栏状态');
      }
    } catch (error) {
      console.error('更新工具栏状态时出错:', error);
    }
  }, [iframeRef, activeEditorElement, fontOptions]); // 添加fontOptions作为依赖

  // 添加对iframe加载完成的监听
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe) return;

    const handleIframeLoad = () => {
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDoc) return;
      
      // 直接在iframe内部添加selectionchange事件监听
      const handleSelectionChangeInIframe = () => {
        updateToolbarState();
      };
      
      iframeDoc.addEventListener('selectionchange', handleSelectionChangeInIframe);
      
      return () => {
        iframeDoc.removeEventListener('selectionchange', handleSelectionChangeInIframe);
      };
    };
    
    if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
      handleIframeLoad();
    } else {
      iframe.addEventListener('load', handleIframeLoad);
    }
    
    return () => {
      iframe.removeEventListener('load', handleIframeLoad);
    };
  }, [iframeRef, updateToolbarState]);

  useEffect(() => {
    const iframeDoc = iframeRef.current?.contentDocument;
    if (iframeDoc) {
      const debouncedUpdate = debounce(updateToolbarState, 150);
      iframeDoc.addEventListener('selectionchange', debouncedUpdate);
      iframeDoc.addEventListener('click', updateToolbarState);
      iframeDoc.addEventListener('keyup', debouncedUpdate);
      
      updateToolbarState();

      return () => {
        debouncedUpdate.cancel();
        iframeDoc.removeEventListener('selectionchange', debouncedUpdate);
        iframeDoc.removeEventListener('click', updateToolbarState);
        iframeDoc.removeEventListener('keyup', debouncedUpdate);
      };
    }
  }, [iframeRef, updateToolbarState]);

  // 添加对activeEditorElement变化的监听
  useEffect(() => {
    if (activeEditorElement) {
      // console.log('activeEditorElement变化，更新工具栏状态');
      updateToolbarState();
    }
  }, [activeEditorElement, updateToolbarState]);
  
  // 添加对编辑器失焦事件的监听
  useEffect(() => {
    // 重置工具栏状态到默认值的函数
    const resetToolbarState = () => {
      // console.log('重置工具栏状态到默认值');
      setActiveFont('inherit');
      setActiveSize('16px');
      setActiveColor('#000000');
      setActiveBackColor('#000000');
      setBold(false);
      setItalic(false);
      setUnderline(false);
      setStrikethrough(false);
      setActiveAlignment('left');
      setActiveBorderStyle('');
      setActiveBorderWidth('0px');
      setActiveBorderColor('#000000');
      setActiveVerticalAlign('auto');
      setActiveLineHeight('normal');
      setActiveLetterSpacing('normal');
      setActiveParagraphSpacing('0px');
    };
    
    const iframeDoc = iframeRef.current?.contentDocument || iframeRef.current?.contentWindow?.document;
    if (iframeDoc) {
      // 监听自定义的editor-element-blur事件
      const handleEditorBlur = (event) => {
        const target = event.detail?.target;
        // console.log('接收到编辑器元素失焦事件:', target?.tagName || '未知元素');
        
        // 如果没有新的activeEditorElement，则重置工具栏状态
        if (!activeEditorElement || (target && target === activeEditorElement)) {
          resetToolbarState();
        } else if (target && activeEditorElement && target !== activeEditorElement) {
          // 如果有新的activeEditorElement，则更新工具栏状态
          // console.log('切换到新的编辑元素，更新工具栏状态');
          updateToolbarState();
        }
      };
      
      iframeDoc.addEventListener('editor-element-blur', handleEditorBlur);
      
      return () => {
        iframeDoc.removeEventListener('editor-element-blur', handleEditorBlur);
      };
    }
  }, [iframeRef, activeEditorElement, updateToolbarState]);
  // #endregion

  // #region Event Handlers
  const handleMouseDown = (e) => e.preventDefault();
  
  const handleCommand = (command, value = null) => {
    if (!areButtonsEnabled && command !== 'insertImage') return;
    
    // console.log('执行命令:', command, value);
    
    const iframe = iframeRef.current;
    if (iframe) {
        iframe.contentWindow.focus();
        if (lastSelectionRef.current) {
            // console.log('恢复上次选区');
            const selection = iframe.contentWindow.getSelection();
            selection.removeAllRanges();
            selection.addRange(lastSelectionRef.current);
        }
    }
    
    // 执行命令
    executeCommandInternal(command, value);
    
    // 根据命令类型更新对应的状态
    switch (command) {
      case 'bold':
        // console.log('更新加粗状态:', !isBold);
        setBold(!isBold);
        break;
      case 'italic':
        // console.log('更新斜体状态:', !isItalic);
        setItalic(!isItalic);
        break;
      case 'underline':
        // console.log('更新下划线状态:', !isUnderline);
        setUnderline(!isUnderline);
        break;
      case 'strikeThrough':
        // console.log('更新删除线状态:', !isStrikethrough);
        setStrikethrough(!isStrikethrough);
        break;
      case 'justifyLeft':
        // console.log('更新对齐状态: left');
        setActiveAlignment('left');
        break;
      case 'justifyCenter':
        // console.log('更新对齐状态: center');
        setActiveAlignment('center');
        break;
      case 'justifyRight':
        // console.log('更新对齐状态: right');
        setActiveAlignment('right');
        break;
      case 'fontName':
        // console.log('更新字体:', value);
        setActiveFont(value);
        break;
      case 'fontSize':
        // console.log('更新字号:', value);
        setActiveSize(value);
        break;
      case 'foreColor':
        // console.log('更新文本颜色:', value);
        setActiveColor(value);
        break;
      case 'backColor':
        // console.log('更新背景颜色:', value);
        setActiveBackColor(value);
        break;
      case 'verticalAlign-top':
        // console.log('更新垂直对齐: top');
        setActiveVerticalAlign('top');
        break;
      case 'verticalAlign-middle':
        // console.log('更新垂直对齐: middle');
        setActiveVerticalAlign('middle');
        break;
      case 'verticalAlign-bottom':
        // console.log('更新垂直对齐: bottom');
        setActiveVerticalAlign('bottom');
        break;
      case 'lineHeight':
        // console.log('更新行高:', value);
        setActiveLineHeight(value);
        break;
      case 'letterSpacing':
        // console.log('更新字间距:', value);
        setActiveLetterSpacing(value);
        break;
      case 'paragraphSpacing':
        // console.log('更新段落间距:', value);
        setActiveParagraphSpacing(value);
        break;
      case 'border-style':
        // console.log('更新边框样式:', value);
        setActiveBorderStyle(value);
        break;
      case 'border-width':
        // console.log('更新边框宽度:', value);
        setActiveBorderWidth(value);
        break;
      case 'border-color':
        // console.log('更新边框颜色:', value);
        setActiveBorderColor(value);
        break;
      default:
        // 对于其他命令，延迟更新工具栏状态
        // console.log('其他命令，延迟更新工具栏状态');
        setTimeout(updateToolbarState, 100);
        break;
    }
  };
  
  const handleLinkButtonClick = (e) => {
    handleMouseDown(e);
    setModalInfo({ visible: true, type: 'link', key: Date.now() });
  };
  
  const handleImageButtonClick = (e) => {
    handleMouseDown(e);
    setModalInfo({ visible: true, type: 'image', key: Date.now() });
  };

  const handleModalCancel = () => setModalInfo({ ...modalInfo, visible: false });

  const setLink = (url) => {
    handleCommand('createLink', url);
    setModalInfo({ ...modalInfo, visible: false });
  };

  const setImage = (url) => {
    handleCommand('insertImage', url);
    setModalInfo({ ...modalInfo, visible: false });
  };

  const handleSwitchChange = (checked) => {
    setIsDeleteMode(checked);
    if (iframeRef.current) {
      const iframeDoc = iframeRef.current.contentDocument;
      if (iframeDoc?.defaultView?.textEditor?.setDeleteMode) {
        iframeDoc.defaultView.textEditor.setDeleteMode(checked);
      }
    }
  };
  // #endregion

  // #region Render Methods
  const renderIconButton = (tooltip, icon, command, isActive) => (
    <Tooltip title={tooltip} mouseEnterDelay={0.5}>
      <button onMouseDown={handleMouseDown} onClick={() => handleCommand(command)} className={`${toolbarButtonClass} ${isActive ? activeToolbarButtonClass : ''}`} disabled={!areButtonsEnabled}>
        {icon}
      </button>
    </Tooltip>
  );

  return (
    <div className="bg-white p-2 rounded-lg shadow-lg border border-gray-200 flex flex-col items-stretch gap-2" style={{ zIndex: 1000 }} onMouseDown={handleMouseDown}>

      <div className="flex flex-wrap items-center gap-x-2 gap-y-2">
        <div className="flex items-center gap-x-1">
          <Tooltip title="字体"><Type size={16} className="text-gray-500"/></Tooltip>
            <Select onChange={value => handleCommand('fontName', value)} value={activeFont} disabled={!areButtonsEnabled} options={fontOptions} style={{width: 120}} size="small" />
        </div>
        <div className="flex items-center gap-x-1">
          <Tooltip title="字号"><Baseline size={16} className="text-gray-500"/></Tooltip>
             <Select onChange={value => handleCommand('fontSize', value)} value={activeSize} disabled={!areButtonsEnabled} options={fontSizeOptions} style={{width: 80}} size="small" />
        </div>
        <Space>
          <div className="flex items-center gap-x-1">
            <Tooltip title="文本颜色"><CaseSensitive size={16} className="text-gray-500" /></Tooltip>
            <ColorPicker value={activeColor} onChangeComplete={(color) => handleCommand('foreColor', color.toHexString())} disabled={!areButtonsEnabled}>
                    <Button style={{backgroundColor: activeColor}} className="w-6 h-6 p-0 border border-gray-300"> </Button>
            </ColorPicker>
          </div>
          <div className="flex items-center gap-x-1">
            <Tooltip title="背景颜色"><Highlighter size={16} className="text-gray-500"/></Tooltip>
            <ColorPicker value={activeBackColor} onChangeComplete={(color) => handleCommand('backColor', color.toHexString())} disabled={!areButtonsEnabled}>
                    <Button style={{backgroundColor: activeBackColor}} className="w-6 h-6 p-0 border border-gray-300"> </Button>
            </ColorPicker>
          </div>
        </Space>
      </div>
      <Divider className="my-1" />

      {/* 新增文本间距控制区域 */}
      <div className="flex flex-wrap items-center gap-x-2 gap-y-2">
        <div className="flex items-center gap-x-1">
          <Tooltip title="行高"><ArrowUpDown size={16} className="text-gray-500"/></Tooltip>
          <Select 
            onChange={value => handleCommand('lineHeight', value)} 
            value={activeLineHeight} 
            disabled={!areButtonsEnabled} 
            options={lineHeightOptions} 
            style={{width: 80}} 
            size="small" 
          />
        </div>
        <div className="flex items-center gap-x-1">
          <Tooltip title="字间距"><MoveHorizontal size={16} className="text-gray-500"/></Tooltip>
          <Select 
            onChange={value => handleCommand('letterSpacing', value)} 
            value={activeLetterSpacing} 
            disabled={!areButtonsEnabled} 
            options={letterSpacingOptions} 
            style={{width: 80}} 
            size="small" 
          />
        </div>
        <div className="flex items-center gap-x-1">
          <Tooltip title="段落间距"><MoveVertical size={16} className="text-gray-500"/></Tooltip>
          <Select 
            onChange={value => handleCommand('paragraphSpacing', value)} 
            value={activeParagraphSpacing} 
            disabled={!areButtonsEnabled} 
            options={paragraphSpacingOptions} 
            style={{width: 80}} 
            size="small" 
          />
        </div>
      </div>
      <Divider className="my-1" />

      <div className="flex flex-wrap items-center gap-x-2 gap-y-2">
        <div className="flex items-center gap-x-1">
          <Tooltip title="边框样式"><SquareDashedBottom size={16} className="text-gray-500"/></Tooltip>
          <Select 
            onChange={value => handleCommand('border-style', value === undefined ? 'none' : value)} 
            value={activeBorderStyle} 
            allowClear 
            placeholder="样式" 
            disabled={!areButtonsEnabled} 
            options={borderStyleOptions} 
            style={{width: 90}} 
            size="small" 
          />
        </div>
        <div className="flex items-center gap-x-1">
          <Tooltip title="边框宽度"><Frame size={16} className="text-gray-500"/></Tooltip>
            <Select onChange={value => handleCommand('border-width', value)} value={activeBorderWidth} disabled={!areButtonsEnabled} options={borderWidthOptions} style={{width: 80}} size="small" placeholder="宽度"/>
        </div>
        <div className="flex items-center gap-x-1">
          <Tooltip title="边框颜色"><Palette size={16} className="text-gray-500"/></Tooltip>
        <ColorPicker value={activeBorderColor} onChangeComplete={(color) => handleCommand('border-color', color.toHexString())} disabled={!areButtonsEnabled}>
                <Button style={{backgroundColor: activeBorderColor}} className="w-6 h-6 p-0 border border-gray-300"> </Button>
        </ColorPicker>
        </div>
      </div>
      <Divider className="my-1" />

      <div className="flex flex-wrap items-center gap-x-1 gap-y-1">
        {renderIconButton('加粗', <Bold size={18} />, 'bold', isBold)}
        {renderIconButton('斜体', <Italic size={18} />, 'italic', isItalic)}
        {renderIconButton('下划线', <Underline size={18} />, 'underline', isUnderline)}
        {renderIconButton('删除线', <Strikethrough size={18} />, 'strikeThrough', isStrikethrough)}
        <div className="h-5 w-px bg-gray-300 mx-1"></div>
        <div className="flex items-center">
        {renderIconButton('左对齐', <AlignLeft size={18} />, 'justifyLeft', activeAlignment === 'left')}
        {renderIconButton('居中对齐', <AlignCenter size={18} />, 'justifyCenter', activeAlignment === 'center')}
        {renderIconButton('右对齐', <AlignRight size={18} />, 'justifyRight', activeAlignment === 'right')}
        {/* 新增垂直位置控制按钮 */}
        <div className="h-5 w-px bg-gray-300 mx-1"></div>
        {renderIconButton('置顶', <AlignStartVertical size={18} />, 'verticalAlign-top', activeVerticalAlign === 'top')}
        {renderIconButton('垂直居中', <AlignCenterVertical size={18} />, 'verticalAlign-middle', activeVerticalAlign === 'middle')}
        {renderIconButton('置底', <AlignEndVertical size={18} />, 'verticalAlign-bottom', activeVerticalAlign === 'bottom')}
        </div>
        <div className="h-5 w-px bg-gray-300 mx-1"></div>
        {renderIconButton('无序列表', <List size={18} />, 'insertUnorderedList', false)}
        {renderIconButton('有序列表', <ListOrdered size={18} />, 'insertOrderedList', false)}
      </div>
      <Divider className="my-1" />
      
      <div className="flex flex-wrap items-center gap-x-1 gap-y-1">
        <Tooltip title="插入链接"><button onClick={handleLinkButtonClick} className={toolbarButtonClass} disabled={!areButtonsEnabled}><Link2 size={18} /></button></Tooltip>
        <Tooltip title="插入图片"><button onClick={handleImageButtonClick} className={toolbarButtonClass} disabled={!areButtonsEnabled}><ImageIcon size={18} /></button></Tooltip>
        <Tooltip title="插入文本框"><button onClick={() => handleCommand('insertTextBox')} className={toolbarButtonClass} disabled={!areButtonsEnabled}><Square size={18} /></button></Tooltip>
        <div className="h-5 w-px bg-gray-300 mx-1"></div>
        <Tooltip title="上移一层" mouseEnterDelay={0.5}><button onClick={onMoveUp} disabled={!areButtonsEnabled} className={toolbarButtonClass}><ArrowUp size={18} /></button></Tooltip>
        <Tooltip title="下移一层" mouseEnterDelay={0.5}><button onClick={onMoveDown} disabled={!areButtonsEnabled} className={toolbarButtonClass}><ArrowDown size={18} /></button></Tooltip>
      </div>
      <Divider className="my-1" />

      <div className="flex items-center justify-between">
        <Space>
          <Tooltip title="上一步" mouseEnterDelay={0.5}>
            <Button onClick={() => handleCommand('undo')} disabled={!areButtonsEnabled} icon={<RotateCcw size={14} />}>上一步</Button>
          </Tooltip>
          <Tooltip title="下一步" mouseEnterDelay={0.5}>
            <Button onClick={() => handleCommand('redo')} disabled={!areButtonsEnabled} icon={<RotateCw size={14} />}>下一步</Button>
          </Tooltip>
        </Space>
        <div className="flex flex-col items-center">
            <Switch checked={isDeleteMode} onChange={handleSwitchChange} size="small" />
          <label className="text-xs select-none mt-1">删除模式</label>
        </div>
      </div>

      <Modal title={modalInfo.type === 'link' ? "插入或编辑链接" : "插入图片"} open={modalInfo.visible} onCancel={handleModalCancel} key={modalInfo.key} footer={null} destroyOnHidden centered>
        <LinkInput onSetLink={modalInfo.type === 'link' ? setLink : setImage} onCancel={handleModalCancel} type={modalInfo.type} />
      </Modal>
    </div>
  );
});
// #endregion

PreviewEditorToolbar.propTypes = {
  iframeRef: PropTypes.object.isRequired,
  executeCommandInternal: PropTypes.func.isRequired,
  areButtonsEnabled: PropTypes.bool,
  onMoveUp: PropTypes.func,
  onMoveDown: PropTypes.func,
  activeEditorElement: PropTypes.object,
};

export default PreviewEditorToolbar;

/**
 * 系统设置模块
 */
window.settingsModule = (function() {
  function init() {
    // 绑定事件
    bindEvents();

    // 加载设置数据
    fetchSettings();

    // 系统信息
    fetchSystemInfo();

    // 备份配置
    setupBackup();
  }

  function bindEvents() {
    // 积分设置保存
    const pointsSettingsForm = document.getElementById('pointsSettingsForm');
    if (pointsSettingsForm) {
      pointsSettingsForm.addEventListener('submit', function(e) {
        e.preventDefault();
        saveSystemSettings();
      });
    }

    // HTML处理配置保存
    const htmlProcessingForm = document.getElementById('htmlProcessingForm');
    if (htmlProcessingForm) {
      htmlProcessingForm.addEventListener('submit', function(e) {
        e.preventDefault();
        saveHtmlProcessingConfig();
      });
    }

    // 网站信息保存
    const siteSettingsForm = document.getElementById('siteSettingsForm');
    if (siteSettingsForm) {
      siteSettingsForm.addEventListener('submit', function(e) {
        e.preventDefault();
        saveSiteSettings();
      });
    }

    // 隐私政策保存
    const privacyPolicyForm = document.getElementById('privacyPolicyForm');
    if (privacyPolicyForm) {
      privacyPolicyForm.addEventListener('submit', function(e) {
        e.preventDefault();
        savePrivacyPolicy();
      });
    }

    // 用户协议保存
    const userAgreementForm = document.getElementById('userAgreementForm');
    if (userAgreementForm) {
      userAgreementForm.addEventListener('submit', function(e) {
        e.preventDefault();
        saveUserAgreement();
      });
    }

    // 备份数据库
    const backupDatabaseBtn = document.getElementById('backupDatabaseBtn');
    if (backupDatabaseBtn) {
      backupDatabaseBtn.addEventListener('click', backupDatabase);
    }

    // 恢复数据库
    const restoreDatabaseBtn = document.getElementById('restoreDatabaseBtn');
    if (restoreDatabaseBtn) {
      restoreDatabaseBtn.addEventListener('click', restoreDatabase);
    }
  }

  function fetchSettings() {
    const token = localStorage.getItem('token');
    if (!token) return;

    // 加载HTML处理配置
    fetchHtmlProcessingConfig();

    // 显示加载状态
    const settingsForm = document.getElementById('pointsSettingsForm');
    if (settingsForm) {
      // 保存原始HTML，以便在加载失败时恢复
      const originalHTML = settingsForm.innerHTML;
      // 显示加载状态
      settingsForm.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></div>';

      // 将原始HTML保存到表单的data属性中
      settingsForm.dataset.originalHtml = originalHTML;
    }

    // 获取系统设置
    fetch('/api/admin/settings', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP错误，状态: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (!data.success) {
        throw new Error(data.message || '获取设置失败');
      }

      // 成功获取设置数据

      // 恢复表单
      if (settingsForm) {
        // 恢复原始HTML
        if (settingsForm.dataset.originalHtml) {
          settingsForm.innerHTML = settingsForm.dataset.originalHtml;
        } else {
          // 如果没有保存原始HTML，则使用默认HTML
          settingsForm.innerHTML = `
            <div class="mb-3">
              <label for="newUserPoints" class="form-label">新用户初始积分</label>
              <input type="number" class="form-control" id="newUserPoints" min="0" step="1">
              <div class="form-text">新用户注册后自动获得的积分数量</div>
            </div>
            <div class="mb-3">
              <label for="coverPointsCost" class="form-label">生成封面所需积分</label>
              <input type="number" class="form-control" id="coverPointsCost" min="0" step="1">
              <div class="form-text">用户每次生成封面消耗的积分数量</div>
            </div>
            <div class="mb-3">
              <label for="verifyCodeStorageType" class="form-label">验证码存储方式</label>
              <select class="form-select" id="verifyCodeStorageType">
                <option value="memory">内存存储（本地测试环境）</option>
                <option value="database">数据库存储（云服务器部署）</option>
              </select>
              <div class="form-text">内存存储适用于本地测试环境，数据库存储适用于云服务器部署</div>
            </div>
            <button type="submit" class="btn btn-primary" id="saveSettingsBtn">保存系统设置</button>
          `;
        }
      }

      // 填充积分设置
      document.getElementById('newUserPoints').value = data.data.new_user_points || 50;
      document.getElementById('coverPointsCost').value = data.data.cover_points_cost || 5;

      // 填充验证码存储方式
      const verifyCodeStorageType = document.getElementById('verifyCodeStorageType');
      if (verifyCodeStorageType) {
        verifyCodeStorageType.value = data.data.verify_code_storage_type || 'memory';
      }

      // 填充网站信息
      document.getElementById('siteName').value = data.data.site_name || '';
      document.getElementById('siteDescription').value = data.data.site_description || '';
      document.getElementById('contactEmail').value = data.data.contact_email || '';
      document.getElementById('icp').value = data.data.icp || '';

      // 获取隐私政策和用户协议内容
      fetchPolicies();
    })
    .catch(error => {
      // 获取设置失败
      if (settingsForm) {
        // 恢复原始HTML
        if (settingsForm.dataset.originalHtml) {
          settingsForm.innerHTML = settingsForm.dataset.originalHtml;
          // 显示错误消息
          const errorDiv = document.createElement('div');
          errorDiv.className = 'alert alert-danger';
          errorDiv.textContent = `获取设置失败: ${error.message}`;
          settingsForm.prepend(errorDiv);
        } else {
          settingsForm.innerHTML = `<div class="alert alert-danger">获取设置失败: ${error.message}</div>`;
        }
      }
    });
  }

  function fetchPolicies() {
    const token = localStorage.getItem('token');
    if (!token) return;

    // 获取隐私政策
    fetch('/api/system/policies', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP错误，状态: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (!data.success) {
        throw new Error(data.message || '获取政策内容失败');
      }

      // 成功获取政策数据

      // 填充隐私政策
      const privacyPolicyContent = document.getElementById('privacyPolicyContent');
      if (privacyPolicyContent) {
        privacyPolicyContent.value = data.data.privacy_policy || '';
      }

      const privacyPolicyVersion = document.getElementById('privacyPolicyVersion');
      if (privacyPolicyVersion) {
        privacyPolicyVersion.value = data.data.privacy_policy_version || '1.0';
      }

      // 填充用户协议
      const userAgreementContent = document.getElementById('userAgreementContent');
      if (userAgreementContent) {
        userAgreementContent.value = data.data.user_agreement || '';
      }

      const userAgreementVersion = document.getElementById('userAgreementVersion');
      if (userAgreementVersion) {
        userAgreementVersion.value = data.data.user_agreement_version || '1.0';
      }
    })
    .catch(error => {
      // 获取政策内容失败
      // 显示错误消息
      alert(`获取政策内容失败: ${error.message}`);
    });
  }

  // 系统信息功能预留
  function fetchSystemInfo() {
    // 此功能暂未实现，预留接口
    // 后端未提供 /api/admin/system-info 接口
  }

  function setupBackup() {
    // 设置备份相关功能
  }

  function saveSystemSettings() {
    const token = localStorage.getItem('token');
    if (!token) return;

    const newUserPoints = document.getElementById('newUserPoints').value;
    const coverPointsCost = document.getElementById('coverPointsCost').value;
    const verifyCodeStorageType = document.getElementById('verifyCodeStorageType').value;

    const settingsData = {
      new_user_points: parseInt(newUserPoints),
      cover_points_cost: parseInt(coverPointsCost),
      verify_code_storage_type: verifyCodeStorageType
    };

    // 显示加载状态
    const saveBtn = document.getElementById('saveSettingsBtn');
    if (saveBtn) {
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
    }

    // 发送请求
    fetch('/api/admin/settings/system', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(settingsData)
    })
    .then(response => response.json())
    .then(data => {
      // 恢复按钮状态
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存系统设置';
      }

      if (data.success) {
        alert('保存系统设置成功');
      } else {
        alert('保存系统设置失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      // 恢复按钮状态
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存系统设置';
      }

      // 保存系统设置失败
      alert('保存系统设置失败，请稍后再试');
    });
  }

  function saveSiteSettings() {
    const token = localStorage.getItem('token');
    if (!token) return;

    const siteName = document.getElementById('siteName').value;
    const siteDescription = document.getElementById('siteDescription').value;
    const contactEmail = document.getElementById('contactEmail').value;
    const icp = document.getElementById('icp').value;

    const settingsData = {
      site_name: siteName,
      site_description: siteDescription,
      contact_email: contactEmail,
      icp: icp
    };

    // 显示加载状态
    const saveBtn = document.getElementById('siteSettingsSaveBtn');
    if (saveBtn) {
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
    }

    // 发送请求
    fetch('/api/admin/settings/site', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(settingsData)
    })
    .then(response => response.json())
    .then(data => {
      // 恢复按钮状态
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存网站信息';
      }

      if (data.success) {
        alert('保存网站信息成功');
      } else {
        alert('保存网站信息失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      // 恢复按钮状态
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存网站信息';
      }

      // 保存网站信息失败
      alert('保存网站信息失败，请稍后再试');
    });
  }

  // 备份数据库
  function backupDatabase() {
    const token = localStorage.getItem('token');
    if (!token) return;

    // 显示加载状态
    const backupBtn = document.getElementById('backupDatabaseBtn');
    if (backupBtn) {
      backupBtn.disabled = true;
      backupBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 备份中...';
    }

    // 发送备份请求
    fetch('/api/admin/backup/database', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('备份请求失败');
      }
      return response.blob();
    })
    .then(blob => {
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      const date = new Date().toISOString().replace(/[:.]/g, '-');
      a.href = url;
      a.download = `database-backup-${date}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // 恢复按钮状态
      if (backupBtn) {
        backupBtn.disabled = false;
        backupBtn.innerHTML = '<i class="bi bi-download"></i> 备份数据库';
      }

      alert('数据库备份成功');
    })
    .catch(error => {
      // 数据库备份失败

      // 恢复按钮状态
      if (backupBtn) {
        backupBtn.disabled = false;
        backupBtn.innerHTML = '<i class="bi bi-download"></i> 备份数据库';
      }

      alert('数据库备份失败，请稍后再试');
    });
  }

  // 恢复数据库
  function restoreDatabase() {
    const token = localStorage.getItem('token');
    if (!token) return;

    const fileInput = document.getElementById('restoreFileInput');
    if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
      alert('请先选择备份文件');
      return;
    }

    // 确认是否恢复
    if (!confirm('确定要从备份文件恢复数据库吗？此操作可能会覆盖现有数据，无法撤销！')) {
      return;
    }

    const file = fileInput.files[0];
    const formData = new FormData();
    formData.append('backup', file);

    // 显示加载状态
    const restoreBtn = document.getElementById('restoreDatabaseBtn');
    if (restoreBtn) {
      restoreBtn.disabled = true;
      restoreBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 恢复中...';
    }

    // 发送恢复请求
    fetch('/api/admin/restore/database', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      // 恢复按钮状态
      if (restoreBtn) {
        restoreBtn.disabled = false;
        restoreBtn.innerHTML = '<i class="bi bi-upload"></i> 恢复';
      }

      if (data.success) {
        alert('数据库恢复成功，将在3秒后刷新页面');
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        alert('数据库恢复失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      // 数据库恢复失败

      // 恢复按钮状态
      if (restoreBtn) {
        restoreBtn.disabled = false;
        restoreBtn.innerHTML = '<i class="bi bi-upload"></i> 恢复';
      }

      alert('数据库恢复失败，请稍后再试');
    });
  }

  // 保存隐私政策
  function savePrivacyPolicy() {
    const token = localStorage.getItem('token');
    if (!token) return;

    const content = document.getElementById('privacyPolicyContent').value;
    const version = document.getElementById('privacyPolicyVersion').value;

    if (!content) {
      alert('请输入隐私政策内容');
      return;
    }

    if (!version) {
      alert('请输入版本号');
      return;
    }

    const policyData = {
      content,
      version
    };

    // 显示加载状态
    const saveBtn = document.getElementById('privacyPolicySaveBtn');
    if (saveBtn) {
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
    }

    // 发送请求
    fetch('/api/admin/policies/privacy', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(policyData)
    })
    .then(response => response.json())
    .then(data => {
      // 恢复按钮状态
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存隐私政策';
      }

      if (data.success) {
        alert('保存隐私政策成功');
      } else {
        alert('保存隐私政策失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      // 恢复按钮状态
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存隐私政策';
      }

      // 保存隐私政策失败
      alert('保存隐私政策失败，请稍后再试');
    });
  }

  // 保存用户协议
  function saveUserAgreement() {
    const token = localStorage.getItem('token');
    if (!token) return;

    const content = document.getElementById('userAgreementContent').value;
    const version = document.getElementById('userAgreementVersion').value;

    if (!content) {
      alert('请输入用户协议内容');
      return;
    }

    if (!version) {
      alert('请输入版本号');
      return;
    }

    const agreementData = {
      content,
      version
    };

    // 显示加载状态
    const saveBtn = document.getElementById('userAgreementSaveBtn');
    if (saveBtn) {
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
    }

    // 发送请求
    fetch('/api/admin/policies/agreement', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(agreementData)
    })
    .then(response => response.json())
    .then(data => {
      // 恢复按钮状态
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存用户协议';
      }

      if (data.success) {
        alert('保存用户协议成功');
      } else {
        alert('保存用户协议失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      // 恢复按钮状态
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存用户协议';
      }

      // 保存用户协议失败
      alert('保存用户协议失败，请稍后再试');
    });
  }

  /**
   * 获取HTML处理配置
   */
  function fetchHtmlProcessingConfig() {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch('/api/admin/config/rendering-mode', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const config = data.data;

        // 设置默认渲染方式
        const serverSideRadio = document.getElementById('serverSideRendering');
        const clientSideRadio = document.getElementById('clientSideSafeLoading');

        if (config.defaultRenderingMode === 'SERVER_SIDE_RENDERING') {
          if (serverSideRadio) serverSideRadio.checked = true;
        } else {
          if (clientSideRadio) clientSideRadio.checked = true;
        }
      }
    })
    .catch(error => {
      console.error('获取HTML处理配置失败:', error);
    });

    // 获取安全检测配置
    fetch('/api/admin/config/security-rules', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const config = data.data;

        // 设置安全检测开关
        const xssDetection = document.getElementById('enableXssDetection');
        const maliciousScript = document.getElementById('enableMaliciousScriptDetection');
        const dangerousTags = document.getElementById('enableDangerousTagDetection');
        const externalResource = document.getElementById('enableExternalResourceValidation');

        if (xssDetection) xssDetection.checked = config.enableXssDetection;
        if (maliciousScript) maliciousScript.checked = config.enableMaliciousScriptDetection;
        if (dangerousTags) dangerousTags.checked = config.enableDangerousTagDetection;
        if (externalResource) externalResource.checked = config.enableExternalResourceValidation;
      }
    })
    .catch(error => {
      console.error('获取安全检测配置失败:', error);
    });
  }

  /**
   * 保存HTML处理配置
   */
  function saveHtmlProcessingConfig() {
    const token = localStorage.getItem('token');
    if (!token) return;

    const saveBtn = document.getElementById('saveHtmlProcessingBtn');
    if (saveBtn) {
      saveBtn.disabled = true;
      saveBtn.textContent = '保存中...';
    }

    // 获取渲染方式配置
    const defaultRenderingMode = document.querySelector('input[name="defaultRenderingMode"]:checked')?.value || 'SERVER_SIDE_RENDERING';

    // 保存渲染方式配置
    fetch('/api/admin/config/rendering-mode', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        defaultRenderingMode: defaultRenderingMode,
        enableAdvancedMode: true,
        fallbackMode: 'CLIENT_SIDE_SAFE_LOADING'
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // 保存安全检测配置
        const securityConfig = {
          enableXssDetection: document.getElementById('enableXssDetection')?.checked || false,
          enableMaliciousScriptDetection: document.getElementById('enableMaliciousScriptDetection')?.checked || false,
          enableDangerousTagDetection: document.getElementById('enableDangerousTagDetection')?.checked || false,
          enableExternalResourceValidation: document.getElementById('enableExternalResourceValidation')?.checked || false
        };

        return fetch('/api/admin/config/security-rules', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(securityConfig)
        });
      } else {
        throw new Error(data.message || '保存渲染方式配置失败');
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('HTML处理配置保存成功');
      } else {
        throw new Error(data.message || '保存安全检测配置失败');
      }
    })
    .catch(error => {
      console.error('保存HTML处理配置失败:', error);
      alert('保存HTML处理配置失败，请稍后再试');
    })
    .finally(() => {
      // 恢复按钮状态
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = '保存HTML处理配置';
      }
    });
  }

  // 公开API
  return {
    init: init
  };
})();

const express = require('express');
const router = express.Router();
const adminPaymentController = require('../controllers/adminPaymentController');
const { auth, adminAuth } = require('../middlewares/authMiddleware');
const { validate, schemas } = require('../utils/validator');

// 所有管理员路由都需要认证和管理员权限
router.use(auth);
router.use(adminAuth);

// 订单管理
router.get('/orders', adminPaymentController.getOrdersList);
router.get('/orders/:id', adminPaymentController.getOrderDetail);
router.put('/orders/:id/status', adminPaymentController.updateOrderStatus);
router.put('/orders/:id/close', adminPaymentController.closeOrder);
router.delete('/orders/:id', adminPaymentController.deleteOrder);

// 会员套餐管理
router.get('/member-packages', adminPaymentController.getMemberPackages);
router.post('/member-packages', adminPaymentController.createMemberPackage);
router.get('/member-packages/:id', adminPaymentController.getMemberPackageById);
router.put('/member-packages/:id', adminPaymentController.updateMemberPackage);
router.delete('/member-packages/:id', adminPaymentController.deleteMemberPackage);

// 积分套餐管理
router.get('/point-packages', adminPaymentController.getPointPackages);
router.post('/point-packages', adminPaymentController.createPointPackage);
router.get('/point-packages/:id', adminPaymentController.getPointPackageById);
router.put('/point-packages/:id', adminPaymentController.updatePointPackage);
router.delete('/point-packages/:id', adminPaymentController.deletePointPackage);

// 退款管理
router.get('/refunds', adminPaymentController.getRefundsList);
router.post('/refunds', adminPaymentController.createRefund);
router.get('/refunds/:id', adminPaymentController.getRefundDetail);
router.put('/refunds/:id/status', adminPaymentController.updateRefundStatus);

// 支付配置管理
router.get('/payment-configs', adminPaymentController.getPaymentConfigs);
router.post('/payment-configs', adminPaymentController.createPaymentConfig);
router.put('/payment-configs/:id', adminPaymentController.updatePaymentConfig);
router.delete('/payment-configs/:id', adminPaymentController.deletePaymentConfig);

// 支付统计
router.get('/statistics', adminPaymentController.getPaymentStatistics);

module.exports = router;

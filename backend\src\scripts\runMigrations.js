const fs = require('fs');
const path = require('path');
const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

// 迁移文件目录
const migrationsDir = path.join(__dirname, '../migrations');

// 执行迁移
async function runMigrations() {
  try {
    logger.info('开始执行数据库迁移...');
    
    // 确保数据库连接
    await sequelize.authenticate();
    logger.info('数据库连接成功');
    
    // 获取所有迁移文件
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.js'))
      .sort(); // 按文件名排序，确保按顺序执行
    
    // 创建迁移记录表（如果不存在）
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // 获取已执行的迁移
    const [executedMigrations] = await sequelize.query(
      'SELECT name FROM migrations'
    );
    const executedMigrationNames = executedMigrations.map(m => m.name);
    
    // 执行未执行的迁移
    for (const file of migrationFiles) {
      if (!executedMigrationNames.includes(file)) {
        logger.info(`执行迁移: ${file}`);
        
        const migration = require(path.join(migrationsDir, file));
        
        // 开始事务
        const transaction = await sequelize.transaction();
        
        try {
          // 执行迁移的up方法
          await migration.up(sequelize.getQueryInterface(), sequelize);
          
          // 记录迁移已执行
          await sequelize.query(
            'INSERT INTO migrations (name) VALUES (?)',
            {
              replacements: [file],
              transaction
            }
          );
          
          // 提交事务
          await transaction.commit();
          logger.info(`迁移 ${file} 执行成功`);
        } catch (error) {
          // 回滚事务
          await transaction.rollback();
          logger.error(`迁移 ${file} 执行失败:`, error);
          throw error;
        }
      } else {
        logger.info(`迁移 ${file} 已执行，跳过`);
      }
    }
    
    logger.info('所有迁移执行完成');
  } catch (error) {
    logger.error('执行迁移时出错:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行迁移
runMigrations();

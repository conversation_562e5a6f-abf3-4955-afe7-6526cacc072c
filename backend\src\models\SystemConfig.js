const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 系统配置模型
 * 对应数据库中的system_configs表
 */
const SystemConfig = sequelize.define('SystemConfig', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '配置ID，唯一标识'
  },
  config_key: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '配置项的键名，不可重复'
  },
  config_value: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '配置项的值'
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '配置项的描述说明'
  }
}, {
  tableName: 'system_configs',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

module.exports = SystemConfig;

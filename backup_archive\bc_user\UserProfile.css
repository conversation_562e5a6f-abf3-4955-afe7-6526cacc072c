.user-profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.user-profile-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.profile-card,
.records-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.profile-header {
  display: flex;
  flex-direction: row;
  gap: 24px;
}

.avatar-container {
  flex-shrink: 0;
}

.profile-info {
  flex-grow: 1;
}

.profile-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.profile-detail-item {
  margin-bottom: 12px;
  display: flex;
}

.profile-detail-item strong {
  margin-right: 12px;
  width: 80px;
  display: inline-block;
}

.edit-actions {
  display: flex;
  gap: 8px;
}

.points-summary {
  display: flex;
  margin-bottom: 20px;
  gap: 40px;
  flex-wrap: wrap;
}

.point-stat {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--component-background, #fff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.point-icon {
  font-size: 36px;
  margin-right: 16px;
}

.point-icon.available {
  color: #52c41a;
}

.point-value {
  font-size: 24px;
  font-weight: 600;
}

.point-label {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 响应式适配 */
@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .profile-title-row {
    flex-direction: column;
    gap: 16px;
  }
  
  .profile-detail-item {
    flex-direction: column;
    margin-bottom: 16px;
  }
  
  .profile-detail-item strong {
    width: 100%;
    margin-bottom: 4px;
  }
}

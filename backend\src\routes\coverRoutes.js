const express = require('express');
const router = express.Router();
const coverController = require('../controllers/coverController');
const { auth } = require('../middlewares/authMiddleware');
const pointsMiddleware = require('../middlewares/pointsMiddleware');
const { validate, schemas } = require('../utils/validator');
const upload = require('../utils/uploadUtils');

// 公共路由，不需要认证
// 获取封面风格列表
router.get('/styles', coverController.getStyleList);

// 获取封面分享内容（公开路由，不需要认证）
router.get('/share/:code', coverController.getSharedCover);

// 获取基础提示词
router.get('/base-prompts', coverController.getBasePrompts);

// 检查认证中间件，以下路由都需要认证
router.use(auth);

// 获取指定风格
router.get('/style/:id', coverController.getStyleById);

// 生成封面（需要检查积分）
router.post('/generate', validate(schemas.generateCover), pointsMiddleware.checkPoints('generate_cover'), coverController.generateCover);

// 获取封面详情
router.get('/:id', coverController.getCoverDetail);

// 获取封面详情（用于二次编辑，包含完整数据）
router.get('/:id/edit', coverController.getCover);

// 通过cover_code获取封面详情（用于二次编辑，包含完整数据）
router.get('/code/:code/edit', coverController.getCoverByCode);

// 保存编辑后的HTML内容
router.post('/:id/save', coverController.saveEditedHtml);

// 删除封面
router.delete('/:id', coverController.deleteCover);

// 生成封面HTML（管理员功能，不扣除积分）
router.post('/generate-html', coverController.generateCoverHtml);

// 取消封面生成任务
router.post('/cancel', coverController.cancelGenerateCover);

// 保存自定义封面（上传文件或粘贴代码）
router.post('/save-custom', coverController.saveCustomCover);

// 用户操作封面记录相关路由
router.get('/user/covers', auth, coverController.getUserCovers);
router.put('/user/covers/:id/hide', auth, coverController.hideCoverRecord);
router.get('/user/covers/:id/html', auth, coverController.getCoverHtml);

// 上传封面自定义图片
router.post('/upload-image', upload.single('image'), coverController.uploadImage);

// 后台管理员路由
const adminRouter = express.Router();
adminRouter.use(auth); // 先验证用户登录

// 这里可以添加管理员权限验证中间件
// adminRouter.use(adminAuth);

// 后台封面记录管理路由
adminRouter.get('/cover-records', coverController.getAdminCoverRecords);
adminRouter.get('/cover-records/:id', coverController.getCoverDetail);
adminRouter.delete('/cover-records/:id', coverController.deleteCoverRecord);
adminRouter.patch('/cover-records/:id/status', coverController.updateCoverStatus);

// 注册管理员路由
router.use('/admin', adminRouter);

module.exports = router;

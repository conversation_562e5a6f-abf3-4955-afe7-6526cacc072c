import React, { useEffect, useState, useCallback, useRef, useMemo, useContext } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { message, Modal } from 'antd';
import axios from 'axios';
import Chat<PERSON><PERSON>bar from './ChatSidebar';
import ChatMainArea from './ChatMainArea';
import ChatInputArea from './ChatInputArea';
import { getCurrentUser } from '../../services/authService';
import { generateCoverHTML, buildPrompt, cancelCoverGeneration, getCurrentTaskId } from '../../services/aiService';
import { downloadHtml } from './utils/previewUtils';
import { handleViewSource as handleViewSourceUtil } from '../../shared/utils/sourceUtils';
import { useNavigationGuard } from '../../utils/leaveWarning';
import { consumePoints, checkFeatureAvailability } from '../../services/featureService';
import logger from '../../services/logs/frontendLogger';
import useChatStyleAndSizeSelection from './hooks/useChatStyleAndSizeSelection';
import usePointsManagement from '../../shared/hooks/usePointsManagement';
import useDebugMode from '../../shared/hooks/useDebugMode';
import ViewSourceModal from './ViewSourceModal';
import { encryptStyleId, decryptStyleId, encryptSizeId, decryptSizeId } from '../../utils/urlEncryption';
import { checkLogin } from '../../utils/authUtils';
import useAuth from '../../hooks/useAuth';
import { addEditableAttributeToHtml as addEditableAttributeToHtmlCore } from './utils/textEditorCore';
import { cleanHtmlContent, downloadHtmlWithCleanup } from './utils/downloadUtils';
import { handleOpenPreview } from './utils/previewUtils'; // 导入handleOpenPreview函数

// 添加一个统一的状态来管理输入区域的数据
const initialCoverData = {
  title: '',
  subtitle: '',
  accountName: '',
};

// 添加风格示例预览组件
const StyleExamplePreview = ({ htmlContent, onClose }) => {
  const iframeRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    if (iframeRef.current && htmlContent) {
      setIsLoading(true);
      const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
      if (iframeDoc) {
        // 预处理HTML内容，移除脚本和iframe
        const safeHtmlContent = htmlContent
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '<div class="iframe-placeholder">[iframe 内容已移除以确保安全显示]</div>')
          // 移除所有内联事件处理器属性（如onclick, onload等）
          .replace(/\son\w+\s*=\s*(['"]).*?\1/gi, '')
          // 移除href中的javascript:协议
          .replace(/href\s*=\s*(['"])javascript:.*?\1/gi, 'href="javascript:void(0)"');
        
        iframeDoc.open();
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              html, body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: hidden;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                background-color: transparent;
              }
              body {
                display: flex;
                justify-content: center;
                align-items: center;
              }
              * {
                max-width: 100%;
              }
              img, svg, video {
                max-width: 100%;
                height: auto;
                display: block;
                margin: 0 auto;
              }
              /* 使用CSS隐藏可能遗漏的脚本和iframe */
              script, iframe {
                display: none !important;
              }
              .iframe-placeholder {
                padding: 10px;
                border: 1px dashed #ccc;
                margin: 10px 0;
              }
            </style>
          </head>
          <body>
            ${safeHtmlContent}
          </body>
          </html>
        `);
        iframeDoc.close();
        
        // 内容加载完成
        iframeRef.current.onload = () => {
          setIsLoading(false);
        };
      }
    }
  }, [htmlContent]);
  
  return (
    <div className="w-full h-full flex flex-col items-center justify-center">
      {/* 统一为单一预览容器，固定尺寸 */}
      <div className="flex flex-col rounded-lg shadow-md overflow-hidden" style={{ width: '800px', height: '600px' }}>
        {/* 标题栏 */}
        <div className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-3 px-6 shadow-md flex items-center justify-between">
          <h3 className="text-lg font-medium">风格示例预览</h3>
          <button 
            onClick={onClose} 
            className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-1.5 rounded text-sm font-medium transition-all flex items-center"
          >
            <span>返回编辑</span>
          </button>
        </div>
      
        {/* 内容区域 - 直接在这里加载HTML，不需要内部容器 */}
        <div className="relative bg-white flex-grow">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-80 z-10">
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                <p className="text-gray-600">加载中...</p>
              </div>
            </div>
          )}
          <iframe
            ref={iframeRef}
            className="w-full h-full border-none"
            title="风格示例预览"
            referrerPolicy="no-referrer"
          />
          
          {/* 底部提示信息 */}
          <div className="absolute bottom-0 left-0 right-0 bg-gray-800 bg-opacity-75 text-white text-center py-3 px-4 text-sm z-10">
            这是风格示例，展示了该风格的设计效果。点击"返回编辑"继续编辑您的封面。
          </div>
        </div>
      </div>
    </div>
  );
};

const ChatGenerate = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // 添加一个触发路由导航事件的辅助函数
  const dispatchRouterNavigation = useCallback((destination) => {
    window.dispatchEvent(new CustomEvent('router-navigation', { 
      detail: { destination } 
    }));
  }, []);
  
  // 自定义导航包装函数，在使用React Router导航前触发保存事件
  const navigateWithSave = useCallback((to, options = {}) => {
    // 触发路由导航事件，让监听器有机会保存数据
    dispatchRouterNavigation(to);
    
    // 短暂延迟执行实际导航，给保存操作留出时间
    setTimeout(() => {
      navigate(to, options);
    }, 50);
  }, [navigate, dispatchRouterNavigation]);
  
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const previewAreaRef = useRef(null);
  const inputAreaRef = useRef(null); // 添加对ChatInputArea的引用
  
  // 修改isInputAreaCollapsed的初始值为true，使输入框区域默认折叠
  const [isInputAreaCollapsed, setIsInputAreaCollapsed] = useState(true);
  const [activeView, setActiveView] = useState('chat');
  
  // 添加未保存更改状态跟踪
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [generatedHTML, setGeneratedHTML] = useState(null);
  const [formData, setFormData] = useState(null);
  const [progress, setProgress] = useState(0);
  const [feedbackText, setFeedbackText] = useState('');
  const [apiRawResponse, setApiRawResponse] = useState('');
  const [showSourceModal, setShowSourceModal] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [previewLoaded, setPreviewLoaded] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [generatedCoverCode, setGeneratedCoverCode] = useState('');
  const [currentUser, setCurrentUser] = useState(null);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [shareLinkDialogOpen, setShareLinkDialogOpen] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  
  // 添加一个状态来管理 ChatInputArea 的数据
  const [coverData, setCoverData] = useState(initialCoverData);

  // 添加风格示例HTML相关状态
  const [styleExampleHtml, setStyleExampleHtml] = useState('');
  const [showingStyleExample, setShowingStyleExample] = useState(false);

  // 处理URL参数，支持二次编辑功能
  const [coverIdFromUrl, setCoverIdFromUrl] = useState(null);
  const [coverCodeFromUrl, setCoverCodeFromUrl] = useState(null);

  // 添加一个状态标志控制URL更新行为
  const [shouldUpdateUrl, setShouldUpdateUrl] = useState(false);

  // 添加一个状态变量，用于跟踪是否是从重置状态显示风格示例
  const [isResetMode, setIsResetMode] = useState(false);
  
  // 添加预览加载状态
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  
  // 添加一个新的状态变量，控制输入框区域的显示/隐藏
  const [showInputArea, setShowInputArea] = useState(false);

  const toggleInputAreaCollapse = useCallback((forcedState, resetExpanded = false) => {
    if (typeof forcedState === 'boolean') {
      setIsInputAreaCollapsed(forcedState);
    } else {
      setIsInputAreaCollapsed(prev => !prev);
    }
    
    // 如果需要重置展开状态，利用ref暴露一个方法来重置hasBeenManuallyExpanded状态
    if (resetExpanded && inputAreaRef && inputAreaRef.current && inputAreaRef.current.resetExpandedState) {
      inputAreaRef.current.resetExpandedState();
    }
  }, [inputAreaRef]);
  
  // 使用拆分后的模块 - 只声明一次
  const { debugMode } = useDebugMode();
  
  // 只声明一次useChatStyleAndSizeSelection
  const {
    selectedStyle,
    selectedSizeType,
    styleImages,
    justGenerated,
    availableStyles,
    availableSizeTypes,
    isLoading: styleAndSizeLoading,
    setJustGenerated,
    handleStyleChange,
    handleSizeTypeChange,
    fetchSizeTypes,
    fetchStyles,
    refreshData: refreshStyleAndSizeData,
    setSelectedExampleId,
  } = useChatStyleAndSizeSelection();

  // 使用useAuth钩子获取认证相关功能 - 只声明一次
  const { checkTokenValidity, refreshUserInfo } = useAuth();
  
  // 处理积分扣除成功后的回调
  const handlePointsDeducted = useCallback(async (newPoints) => {
    try {
      // 刷新用户信息
      await refreshUserInfo();
      
      // 更新当前用户状态
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          const userData = JSON.parse(userStr);
          setCurrentUser(userData);
        } catch (error) {
          console.error('解析用户数据失败:', error);
        }
      }
    } catch (error) {
      console.error('更新用户积分信息失败:', error);
    }
  }, [refreshUserInfo]);
  
  // 使用积分管理模块 - 只声明一次
  const {
    pointsCost,
    pointsDeducted,
    isGenerating,
    setPointsCost,
    setPointsDeducted,
    setIsGenerating,
    deductPoints,
    checkLoginAndPoints,
    handleGenerationSuccess,
    handlePreviewLoaded: handlePointsPreviewLoaded
  } = usePointsManagement({
    onStartGeneration: async (values) => {
      // 直接构建提示词并生成封面
      try {
        setLoading(true);
        // 此处的 currentUser 是组件顶层状态，确保它是最新的
        const promptData = await buildPrompt(values, selectedStyle, selectedSizeType, currentUser?.is_vip);
        await generateCover(promptData, values);
      } catch (error) {
        logger.error('Error in onStartGeneration before calling generateCover:', error);
        message.error('处理生成请求时出错，请重试。');
        setLoading(false);
        setIsGenerating(false); // 确保在出错时重置状态
      }
    },
    onPointsDeducted: handlePointsDeducted, // 保留积分扣除回调
    disableBeforeUnload: false // 必须设置为false或移除，以启用页面离开时的积分扣除逻辑
  });
  
  // 添加预览加载完成的处理函数 - 在所有依赖的变量声明之后定义
  const handlePreviewLoadComplete = useCallback(() => {
    setPreviewLoaded(true);
    
    // 调用积分管理模块中的handlePreviewLoaded函数处理积分相关逻辑
    if (handlePointsPreviewLoaded) {
      handlePointsPreviewLoaded();
    }
  }, [handlePointsPreviewLoaded]);

  // 从URL参数中获取view并设置activeView
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const viewFromUrl = searchParams.get('view');
    const validViews = ['chat', 'profile', 'points', 'covers', 'my-creations', 'auth', 'payment', 'payment-result', 'membership', 'orders'];

    const currentPath = location.pathname;
    const isDefaultChatPath = currentPath === '/' || currentPath === '/chat';

    if (viewFromUrl && validViews.includes(viewFromUrl)) {
      if (activeView !== viewFromUrl) {
        setActiveView(viewFromUrl);
        
        // 当视图切换到非chat视图时，收起输入区域
        if (viewFromUrl !== 'chat' && !isInputAreaCollapsed) {
          toggleInputAreaCollapse(true);
        }
      }
    } else if (!viewFromUrl && isDefaultChatPath) {
      if (activeView !== 'chat') setActiveView('chat');
    } else if (viewFromUrl && !validViews.includes(viewFromUrl)){
      if (activeView !== 'chat') setActiveView('chat'); 
      // Consider navigating to a default/404 page if a view is invalid and you don't want to default to chat
      // navigate('/', { replace: true }); 
    } else if (!viewFromUrl && !isDefaultChatPath) {
       // If no view param and not on a default chat path, maybe this is a different top-level route.
       // Or, if it's intended to be part of this component's view system but without a param,
       // ensure activeView is valid or reset.
       if (!validViews.includes(activeView)) {
         setActiveView('chat');
       }
    }
  }, [location.search, location.pathname, activeView, navigate, isInputAreaCollapsed, toggleInputAreaCollapse]); // 添加依赖项

  // 从URL参数中提取coverId、edit参数或code参数
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const coverId = searchParams.get('coverId');
    const editId = searchParams.get('edit');
    const code = searchParams.get('code');

    if (code) {
      // 如果有code参数，使用cover_code加载封面
      setCoverCodeFromUrl(code);
      logger.info('从URL参数中检测到封面编码，准备加载封面数据', {
        coverCode: code,
        source: 'code参数'
      });
    } else if (coverId || editId) {
      // 兼容旧版：如果有coverId或edit参数，使用ID加载封面
      const id = coverId || editId;
      setCoverIdFromUrl(id);
      logger.info('从URL参数中检测到封面ID，准备加载封面数据', {
        coverId: id,
        source: coverId ? 'coverId参数' : 'edit参数'
      });
    }
  }, [location]);

  const toggleSidebarCollapse = useCallback(() => {
    setIsSidebarCollapsed(prev => !prev);
  }, []);

  const getInitialStateFromURL = useCallback(() => {
    const queryParams = new URLSearchParams(location.search);

    // 检查是否有code参数（用于加载封面）
    const coverCode = queryParams.get('code');
    const coverId = queryParams.get('coverId') || queryParams.get('edit');
    
    // 如果URL中有code或coverId参数，则表示需要加载封面数据
    const shouldLoadCover = !!(coverCode || coverId);

    return {
      coverData: {
        title: queryParams.get('title') || '',
        subtitle: queryParams.get('subtitle') || '',
        accountName: queryParams.get('accountName') || '',
        showCoverText: queryParams.get('showCoverText') !== 'false', // 默认显示封面文案
        showAccountName: !!queryParams.get('accountName'), // 如果有账号名称则显示
        showSubtitle: !!queryParams.get('subtitle'), // 如果有副标题则显示
        autoOptimize: queryParams.get('autoOptimize') !== 'false', // 默认开启AI提炼
        customImageUrl: queryParams.get('customImageUrl') || '',
        customImageType: queryParams.get('customImageType') || '',
      },
      // 安全优化：解密URL中的风格和尺寸ID
      selectedStyleId: queryParams.get('styleId') ? decryptStyleId(queryParams.get('styleId')) : null,
      selectedSizeId: queryParams.get('sizeId') ? decryptSizeId(queryParams.get('sizeId')) : null,
      previewImageUrl: null, // 安全优化：不从URL读取预览参数
      previewHtmlContent: null, // 安全优化：不从URL读取HTML内容
      isPreviewAreaActive: shouldLoadCover, // 如果需要加载封面，则激活预览区域
      isInputAreaVisible: true, // ChatInputArea 现在总是可见，除非内部逻辑控制
      coverCode: coverCode || null, // 保存封面编码
      coverId: coverId || null, // 保存封面ID
    };
  }, [location.search]);

  const [appState, setAppState] = useState(getInitialStateFromURL);

  // 处理显示风格示例HTML的函数
  const handleShowStyleExample = (html) => {
    // 如果预览区域已经激活并加载了HTML内容，则不显示风格示例
    // 但如果是在重置模式下，应该允许显示风格示例
    if (appState.isPreviewAreaActive && appState.previewHtmlContent && !isResetMode) {
      // 直接返回，不显示任何提示信息
      return;
    }
    
    if (!html) {
      // 移除提示信息，直接返回
      return;
    }
    
    // 取消可能正在进行的自动保存操作
    if (previewAreaRef.current && previewAreaRef.current.cancelAutoSave) {
      previewAreaRef.current.cancelAutoSave();
    }
    
    // 设置风格示例HTML并显示预览
    setStyleExampleHtml(html);
    setShowingStyleExample(true);
    
    // 设置状态确保风格示例能够正确显示
    setLoading(false);
    setProgress(0);
    
    // 如果是重置模式，清除该标记
    if (isResetMode) {
      setIsResetMode(false);
    }
  };

  // 处理关闭风格示例HTML的函数
  const handleCloseStyleExample = () => {
    setShowingStyleExample(false);
    // 重置预览区域的加载状态
    if (previewAreaRef.current && previewAreaRef.current.resetLoadState) {
      previewAreaRef.current.resetLoadState();
    }
    // 确保不显示任何加载指示器
    setLoading(false);
    setProgress(0);
    
    // 如果当前处于重置模式，清除该标记
    if (isResetMode) {
      setIsResetMode(false);
    }
  };

  // 加载用户信息
  useEffect(() => {
    const fetchUserInfo = async () => {
      try {
        // 直接使用refreshUserInfo函数更新用户信息
        await refreshUserInfo();
        
        // 从localStorage获取更新后的用户数据
        const userStr = localStorage.getItem('user');
        if (userStr) {
          try {
            const userData = JSON.parse(userStr);
            setCurrentUser(userData);
          } catch (error) {
            console.error('解析用户数据失败:', error);
          }
        } else {
          setCurrentUser(null);
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        setCurrentUser(null);
      }
    };

    fetchUserInfo();
  }, [refreshUserInfo]);

  // 当有coverId或coverCode时，通过ID或code加载封面数据
  useEffect(() => {
    const loadData = async () => {
      let isPublicView = false;
      
      // 从URL参数中提取coverId、edit参数或code参数
      const searchParams = new URLSearchParams(location.search);
      const editId = searchParams.get('edit') || searchParams.get('coverId');
      const codeToLoad = searchParams.get('code');
      // 检查source参数，用于标识来源（upload或paste）
      const source = searchParams.get('source');
      // 检查mode=edit参数
      const isEditMode = searchParams.get('mode') === 'edit';
      const viewParam = searchParams.get('view');

      if (viewParam) {
        setActiveView(viewParam);
      }
      
      // 如果有code参数，并且source是upload或paste，表示是从上传或粘贴页面跳转过来的
      if (codeToLoad && (source === 'upload' || source === 'paste')) {
        try {
          setLoading(true);
          
          const token = localStorage.getItem('token');
          if (!token) {
            message.error('需要登录才能编辑，请先登录');
            setLoading(false);
            return;
          }

          const response = await axios.get(`/api/cover/code/${codeToLoad}/edit?_=${new Date().getTime()}`, {
            headers: { Authorization: `Bearer ${token}` },
          });
          
          if (response && response.data.success) {
            const coverData = response.data.data;
            if (coverData.html_content && coverData.html_content.trim() !== '') {
              // 使用source作为来源，表示从上传或粘贴页面加载
              handleLoadCoverData(coverData, source, true);
            } else {
              message.warning('该封面没有HTML内容，无法加载');
            }
          } else {
            message.error(response ? response.data.message : '加载封面数据失败');
          }
        } catch (error) {
          console.error('获取封面数据失败:', error);
          message.error('获取封面数据失败，请稍后再试');
        } finally {
          setLoading(false);
        }
        return; // 处理完后直接返回
      }
      
      // 如果是从"我的作品"跳转过来进行编辑
      if (isEditMode) {
        try {
          const pendingCoverDataStr = localStorage.getItem('pendingCoverData');
          if (pendingCoverDataStr) {
            const coverData = JSON.parse(pendingCoverDataStr);
            // 使用一个短暂的延迟确保UI已准备好
            setTimeout(() => {
              handleLoadCoverData(coverData, 'profile', false);
            }, 100);
            // 清除localStorage和URL参数，避免刷新时重复加载
            localStorage.removeItem('pendingCoverData');
            window.history.replaceState(null, '', location.pathname);
          }
        } catch (error) {
          console.error('从localStorage加载封面数据失败:', error);
          localStorage.removeItem('pendingCoverData');
        }
        return; // 处理完后直接返回
      }

      if (!codeToLoad && !editId) {
        // 如果没有需要加载的封面，并且本地有草稿，则加载草稿
        try {
          const editCoverDataStr = localStorage.getItem('editCoverData');
          if (editCoverDataStr) {
            const coverData = JSON.parse(editCoverDataStr);
            // 使用'localStorage'作为source
            handleLoadCoverData(coverData, 'localStorage', true);
            // 清除localStorage
            localStorage.removeItem('editCoverData');
          }
        } catch (error) {
          console.error('解析localStorage中的封面数据失败:', error);
        }
        return; // 处理完后直接返回
      }

      try {
        setLoading(true);
        let response;
        if (isPublicView && codeToLoad) {
          // 公开分享浏览
          response = await axios.get(`/api/cover/share/${codeToLoad}`);
          
          if (response && response.data.success) {
            const coverData = response.data.data;
            if (coverData.html_content) {
              // 处理HTML内容，确保正确显示
              const processedHtml = addEditableAttributeToHtml(coverData.html_content);
              
              // 更新appState，设置预览区域为活跃状态
              setAppState(prevState => ({
                ...prevState,
                previewHtmlContent: processedHtml,
                isPreviewAreaActive: true,
                coverCode: coverData.cover_code || codeToLoad,
              }));
              
              // 设置生成的HTML，用于预览
              setGeneratedHTML(coverData.html_content);
              
              // 使用公开分享数据更新其他状态
              if (coverData.cover_style) {
                const styleIdForApi = isNaN(coverData.cover_style) ? coverData.cover_style : parseInt(coverData.cover_style);
                handleAppStateStyleChange(styleIdForApi);
              }
              
              if (coverData.cover_type) {
                handleAppStateSizeChange(coverData.cover_type);
              }
              
              setJustGenerated(true);
              setTimeout(() => {
                // 重置预览加载状态，确保能正确触发输入框折叠
                setPreviewLoaded(false);
                handlePreviewLoadComplete();
                // 强制折叠输入框并重置其展开状态
                toggleInputAreaCollapse(true, true);
              }, 500);
            } else {
              message.warning('该封面没有HTML内容，无法预览');
            }
          } else {
            message.error(response ? response.data.message : '加载封面数据失败');
          }
        
          // 通过code加载以供编辑
          const token = localStorage.getItem('token');
          if (!token) {
            message.error('需要登录才能编辑，请先登录');
            setLoading(false);
            return;
          }
          response = await axios.get(`/api/cover/code/${codeToLoad}/edit?_=${new Date().getTime()}`, {
            headers: { Authorization: `Bearer ${token}` },
          });
          
          if (response && response.data.success) {
            const coverData = response.data.data;
            if (coverData.html_content) {
              // 使用'url'作为source，表示从URL参数加载
              handleLoadCoverData(coverData, 'url', false);
            } else {
              message.warning('该封面没有HTML内容，无法加载');
            }
          } else {
            message.error(response ? response.data.message : '加载封面数据失败');
          }
        } else if (codeToLoad) {
          // 通过code加载封面数据
          const token = localStorage.getItem('token');
          if (!token) {
            message.error('需要登录才能编辑，请先登录');
            setLoading(false);
            return;
          }

          response = await axios.get(`/api/cover/code/${codeToLoad}/edit?_=${new Date().getTime()}`, {
            headers: { Authorization: `Bearer ${token}` },
          });
          
          if (response && response.data.success) {
            const coverData = response.data.data;
            if (coverData.html_content) {
              // 使用'url'作为source，表示从URL参数加载
              handleLoadCoverData(coverData, 'url', true);
            } else {
              message.warning('该封面没有HTML内容，无法加载');
            }
          } else {
            message.error(response ? response.data.message : '加载封面数据失败');
          }
        } else if (editId) {
          // 通过ID加载封面数据
          const token = localStorage.getItem('token');
          if (!token) {
            message.error('需要登录才能编辑，请先登录');
            setLoading(false);
            return;
          }

          response = await axios.get(`/api/cover/${editId}/edit?_=${new Date().getTime()}`, {
            headers: { Authorization: `Bearer ${token}` },
          });

          if (response && response.data.success) {
            const coverData = response.data.data;
            if (coverData.html_content) {
              // 使用'url'作为source，表示从URL参数加载
              handleLoadCoverData(coverData, 'url', true);
            } else {
              message.warning('该封面没有HTML内容，无法加载');
            }
          } else {
            message.error(response ? response.data.message : '加载封面数据失败');
          }
        } else if (isEditMode) {
          // 从localStorage加载封面数据
          try {
            const editCoverDataStr = localStorage.getItem('editCoverData');
            if (editCoverDataStr) {
              const coverData = JSON.parse(editCoverDataStr);
              // 使用'localStorage'作为source
              handleLoadCoverData(coverData, 'localStorage', true);
              // 清除localStorage
              localStorage.removeItem('editCoverData');
        }
      } catch (error) {
            console.error('解析localStorage中的封面数据失败:', error);
          }
        }
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [coverIdFromUrl, coverCodeFromUrl, location.pathname, location.search]);

  // Helper function to add data-editable-fengmian="true" to specified elements in an HTML string
  // This is a basic implementation that delegates to the core implementation in textEditorCore.js
  const addEditableAttributeToHtml = (htmlString) => {
    // 调用从textEditorCore导入的核心实现
    return addEditableAttributeToHtmlCore(htmlString);
  };

  // 页面加载
  useEffect(() => {
    // 页面加载时的处理
  }, [location]);

  // 尺寸类型变更时重置生成状态
  useEffect(() => {
    if (justGenerated) {
      setJustGenerated(false);
    }
  }, [selectedSizeType, justGenerated]);

  // 在组件挂载时刷新风格和尺寸类型数据
  useEffect(() => {
    // 刷新风格和尺寸类型数据
    fetchStyles();
    fetchSizeTypes();
  }, [fetchStyles, fetchSizeTypes]);

  // 使用新的导航拦截hook，替代之前的两个useEffect
  const { cleanup: cleanupNavGuard } = useNavigationGuard(
    isGenerating, // 正在生成时才激活导航拦截
    setPendingNavigation,
    logger,
    pointsCost,
    pointsDeducted,
    setPointsDeducted,
    // 使用实际的consumePoints函数，确保积分扣除逻辑正常工作
    consumePoints,
    false // 设置为false，启用beforeunload事件监听器，与usePointsManagement保持一致
  );

  // 组件卸载时清理导航拦截
  useEffect(() => {
    return () => {
      cleanupNavGuard && cleanupNavGuard();
    };
  }, [cleanupNavGuard]);

  // 添加获取反馈文本的函数
  const getFeedbackText = (progressValue) => {
    if (progressValue < 15) return "正在连接AI创意引擎...";
    if (progressValue < 35) return "AI正在解析您的内容...";
    if (progressValue < 55) return "构思创意元素与布局...";
    if (progressValue < 75) return "生成视觉设计方案...";
    if (progressValue < 90) return "优化细节与排版...";
    return "即将完成，准备展示您的专属封面...";
  };

  // 添加智能进度模拟系统
  useEffect(() => {
    // 仅在生成状态且进度未达到95%时进行模拟
    if (isGenerating && progress < 95) {
      // 非线性进度模拟 - 开始快，中间慢，接近完成又稍快
      const calculateNextIncrement = (currentProgress) => {
        if (currentProgress < 30) return 1.8; // 开始阶段快速增长
        if (currentProgress < 60) return 1.0; // 中间阶段减缓
        if (currentProgress < 85) return 0.7; // 接近完成更慢
        return 0.4; // 最后阶段几乎停滞
      };

      // 使用间隔时间，减少状态更新频率
      const interval = setInterval(() => {
        setProgress(prev => {
          const nextIncrement = calculateNextIncrement(prev);
          const newProgress = Math.min(95, prev + nextIncrement);

          // 同步更新反馈文本
          setFeedbackText(getFeedbackText(newProgress));

          return newProgress;
        });
      }, 800); // 修改为800毫秒间隔，提供更流畅的进度感

      return () => clearInterval(interval);
    }
  }, [isGenerating, progress, setFeedbackText]);

  // 当location.search变化时（例如浏览器前进/后退导航），从URL重新初始化状态
  useEffect(() => {
    // When location.search changes (e.g. back/forward browser navigation),
    // re-initialize state from URL.
    setAppState(getInitialStateFromURL());
  }, [getInitialStateFromURL, location.search]); // Added location.search to re-run if query params change externally

  // 在组件挂载后设置延迟，允许URL更新
  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldUpdateUrl(true);
    }, 1000); // 延迟1秒后允许URL更新
    return () => clearTimeout(timer);
  }, []);

  const updateUrlParams = useCallback(() => {
    // 如果不应该更新URL（如初始加载阶段），则直接返回
    if (!shouldUpdateUrl) return;
    
    // 如果有coverCode，使用coverCode作为URL参数
    if (appState.coverCode) {
      const newSearch = `code=${appState.coverCode}`;
      if (location.search.substring(1) !== newSearch) {
        // 使用 window.history.replaceState 替代 navigate
        window.history.replaceState(null, '', `${location.pathname}?${newSearch}`);
      }
      return;
    }

    // 如果有coverId，使用coverId作为URL参数
    if (appState.coverId) {
      const newSearch = `edit=${appState.coverId}`;
      if (location.search.substring(1) !== newSearch) {
        // 使用 window.history.replaceState 替代 navigate
        window.history.replaceState(null, '', `${location.pathname}?${newSearch}`);
      }
      return;
    }

    const queryParams = new URLSearchParams(location.search); // Preserve existing params not managed by this component

    // Helper to set or delete param based on value
    const setOrDelete = (key, value) => {
      if (value) {
        queryParams.set(key, value);
      } else {
        queryParams.delete(key);
      }
    };

    // 设置基本参数
    setOrDelete('title', appState.coverData.title);
    setOrDelete('subtitle', appState.coverData.subtitle);
    setOrDelete('accountName', appState.coverData.accountName);
    // 安全优化：加密风格和尺寸ID后写入URL
    setOrDelete('styleId', appState.selectedStyleId ? encryptStyleId(appState.selectedStyleId) : null);
    setOrDelete('sizeId', appState.selectedSizeId ? encryptSizeId(appState.selectedSizeId) : null);

    // 设置显示控制参数
    if (!appState.coverData.showCoverText) {
      setOrDelete('showCoverText', 'false');
    } else {
      queryParams.delete('showCoverText');
    }

    // 设置AI提炼参数
    if (!appState.coverData.autoOptimize) {
      setOrDelete('autoOptimize', 'false');
    } else {
      queryParams.delete('autoOptimize');
    }

    // 设置自定义图片参数
    setOrDelete('customImageUrl', appState.coverData.customImageUrl);
    // 只有当用户明确选择了自定义图片类型且不是默认值'background'时，才添加该参数
    if (appState.coverData.customImageType && appState.coverData.customImageType !== 'background') {
      setOrDelete('customImageType', appState.coverData.customImageType);
    } else {
      queryParams.delete('customImageType');
    }

    // 设置预览参数
    // setOrDelete('previewUrl', appState.previewImageUrl); // previewImageUrl might not be needed in URL if htmlContent is primary
    // setOrDelete('previewHtml', appState.previewHtmlContent); // 安全优化：移除HTML内容参数，避免URL过长和安全风险

    const newSearch = queryParams.toString();
    if (location.search.substring(1) !== newSearch) { // Avoid pushing same history state
      // 使用 window.history.replaceState 替代 navigate 以减少导航事件
      // 先触发路由导航事件，让监听器有机会保存数据
      dispatchRouterNavigation(`${location.pathname}${newSearch ? `?${newSearch}` : ''}`);
      
      // 延迟执行导航，给保存操作留出时间
      setTimeout(() => {
      window.history.replaceState(
        null, 
        '', 
        `${location.pathname}${newSearch ? `?${newSearch}` : ''}`
      );
      }, 50);
    }
  }, [appState, location.pathname, location.search, shouldUpdateUrl, dispatchRouterNavigation]);

  // Re-enable URL update, but perhaps with a debounce or on specific state changes
  // For now, let's update it when core data that should be in URL changes.
  useEffect(() => {
    // This effect will run whenever these specific parts of appState change.
    // This is more targeted than updating on every appState change.
    updateUrlParams();
  }, [
    appState.coverData.title,
    appState.coverData.subtitle,
    appState.coverData.accountName,
    appState.coverData.showCoverText,
    appState.coverData.showAccountName,
    appState.coverData.showSubtitle,
    appState.coverData.autoOptimize,
    appState.coverData.customImageUrl,
    appState.coverData.customImageType,
    appState.selectedStyleId,
    appState.selectedSizeId,
    appState.previewHtmlContent,
    appState.coverCode,
    appState.coverId,
    updateUrlParams // updateUrlParams itself is a dependency due to useCallback
  ]);

  // 处理表单提交
  const handleFormSubmit = async (values, sizeId, styleId) => {
    logger.info('CoverForm submitted values:', values, 'sizeId:', sizeId, 'styleId:', styleId);
    
    // 立即折叠输入框，减少延迟感
    toggleInputAreaCollapse(true);

    // 如果正在显示风格示例，先关闭它
    if (showingStyleExample) {
      setShowingStyleExample(false);
    }
    
    // 如果提供了尺寸和风格参数，更新状态
    if (sizeId) handleAppStateSizeChange(sizeId);
    if (styleId) handleAppStateStyleChange(styleId);

    // 统一入口：直接调用积分管理模块的检查函数
    // 该函数会处理登录、积分检查、弹窗确认等所有前置逻辑
    // 成功后会通过 onStartGeneration 回调来触发真正的 generateCover
    await checkLoginAndPoints(values);
  };

  // 生成封面的核心函数
  const generateCover = async (promptData, formValues) => {
    try {
      // 在替换HTML内容前先保存当前内容，但只在必要时且可以安全保存时触发保存
      if (previewAreaRef.current && 
          typeof previewAreaRef.current.handleSave === 'function' && 
          typeof previewAreaRef.current.canSave === 'function' &&
          previewAreaRef.current.canSave() &&
          appState.isPreviewAreaActive && 
          appState.coverId && 
          hasUnsavedChanges) {
        previewAreaRef.current.handleSave('router');
      }
      
      // 如果正在显示风格示例，先关闭它
      if (showingStyleExample) {
        setShowingStyleExample(false);
      }
      
      // 注意：积分确认已在usePointsManagement的checkLoginAndPoints函数中处理
      // 此处只需确保状态正确即可
      
      // 确保生成状态正确设置
      setIsGenerating(true);
      setLoading(true);
      setPreviewLoaded(false); // 重置previewLoaded状态，确保后续能正确触发折叠
      setProgress(15);
      setFeedbackText(getFeedbackText(15));
      setProgress(30);
      
      logger.info('开始生成封面流程', {
        pointsDeducted: pointsDeducted,
        pointsCost,
        formData: {
          hasCoverText: !!formValues.coverText,
          showCoverText: formValues.showCoverText,
          showAccountName: formValues.showAccountName,
          showSubtitle: formValues.showSubtitle,
          customImageUrl: formValues.customImageUrl,
          customImageType: formValues.customImageType || 'background'
        }
      });
      setProgress(40);

      // 使用当前选择的尺寸和风格
      const currentSizeType = selectedSizeType;
      const currentStyle = selectedStyle;

      logger.info('发送AI封面生成请求', { style: currentStyle, size: currentSizeType });
      // 确保selectedStyle是有效的风格ID
      const styleIdForApi = isNaN(currentStyle) ? currentStyle : parseInt(currentStyle);
      const result = await generateCoverHTML(formValues, styleIdForApi, currentSizeType);

      setProgress(80);
      setFeedbackText(getFeedbackText(80));

      const response = {
        data: {
          generatedHtml: result.processedHtml
        }
      };

      const responseData = response.data;

      if (!responseData || !responseData.generatedHtml) {
        logger.error('API响应缺少必要数据', { response: responseData });
        message.error('接收到的数据格式错误，请联系管理员');
        setLoading(false);
        setIsGenerating(false);
        return;
      }

      let htmlContent;
      try {
        htmlContent = responseData.generatedHtml;

        if (htmlContent.startsWith('http')) {
          logger.info('发送获取URL内容请求', { url: htmlContent });
          const urlContentResponse = await axios.get(htmlContent);
          htmlContent = urlContentResponse.data;
        }

        setApiRawResponse(responseData);
        setGeneratedHTML(htmlContent);
        setJustGenerated(true);
        setPreviewLoaded(false);

        if (result.coverCode) {
          setGeneratedCoverCode(result.coverCode);
          logger.info('已保存生成的cover_code', { coverCode: result.coverCode });

          const newUrl = `${window.location.pathname}?code=${result.coverCode}`;
          window.history.pushState({ path: newUrl }, '', newUrl);
          logger.info('已更新URL为使用cover_code的形式', { newUrl });
        } else {
          setGeneratedCoverCode('');
          logger.info('生成的响应中没有cover_code');
        }

        message.success('封面生成成功！');

        // 封面成功生成后，调用handleGenerationSuccess执行积分扣除
        const deductResult = await handleGenerationSuccess();
        logger.info('积分扣除结果', { deductResult });

        setProgress(90);
        setFeedbackText(getFeedbackText(90));
        logger.info('封面生成完成，准备显示');

        // 更新appState
        setAppState(prevState => ({
          ...prevState,
          previewHtmlContent: htmlContent,
          isPreviewAreaActive: true,
          coverCode: result.coverCode || null,
        }));
      } catch (extractError) {
        logger.error('提取HTML内容失败', { error: extractError.message });
        message.error('处理AI返回内容失败，请重试');
        setLoading(false);
        setIsGenerating(false);
        return;
      }

      setProgress(100);
      setFeedbackText("封面生成完成！");
      setLoading(false);
    } catch (error) {
      logger.error('生成封面时发生错误', { error: error.message, stack: error.stack });
      setLoading(false);
      setIsGenerating(false);

      if (axios.isCancel(error) ||
          (error.message && error.message.includes('canceled')) ||
          (error.message && error.message.includes('aborted'))) {
        logger.info('封面生成被用户中断');
      } else if (error.response && error.response.status >= 500) {
        logger.error('服务器错误', { status: error.response.status, error: error.message });
        message.error({
          content: '服务器暂时无法处理您的请求，请稍后再试',
          duration: 4
        });

        if (error.styleId) {
          logger.info('保留错误中的风格信息', { styleId: error.styleId });
          handleAppStateStyleChange(error.styleId);
        }

        if (error.sizeType) {
          logger.info('保留错误中的尺寸信息', { sizeType: error.sizeType });
          handleAppStateSizeChange(error.sizeType);
        }
      } else {
        logger.error(`生成失败: ${error.message}`);
        message.error('生成封面失败，请稍后重试');
      }
    } finally {
      setIsGenerating(false);
      setLoading(false);
      setProgress(0);
      setFeedbackText('');
      // 确保折叠输入框并重置展开状态
      toggleInputAreaCollapse(true, true);
    }
  };

  const handleCoverGenerated = (generatedData) => {
    setGeneratedHTML(generatedData.html);
    setGeneratedCoverCode(generatedData.code);
    
    // 更新 coverData
    setCoverData(prev => ({
      ...prev,
      title: generatedData.revisedTitle || prev.title,
      subtitle: generatedData.revisedSubtitle || prev.subtitle,
    }));

    handleGenerationSuccess();
    setLoading(false);
    setProgress(100);
    
    // 强制折叠输入框并重置其展开状态
    toggleInputAreaCollapse(true, true);
    
    // 如果有积分消耗，更新积分状态
    if (generatedData.pointsCost) {
      setPointsCost(generatedData.pointsCost);
      setPointsDeducted(true);
    }
  };

  const handleInputChange = useCallback((field, value) => {
    setCoverData(prevData => ({
      ...prevData,
      [field]: value
    }));
  }, []);

  // 处理应用状态风格变化，提前定义以供其他函数引用
  const handleAppStateStyleChange = useCallback((newStyleId) => {
    try {
      // 确保newStyleId是有效的风格ID
      const styleIdForApi = isNaN(newStyleId) ? newStyleId : parseInt(newStyleId);

      // 检查是否已经有预览内容
      const hasExistingPreview = appState && appState.isPreviewAreaActive && appState.previewHtmlContent;
      
      // 只更新风格ID，不更新预览内容
      setAppState(prevState => ({
        ...prevState,
        selectedStyleId: styleIdForApi,
      }));

      // 同时调用useStyleAndSizeSelection中的handleStyleChange
      // 如果已经有预览内容，传递第二个参数控制不要加载风格示例
      if (hasExistingPreview) {
        handleStyleChange(styleIdForApi, true);
      } else {
        handleStyleChange(styleIdForApi);
      }

    } catch (error) {
      // 静默处理风格预览错误，不影响功能

      // 更新风格 ID，但保留现有的 HTML 内容
      const styleIdForApi = isNaN(newStyleId) ? newStyleId : parseInt(newStyleId);
      setAppState(prevState => ({
        ...prevState,
        selectedStyleId: styleIdForApi,
      }));

      // 同时调用useStyleAndSizeSelection中的handleStyleChange
      handleStyleChange(styleIdForApi);
    }
  }, [handleStyleChange, appState]);

  // 处理应用状态尺寸变化，提前定义以供其他函数引用
  const handleAppStateSizeChange = useCallback((newSizeId) => {
    setAppState(prevState => ({ ...prevState, selectedSizeId: newSizeId }));

    // 同时调用useStyleAndSizeSelection中的handleSizeTypeChange
    handleSizeTypeChange(newSizeId);
  }, [handleSizeTypeChange]);

  // 添加加载封面数据的处理函数，用于从侧边栏接收封面数据并加载到编辑器中
  // 现在所有依赖函数都已经定义
  const handleLoadCoverData = useCallback((coverData, source = 'sidebar', shouldUpdateUrl = true) => {
    if (!coverData) {
      message.error('封面数据无效，无法加载');
      return;
    }

    try {
      if (showingStyleExample) {
        setShowingStyleExample(false);
      }

      if (previewAreaRef.current) {
              // 在替换HTML内容前先保存当前内容，但只在必要时且可以安全保存时触发保存
      if (typeof previewAreaRef.current.handleSave === 'function' && 
          typeof previewAreaRef.current.canSave === 'function' &&
          previewAreaRef.current.canSave() &&
          appState.isPreviewAreaActive && 
          appState.coverId && 
          hasUnsavedChanges) {
        previewAreaRef.current.handleSave('router');
      }
        if (previewAreaRef.current.resetLoadState) {
          previewAreaRef.current.resetLoadState();
        }
        if (previewAreaRef.current.cancelAutoSave) {
          previewAreaRef.current.cancelAutoSave();
        }
      }

      // 确保无论在哪个视图点击编辑封面，都切换到创建封面视图
      setActiveView('chat');
      
      // 设置showInputArea为true，确保输入框区域显示
      setShowInputArea(true);

      const htmlContent = coverData.edited_html_content || coverData.html_content;
      if (!htmlContent || htmlContent.trim() === '') {
        message.warning('该封面没有HTML内容，无法加载');
        return;
      }

      const processedHtml = addEditableAttributeToHtml(htmlContent);

      // 更新组件级别的coverData状态，确保历史数据能正确显示在输入框中
      setCoverData({
        title: coverData.cover_text || '',
        subtitle: coverData.subtitle || '',
        accountName: coverData.account_name || '',
      });

      setAppState(prevState => ({
        ...prevState,
        previewHtmlContent: processedHtml,
        isPreviewAreaActive: true,
        coverCode: coverData.cover_code || null,
        coverId: coverData.id || null,
        coverData: {
          ...prevState.coverData,
          title: coverData.cover_text || '',
          subtitle: coverData.subtitle || '',
          accountName: coverData.account_name || '',
          showCoverText: true,
          showAccountName: !!coverData.account_name,
          showSubtitle: !!coverData.subtitle,
          autoOptimize: true,
          customImageUrl: '',
          customImageType: '',
        }
      }));

      setFormData({
        coverText: coverData.cover_text || '',
        accountName: coverData.account_name || '',
        subtitle: coverData.subtitle || '',
        showAccountName: !!coverData.account_name,
        showSubtitle: !!coverData.subtitle,
        autoOptimize: true
      });

      if (coverData.cover_style) {
        const styleIdForApi = isNaN(coverData.cover_style) ? coverData.cover_style : parseInt(coverData.cover_style);
        handleAppStateStyleChange(styleIdForApi);
      }

      if (coverData.cover_type) {
        handleAppStateSizeChange(coverData.cover_type);
      }

      setJustGenerated(true);
      setGeneratedHTML(coverData.html_content);
      setApiRawResponse(coverData.original_html_content || coverData.html_content);
      if (coverData.cover_code) {
        setGeneratedCoverCode(coverData.cover_code);
      }
      
      // 设置hasUnsavedChanges状态
      setHasUnsavedChanges(true);
      
      // 只在指定需要更新URL时更新
      if (shouldUpdateUrl) {
        // 强制更新URL，确保反映当前加载的封面数据
        if (coverData.cover_code) {
          dispatchRouterNavigation(`${location.pathname}?code=${coverData.cover_code}`);
          setTimeout(() => {
          window.history.replaceState(null, '', `${location.pathname}?code=${coverData.cover_code}`);
          }, 50);
        } else if (coverData.id) {
          dispatchRouterNavigation(`${location.pathname}?edit=${coverData.id}`);
          setTimeout(() => {
          window.history.replaceState(null, '', `${location.pathname}?edit=${coverData.id}`);
          }, 50);
        }
      }

      // 重置预览加载状态，确保后续能正确触发输入框折叠
      setPreviewLoaded(false);

      setTimeout(() => {
        handlePreviewLoadComplete();
        // 强制折叠输入框并重置其展开状态，确保每次加载新的封面时输入框都会自动折叠
        toggleInputAreaCollapse(true, true);
      }, 500);
      
      // 只在从侧边栏加载封面数据时显示成功消息
      if (source === 'sidebar') {
        message.success('已成功加载封面数据，可以进行二次编辑');
      }
    } catch (error) {
      console.error('处理封面数据失败:', error);
      message.error('加载封面数据失败，请稍后再试');
    }
  }, [showingStyleExample, handleAppStateStyleChange, handleAppStateSizeChange, handlePreviewLoadComplete, addEditableAttributeToHtml, location.pathname, dispatchRouterNavigation]);

  const handleResetDesign = () => {
    // 如果有未保存的更改或预览区域处于活跃状态，显示确认对话框
    if (appState.isPreviewAreaActive && hasUnsavedChanges) {
      Modal.confirm({
        title: '确定要重置设计吗？',
        content: '重置将清除当前未保存的编辑内容，此操作无法撤销。',
        okText: '确定重置',
        cancelText: '取消',
        onOk: performReset,
      });
    } else {
      performReset();
    }
  };

  const performReset = () => {
    const wasPreviewActive = appState.isPreviewAreaActive;

    // 在替换HTML内容前先保存当前内容，但只在必要时且可以安全保存时触发保存
    if (previewAreaRef.current && 
        typeof previewAreaRef.current.handleSave === 'function' && 
        typeof previewAreaRef.current.canSave === 'function' &&
        previewAreaRef.current.canSave() &&
        appState.isPreviewAreaActive && 
        appState.coverId && 
        hasUnsavedChanges) {
      previewAreaRef.current.handleSave('router');
    }

    // 关闭风格示例预览（如果正在显示）
    if (showingStyleExample) {
      setShowingStyleExample(false);
    }
    
    // 先重置ChatPreview组件的状态
    try {
      if (previewAreaRef.current && typeof previewAreaRef.current.resetLoadState === 'function') {
        previewAreaRef.current.resetLoadState();
      }
    } catch (error) {
      console.error('重置预览组件状态失败:', error);
    }

    // 设置重置模式标志
    setIsResetMode(true);
    
    // 切换视图到'chat'
    setActiveView('chat');
    
    // 清除预览区域的内容
    setAppState(prevState => ({
      ...prevState,
      previewHtmlContent: '',
      isPreviewAreaActive: false,
      coverCode: null,
      coverId: null,
      selectedStyleId: null,
      selectedSizeId: null,
      previewImageUrl: null,
      isInputAreaVisible: true,
      coverData: {
        title: '',
        subtitle: '',
        accountName: '',
        showCoverText: true,
        showAccountName: false,
        showSubtitle: false,
        autoOptimize: true,
        customImageUrl: '',
        customImageType: '',
      }
    }));

    // 重置表单数据
    setFormData({
      coverText: '',
      accountName: '',
      subtitle: '',
      showAccountName: false,
      showSubtitle: false,
      autoOptimize: true
    });
    
    // 重置 ChatInputArea 的数据
    setCoverData(initialCoverData);

    // 清除生成的HTML和封面代码
    setGeneratedHTML('');
    setGeneratedCoverCode('');
    setApiRawResponse('');
    setPreviewLoaded(false);

    // 重置生成状态
    setIsGenerating(false);
    setProgress(0);
    setFeedbackText('');
    setPointsDeducted(false);
    setPointsCost(0);

    // 重置其他状态
    setJustGenerated(false);
    setShowSourceModal(false);
    setSelectedExampleId(null);
    
    // 重置未保存状态
    setHasUnsavedChanges(false);
    
    // 设置showInputArea为false，确保在重置设计时隐藏输入框
    setShowInputArea(false);

    // 确保输入区域展开并重置手动展开状态
    if (isInputAreaCollapsed) {
      toggleInputAreaCollapse(false, true);
    } else {
      // 即使不需要改变折叠状态，也重置手动展开标记
      toggleInputAreaCollapse(false, true);
    }

    // 使用 React Router 的 navigate 函数重置 URL，这会正确触发路由更新
    navigateWithSave('/', { replace: true });

    // 临时禁用URL更新，防止状态重置后又被URL参数影响
    setShouldUpdateUrl(false);
    setTimeout(() => {
      setShouldUpdateUrl(true);
    }, 100);

    // 显示提示消息
    if (wasPreviewActive) {
      message.success('设计已重置');
    }
  };

  const chatInputAreaProps = {
    // isVisible: appState.isInputAreaVisible, // 不再从这里传递isVisible
    // toggleVisibility: toggleInputVisibility, // 不再需要
    onCoverGenerated: handleCoverGenerated,
    coverData: appState.coverData,
    onInputChange: handleInputChange,
    selectedStyleId: appState.selectedStyleId,
    onStyleChange: handleAppStateStyleChange,
    selectedSizeId: appState.selectedSizeId,
    onSizeChange: handleAppStateSizeChange,
    isPreviewLoaded: isResetMode ? false : appState.isPreviewAreaActive, // 在重置模式下，强制设为false
    isGenerating: isGenerating,
    setIsGenerating: setIsGenerating,
    progress: progress,
    setProgress: setProgress,
    feedbackText: feedbackText,
    setFeedbackText: setFeedbackText,
    pointsDeducted: pointsDeducted,
    setPointsDeducted: setPointsDeducted,
    pointsCost: pointsCost,
    setPointsCost: setPointsCost,
    coverCode: appState.coverCode,
    coverId: appState.coverId,
    onFormSubmit: handleFormSubmit,
    selectedStyle: selectedStyle,
    selectedSizeType: selectedSizeType,
    currentUser: currentUser,
    debugMode: debugMode,
    onShowStyleExample: handleShowStyleExample, // 添加显示风格示例HTML的回调函数
  };

  // 新增: 下载前清理编辑UI并在下载后恢复
  const cleanForDownloadAndRestore = useCallback((downloadCallback) => {
    try {
      // 获取iframe元素
      const iframe = document.querySelector('.chat-preview-container iframe');
      if (!iframe || !iframe.contentDocument) {
        console.error('无法访问预览内容');
        downloadCallback();
        return;
      }

      const iframeDoc = iframe.contentDocument;
      
      // 1. 保存当前选择区域
      let savedSelections = [];
      if (iframeDoc.getSelection) {
        const selection = iframeDoc.getSelection();
        for (let i = 0; i < selection.rangeCount; i++) {
          savedSelections.push(selection.getRangeAt(i).cloneRange());
        }
        // 清除当前选择
        selection.removeAllRanges();
      }
      
      // 2. 保存具有编辑相关类和属性的元素的状态
      const editingElements = [];
      
      // 使用与cleanHtmlContent函数相同的选择器查找所有编辑相关元素
      const editableElements = iframeDoc.querySelectorAll(
        '[contenteditable], [data-editing], [data-field], [data-editable-fengmian], ' + 
        '.drag-mode, .dragging, .editing-active-outline, .selected-for-drag, .resize-mode'
      );
      
      editableElements.forEach(el => {
        // 保存元素的当前状态
        const elementState = {
          element: el,
          classes: el.className,
          attributes: {}
        };
        
        // 保存需要临时移除的属性
        ['contenteditable', 'data-editing', 'data-field', 'data-editable-fengmian', 'title', 'tabindex'].forEach(attr => {
          if (el.hasAttribute(attr)) {
            elementState.attributes[attr] = el.getAttribute(attr);
          }
        });
        
        // 如果元素有样式，保存可能需要临时修改的样式
        if (el.style) {
          elementState.styles = {
            outline: el.style.outline,
            outlineStyle: el.style.outlineStyle,
            outlineWidth: el.style.outlineWidth,
            outlineColor: el.style.outlineColor,
            outlineOffset: el.style.outlineOffset,
            border: el.style.border,
            borderStyle: el.style.borderStyle,
            borderWidth: el.style.borderWidth,
            borderColor: el.style.borderColor,
            boxShadow: el.style.boxShadow,
            cursor: el.style.cursor,
            userSelect: el.style.userSelect,
            webkitUserSelect: el.style.webkitUserSelect,
            msUserSelect: el.style.msUserSelect,
          };
        }
        
        editingElements.push(elementState);
      });
      
      // 3. 移除编辑器UI元素
      const uiElementsToHide = iframeDoc.querySelectorAll(
        '.resize-handle, .drag-handle, .text-editor-toolbar, .text-editor-toolbar-container, ' + 
        '.color-picker, .color-picker-wrapper, .color-picker-popup'
      );
      
      // 保存UI元素的显示状态，然后临时隐藏它们
      const uiElementsState = [];
      uiElementsToHide.forEach(el => {
        uiElementsState.push({
          element: el,
          display: el.style.display
        });
        el.style.display = 'none';
      });
      
      // 4. 清理编辑相关的类和属性
      editableElements.forEach(el => {
        // 移除编辑相关属性
        ['contenteditable', 'data-editing', 'data-field', 'data-editable-fengmian', 'title', 'tabindex'].forEach(attr => {
          el.removeAttribute(attr);
        });
        
        // 移除编辑相关类
        el.classList.remove('drag-mode', 'dragging', 'resize-mode', 'editing-active-outline', 'selected-for-drag');
        
        // 清理编辑相关样式
        if (el.style) {
          el.style.outline = 'none';
          el.style.outlineStyle = 'none';
          el.style.outlineWidth = '0';
          el.style.outlineColor = 'transparent';
          el.style.outlineOffset = '0';
          el.style.border = 'none';
          el.style.borderStyle = 'none';
          el.style.borderWidth = '0';
          el.style.borderColor = 'transparent';
          el.style.boxShadow = 'none';
          el.style.cursor = 'default';
          el.style.userSelect = 'none';
          el.style.webkitUserSelect = 'none';
          el.style.msUserSelect = 'none';
}
      });
      
      // 5. 执行下载回调
      downloadCallback();
      
      // 6. 在下载操作完成后恢复状态（使用短延时确保下载已开始）
      setTimeout(() => {
        // 恢复编辑元素的原始状态
        editingElements.forEach(item => {
          // 恢复类名
          item.element.className = item.classes;
          
          // 恢复属性
          for (const [attr, value] of Object.entries(item.attributes)) {
            item.element.setAttribute(attr, value);
          }
          
          // 恢复样式
          if (item.styles) {
            for (const [prop, value] of Object.entries(item.styles)) {
              if (value) item.element.style[prop] = value;
}
          }
        });
        
        // 恢复UI元素的显示状态
        uiElementsState.forEach(item => {
          item.element.style.display = item.display || '';
        });

        // 恢复选择区域
        if (iframeDoc.getSelection && savedSelections.length > 0) {
          const selection = iframeDoc.getSelection();
          selection.removeAllRanges();
          savedSelections.forEach(range => {
            try {
              selection.addRange(range);
            } catch (e) {
              // 忽略恢复选择区域时可能出现的错误
            }
          });
        }
      }, 100); // 短延时，确保下载已开始但不过长影响用户体验
    } catch (error) {
      console.error('清理编辑UI失败:', error);
      // 如果出错，仍然尝试执行下载
      downloadCallback();
    }
  }, []);

  // 处理下载封面图片
  const handleDownloadCover = useCallback(async () => {
    cleanForDownloadAndRestore(() => {
      if (!previewAreaRef.current) {
        message.error('预览区域尚未准备好，请稍候。');
        return;
      }

      // 通过ref调用ChatPreview暴露的下载方法
      if (previewAreaRef.current.downloadCurrentView) {
        const coverTitle = appState.coverDetails?.title || appState.currentGenerationParams?.prompt || 'ai封面';
        previewAreaRef.current.downloadCurrentView(coverTitle);
      } else {
        message.error('下载功能不可用，请联系技术支持');
        logger.error('ChatPreview.downloadCurrentView 方法未找到。');
    }
    });
  }, [appState.coverDetails, appState.currentGenerationParams, logger, cleanForDownloadAndRestore]);

  // 处理下载HTML - 使用downloadUtils.js中的函数
  const handleDownloadHtml = useCallback(() => {
    downloadHtmlWithCleanup(cleanForDownloadAndRestore, appState, logger);
  }, [appState, logger, cleanForDownloadAndRestore]);

  // 处理分享
  const handleShare = async () => {
    if (!generatedCoverCode) {
      message.warning('请先生成封面，再进行分享');
      return;
    }
    
    // 判断code是否为空
    if (!appState.coverCode && !generatedCoverCode) {
      message.warning('封面代码为空，无法分享');
      return;
    }

    // 直接生成分享链接，而不发送请求
    try {
      setShareLinkDialogOpen(true);
      const coverCode = appState.coverCode || generatedCoverCode;
      const shareUrl = `${window.location.origin}/share/${coverCode}`;
      setShareUrl(shareUrl);
      
      // 复制到剪贴板
      try {
        await navigator.clipboard.writeText(shareUrl);
        message.success('链接已复制到剪贴板');
      } catch (copyError) {
        console.error('复制链接失败:', copyError);
      }
      
      // 如果需要实际创建分享，可以加上后续API调用
      // 但目前API似乎不工作，先简化处理
      /*
      const response = await axios.post('/api/cover/share', { code: coverCode });
      if (response.data.success) {
        const url = `${window.location.origin}/share/${response.data.data.shareCode}`;
        setShareUrl(url);
      } else {
        message.error(response.data.message || '创建分享链接失败');
      }
      */
    } catch (error) {
      logger.error('创建分享链接失败:', error);
      // 更友好的错误提示
      setShareUrl(`${window.location.origin}/share/${appState.coverCode || generatedCoverCode}`);
    }
  };

  // 处理查看源码
  const handleViewSource = async () => {
    if (!appState.previewHtmlContent) {
      console.error('没有可查看的源码');
      message.error('没有可查看的源码');
      return;
    }

    if (loading || isDownloading) {
      message.warning('生成或下载过程中不能查看源码');
      return;
    }

    try {
      // 检查功能是否可用
      const result = await checkFeatureAvailability('源代码查看');
      if (!result.available) {
        message.error(result.reason || '抱歉，该功能只针对vip用户');
        return;
      }

      // 使用原始封面创建页面的handleViewSource函数
      handleViewSourceUtil(previewAreaRef, setShowSourceModal, appState.previewHtmlContent, apiRawResponse);
    } catch (error) {
      console.error('查看源码失败:', error);
      message.error('查看源码失败，请重试');
    }
  };

  // 处理恢复
  const handleRestore = () => {
    if (loading || isDownloading) {
      message.warning('生成或下载过程中不能恢复原始内容');
      return;
    }

    try {
      // 使用apiRawResponse恢复原始内容
      if (!apiRawResponse) {
        message.error('未找到可恢复的HTML内容');
        return;
      }

      let htmlContent = null;

      // 1. 从 apiRawResponse 中提取最原始的HTML内容
      if (typeof apiRawResponse === 'string') {
        htmlContent = apiRawResponse;
      } else if (typeof apiRawResponse === 'object' && apiRawResponse !== null) {
        htmlContent = apiRawResponse.processedHtml || 
                      apiRawResponse.rawHtml || 
                      apiRawResponse.html || 
                      apiRawResponse.html_content || 
                      apiRawResponse.generatedHtml || 
                      (apiRawResponse.data && apiRawResponse.data.html_content);
      }

      if (htmlContent) {
        // 2. 将原始HTML和当前元数据打包，复用 handleLoadCoverData 函数
        const restoredCoverData = {
          html_content: htmlContent, // 使用最原始的HTML
          // 保留当前选择的风格和尺寸，以及ID
          cover_style: appState.selectedStyleId,
          cover_type: appState.selectedSizeId,
          cover_code: appState.coverCode,
          id: appState.coverId,
          // 从当前状态获取其他可能需要的信息
          cover_text: appState.coverData.title,
          subtitle: appState.coverData.subtitle,
          account_name: appState.coverData.accountName,
        };
        
        // 3. 调用已知能正常工作的加载函数，并禁止其更新URL
        handleLoadCoverData(restoredCoverData, 'restore', false);

        message.success('已恢复原始内容');
      } else {
        message.error('未找到可恢复的HTML内容');
      }
    } catch (error) {
      message.error('恢复失败，请重试');
    }
  };

  // 修改全屏预览功能，恢复原有实现
  const handlePreview = useCallback(() => {
    if (!appState.previewHtmlContent) {
      message.warning('预览内容为空，无法全屏预览');
      return;
    }

    // 使用handleOpenPreview函数替代原来的实现
    handleOpenPreview(previewAreaRef, setIsPreviewLoading);
  }, [appState.previewHtmlContent, previewAreaRef, setIsPreviewLoading]);

  // 添加保存功能，调用预览组件的保存方法
  const handleSave = useCallback(() => {
    if (previewAreaRef.current && 
        typeof previewAreaRef.current.handleSave === 'function') {
      // 如果canSave方法存在，先检查是否可以安全保存
      if (typeof previewAreaRef.current.canSave === 'function') {
        if (previewAreaRef.current.canSave()) {
      previewAreaRef.current.handleSave('manual');
        } else {
          message.warning('预览内容正在加载，请稍后再试');
        }
      } else {
        // 向后兼容：直接调用handleSave
        previewAreaRef.current.handleSave('manual');
      }
    } else {
      message.warning('保存功能暂不可用');
    }
  }, [previewAreaRef]);

  const chatPreviewProps = {
    ref: previewAreaRef,
    htmlContent: appState.previewHtmlContent,
    coverImageUrl: appState.previewImageUrl,
    isLoading: loading,
    isGenerating: isGenerating,
    isPreviewActive: appState.isPreviewAreaActive,
    onDownloadCover: handleDownloadCover,
    onDownloadHtml: handleDownloadHtml,
    onShare: handleShare,
    onRestore: handleRestore,
    onViewSource: handleViewSource,
    onPreview: handlePreview, // 添加全屏预览功能
    onSave: handleSave, // 添加保存功能
    coverCode: appState.coverCode,
    coverId: appState.coverId,
    progress: progress,
    feedbackText: feedbackText,
    showSourceModal: showSourceModal,
    setShowSourceModal: setShowSourceModal,
    debugMode: debugMode,
    selectedSizeType: selectedSizeType, // 添加封面尺寸类型，用于调整预览区域大小
    isGenerating: isGenerating, // 添加isGenerating属性，确保生成过程中按钮不可点击
    isPreviewLoading: isPreviewLoading // 添加预览加载状态
  };

  // 确保在加载新HTML内容时关闭风格示例预览
  useEffect(() => {
    // 检查是否有HTML内容并且不是空字符串
    const hasHtmlContent = 
      (appState.previewHtmlContent && appState.previewHtmlContent.trim() !== '') || 
      (generatedHTML && generatedHTML.trim() !== '');
    
    if (hasHtmlContent && !isResetMode) {
      // 如果有实际HTML内容且当前正在显示风格示例，则关闭风格示例
      if (showingStyleExample) {
        setShowingStyleExample(false);
      }
    }
  }, [appState.previewHtmlContent, generatedHTML, showingStyleExample, isResetMode]);

  // 添加点击侧边栏导航项的处理函数
  const handleNavItemClick = (view) => {
    // 定义清除消息的函数
    const clearMessage = () => {
      message.destroy(); // 清除所有消息
    };

    // 定义状态清理的函数
    const cleanupState = () => {
      // 清理消息和生成状态
      clearMessage();
      if (isGenerating) {
        handleCancelGeneration();
      }
      
      // 重置关键状态
      setLoading(false);
      setProgress(0);
      setFeedbackText('');
      setGeneratedHTML(null);
      setIsGenerating(false);
      setPreviewLoaded(false);
      
      // 重置预览区域状态
      setAppState(prev => ({
        ...prev,
        previewHtmlContent: null,
        isPreviewAreaActive: false,
        previewImageUrl: null,
      }));

      // 取消正在进行的任务
      const currentTaskId = getCurrentTaskId();
      if (currentTaskId) {
        cancelCoverGeneration(currentTaskId).catch(err => {
          console.error('取消生成失败:', err);
        });
      }
      
      // 设置是否有未保存更改
      setHasUnsavedChanges(false);
    };

    // 执行导航的函数
    const performNavigation = () => {
      if (view !== activeView) {
        // 执行轻量级状态更新
        setActiveView(view);
        
        // 当切换到非chat视图时，收起输入区域
        if (view !== 'chat' && !isInputAreaCollapsed) {
          toggleInputAreaCollapse(true);
        }
        
        // 清除可能的消息通知
        clearMessage();
        
        // 使用navigateWithSave函数进行页面导航，确保自动保存先执行
        switch(view) {
          case 'chat':
            navigateWithSave('/', { replace: true });
            break;
          case 'profile':
            navigateWithSave('/profile', { replace: true });
            break;
          case 'points':
            navigateWithSave('/points', { replace: true });
            break;
          case 'orders':
            navigateWithSave('/orders', { replace: true });
            break;
          case 'covers':
            navigateWithSave('/covers', { replace: true });
            break;
          case 'my-creations':
            navigateWithSave('/my-creations', { replace: true });
            break;
          case 'membership':
            navigateWithSave('/membership', { replace: true });
            break;
          case 'auth':
            navigateWithSave('/auth', { replace: true });
            break;
          case 'payment':
            navigateWithSave('/payment', { replace: true });
            break;
          case 'payment-result':
            navigateWithSave('/payment-result', { replace: true });
            break;
          default:
            navigateWithSave('/', { replace: true });
        }
      }
    };

    // 检查是否有未保存的更改
    if (hasUnsavedChanges && appState.isPreviewAreaActive && view !== 'chat' && !isGenerating) {
      Modal.confirm({
        title: '离开提示',
        content: '您有未保存的封面内容，离开此页面可能会丢失您的编辑。确定离开吗？',
        okText: '继续离开',
        cancelText: '留在此页',
        onOk: () => {
          // 执行导航（包含状态清理）
          performNavigation();
        },
        onCancel: () => {
          // 用户取消离开，不执行任何操作
        }
      });
    } else {
      // 没有未保存的更改或正在生成中，直接更新视图和URL
      performNavigation();
    }
  };
  
  // 添加取消生成的处理函数
  const handleCancelGeneration = useCallback(async () => {
    try {
      setLoading(false);
      setIsGenerating(false);
      setProgress(0);
      setFeedbackText('');
      // 取消当前任务
      const currentTaskId = getCurrentTaskId();
      if (currentTaskId) {
        await cancelCoverGeneration(currentTaskId);
      }
      message.info('已取消生成');
    } catch (error) {
      console.error('取消生成失败:', error);
    }
  }, []);






  
  // 当AppState中的previewHtmlContent更新时，设置hasUnsavedChanges
  useEffect(() => {
    if (appState.previewHtmlContent && appState.previewHtmlContent.trim() !== '' && appState.isPreviewAreaActive) {
      setHasUnsavedChanges(true);
    }
  }, [appState.previewHtmlContent, appState.isPreviewAreaActive]);

  // 当生成的HTML内容更新时，也设置hasUnsavedChanges
  useEffect(() => {
    if (generatedHTML && generatedHTML.trim() !== '') {
      setHasUnsavedChanges(true);
    }
  }, [generatedHTML]);

  // 处理从localStorage加载待处理的封面数据
  useEffect(() => {
    // 只有当activeView是'chat'时才检查localStorage
    if (activeView === 'chat') {
      const pendingData = localStorage.getItem('pendingCoverData');
      if (pendingData) {
        try {
          const coverData = JSON.parse(pendingData);
          // 加载封面数据
          handleLoadCoverData(coverData, 'sidebar', true);
          // 清除localStorage
          localStorage.removeItem('pendingCoverData');
        } catch (error) {
          console.error('解析等待处理的封面数据失败', error);
        }
      }
    }
  }, [activeView, handleLoadCoverData]);

  // 添加一个单独的useEffect，确保页面刷新后还能保持在当前view
  useEffect(() => {
    // 监听页面加载事件
    const handlePageLoad = () => {
      const pathname = location.pathname;
      
      // 根据路径确定当前视图
      if (pathname === '/' || pathname === '/home') {
        setActiveView('chat');
      } else if (pathname === '/profile') {
        setActiveView('profile');
      } else if (pathname === '/points') {
        setActiveView('points');
      } else if (pathname === '/orders') {
        setActiveView('orders');
      } else if (pathname === '/covers') {
        setActiveView('covers');
      } else if (pathname === '/my-creations') {
        setActiveView('my-creations');
      } else if (pathname === '/auth') {
        setActiveView('auth');
      } else if (pathname === '/payment') {
        setActiveView('payment');
      } else if (pathname === '/payment-result') {
        setActiveView('payment-result');
      } else if (pathname === '/membership') {
        setActiveView('membership');
      } else if (pathname === '/file-upload') {
        setActiveView('file-upload');
      } else if (pathname === '/code-paste') {
        setActiveView('code-paste');
      } else {
        setActiveView('chat');
      }
    };

    // 页面加载时立即执行一次
    handlePageLoad();

    // 监听popstate事件，处理浏览器前进后退操作
    window.addEventListener('popstate', handlePageLoad);
    
    return () => {
      window.removeEventListener('popstate', handlePageLoad);
    };
  }, [location.pathname]);

  // 添加事件监听器，响应从MainLayout触发的自定义事件
  useEffect(() => {
    // 监听重置设计事件
    const handleResetEvent = () => {
      performReset();
    };

    // 监听加载封面数据事件
    const handleLoadCoverEvent = (event) => {
      if (event.detail && event.detail.coverData) {
        handleLoadCoverData(event.detail.coverData);
      }
    };

    // 监听视图变更事件
    const handleViewChangeEvent = (event) => {
      if (event.detail && event.detail.view) {
        setActiveView(event.detail.view);
      }
    };

    // 添加事件监听器
    window.addEventListener('resetDesign', handleResetEvent);
    window.addEventListener('loadCoverData', handleLoadCoverEvent);
    window.addEventListener('viewChange', handleViewChangeEvent);

    // 发送未保存更改状态到MainLayout
    const sendUnsavedChangesStatus = () => {
      const event = new CustomEvent('unsavedChanges', { 
        detail: { hasUnsavedChanges: hasUnsavedChanges } 
      });
      window.dispatchEvent(event);
    };

    // 当hasUnsavedChanges状态变化时，通知MainLayout
    sendUnsavedChangesStatus();

    // 清理函数
    return () => {
      window.removeEventListener('resetDesign', handleResetEvent);
      window.removeEventListener('loadCoverData', handleLoadCoverEvent);
      window.removeEventListener('viewChange', handleViewChangeEvent);
    };
  }, [hasUnsavedChanges, performReset]);

  // 添加事件监听器来监听expandInputArea自定义事件
  useEffect(() => {
    const handleExpandInputArea = () => {
      setIsInputAreaCollapsed(false);
    };

    window.addEventListener('expandInputArea', handleExpandInputArea);

    return () => {
      window.removeEventListener('expandInputArea', handleExpandInputArea);
    };
  }, []);

  // 添加事件监听器来监听showInputArea自定义事件
  useEffect(() => {
    const handleShowInputArea = () => {
      setShowInputArea(true);
    };

    window.addEventListener('showInputArea', handleShowInputArea);

    return () => {
      window.removeEventListener('showInputArea', handleShowInputArea);
    };
  }, []);

  return (
    <>
      {/* 查看源码模态框 */}
      <ViewSourceModal
        visible={showSourceModal}
        onClose={() => setShowSourceModal(false)}
        htmlContent={generatedHTML}
        apiResponse={apiRawResponse}
      />

      {/* 分享链接对话框 */}
      <Modal
        title="分享封面"
        open={shareLinkDialogOpen}
        onCancel={() => setShareLinkDialogOpen(false)}
        footer={[
          <button
            key="close"
            onClick={() => setShareLinkDialogOpen(false)}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
          >
            关闭
          </button>,
          <button
            key="copy"
            onClick={async () => {
              try {
                await navigator.clipboard.writeText(shareUrl);
                message.success('链接已复制到剪贴板');
              } catch (error) {
                console.error('复制失败:', error);
                message.error('复制失败，请手动复制');
              }
            }}
            className="ml-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            复制链接
          </button>
        ]}
        styles={{ body: { padding: '16px' } }}
      >
        <div className="mt-4">
          <p className="mb-2">您可以通过以下链接分享此封面：</p>
          <div className="flex items-center">
            <input
              type="text"
              value={shareUrl}
              readOnly
              className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <p className="mt-4 text-sm text-gray-500">
            注意：分享链接有效期为30天，请及时保存。
          </p>
        </div>
      </Modal>

      {/* 主内容区域 - 删除了左侧边栏，直接显示右侧区域 */}
      <div className="flex-1 flex flex-col overflow-hidden relative bg-gray-100 h-full"> {/* 添加背景色, 确保高度充满 */}
        {/* 主内容区: 完全占据顶部区域空间 */}
        <main className={`flex-1 overflow-hidden relative ${showingStyleExample ? 'p-0 flex items-center justify-center' : 'p-4 md:p-6 lg:p-8 px-4 md:px-6 lg:px-8'}`}>
          {showingStyleExample ? (
            // 如果正在显示风格示例HTML，显示StyleExamplePreview组件
            <StyleExamplePreview 
              htmlContent={styleExampleHtml} 
              onClose={handleCloseStyleExample} 
            />
          ) : (
            // 否则显示正常的预览区域
            <ChatMainArea
              chatPreviewProps={{
                isPreviewActive: appState.isPreviewAreaActive,
                htmlContent: appState.previewHtmlContent,
                coverImageUrl: appState.previewImageUrl,
                isDownloading: isDownloading,
                onLoadComplete: handlePreviewLoadComplete,
                showStyleExample: showingStyleExample,
                styleExampleHtml: styleExampleHtml,
                onCloseStyleExample: handleCloseStyleExample,
                showTip: !justGenerated && !appState.isPreviewActive,
                // 添加缺失的功能按钮回调
                onDownloadCover: handleDownloadCover,
                onDownloadHtml: handleDownloadHtml,
                onShare: handleShare,
                onRestore: handleRestore,
                onViewSource: handleViewSource,
                onPreview: handlePreview, // 添加全屏预览功能
                onSave: handleSave, // 添加保存功能
                coverCode: appState.coverCode,
                coverId: appState.coverId,
                progress: progress,
                feedbackText: feedbackText,
                showSourceModal: showSourceModal,
                setShowSourceModal: setShowSourceModal,
                debugMode: debugMode,
                selectedSizeType: selectedSizeType,
                isGenerating: isGenerating, // 添加isGenerating属性，确保生成过程中按钮不可点击
                isPreviewLoading: isPreviewLoading // 添加预览加载状态
              }}
              previewRef={previewAreaRef}
              activeView={activeView} // 传递activeView到ChatMainArea
            />
          )}
        </main>

        {/* 输入区容器 - 作为底部固定的元素，与main平行，宽度自适应编辑台 */}
        {activeView === 'chat' && showInputArea && (
          <div className="px-2 pb-2 md:px-4 md:pb-3 lg:px-6 lg:pb-4 mt-auto bg-transparent z-10"
               style={{ 
                 marginTop: '-20px',
                 width: appState.isPreviewAreaActive ? 'calc(100% - 320px)' : '100%',
                 transition: 'width 300ms ease-in-out'
               }}>
            <div className="max-w-4xl mx-auto">
              <ChatInputArea
                // 折叠控制
                isCollapsed={isInputAreaCollapsed}
                toggleCollapse={toggleInputAreaCollapse}
                // 表单数据和回调
                coverData={coverData}
                onInputChange={handleInputChange}
                onFormSubmit={handleFormSubmit}
                // 尺寸和风格
                selectedSizeId={appState.selectedSizeId}
                onSizeChange={handleAppStateSizeChange}
                selectedStyleId={appState.selectedStyleId}
                onStyleChange={handleAppStateStyleChange}
                // 生成状态
                isGenerating={isGenerating}
                onCancel={handleCancelGeneration}
                // 其他
                onShowStyleExample={handleShowStyleExample}
                isPreviewLoaded={previewLoaded || appState.isPreviewAreaActive}
                setFeedbackText={setFeedbackText}
                coverId={appState.coverId}
                coverCode={appState.coverCode}
                ref={inputAreaRef} // 添加ref
              />


            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default ChatGenerate;


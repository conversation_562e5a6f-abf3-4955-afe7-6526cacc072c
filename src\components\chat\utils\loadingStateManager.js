/**
 * 加载状态管理器 - 优化用户体验
 * 第二十阶段：用户体验优化
 */

import logger from '../../../services/logs/frontendLogger';

/**
 * 加载状态类型
 */
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning'
};

/**
 * 加载阶段
 */
export const LOADING_PHASES = {
  INITIALIZING: 'initializing',
  PARSING_HTML: 'parsing_html',
  LOADING_RESOURCES: 'loading_resources',
  SETTING_UP_EDITOR: 'setting_up_editor',
  FINALIZING: 'finalizing'
};

/**
 * 加载状态管理器类
 */
class LoadingStateManager {
  constructor() {
    this.currentState = LOADING_STATES.IDLE;
    this.currentPhase = null;
    this.progress = 0;
    this.message = '';
    this.startTime = null;
    this.phaseStartTime = null;
    this.listeners = new Set();
    this.phaseProgress = new Map();
  }

  /**
   * 添加状态监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.add(listener);
  }

  /**
   * 移除状态监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    this.listeners.delete(listener);
  }

  /**
   * 通知所有监听器
   */
  notifyListeners() {
    const stateInfo = {
      state: this.currentState,
      phase: this.currentPhase,
      progress: this.progress,
      message: this.message,
      duration: this.startTime ? Date.now() - this.startTime : 0,
      phaseDuration: this.phaseStartTime ? Date.now() - this.phaseStartTime : 0
    };

    this.listeners.forEach(listener => {
      try {
        listener(stateInfo);
      } catch (error) {
        logger.error('加载状态监听器错误', { error: error.message });
      }
    });
  }

  /**
   * 开始加载
   * @param {string} message - 加载消息
   */
  startLoading(message = '正在加载...') {
    this.currentState = LOADING_STATES.LOADING;
    this.currentPhase = null;
    this.progress = 0;
    this.message = message;
    this.startTime = Date.now();
    this.phaseStartTime = null;
    this.phaseProgress.clear();

    logger.info('开始加载', { message });
    this.notifyListeners();
  }

  /**
   * 设置加载阶段
   * @param {string} phase - 加载阶段
   * @param {string} message - 阶段消息
   * @param {number} progress - 进度百分比
   */
  setPhase(phase, message = '', progress = null) {
    this.currentPhase = phase;
    this.phaseStartTime = Date.now();
    
    if (message) {
      this.message = message;
    }
    
    if (progress !== null) {
      this.progress = Math.max(0, Math.min(100, progress));
    } else {
      // 自动计算进度
      this.progress = this.calculateAutoProgress(phase);
    }

    logger.debug('设置加载阶段', { phase, message, progress: this.progress });
    this.notifyListeners();
  }

  /**
   * 自动计算进度
   * @param {string} phase - 当前阶段
   * @returns {number} 进度百分比
   */
  calculateAutoProgress(phase) {
    const phaseWeights = {
      [LOADING_PHASES.INITIALIZING]: 10,
      [LOADING_PHASES.PARSING_HTML]: 25,
      [LOADING_PHASES.LOADING_RESOURCES]: 40,
      [LOADING_PHASES.SETTING_UP_EDITOR]: 20,
      [LOADING_PHASES.FINALIZING]: 5
    };

    let totalProgress = 0;
    const phases = Object.keys(phaseWeights);
    const currentIndex = phases.indexOf(phase);

    // 计算已完成阶段的进度
    for (let i = 0; i < currentIndex; i++) {
      totalProgress += phaseWeights[phases[i]];
    }

    // 添加当前阶段的部分进度
    if (currentIndex >= 0) {
      totalProgress += phaseWeights[phase] * 0.5; // 当前阶段算50%完成
    }

    return Math.min(100, totalProgress);
  }

  /**
   * 更新进度
   * @param {number} progress - 进度百分比
   * @param {string} message - 可选的消息更新
   */
  updateProgress(progress, message = null) {
    this.progress = Math.max(0, Math.min(100, progress));
    
    if (message) {
      this.message = message;
    }

    this.notifyListeners();
  }

  /**
   * 设置成功状态
   * @param {string} message - 成功消息
   */
  setSuccess(message = '加载完成') {
    this.currentState = LOADING_STATES.SUCCESS;
    this.currentPhase = null;
    this.progress = 100;
    this.message = message;

    const duration = this.startTime ? Date.now() - this.startTime : 0;
    logger.info('加载成功', { message, duration });
    this.notifyListeners();

    // 3秒后自动重置为空闲状态
    setTimeout(() => {
      if (this.currentState === LOADING_STATES.SUCCESS) {
        this.reset();
      }
    }, 3000);
  }

  /**
   * 设置错误状态
   * @param {string} message - 错误消息
   * @param {Error} error - 错误对象
   */
  setError(message = '加载失败', error = null) {
    this.currentState = LOADING_STATES.ERROR;
    this.currentPhase = null;
    this.message = message;

    const duration = this.startTime ? Date.now() - this.startTime : 0;
    logger.error('加载失败', { message, error: error?.message, duration });
    this.notifyListeners();
  }

  /**
   * 设置警告状态
   * @param {string} message - 警告消息
   */
  setWarning(message = '加载完成但有警告') {
    this.currentState = LOADING_STATES.WARNING;
    this.currentPhase = null;
    this.progress = 100;
    this.message = message;

    logger.warn('加载警告', { message });
    this.notifyListeners();

    // 5秒后自动重置为空闲状态
    setTimeout(() => {
      if (this.currentState === LOADING_STATES.WARNING) {
        this.reset();
      }
    }, 5000);
  }

  /**
   * 重置状态
   */
  reset() {
    this.currentState = LOADING_STATES.IDLE;
    this.currentPhase = null;
    this.progress = 0;
    this.message = '';
    this.startTime = null;
    this.phaseStartTime = null;
    this.phaseProgress.clear();

    this.notifyListeners();
  }

  /**
   * 获取当前状态信息
   * @returns {Object} 状态信息
   */
  getStateInfo() {
    return {
      state: this.currentState,
      phase: this.currentPhase,
      progress: this.progress,
      message: this.message,
      duration: this.startTime ? Date.now() - this.startTime : 0,
      phaseDuration: this.phaseStartTime ? Date.now() - this.phaseStartTime : 0,
      isLoading: this.currentState === LOADING_STATES.LOADING,
      isSuccess: this.currentState === LOADING_STATES.SUCCESS,
      isError: this.currentState === LOADING_STATES.ERROR,
      isWarning: this.currentState === LOADING_STATES.WARNING,
      isIdle: this.currentState === LOADING_STATES.IDLE
    };
  }

  /**
   * 创建阶段管理器
   * @param {Array} phases - 阶段列表
   * @returns {Object} 阶段管理器
   */
  createPhaseManager(phases) {
    let currentPhaseIndex = 0;
    
    return {
      nextPhase: (message = '') => {
        if (currentPhaseIndex < phases.length) {
          const phase = phases[currentPhaseIndex];
          this.setPhase(phase, message);
          currentPhaseIndex++;
          return phase;
        }
        return null;
      },
      
      getCurrentPhase: () => {
        return currentPhaseIndex > 0 ? phases[currentPhaseIndex - 1] : null;
      },
      
      getRemainingPhases: () => {
        return phases.slice(currentPhaseIndex);
      },
      
      isLastPhase: () => {
        return currentPhaseIndex >= phases.length;
      }
    };
  }
}

// 创建全局加载状态管理器实例
const loadingStateManager = new LoadingStateManager();

// 导出管理器实例和相关常量
export default loadingStateManager;
export { loadingStateManager };

/**
 * 便捷函数：开始加载
 */
export const startLoading = (message) => {
  loadingStateManager.startLoading(message);
};

/**
 * 便捷函数：设置阶段
 */
export const setLoadingPhase = (phase, message, progress) => {
  loadingStateManager.setPhase(phase, message, progress);
};

/**
 * 便捷函数：更新进度
 */
export const updateLoadingProgress = (progress, message) => {
  loadingStateManager.updateProgress(progress, message);
};

/**
 * 便捷函数：设置成功
 */
export const setLoadingSuccess = (message) => {
  loadingStateManager.setSuccess(message);
};

/**
 * 便捷函数：设置错误
 */
export const setLoadingError = (message, error) => {
  loadingStateManager.setError(message, error);
};

/**
 * 便捷函数：设置警告
 */
export const setLoadingWarning = (message) => {
  loadingStateManager.setWarning(message);
};

/**
 * 便捷函数：重置状态
 */
export const resetLoadingState = () => {
  loadingStateManager.reset();
};

/**
 * 便捷函数：获取状态信息
 */
export const getLoadingStateInfo = () => {
  return loadingStateManager.getStateInfo();
};

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 基础提示词模板模型
 * 对应数据库中的base_prompts表
 */
const BasePrompt = sequelize.define('BasePrompt', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '模板ID，唯一标识'
  },
  id_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '封面模板唯一标识码，英文字母、数字和下划线',
    unique: true
  },
  cover_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '封面类型中文名称，用于前端显示'
  },
  cover_size: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '封面尺寸规格'
  },
  prompt_content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '基础提示词模板内容'
  }
}, {
  tableName: 'base_prompts',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

module.exports = BasePrompt;

/**
 * API密钥池管理器 - 简化版本
 */

const { AIServiceConfig } = require('../models');
const { decryptApiKey } = require('./encryption');
const logger = require('./logger');

// 池状态
let apiKeyPool = [];
let isInitialized = false;
let currentIndex = 0;

// 密钥使用状态
let keyStatus = new Map();

// 默认重试次数
const DEFAULT_RETRY_COUNT = 3;

// 默认最大并发请求数（当服务配置为无限制时使用）
const DEFAULT_MAX_CONCURRENT_REQUESTS = 10;

/**
 * 初始化密钥池
 */
const initKeyPool = async () => {
  try {
    await refreshKeyPool();
    
    // 检查是否有活跃的服务，如果没有则激活第一个
    if (apiKeyPool.length === 0) {
      const firstService = await AIServiceConfig.findOne({
        order: [['id', 'ASC']]
      });
      
      if (firstService) {
        firstService.is_active = true;
        await firstService.save();
        logger.info(`没有活跃的API服务，已自动激活第一个可用服务: ${firstService.service_name}`);
        
        // 重新刷新密钥池以加载刚刚激活的服务
        await refreshKeyPool();
      }
    }
    
    isInitialized = true;
    logger.info(`API密钥池初始化完成，共加载 ${apiKeyPool.length} 个活跃服务`);
  } catch (error) {
    logger.error('API密钥池初始化失败:', error);
  }
};

/**
 * 刷新密钥池
 */
const refreshKeyPool = async () => {
  try {
    // 获取所有启用的AI服务配置，改为查询所有活跃的服务
    const services = await AIServiceConfig.findAll({
      where: { is_active: true }
    });

    // 保存现有状态以便重置
    const oldKeyStatus = keyStatus;
    
    // 重置
    apiKeyPool = [];
    keyStatus = new Map();

    // 处理每个服务配置
    for (const service of services) {
      try {
        // 解密API密钥
        let apiKeyData = service.api_key;
        try {
          if (apiKeyData.startsWith('"') && apiKeyData.endsWith('"')) {
            apiKeyData = JSON.parse(apiKeyData);
          }
        } catch (e) {
          logger.warn(`API密钥格式解析失败: ${service.id}`, e);
          continue;
        }
        
        const apiKey = decryptApiKey(apiKeyData);
        
        // 构建密钥条目
        const keyEntry = {
          id: service.id,
          serviceName: service.service_name,
          baseUrl: service.base_url,
          apiKey: apiKey,
          modelName: service.model_name,
          requestFormat: service.request_format ? JSON.parse(service.request_format) : null,
          responseFormat: service.response_format ? JSON.parse(service.response_format) : null,
          maxConcurrentRequests: service.max_concurrent_requests || 5,
          priority: service.priority || 0,
          weight: service.weight || 1
        };
        
        // 添加到池中
        apiKeyPool.push(keyEntry);
        
        // 密钥ID
        const keyId = `${service.id}-${service.serviceName}-${apiKey.substring(0, 5)}`;
        
        // 初始化或继承使用状态
        keyStatus.set(keyId, oldKeyStatus.get(keyId) || {
          activeRequests: 0,
          totalRequests: 0,
          failures: 0,
          lastUsed: 0
        });
        
        logger.info(`已加载API服务: ${service.service_name}, ID: ${service.id}, 并发限制: ${keyEntry.maxConcurrentRequests}`);
      } catch (error) {
        logger.error(`加载API服务失败: ${service.id}`, error);
      }
    }
    
    logger.info(`已刷新API密钥池, 共 ${apiKeyPool.length} 个活跃服务`);
    return apiKeyPool.length;
  } catch (error) {
    logger.error('刷新API密钥池失败:', error);
    return 0;
  }
};

/**
 * 获取下一个可用的API服务
 * @param {number} retryCount - 重试次数
 * @returns {Promise<Object>} 可用的API服务信息
 */
const getNextApiService = async (retryCount = DEFAULT_RETRY_COUNT) => {
  // 确保初始化完成
  if (!isInitialized) {
    await initKeyPool();
  }

  // 如果密钥池为空，强制刷新
  if (apiKeyPool.length === 0) {
    await refreshKeyPool();
    if (apiKeyPool.length === 0) {
      throw new Error('API密钥池为空');
    }
  }
  
  // 找出所有未达到并发上限的API服务
  const availableApis = [];
  for (let i = 0; i < apiKeyPool.length; i++) {
    const service = apiKeyPool[i];
    const keyId = `${service.id}-${service.serviceName}-${service.apiKey.substring(0, 5)}`;
    const status = keyStatus.get(keyId) || { activeRequests: 0, totalRequests: 0, failures: 0, lastUsed: 0 };
    const maxRequests = service.maxConcurrentRequests > 0 ? service.maxConcurrentRequests : DEFAULT_MAX_CONCURRENT_REQUESTS;
    
    if (status.activeRequests < maxRequests) {
      availableApis.push({
        index: i,
        service,
        keyId,
        status
      });
    }
  }
  
  // 如果没有可用API，并且还有重试机会，刷新并重试
  if (availableApis.length === 0) {
    if (retryCount > 0) {
      logger.warn(`所有API密钥都已达到并发限制，刷新后重试，剩余重试次数: ${retryCount}`);
      await refreshKeyPool();
      return getNextApiService(retryCount - 1);
    }
    throw new Error('所有API服务都已达到并发限制');
  }
  
  // 1. 先找出所有完全空闲的API（activeRequests = 0）
  const idleApis = availableApis.filter(item => item.status.activeRequests === 0);
  
  let selectedApiInfo;
  
  if (idleApis.length > 0) {
    // 如果有空闲API，以轮询方式选择一个
    
    // 根据当前索引计算下一个要使用的API的索引
    // 为了实现真正的轮询，我们按照API在池中的顺序排序
    const sortedIdleApis = [...idleApis].sort((a, b) => {
      // 计算相对于currentIndex的距离，确保循环
      const distA = (a.index - currentIndex + apiKeyPool.length) % apiKeyPool.length;
      const distB = (b.index - currentIndex + apiKeyPool.length) % apiKeyPool.length;
      
      // 选择距离当前索引最近但不是当前索引的（确保轮询）
      // 注意：距离为0表示是当前索引，我们想要下一个，所以优先选择距离最小但大于0的
      if (distA === 0 && distB > 0) return 1;  // b优先
      if (distB === 0 && distA > 0) return -1; // a优先
      return distA - distB;
    });
    
    // 如果没有空闲API在currentIndex之后，就从第一个空闲API开始
    selectedApiInfo = sortedIdleApis[0];
    
    // 更新currentIndex为选中的API索引，为下次选择做准备
    currentIndex = selectedApiInfo.index;
  } else {
    // 2. 如果没有空闲API，选择负载最轻且最早使用的API
    selectedApiInfo = availableApis.reduce((best, current) => {
      // 优先选择活跃请求数最少的
      if (current.status.activeRequests < best.status.activeRequests) {
        return current;
      }
      
      // 活跃请求数相同时，处理lastUsed
      if (current.status.activeRequests === best.status.activeRequests) {
        // 特殊处理lastUsed为0的情况（表示从未使用过）
        if (current.status.lastUsed === 0 && best.status.lastUsed > 0) {
          return current; // 优先选择从未使用过的
        }
        if (best.status.lastUsed === 0 && current.status.lastUsed > 0) {
          return best;
        }
        
        // 两者都使用过或都未使用过，选择最早使用的（或随机一个）
        if ((current.status.lastUsed < best.status.lastUsed && current.status.lastUsed > 0) || 
            (current.status.lastUsed === 0 && best.status.lastUsed === 0)) {
          return current;
        }
      }
      return best;
    }, availableApis[0]);
    
    // 更新currentIndex为选中的API索引，为下次选择做准备
    currentIndex = selectedApiInfo.index;
  }
  
  // 更新所选API的使用状态
  const useStatus = selectedApiInfo.status;
  useStatus.activeRequests++;
  useStatus.totalRequests++;
  useStatus.lastUsed = Date.now();
  keyStatus.set(selectedApiInfo.keyId, useStatus);
  
  logger.debug(`使用API密钥: ${selectedApiInfo.keyId}, 活跃: ${useStatus.activeRequests}, 总计: ${useStatus.totalRequests}, 索引: ${selectedApiInfo.index}, 选择策略: ${idleApis.length > 0 ? '轮询空闲API' : '最早使用API'}`);
  
  return { ...selectedApiInfo.service, keyId: selectedApiInfo.keyId };
};

/**
 * 释放API服务
 * @param {string} keyId - 密钥ID
 * @param {boolean} success - 请求是否成功
 */
const releaseApiService = (keyId, success = true) => {
  if (!keyId || !keyStatus.has(keyId)) {
    return false;
  }
  
  const status = keyStatus.get(keyId);
  status.activeRequests = Math.max(0, status.activeRequests - 1);
  
  if (!success) {
    status.failures++;
  }
  
  return true;
};

/**
 * 获取密钥池状态
 */
const getKeyPoolStatus = () => {
  const poolStatus = apiKeyPool.map(service => {
    const keyId = `${service.id}-${service.serviceName}-${service.apiKey.substring(0, 5)}`;
    const status = keyStatus.get(keyId) || { activeRequests: 0, totalRequests: 0, failures: 0, lastUsed: 0 };
    
    return {
      id: service.id,
      name: service.serviceName,
      model: service.modelName,
      activeRequests: status.activeRequests,
      totalRequests: status.totalRequests,
      failures: status.failures,
      maxConcurrent: service.maxConcurrentRequests,
      lastUsed: status.lastUsed > 0 ? new Date(status.lastUsed).toISOString() : null
    };
  });
  
  return {
    totalKeys: apiKeyPool.length,
    initialized: isInitialized,
    keys: poolStatus
  };
};

module.exports = {
  getNextApiService,
  releaseApiService,
  refreshKeyPool,
  getKeyPoolStatus
};

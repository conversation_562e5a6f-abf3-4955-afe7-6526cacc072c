const { User } = require('../models');
const { successResponse, errorResponse } = require('../utils/responseUtils');
const { sendSmsCode, verifySmsCode } = require('../utils/smsCodeManager');
const logger = require('../utils/logger');

/**
 * 发送验证码
 * @route POST /api/sms/send
 */
const sendVerificationCode = async (req, res) => {
  const { phone, purpose } = req.body;

  try {
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return errorResponse(res, '手机号格式不正确', 400);
    }

    // 验证用途
    if (!['login', 'register', 'reset_password'].includes(purpose)) {
      return errorResponse(res, '无效的验证码用途', 400);
    }

    // 检查是否存在用户
    const existingUser = await User.findOne({ where: { phone } });

    // 如果是注册，检查用户是否已存在
    if (purpose === 'register' && existingUser) {
      return errorResponse(res, '该手机号已注册', 400);
    }

    // 如果是重置密码，检查用户是否存在
    if (purpose === 'reset_password' && !existingUser) {
      return errorResponse(res, '该手机号未注册', 404);
    }

    // 登录时不再检查用户是否存在，因为我们支持未注册用户通过验证码登录自动注册

    // 发送验证码
    const result = await sendSmsCode(phone, purpose);

    if (result.success) {
      logger.info(`向手机号 ${phone} 发送${purpose}验证码成功`);
      return successResponse(res, result.message, {
        // 开发环境返回验证码，方便测试
        devCode: result.devCode
      });
    } else {
      logger.error(`向手机号 ${phone} 发送${purpose}验证码失败: ${result.message}`);
      return errorResponse(res, result.message, 500);
    }
  } catch (error) {
    logger.error('发送验证码失败:', error);
    return errorResponse(res, '发送验证码失败，请稍后再试', 500);
  }
};

/**
 * 验证验证码
 * @route POST /api/sms/verify
 */
const verifyCode = async (req, res) => {
  const { phone, verifyCode, purpose } = req.body;

  try {
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return errorResponse(res, '手机号格式不正确', 400);
    }

    // 验证用途
    if (!['login', 'register', 'reset_password'].includes(purpose)) {
      return errorResponse(res, '无效的验证码用途', 400);
    }

    // 验证验证码
    const isValid = await verifySmsCode(phone, verifyCode, purpose);

    if (isValid) {
      logger.info(`验证码验证成功，手机号: ${phone}, 用途: ${purpose}`);
      return successResponse(res, '验证码验证成功');
    } else {
      logger.warn(`验证码验证失败，手机号: ${phone}, 用途: ${purpose}`);
      return errorResponse(res, '验证码无效或已过期', 400);
    }
  } catch (error) {
    logger.error('验证验证码失败:', error);
    return errorResponse(res, '验证失败，请稍后再试', 500);
  }
};

module.exports = {
  sendVerificationCode,
  verifyCode
};

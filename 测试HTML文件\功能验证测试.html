<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML功能完整性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .interactive-button {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .interactive-button:hover {
            background: #ff5252;
            transform: translateY(-2px);
        }
        .result-display {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            min-height: 50px;
        }
        .navigation-tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            background: rgba(255,255,255,0.2);
            border: none;
            padding: 10px 20px;
            color: white;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background: rgba(255,255,255,0.4);
        }
        .tab-content {
            display: none;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 0 10px 10px 10px;
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: none;
            border-radius: 5px;
            background: rgba(255,255,255,0.9);
            color: #333;
        }
    </style>
</head>
<body>
    <h1>🧪 HTML功能完整性测试页面</h1>
    <p>此页面用于测试高级模式是否完整保留HTML的所有交互功能</p>

    <!-- 导航标签测试 -->
    <div class="navigation-tabs">
        <button class="tab active" onclick="showTab('basic')">基础交互</button>
        <button class="tab" onclick="showTab('advanced')">高级功能</button>
        <button class="tab" onclick="showTab('form')">表单处理</button>
    </div>

    <!-- 基础交互测试 -->
    <div id="basic" class="tab-content active">
        <div class="test-section">
            <h3>🎯 基础交互功能测试</h3>
            <button class="interactive-button" onclick="testBasicClick()">点击测试</button>
            <button class="interactive-button" onclick="testCounter()">计数器测试</button>
            <button class="interactive-button" onclick="testColorChange()">颜色变化测试</button>
            <div id="basicResult" class="result-display">等待测试...</div>
        </div>
    </div>

    <!-- 高级功能测试 -->
    <div id="advanced" class="tab-content">
        <div class="test-section">
            <h3>⚡ 高级JavaScript功能测试</h3>
            <button class="interactive-button" onclick="testLocalStorage()">本地存储测试</button>
            <button class="interactive-button" onclick="testAjaxSimulation()">AJAX模拟测试</button>
            <button class="interactive-button" onclick="testDynamicContent()">动态内容生成</button>
            <div id="advancedResult" class="result-display">等待测试...</div>
        </div>
    </div>

    <!-- 表单处理测试 -->
    <div id="form" class="tab-content">
        <div class="test-section">
            <h3>📝 表单处理功能测试</h3>
            <form onsubmit="return testFormSubmit(event)">
                <div class="form-group">
                    <label>姓名：</label>
                    <input type="text" id="userName" placeholder="请输入姓名">
                </div>
                <div class="form-group">
                    <label>邮箱：</label>
                    <input type="email" id="userEmail" placeholder="请输入邮箱">
                </div>
                <div class="form-group">
                    <label>留言：</label>
                    <textarea id="userMessage" rows="4" placeholder="请输入留言"></textarea>
                </div>
                <button type="submit" class="interactive-button">提交表单</button>
            </form>
            <div id="formResult" class="result-display">等待表单提交...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let clickCount = 0;
        let currentColor = '#ff6b6b';

        // 标签切换功能
        function showTab(tabName) {
            // 隐藏所有标签内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 基础交互测试
        function testBasicClick() {
            document.getElementById('basicResult').innerHTML = 
                `✅ 基础点击功能正常！时间：${new Date().toLocaleTimeString()}`;
        }

        function testCounter() {
            clickCount++;
            document.getElementById('basicResult').innerHTML = 
                `🔢 计数器功能正常！当前计数：${clickCount}`;
        }

        function testColorChange() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
            currentColor = colors[Math.floor(Math.random() * colors.length)];
            document.body.style.background = `linear-gradient(135deg, ${currentColor} 0%, #764ba2 100%)`;
            document.getElementById('basicResult').innerHTML = 
                `🎨 颜色变化功能正常！当前颜色：${currentColor}`;
        }

        // 高级功能测试
        function testLocalStorage() {
            const testData = { test: true, timestamp: Date.now() };
            localStorage.setItem('htmlTest', JSON.stringify(testData));
            const retrieved = JSON.parse(localStorage.getItem('htmlTest'));
            document.getElementById('advancedResult').innerHTML = 
                `💾 本地存储功能正常！存储时间：${new Date(retrieved.timestamp).toLocaleTimeString()}`;
        }

        function testAjaxSimulation() {
            // 模拟AJAX请求
            setTimeout(() => {
                document.getElementById('advancedResult').innerHTML = 
                    `🌐 AJAX模拟功能正常！模拟数据加载完成`;
            }, 1000);
            document.getElementById('advancedResult').innerHTML = '⏳ 正在模拟AJAX请求...';
        }

        function testDynamicContent() {
            const dynamicDiv = document.createElement('div');
            dynamicDiv.style.cssText = 'background: rgba(255,255,255,0.2); padding: 10px; margin: 10px 0; border-radius: 5px;';
            dynamicDiv.innerHTML = `🚀 动态生成的内容 - ${new Date().toLocaleString()}`;
            document.getElementById('advancedResult').appendChild(dynamicDiv);
        }

        // 表单处理测试
        function testFormSubmit(event) {
            event.preventDefault();
            const name = document.getElementById('userName').value;
            const email = document.getElementById('userEmail').value;
            const message = document.getElementById('userMessage').value;
            
            document.getElementById('formResult').innerHTML = 
                `📋 表单提交功能正常！<br>
                姓名：${name}<br>
                邮箱：${email}<br>
                留言：${message}<br>
                提交时间：${new Date().toLocaleString()}`;
            
            return false;
        }

        // 页面加载完成提示
        window.onload = function() {
            console.log('🎉 HTML功能完整性测试页面加载完成！');
            console.log('📝 测试说明：');
            console.log('1. 如果所有按钮都能正常点击并显示结果，说明JavaScript功能正常');
            console.log('2. 如果标签切换正常，说明DOM操作功能正常');
            console.log('3. 如果表单能正常提交，说明表单处理功能正常');
            console.log('4. 如果本地存储测试正常，说明浏览器API功能正常');
        };
    </script>
</body>
</html>

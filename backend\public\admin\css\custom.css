/**
 * 自定义样式，解决内联样式问题
 */

/* 预览图像样式 */
.preview-image {
  max-width: 200px;
  max-height: 200px;
}

/* 日志详情样式 */
.log-detail-info {
  max-height: 300px;
  overflow-y: auto;
}

/* 分页大小选择下拉框 */
.page-size-select {
  width: 80px;
}

/* iframe预览容器 */
.preview-iframe-container {
  width: 100%;
  height: 500px;
  border: none;
}

/* 封面预览图片 */
.cover-preview-image {
  max-width: 100%;
  margin-top: 20px;
}

/* 加载遮罩层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  color: white;
  font-size: 1.2rem;
}

/* AI聊天测试界面样式 */
.chat-messages {
  max-height: 500px;
  overflow-y: auto;
}

/* 系统监控样式 */
.health-status-excellent {
  color: #28a745 !important;
}

.health-status-good {
  color: #ffc107 !important;
}

.health-status-fair {
  color: #fd7e14 !important;
}

.health-status-poor {
  color: #dc3545 !important;
}

#performanceChart {
  max-height: 400px;
}

.stat-card {
  transition: transform 0.2s ease-in-out;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.chat-message {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: flex-start;
}

.chat-message.user {
  justify-content: flex-end;
}

.chat-message.ai {
  justify-content: flex-start;
}

.chat-message .message-content {
  max-width: 75%;
  padding: 0.875rem 1rem;
  border-radius: 1.125rem;
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.chat-message.user .message-content {
  order: 0;
  background-color: #007bff;
  color: white;
  border-bottom-right-radius: 0.25rem;
}

.chat-message.ai .message-content {
  order: 1;
  background-color: #ffffff;
  color: #333;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 0.25rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.chat-message .message-time {
  font-size: 0.7rem;
  color: #6c757d;
  margin-top: 0.5rem;
  opacity: 0.8;
}

.chat-message.user .message-time {
  text-align: left;
  color: rgba(255, 255, 255, 0.8);
}

.chat-message.ai .message-time {
  text-align: right;
  color: #6c757d;
}

.chat-message .message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  margin: 0 0.75rem;
  flex-shrink: 0;
}

.chat-message.user .message-avatar {
  background-color: #007bff;
  color: white;
  order: 1;
  margin-left: 0.75rem;
  margin-right: 0;
}

.chat-message.ai .message-avatar {
  background-color: #28a745;
  color: white;
  order: 0;
  margin-left: 0;
  margin-right: 0.75rem;
}

.file-preview-item {
  position: relative;
  display: inline-block;
  margin: 0.25rem;
}

.file-preview-item .file-preview {
  max-width: 100px;
  max-height: 100px;
  border-radius: 0.375rem;
  border: 1px solid #dee2e6;
}

.file-preview-item .file-info {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 0.5rem;
  max-width: 200px;
  font-size: 0.875rem;
}

.file-preview-item .remove-file {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #dc3545;
  color: white;
  border: none;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.api-detail-section {
  margin-bottom: 0.75rem;
}

.api-detail-section h6 {
  color: #495057;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.api-detail-content {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 0.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  max-height: 180px;
  overflow-y: auto;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-all;
}

.api-detail-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
}

.loading-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-style: italic;
}

.loading-message .spinner-border {
  width: 1rem;
  height: 1rem;
}

.chat-input-area textarea:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.message-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
  margin-top: 0.5rem;
}

.message-content .file-attachment {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
  margin-top: 0.5rem;
}

.message-content .file-attachment i {
  font-size: 1.25rem;
}

.message-content .file-attachment .file-name {
  font-weight: 500;
}

.message-content .file-attachment .file-size {
  font-size: 0.75rem;
  opacity: 0.8;
}

/* 聊天界面优化 */
#aiChatTestModal .modal-dialog {
  max-width: 1200px;
  height: 90vh;
  margin: 2vh auto;
}

#aiChatTestModal .modal-content {
  height: 90vh;
}

#aiChatTestModal .modal-body {
  overflow: hidden;
}

#aiChatTestModal .modal-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid #dee2e6;
}

/* 聊天输入区域样式 */
.chat-input-area {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.chat-input-area .input-group {
  align-items: flex-end;
}

.chat-input-area textarea {
  border-radius: 0.375rem 0 0 0.375rem;
}

.api-detail-section:last-child {
  margin-bottom: 0;
}

/* 聊天消息区域优化 */
.chat-messages {
  flex: 1 !important;
  overflow-y: auto !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 聊天消息样式 */
.chat-message {
  display: flex;
  margin-bottom: 1rem;
  align-items: flex-start;
}

.chat-message.user {
  flex-direction: row-reverse;
}

.chat-message.user .message-content {
  background-color: #007bff;
  color: white;
  margin-right: 0.5rem;
}

.chat-message.ai .message-content {
  background-color: #f8f9fa;
  color: #333;
  margin-left: 0.5rem;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #6c757d;
  color: white;
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
  padding: 0.75rem;
  border-radius: 1rem;
  position: relative;
}

.message-text {
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.5;
  margin: 0;
}

.message-time {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

.loading-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

import axios from 'axios';

/**
 * 认证相关的API服务
 */

// 设置axios默认配置
// 注意：不要修改全局axios默认设置，可能影响其他服务
const instance = axios.create({
  baseURL: '/api', // 使用相对路径，依赖Vite的代理配置
  headers: {
    'Content-Type': 'application/json'
  }
});

// 添加请求拦截器，为每个请求添加token
instance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 发送短信验证码
 * @param {string} phone - 手机号
 * @param {string} purpose - 用途，如'login'、'register'等
 * @returns {Promise} - API响应
 */
export const sendSmsCode = async (phone, purpose) => {
  try {
    const response = await instance.post('/api/sms/send', { phone, purpose });
    return response.data;
  } catch (error) {
    console.error('发送验证码失败:', error);
    throw error;
  }
};

/**
 * 密码登录
 * @param {Object} data - 登录数据，包含phone和password
 * @returns {Promise} - API响应
 */
export const login = async (data) => {
  try {
    const response = await instance.post('/api/auth/login', data);
    return response.data;
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
};

/**
 * 验证码登录
 * @param {Object} data - 登录数据，包含phone和verifyCode
 * @returns {Promise} - API响应
 */
export const loginByVerifyCode = async (data) => {
  try {
    const response = await instance.post('/api/auth/login/verify-code', data);
    return response.data;
  } catch (error) {
    console.error('验证码登录失败:', error);
    throw error;
  }
};

/**
 * 密码注册
 * @param {Object} data - 注册数据，包含phone, password和可选的nickname
 * @returns {Promise} - API响应
 */
export const register = async (data) => {
  try {
    const response = await instance.post('/api/auth/register', data);
    return response.data;
  } catch (error) {
    console.error('注册失败:', error);
    throw error;
  }
};

/**
 * 验证码注册
 * @param {Object} data - 注册数据，包含phone, verifyCode和可选的nickname、password
 * @returns {Promise} - API响应
 */
export const registerByVerifyCode = async (data) => {
  try {
    const response = await instance.post('/api/auth/register/verify-code', data);
    return response.data;
  } catch (error) {
    console.error('验证码注册失败:', error);
    throw error;
  }
};

/**
 * 获取当前用户信息
 * @returns {Promise} - API响应
 */
export const getCurrentUser = async () => {
  try {
    // 修复API路径，由于instance已设置baseURL为'/api'，这里不要再加/api前缀
    const response = await instance.get('/auth/me');

    return response.data;
  } catch (error) {
    console.error('获取用户信息失败');
    // 返回错误对象而不是抛出异常，这样可以在调用处更好地处理错误
    return {
      success: false,
      message: error.message || '获取用户信息失败',
      error
    };
  }
};

/**
 * 退出登录（前端处理）
 */
export const logout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  // 可以添加重定向到登录页的逻辑
};

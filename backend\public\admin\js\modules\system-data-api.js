/**
 * 系统数据API - 获取前端真实数据
 * 移除硬编码，提供动态数据获取
 */

window.systemDataAPI = (function() {
  
  /**
   * 获取前端功能配置状态
   * @returns {Promise<Object>} 功能状态数据
   */
  async function getFrontendFeatureStatus() {
    try {
      // 通过postMessage与前端通信获取真实状态
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          // 超时时返回默认状态而不是抛出错误
          console.warn('获取前端数据超时，使用默认状态');
          resolve(getDefaultFeatureStatus().features);
        }, 2000); // 减少超时时间

        // 监听前端响应
        const handleMessage = (event) => {
          if (event.data && event.data.type === 'FEATURE_STATUS_RESPONSE') {
            clearTimeout(timeout);
            window.removeEventListener('message', handleMessage);
            resolve(event.data.payload);
          }
        };

        window.addEventListener('message', handleMessage);

        // 向前端发送请求
        const frontendWindow = window.parent || window.opener;
        if (frontendWindow) {
          frontendWindow.postMessage({
            type: 'GET_FEATURE_STATUS',
            source: 'admin-dashboard'
          }, '*');
        } else {
          // 如果无法与前端通信，返回默认状态
          clearTimeout(timeout);
          window.removeEventListener('message', handleMessage);
          resolve(getDefaultFeatureStatus());
        }
      });
    } catch (error) {
      console.error('获取前端功能状态失败:', error);
      return getDefaultFeatureStatus();
    }
  }

  /**
   * 获取默认功能状态（备用方案）
   * @returns {Object} 默认功能状态
   */
  function getDefaultFeatureStatus() {
    return {
      features: {
        advanced_html_loader: {
          enabled: true, // 与前端featureConfig.js保持一致
          name: '增强型HTML加载器',
          description: '支持复杂静态页面的安全加载',
          lastToggled: null
        },
        intelligent_element_detection: {
          enabled: true, // 与前端featureConfig.js保持一致
          name: '智能元素检测',
          description: '自动识别8种元素类型，提供编辑建议',
          lastToggled: null
        },
        dual_mode_preview: {
          enabled: true, // 与前端featureConfig.js保持一致
          name: '双模式预览',
          description: '标准模式和高级模式智能切换',
          lastToggled: null
        },
        resource_management: {
          enabled: true,
          name: '资源管理系统',
          description: '智能处理外部资源，解决CORS问题',
          lastToggled: null
        },
        system_monitoring: {
          enabled: true,
          name: '系统监控',
          description: '实时监控系统性能，提供优化建议',
          lastToggled: null
        }
      },
      currentMode: 'advanced', // 由于高级功能默认启用，默认模式为高级模式
      lastUpdate: Date.now()
    };
  }

  /**
   * 获取系统性能数据
   * @returns {Promise<Object>} 性能数据
   */
  async function getSystemPerformanceData() {
    try {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('获取性能数据超时'));
        }, 5000);

        const handleMessage = (event) => {
          if (event.data && event.data.type === 'PERFORMANCE_DATA_RESPONSE') {
            clearTimeout(timeout);
            window.removeEventListener('message', handleMessage);
            resolve(event.data.payload);
          }
        };

        window.addEventListener('message', handleMessage);

        const frontendWindow = window.parent || window.opener;
        if (frontendWindow) {
          frontendWindow.postMessage({
            type: 'GET_PERFORMANCE_DATA',
            source: 'admin-dashboard'
          }, '*');
        } else {
          clearTimeout(timeout);
          window.removeEventListener('message', handleMessage);
          resolve(getDefaultPerformanceData());
        }
      });
    } catch (error) {
      console.error('获取性能数据失败:', error);
      return getDefaultPerformanceData();
    }
  }

  /**
   * 获取默认性能数据
   * @returns {Object} 默认性能数据
   */
  function getDefaultPerformanceData() {
    return {
      healthScore: 85,
      healthStatus: 'good',
      loadingPerformance: {
        average: 1250,
        trend: 'improving',
        samples: []
      },
      memoryUsage: {
        current: 65,
        trend: 'stable',
        history: []
      },
      errorRate: {
        current: 0.2,
        trend: 'decreasing',
        recent: []
      },
      lastUpdate: Date.now()
    };
  }

  /**
   * 切换功能状态
   * @param {string} featureId - 功能ID
   * @param {boolean} enabled - 是否启用
   * @returns {Promise<boolean>} 是否成功
   */
  async function toggleFeature(featureId, enabled) {
    try {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('切换功能超时'));
        }, 10000);

        const handleMessage = (event) => {
          if (event.data && event.data.type === 'FEATURE_TOGGLE_RESPONSE') {
            clearTimeout(timeout);
            window.removeEventListener('message', handleMessage);
            resolve(event.data.payload.success);
          }
        };

        window.addEventListener('message', handleMessage);

        const frontendWindow = window.parent || window.opener;
        if (frontendWindow) {
          frontendWindow.postMessage({
            type: 'TOGGLE_FEATURE',
            source: 'admin-dashboard',
            payload: { featureId, enabled }
          }, '*');
        } else {
          clearTimeout(timeout);
          window.removeEventListener('message', handleMessage);
          resolve(false);
        }
      });
    } catch (error) {
      console.error('切换功能失败:', error);
      return false;
    }
  }

  /**
   * 获取优化建议
   * @returns {Promise<Array>} 优化建议列表
   */
  async function getOptimizationSuggestions() {
    try {
      const featureStatus = await getFrontendFeatureStatus();
      const performanceData = await getSystemPerformanceData();
      
      const suggestions = [];

      // 基于功能状态生成建议
      if (!featureStatus.features.advanced_html_loader.enabled) {
        suggestions.push({
          type: 'performance',
          priority: 'medium',
          message: '建议启用增强型HTML加载器以提升复杂页面加载性能',
          action: 'enable_advanced_loader',
          benefits: ['提升加载成功率', '更好的安全隔离', '支持复杂页面结构']
        });
      }

      if (!featureStatus.features.intelligent_element_detection.enabled) {
        suggestions.push({
          type: 'usability',
          priority: 'low',
          message: '智能元素检测可以改善编辑体验',
          action: 'enable_element_detection',
          benefits: ['智能元素推荐', '编辑策略优化', '支持复杂页面编辑']
        });
      }

      // 基于性能数据生成建议
      if (performanceData.loadingPerformance.average > 2000) {
        suggestions.push({
          type: 'performance',
          priority: 'high',
          message: '页面加载时间较长，建议优化加载策略',
          action: 'optimize_loading',
          benefits: ['减少用户等待时间', '提升用户体验']
        });
      }

      if (performanceData.memoryUsage.current > 80) {
        suggestions.push({
          type: 'memory',
          priority: 'medium',
          message: '内存使用率较高，建议检查内存泄漏',
          action: 'check_memory',
          benefits: ['提升系统稳定性', '减少崩溃风险']
        });
      }

      return suggestions;
    } catch (error) {
      console.error('获取优化建议失败:', error);
      return [];
    }
  }

  /**
   * 获取完整的系统状态
   * @returns {Promise<Object>} 完整系统状态
   */
  async function getFullSystemStatus() {
    try {
      // 使用Promise.allSettled来处理部分失败的情况
      const results = await Promise.allSettled([
        getFrontendFeatureStatus(),
        getSystemPerformanceData(),
        getOptimizationSuggestions()
      ]);

      // 提取成功的结果，失败的使用默认值
      const featureStatus = results[0].status === 'fulfilled'
        ? results[0].value
        : getDefaultFeatureStatus().features;

      const performanceData = results[1].status === 'fulfilled'
        ? results[1].value
        : getDefaultPerformanceData();

      const suggestions = results[2].status === 'fulfilled'
        ? results[2].value
        : [];

      return {
        features: featureStatus.features || featureStatus,
        currentMode: featureStatus.currentMode || 'standard',
        performance: performanceData,
        suggestions: suggestions,
        lastUpdate: Date.now(),
        dataSource: 'dynamic' // 标识这是动态数据
      };
    } catch (error) {
      console.error('获取完整系统状态失败:', error);
      // 返回默认状态
      return {
        features: getDefaultFeatureStatus().features,
        currentMode: 'standard',
        performance: getDefaultPerformanceData(),
        suggestions: [],
        lastUpdate: Date.now(),
        dataSource: 'fallback' // 标识这是备用数据
      };
    }
  }

  // 公开接口
  return {
    getFrontendFeatureStatus,
    getSystemPerformanceData,
    toggleFeature,
    getOptimizationSuggestions,
    getFullSystemStatus
  };
})();

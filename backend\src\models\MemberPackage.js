const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 会员套餐模型
 */
const MemberPackage = sequelize.define('member_package', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '套餐名称'
  },
  duration: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '有效期(天)'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '价格'
  },
  discount_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '折扣价格'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '套餐描述'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否激活'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'member_packages',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = MemberPackage; 
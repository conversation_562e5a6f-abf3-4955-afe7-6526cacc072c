/**
 * 加密工具，用于API密钥等敏感信息的加密和解密
 */
const crypto = require('crypto');
const logger = require('./logger');

// 使用环境变量中的密钥，或使用默认密钥（仅开发环境）
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'fengmian_default_encryption_key_32b'; // 32字节
const ENCRYPTION_IV = process.env.ENCRYPTION_IV || 'fengmian_default_iv_16bytes'; // 16字节
const ALGORITHM = 'aes-256-cbc';

// 支付密钥使用单独的加密密钥，增强安全性
const PAYMENT_ENCRYPTION_KEY = process.env.PAYMENT_ENCRYPTION_KEY || ENCRYPTION_KEY;
const PAYMENT_ENCRYPTION_IV = process.env.PAYMENT_ENCRYPTION_IV || ENCRYPTION_IV;

/**
 * 加密API密钥
 * @param {string} apiKey - 需要加密的API密钥
 * @returns {string} - 加密后的字符串
 */
const encryptApiKey = (apiKey) => {
  try {
    // 生成16字节的初始化向量
    const iv = Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf8');
    // 创建32字节的密钥
    const key = Buffer.from(ENCRYPTION_KEY.slice(0, 32), 'utf8');
    
    // 创建加密器
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    
    // 加密
    let encrypted = cipher.update(apiKey, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return encrypted;
  } catch (error) {
    logger.error('加密API密钥失败:', error);
    throw new Error('加密失败');
  }
};

/**
 * 解密API密钥
 * @param {string} encryptedApiKey - 加密后的API密钥
 * @returns {string} - 解密后的原始API密钥
 */
const decryptApiKey = (encryptedApiKey) => {
  try {
    // 生成16字节的初始化向量
    const iv = Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf8');
    // 创建32字节的密钥
    const key = Buffer.from(ENCRYPTION_KEY.slice(0, 32), 'utf8');
    
    // 创建解密器
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    
    // 解密
    let decrypted = decipher.update(encryptedApiKey, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    logger.error('解密API密钥失败:', error);
    throw new Error('解密失败');
  }
};

/**
 * 加密支付密钥
 * @param {string} paymentKey - 需要加密的支付密钥
 * @returns {string} - 加密后的字符串
 */
const encryptPaymentKey = (paymentKey) => {
  try {
    // 生成16字节的初始化向量
    const iv = Buffer.from(PAYMENT_ENCRYPTION_IV.slice(0, 16), 'utf8');
    // 创建32字节的密钥
    const key = Buffer.from(PAYMENT_ENCRYPTION_KEY.slice(0, 32), 'utf8');
    
    // 创建加密器
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    
    // 加密
    let encrypted = cipher.update(paymentKey, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return encrypted;
  } catch (error) {
    logger.error('加密支付密钥失败:', error);
    throw new Error('加密失败');
  }
};

/**
 * 解密支付密钥
 * @param {string} encryptedPaymentKey - 加密后的支付密钥
 * @returns {string} - 解密后的原始支付密钥
 */
const decryptPaymentKey = (encryptedPaymentKey) => {
  try {
    // 生成16字节的初始化向量
    const iv = Buffer.from(PAYMENT_ENCRYPTION_IV.slice(0, 16), 'utf8');
    // 创建32字节的密钥
    const key = Buffer.from(PAYMENT_ENCRYPTION_KEY.slice(0, 32), 'utf8');
    
    // 创建解密器
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    
    // 解密
    let decrypted = decipher.update(encryptedPaymentKey, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    logger.error('解密支付密钥失败:', error);
    throw new Error('解密失败');
  }
};

/**
 * 通用加密函数
 * @param {string} data - 需要加密的数据
 * @param {string} customKey - 自定义密钥（可选）
 * @param {string} customIv - 自定义初始化向量（可选）
 * @returns {string} - 加密后的字符串
 */
const encrypt = (data, customKey = null, customIv = null) => {
  try {
    const iv = customIv 
      ? Buffer.from(customIv.slice(0, 16), 'utf8')
      : Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf8');
      
    const key = customKey
      ? Buffer.from(customKey.slice(0, 32), 'utf8')
      : Buffer.from(ENCRYPTION_KEY.slice(0, 32), 'utf8');
    
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return encrypted;
  } catch (error) {
    logger.error('加密数据失败:', error);
    throw new Error('加密失败');
  }
};

/**
 * 通用解密函数
 * @param {string} encryptedData - 加密后的数据
 * @param {string} customKey - 自定义密钥（可选）
 * @param {string} customIv - 自定义初始化向量（可选）
 * @returns {string} - 解密后的原始数据
 */
const decrypt = (encryptedData, customKey = null, customIv = null) => {
  try {
    const iv = customIv 
      ? Buffer.from(customIv.slice(0, 16), 'utf8')
      : Buffer.from(ENCRYPTION_IV.slice(0, 16), 'utf8');
      
    const key = customKey
      ? Buffer.from(customKey.slice(0, 32), 'utf8')
      : Buffer.from(ENCRYPTION_KEY.slice(0, 32), 'utf8');
    
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
    
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    logger.error('解密数据失败:', error);
    throw new Error('解密失败');
  }
};

module.exports = {
  encryptApiKey,
  decryptApiKey,
  encryptPaymentKey,
  decryptPaymentKey,
  encrypt,
  decrypt
};

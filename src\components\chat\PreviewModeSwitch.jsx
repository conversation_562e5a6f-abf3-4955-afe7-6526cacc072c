/**
 * 预览模式切换UI组件
 * 提供用户友好的模式切换界面，支持标准模式和高级模式的无缝切换
 * 基于主流设计思路，放置在编辑面板顶部，突出VIP功能属性
 */

import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog';
import { Badge } from '../ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { Loader2, Shield, Zap, Settings, Info, Crown } from 'lucide-react';
import previewModeController, { PREVIEW_MODES, getCurrentMode, switchToMode, addModeListener, removeModeListener } from './utils/previewModeController';

/**
 * 主流设计的模式切换开关组件
 */
const PreviewModeSwitch = ({ className = '' }) => {
  const [currentMode, setCurrentMode] = useState(PREVIEW_MODES.STANDARD);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingMode, setPendingMode] = useState(null);

  // 初始化和事件监听
  useEffect(() => {
    // 初始化控制器
    previewModeController.init();

    // 获取当前模式
    const initialMode = getCurrentMode();
    setCurrentMode(initialMode);

    // 监听模式切换事件
    const handleSwitchStart = (data) => {
      setIsTransitioning(true);
    };

    const handleSwitchComplete = (data) => {
      setIsTransitioning(false);
      if (data.success) {
        setCurrentMode(data.to);
        setShowConfirmDialog(false);
        setPendingMode(null);
      }
    };

    addModeListener('switchStart', handleSwitchStart);
    addModeListener('switchComplete', handleSwitchComplete);

    // 清理函数
    return () => {
      removeModeListener('switchStart', handleSwitchStart);
      removeModeListener('switchComplete', handleSwitchComplete);
    };
  }, []);

  /**
   * 处理模式切换点击
   */
  const handleModeToggle = () => {
    if (isTransitioning) return;

    const targetMode = currentMode.id === 'standard' ? PREVIEW_MODES.ADVANCED : PREVIEW_MODES.STANDARD;

    // 如果切换到高级模式，显示确认对话框
    if (targetMode.id === 'advanced') {
      setPendingMode(targetMode);
      setShowConfirmDialog(true);
    } else {
      // 直接切换到标准模式
      performModeSwitch(targetMode);
    }
  };

  /**
   * 执行模式切换
   */
  const performModeSwitch = async (targetMode) => {
    try {
      const success = await switchToMode(targetMode.id, {
        skipConfirmation: false,
        preserveState: true,
        showAnimation: true
      });

      if (!success) {
        console.error('模式切换失败');
      }
    } catch (error) {
      console.error('模式切换出错:', error);
    }
  };

  /**
   * 确认切换到高级模式
   */
  const handleConfirmSwitch = () => {
    if (pendingMode) {
      performModeSwitch(pendingMode);
    }
  };

  /**
   * 取消切换
   */
  const handleCancelSwitch = () => {
    setShowConfirmDialog(false);
    setPendingMode(null);
  };

  /**
   * 获取模式图标
   */
  const getModeIcon = (mode) => {
    switch (mode.id) {
      case 'standard':
        return <Shield className="h-4 w-4" />;
      case 'advanced':
        return <Zap className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  /**
   * 获取模式样式类
   */
  const getModeStyles = (mode) => {
    switch (mode.id) {
      case 'standard':
        return {
          button: 'bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300',
          badge: 'bg-gray-100 text-gray-600'
        };
      case 'advanced':
        return {
          button: 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white border-purple-500',
          badge: 'bg-purple-100 text-purple-700'
        };
      default:
        return {
          button: 'bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300',
          badge: 'bg-gray-100 text-gray-600'
        };
    }
  };

  // 主流的切换开关设计 - 简洁明了
  return (
    <div className={`flex items-center ${className}`}>
      <button
        onClick={handleModeToggle}
        disabled={isTransitioning}
        className={`
          inline-flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium
          transition-all duration-200 border relative z-10
          ${currentMode.id === 'advanced'
            ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white border-purple-500 hover:from-purple-600 hover:to-purple-700'
            : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
          }
          ${isTransitioning ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        title={`当前：${currentMode.name}，点击切换到${currentMode.id === 'standard' ? '高级模式' : '标准模式'}`}
      >
        {isTransitioning ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <>
            {getModeIcon(currentMode)}
            <span>{currentMode.name}</span>
            {currentMode.id === 'advanced' && (
              <Crown className="h-3 w-3 text-yellow-300" />
            )}
          </>
        )}
      </button>



      {/* 确认对话框 */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              <Zap className="h-5 w-5 text-purple-500" />
              切换到高级模式
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              您即将切换到<strong className="text-purple-600">高级模式</strong>，这将启用以下功能：
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Shield className="h-4 w-4 text-green-500" />
                <span>增强的安全策略和资源处理</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Zap className="h-4 w-4 text-blue-500" />
                <span>智能页面复杂度检测</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Settings className="h-4 w-4 text-purple-500" />
                <span>优化的外部资源加载</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Info className="h-4 w-4 text-orange-500" />
                <span>高级错误处理和恢复机制</span>
              </div>
            </div>

            <div className="flex items-start gap-2 p-3 bg-amber-50 rounded-lg border border-amber-200">
              <Info className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <div className="font-medium">注意事项：</div>
                <div>高级模式可能会影响某些复杂页面的兼容性，如遇问题可随时切换回标准模式。</div>
              </div>
            </div>

            <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg border border-purple-200">
              <Crown className="h-4 w-4 text-yellow-500 flex-shrink-0" />
              <div className="text-sm text-purple-800">
                <div className="font-medium">VIP专享功能</div>
                <div>此功能为高级用户专享，享受更强大的预览体验。</div>
              </div>
            </div>
          </div>
          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleCancelSwitch}
              disabled={isTransitioning}
            >
              取消
            </Button>
            <Button
              onClick={handleConfirmSwitch}
              disabled={isTransitioning}
              className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white"
            >
              {isTransitioning ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  切换中...
                </>
              ) : (
                <>
                  <Crown className="h-4 w-4 mr-2 text-yellow-300" />
                  <Zap className="h-4 w-4 mr-2" />
                  确认切换
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};



export default PreviewModeSwitch;

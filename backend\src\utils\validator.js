const Joi = require('joi');
const logger = require('./logger');

/**
 * 通用验证中间件
 * @param {Joi.Schema} schema - Joi验证模式
 * @param {string} property - 要验证的请求属性 (body, params, query)
 */
const validate = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false, // 返回所有错误，而不是在第一个错误时停止
      stripUnknown: true // 移除未定义的属性
    });

    if (error) {
      const errorDetails = error.details.map(detail => ({
        message: detail.message,
        path: detail.path
      }));

      logger.debug('数据验证失败:', errorDetails);

      return res.status(400).json({
        success: false,
        message: '输入数据验证失败',
        errors: errorDetails
      });
    }

    // 验证通过后，用验证后的值替换请求中的数据
    req[property] = value;
    next();
  };
};

/**
 * 常用验证规则
 */
const schemas = {
  // 用户注册验证
  register: Joi.object({
    phone: Joi.string()
      .pattern(/^1[3-9]\d{9}$/)
      .required()
      .messages({
        'string.pattern.base': '手机号格式不正确',
        'any.required': '手机号不能为空'
      }),
    password: Joi.string()
      .min(8)
      .max(32)
      .pattern(/^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/)
      .required()
      .messages({
        'string.min': '密码长度不能少于{{#limit}}个字符',
        'string.max': '密码长度不能超过{{#limit}}个字符',
        'string.pattern.base': '密码必须包含字母和数字',
        'any.required': '密码不能为空'
      }),
    nickname: Joi.string()
      .max(50)
      .allow('', null)
      .messages({
        'string.max': '昵称长度不能超过{{#limit}}个字符'
      })
  }),

  // 用户验证码注册验证
  verifyCodeRegister: Joi.object({
    phone: Joi.string()
      .pattern(/^1[3-9]\d{9}$/)
      .required()
      .messages({
        'string.pattern.base': '手机号格式不正确',
        'any.required': '手机号不能为空'
      }),
    verifyCode: Joi.string()
      .pattern(/^\d{6}$/)
      .required()
      .messages({
        'string.pattern.base': '验证码格式不正确',
        'any.required': '验证码不能为空'
      }),
    nickname: Joi.string()
      .max(50)
      .allow('', null)
      .messages({
        'string.max': '昵称长度不能超过{{#limit}}个字符'
      }),
    password: Joi.string()
      .min(8)
      .max(32)
      .pattern(/^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/)
      .allow('', null)  // 允许为空，会自动生成随机密码
      .messages({
        'string.min': '密码长度不能少于{{#limit}}个字符',
        'string.max': '密码长度不能超过{{#limit}}个字符',
        'string.pattern.base': '密码必须包含字母和数字'
      })
  }),

  // 用户登录验证
  login: Joi.object({
    phone: Joi.string()
      .pattern(/^1[3-9]\d{9}$/)
      .required()
      .messages({
        'string.pattern.base': '手机号格式不正确',
        'any.required': '手机号不能为空'
      }),
    password: Joi.string()
      .required()
      .messages({
        'any.required': '密码不能为空'
      })
  }),

  // 用户验证码登录验证
  verifyCodeLogin: Joi.object({
    phone: Joi.string()
      .pattern(/^1[3-9]\d{9}$/)
      .required()
      .messages({
        'string.pattern.base': '手机号格式不正确',
        'any.required': '手机号不能为空'
      }),
    verifyCode: Joi.string()
      .pattern(/^\d{6}$/)
      .required()
      .messages({
        'string.pattern.base': '验证码格式不正确',
        'any.required': '验证码不能为空'
      })
  }),

  // 短信验证码发送验证
  smsVerification: Joi.object({
    phone: Joi.string()
      .pattern(/^1[3-9]\d{9}$/)
      .required()
      .messages({
        'string.pattern.base': '手机号格式不正确',
        'any.required': '手机号不能为空'
      }),
    purpose: Joi.string()
      .valid('login', 'register', 'reset_password')
      .required()
      .messages({
        'any.only': '验证码用途不正确',
        'any.required': '验证码用途不能为空'
      }),
    verifyCode: Joi.string()
      .pattern(/^\d{6}$/)
      .messages({
        'string.pattern.base': '验证码格式不正确'
      })
  }),

  // 密码重置验证
  resetPassword: Joi.object({
    phone: Joi.string()
      .pattern(/^1[3-9]\d{9}$/)
      .required()
      .messages({
        'string.pattern.base': '手机号格式不正确',
        'any.required': '手机号不能为空'
      }),
    newPassword: Joi.string()
      .min(8)
      .max(32)
      .pattern(/^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/)
      .required()
      .messages({
        'string.min': '密码长度不能少于{{#limit}}个字符',
        'string.max': '密码长度不能超过{{#limit}}个字符',
        'string.pattern.base': '密码必须包含字母和数字',
        'any.required': '新密码不能为空'
      }),
    verifyCode: Joi.string()
      .pattern(/^\d{6}$/)
      .optional()
      .messages({
        'string.pattern.base': '验证码格式不正确'
      })
  }),

  // 封面生成验证
  generateCover: Joi.object({
    cover_type: Joi.string()
      .max(50)
      .required()
      .messages({
        'string.max': '封面类型不能超过{{#limit}}个字符',
        'any.required': '封面类型不能为空'
      }),
    cover_style: Joi.string()
      .required()
      .messages({
        'any.required': '封面风格不能为空'
      }),
    cover_text: Joi.string()
      .required()
      .max(500)
      .messages({
        'any.required': '封面文案不能为空',
        'string.max': '封面文案不能超过{{#limit}}个字符'
      }),
    account_name: Joi.string()
      .allow('', null)
      .max(100)
      .messages({
        'string.max': '账号名称不能超过{{#limit}}个字符'
      }),
    subtitle: Joi.string()
      .allow('', null)
      .max(200)
      .messages({
        'string.max': '副标题不能超过{{#limit}}个字符'
      }),
    auto_title: Joi.boolean()
      .default(false)
  }),

  // ID参数验证
  idParam: Joi.object({
    id: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': 'ID必须是数字',
        'number.integer': 'ID必须是整数',
        'number.positive': 'ID必须是正数',
        'any.required': 'ID不能为空'
      })
  }),

  // 创建订单验证
  createOrder: Joi.object({
    product_type: Joi.string()
      .valid('vip', 'points')
      .required()
      .messages({
        'any.only': '产品类型必须是VIP会员或积分',
        'any.required': '产品类型不能为空'
      }),
    payment_type: Joi.string()
      .valid('wechat', 'alipay', 'mock')
      .required()
      .messages({
        'any.only': '支付方式必须是微信支付、支付宝或模拟支付',
        'any.required': '支付方式不能为空'
      }),
    amount: Joi.number()
      .positive()
      .precision(2)
      .required()
      .messages({
        'number.base': '金额必须是数字',
        'number.positive': '金额必须大于0',
        'number.precision': '金额最多保留两位小数',
        'any.required': '金额不能为空'
      }),
    package_id: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': '套餐ID必须是数字',
        'number.integer': '套餐ID必须是整数',
        'number.positive': '套餐ID必须是正数',
        'any.required': '套餐ID不能为空'
      })
  }),

  // 更新支付方式验证
  updatePaymentMethod: Joi.object({
    payment_type: Joi.string()
      .valid('wechat', 'alipay', 'mock')
      .required()
      .messages({
        'any.only': '支付方式必须是微信支付、支付宝或模拟支付',
        'any.required': '支付方式不能为空'
      })
  }),

  // 基础提示词模板验证
  basePrompt: Joi.object({
    id_code: Joi.string()
      .max(50)
      .pattern(/^[a-zA-Z0-9_]+$/)
      .required()
      .messages({
        'string.max': '标识码不能超过{{#limit}}个字符',
        'string.pattern.base': '标识码只能包含字母、数字和下划线',
        'any.required': '标识码不能为空'
      }),
    cover_type: Joi.string()
      .max(50)
      .required()
      .messages({
        'string.max': '封面类型不能超过{{#limit}}个字符',
        'any.required': '封面类型不能为空'
      }),
    cover_size: Joi.string()
      .required()
      .messages({
        'any.required': '封面尺寸不能为空'
      }),
    prompt_content: Joi.string()
      .required()
      .messages({
        'any.required': '提示词内容不能为空'
      })
  }),

  // 风格提示词模板验证
  stylePrompt: Joi.object({
    style_name: Joi.string()
      .required()
      .max(50)
      .messages({
        'any.required': '风格名称不能为空',
        'string.max': '风格名称不能超过{{#limit}}个字符'
      }),
    prompt_content: Joi.string()
      .required()
      .messages({
        'any.required': '提示词内容不能为空'
      }),
    display_order: Joi.number()
      .integer()
      .default(0)
      .messages({
        'number.base': '显示顺序必须是数字',
        'number.integer': '显示顺序必须是整数'
      })
  }),

  // 风格示例验证
  styleExample: Joi.object({
    style_id: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': '风格ID必须是数字',
        'number.integer': '风格ID必须是整数',
        'number.positive': '风格ID必须是正数',
        'any.required': '风格ID不能为空'
      }),
    image_url: Joi.string()
      .required()
      .uri()
      .messages({
        'any.required': '图片URL不能为空',
        'string.uri': '图片URL格式不正确'
      })
  }),

  // 系统配置验证
  systemConfig: Joi.object({
    config_key: Joi.string()
      .required()
      .max(50)
      .messages({
        'any.required': '配置键不能为空',
        'string.max': '配置键不能超过{{#limit}}个字符'
      }),
    config_value: Joi.string()
      .required()
      .messages({
        'any.required': '配置值不能为空'
      }),
    description: Joi.string()
      .allow('', null)
      .max(255)
      .messages({
        'string.max': '描述不能超过{{#limit}}个字符'
      })
  }),

  // 设置密码验证
  setPassword: Joi.object({
    password: Joi.string()
      .min(8)
      .max(32)
      .pattern(/^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/)
      .required()
      .messages({
        'string.min': '密码长度不能少于{{#limit}}个字符',
        'string.max': '密码长度不能超过{{#limit}}个字符',
        'string.pattern.base': '密码必须包含字母和数字',
        'any.required': '密码不能为空'
      }),
    oldPassword: Joi.string()
      .allow('', null)
      .messages({
        'string.empty': '旧密码不能为空'
      })
  })
};

module.exports = {
  validate,
  schemas
};

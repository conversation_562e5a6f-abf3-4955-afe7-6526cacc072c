/**
 * 封面AI - 会员服务
 * 提供会员相关功能，包括会员状态查询、权益管理、会员升级等
 */

const { sequelize } = require('../models');
const { User, MemberPackage, MemberBenefit, MemberEvent } = require('../models');
const dayjs = require('dayjs');
const logger = require('../utils/logger');

/**
 * 获取用户会员信息
 * @param {number} userId 用户ID
 * @returns {Object} 用户会员信息
 */
exports.getUserMemberInfo = async (userId) => {
  try {
    const user = await User.findByPk(userId);
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    const isVip = user.role === 'vip';
    let vipExpireDays = 0;
    let vipExpireDate = null;
    
    if (isVip && user.vip_expire_date) {
      vipExpireDate = user.vip_expire_date;
      const expireDate = new Date(user.vip_expire_date);
      const today = new Date();
      
      if (expireDate > today) {
        const diffTime = expireDate - today;
        vipExpireDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      } else {
        // 会员已过期，但数据库未更新
        vipExpireDays = 0;
      }
    }
    
    return {
      userId: user.id,
      username: user.username,
      role: user.role,
      isVip,
      vipExpireDays,
      vipExpireDate,
      points: user.points || 0
    };
  } catch (error) {
    logger.error('获取用户会员信息失败', error);
    throw error;
  }
};

/**
 * 获取会员套餐列表
 * @returns {Array} 会员套餐列表
 */
exports.getMemberPackages = async () => {
  try {
    const packages = await MemberPackage.findAll({
      where: { is_active: true },
      order: [['duration', 'ASC']]
    });
    
    return packages.map(pkg => ({
      id: pkg.id,
      name: pkg.name,
      duration: pkg.duration,
      price: pkg.price,
      discountPrice: pkg.discount_price,
      description: pkg.description
    }));
  } catch (error) {
    logger.error('获取会员套餐列表失败', error);
    throw error;
  }
};

/**
 * 获取会员权益列表
 * @param {string} role 用户角色，默认获取所有权益
 * @returns {Array} 会员权益列表
 */
exports.getMemberBenefits = async (role) => {
  try {
    const whereClause = {};
    
    if (role) {
      whereClause.apply_role = role;
    }
    
    whereClause.is_active = true;
    
    const benefits = await MemberBenefit.findAll({
      where: whereClause,
      order: [['sort_order', 'ASC']]
    });
    
    return benefits.map(benefit => ({
      id: benefit.id,
      code: benefit.benefit_code,
      name: benefit.benefit_name,
      description: benefit.benefit_desc,
      icon: benefit.benefit_icon,
      applyRole: benefit.apply_role
    }));
  } catch (error) {
    logger.error('获取会员权益列表失败', error);
    throw error;
  }
};

/**
 * 检查用户是否有特定的会员权益
 * @param {number} userId 用户ID
 * @param {string} benefitCode 权益代码
 * @returns {boolean} 是否具有该权益
 */
exports.checkUserBenefit = async (userId, benefitCode) => {
  try {
    const user = await User.findByPk(userId);
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 检查用户的会员状态，如果已过期则更新为普通用户
    if (user.role === 'vip' && user.vip_expire_date) {
      const expireDate = new Date(user.vip_expire_date);
      const today = new Date();
      
      if (expireDate < today) {
        await user.update({
          role: 'user',
          vip_expire_date: null
        });
        
        // 记录会员过期事件
        await MemberEvent.create({
          user_id: userId,
          event_type: 'expire',
          description: '会员已过期，降级为普通用户'
        });
        
        return false; // 会员已过期，无权益
      }
    }
    
    // 获取该权益对应的角色要求
    const benefit = await MemberBenefit.findOne({
      where: {
        benefit_code: benefitCode,
        is_active: true
      }
    });
    
    if (!benefit) {
      return false; // 权益不存在或未激活
    }
    
    // 检查用户角色是否满足权益要求
    switch (benefit.apply_role) {
      case 'user':
        return true; // 所有用户都有此权益
      case 'vip':
        return user.role === 'vip' || user.role === 'admin'; // VIP和管理员有此权益
      case 'admin':
        return user.role === 'admin'; // 仅管理员有此权益
      default:
        return false;
    }
  } catch (error) {
    logger.error('检查用户权益失败', error);
    throw error;
  }
};

/**
 * 记录会员事件
 * @param {number} userId 用户ID
 * @param {string} eventType 事件类型
 * @param {string} description 事件描述
 * @param {number} relatedId 关联ID（可选）
 * @returns {Object} 创建的事件记录
 */
exports.logMemberEvent = async (userId, eventType, description, relatedId = null) => {
  try {
    const event = await MemberEvent.create({
      user_id: userId,
      event_type: eventType,
      description,
      related_id: relatedId
    });
    
    return event;
  } catch (error) {
    logger.error('记录会员事件失败', error);
    // 这里只记录日志，不抛出异常，避免影响主流程
    return null;
  }
};

/**
 * 更新用户会员状态
 * @param {number} userId 用户ID
 * @param {boolean} isVip 是否设为VIP
 * @param {Date|string} expireDate 过期时间
 * @param {string} reason 操作原因
 * @returns {Object} 更新后的用户信息
 */
exports.updateMemberStatus = async (userId, isVip, expireDate, reason) => {
  const transaction = await sequelize.transaction();
  
  try {
    const user = await User.findByPk(userId, { transaction });
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    const oldRole = user.role;
    const newRole = isVip ? 'vip' : 'user';
    
    await user.update({
      role: newRole,
      vip_expire_date: isVip ? expireDate : null
    }, { transaction });
    
    // 记录会员事件
    let eventType;
    if (oldRole === 'user' && newRole === 'vip') {
      eventType = 'upgrade';
    } else if (oldRole === 'vip' && newRole === 'user') {
      eventType = 'downgrade';
    } else if (oldRole === 'vip' && newRole === 'vip') {
      eventType = 'renew';
    } else {
      eventType = 'other';
    }
    
    await MemberEvent.create({
      user_id: userId,
      event_type: eventType,
      description: reason || `会员状态更新: ${oldRole} -> ${newRole}`
    }, { transaction });
    
    await transaction.commit();
    
    return {
      userId: user.id,
      username: user.username,
      role: user.role,
      isVip: user.role === 'vip',
      vipExpireDate: user.vip_expire_date
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('更新用户会员状态失败', error);
    throw error;
  }
};

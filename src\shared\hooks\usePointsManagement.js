/**
 * 积分管理自定义Hook
 * 管理积分判断、扣除和相关状态
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import { Modal, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { checkFeatureAvailability, consumePoints } from '../../services/featureService';
import logger from '../../services/logs/frontendLogger';
import useAuth from '../../hooks/useAuth';
import { cancelCoverGeneration } from '../../services/aiService';

// 最大保存的已处理操作记录数量
const MAX_PROCESSED_RECORDS = 20;

/**
 * 积分管理Hook
 * @param {Object} options - 配置选项
 * @param {Function} options.onStartGeneration - 开始生成的回调函数
 * @returns {Object} 包含积分管理状态和处理函数的对象
 */
const usePointsManagement = (options = {}) => {
  const { updateUserPoints } = useAuth();
  const navigate = useNavigate();

  // 积分相关状态
  const [pointsCost, setPointsCost] = useState(0);
  const [pointsDeducted, setPointsDeducted] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [operationId, setOperationId] = useState(null);

  // 使用ref存储上次beforeunload事件的时间戳，用于防抖
  const lastBeforeUnloadTime = useRef(0);

  /**
   * 积分扣除函数
   * 统一处理所有积分扣除场景
   * @param {string} reason - 扣除原因
   * @param {number} customPointsCost - 自定义积分成本，如果提供则使用此值
   * @returns {Promise<boolean>} 扣除结果
   */
  const deductPoints = useCallback(async (reason, customPointsCost = null) => {
    // 使用自定义积分成本或状态中的积分成本
    const costToDeduct = customPointsCost !== null ? customPointsCost : pointsCost;

    // 详细日志记录，包含当前状态
    logger.info('【积分扣除】开始执行积分扣除函数', {
      reason,
      pointsDeducted,
      pointsCost: costToDeduct,
      isGenerating,
      operationId
    });

    // 如果已经扣除过积分，则不再重复扣除
    if (pointsDeducted) {
      logger.info('【积分扣除】积分已扣除，跳过重复扣除', { reason });
      return true;
    }

    // 简化条件判断，只检查基本必要条件
    if (!consumePoints || costToDeduct <= 0) {
      logger.info('【积分扣除】基本条件不满足，跳过扣除', {
        hasConsumePoints: !!consumePoints,
        pointsCost: costToDeduct
      });
      return false;
    }

    // 尝试进行扣除
    try {
      // 重新获取功能积分成本，确保使用正确的扣除值
      let actualPointsCost = costToDeduct;
      try {
        const featureCheck = await checkFeatureAvailability('生成封面-积分扣除');
        actualPointsCost = featureCheck.points_cost || costToDeduct || 10;
      } catch (e) {
        logger.warn('获取积分成本失败，使用已保存的值', { pointsCost: costToDeduct });
      }

      logger.info('用户生成封面，扣除积分', { actualPointsCost, reason, operationId });

      // 确保传递正确参数给consumePoints函数
      const result = await consumePoints(
        actualPointsCost, 
        reason || '生成封面', 
        operationId // 确保传递操作ID
      );

      if (result.success) {
        logger.info('积分扣除成功', {
          points: actualPointsCost,
          remaining: result.data.points,
          reason,
          operationId
        });

        setPointsDeducted(true);

        // 清除localStorage中可能存在的未完成任务
        localStorage.removeItem('fengmian_unfinished_points_task');

        // 记录已处理的操作ID，防止重复扣除
        if (operationId) {
          saveProcessedOperation(operationId);
        }

        // 静默更新用户积分，不显示消息提示
        updateUserPoints(result.data.points);

        return true;
      } else {
        logger.error('积分扣除失败', { error: result.message, reason, operationId });
        return false;
      }
    } catch (error) {
      logger.error('积分扣除请求失败', { error: error.message, reason, operationId });
      return false;
    }
  }, [consumePoints, pointsCost, updateUserPoints, pointsDeducted, isGenerating, operationId]);

  /**
   * 保存已处理的操作ID到localStorage
   * @param {string} opId - 操作ID
   */
  const saveProcessedOperation = useCallback((opId) => {
    try {
      const processedOps = localStorage.getItem('fengmian_processed_operations') || '[]';
      let processedOpsArray = JSON.parse(processedOps);

      // 确保是数组
      if (!Array.isArray(processedOpsArray)) {
        processedOpsArray = [];
      }

      // 添加新的操作ID
      processedOpsArray.push({
        id: opId,
        timestamp: Date.now()
      });

      // 只保留最近的N条记录
      if (processedOpsArray.length > MAX_PROCESSED_RECORDS) {
        processedOpsArray = processedOpsArray.slice(-MAX_PROCESSED_RECORDS);
      }

      localStorage.setItem('fengmian_processed_operations', JSON.stringify(processedOpsArray));
    } catch (error) {
      logger.error('保存已处理操作ID失败', { error: error.message, operationId: opId });
    }
  }, []);

  /**
   * 检查操作ID是否已处理
   * @param {string} opId - 操作ID
   * @returns {boolean} 是否已处理
   */
  const isOperationProcessed = useCallback((opId) => {
    try {
      const processedOps = localStorage.getItem('fengmian_processed_operations') || '[]';
      const processedOpsArray = JSON.parse(processedOps);

      // 确保是数组
      if (!Array.isArray(processedOpsArray)) {
        return false;
      }

      // 检查操作ID是否存在
      return processedOpsArray.some(op => op.id === opId);
    } catch (error) {
      logger.error('检查操作ID是否已处理失败', { error: error.message, operationId: opId });
      return false;
    }
  }, []);

  /**
   * 检查登录和积分，并开始生成
   * @param {Object} values - 表单值
   * @returns {Promise<boolean>} 检查结果
   */
  const checkLoginAndPoints = useCallback(async (values) => {
    if (!values || typeof values !== 'object') {
      logger.error('表单数据无效', { values });
      message.error('表单数据无效，请重试');
      return false;
    }

    logger.info('接收到表单数据', { formValues: values });

    // 检查是否正在加载中
    if (isGenerating) {
      logger.warn('正在处理中，忽略重复提交');
      return false;
    }

    // 检查用户是否登录
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');

    if (!token || !userStr) {
      // 确保此时不会设置loading状态，以防止导航被拦截
      logger.info('用户未登录，显示登录提示');
      Modal.confirm({
        title: '需要登录',
        content: '生成封面需要消耗积分，请先登录或注册账号',
        okText: '去登录',
        cancelText: '取消',
        onOk: () => {
          // 使用原生方式跳转，确保即使在拦截状态也能导航
          // 添加redirect参数，登录成功后可以返回此页面
          logger.info('用户选择去登录');
          window.location.href = '/auth?redirect=/generate';
        }
      });
      return false;
    }

    try {
      const user = JSON.parse(userStr);

      // 检查功能是否可用
      const featureCheck = await checkFeatureAvailability('生成封面-积分扣除');

      if (!featureCheck.available) {
        Modal.error({
          title: '功能受限',
          content: featureCheck.reason || '您目前无法使用此功能',
          okText: '知道了'
        });
        return false;
      }

      // 获取每日积分信息
      const hasDailyPoints = user.daily_points > 0;
      const pointsCost = featureCheck.points_cost || 10;
      const currentPoints = featureCheck.current_points || user.points;
      const dailyPoints = user.daily_points || 0;

      // 根据是否有每日积分显示不同的提示信息
      let confirmContent = '';
      if (hasDailyPoints) {
        confirmContent = `生成封面将消耗您 ${pointsCost} 积分，当前积分余额：${currentPoints}，每日赠送余额：${dailyPoints}，优先使用每日赠送积分，是否继续？`;
      } else {
        confirmContent = `生成封面将消耗您 ${pointsCost} 积分，当前积分余额：${currentPoints}，是否继续？`;
      }

      // 积分扣除提示
      const confirmResult = await new Promise(resolve => {
        Modal.confirm({
          title: '积分扣除提示',
          content: confirmContent,
          okText: '确定',
          cancelText: '取消',
          onOk: () => resolve(true),
          onCancel: () => resolve(false)
        });
      });

      if (!confirmResult) {
        console.log('用户取消了生成操作');
        return false;
      }

      // 检查积分是否足够（常规积分 + 每日积分）
      const totalAvailablePoints = currentPoints + dailyPoints;
      if (totalAvailablePoints < pointsCost) {
        Modal.error({
          title: '积分不足',
          content: '您的积分不足，请充值后再试',
          okText: '去充值',
          onOk: () => {
            navigate('/profile');
          }
        });
        return false;
      }

      // 记录本次操作的积分成本，用于后续扣除
      const cost = pointsCost;
      setPointsCost(cost);

      // 重置积分扣除状态
      setPointsDeducted(false);

      // 生成新的操作ID，确保格式统一，包含时间戳和随机字符串
      const newOperationId = `op_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      setOperationId(newOperationId);
      logger.info('生成新的操作ID', { operationId: newOperationId });

      // 设置生成状态为true
      setIsGenerating(true);

      message.loading('开始生成封面...', 0.5);

      // 调用开始生成回调
      if (options.onStartGeneration) {
        options.onStartGeneration(values);
      }

      return true;
    } catch (error) {
      console.error('解析用户数据失败:', error);
      message.error('解析用户数据失败，请重新登录');
      return false;
    }
  }, [isGenerating, navigate, options]);

  /**
   * 处理生成成功
   * 在生成成功时尝试扣除积分
   * @returns {Promise<boolean>} 处理结果
   */
  const handleGenerationSuccess = useCallback(async () => {
    // 尝试扣除积分
    try {
      // 重新获取功能积分成本，确保使用正确的扣除值
      const featureCheck = await checkFeatureAvailability('生成封面-积分扣除');
      const actualPointsCost = featureCheck.points_cost || pointsCost || 10;

      logger.info('封面生成成功，尝试立即扣除积分', {
        actualPointsCost,
        pointsDeducted,
        operationId
      });

      // 如果已经扣除过积分，则不再重复扣除
      if (pointsDeducted) {
        logger.info('积分已经扣除，跳过重复扣除');
        return true;
      }

      // 检查操作ID是否已处理
      if (operationId && isOperationProcessed(operationId)) {
        logger.info('该操作已处理过积分扣除，跳过重复扣除', { operationId });
        setPointsDeducted(true);
        return true;
      }

      // 传递actualPointsCost参数给deductPoints函数
      const deductResult = await deductPoints('生成封面成功-直接扣除', actualPointsCost);

      return deductResult;
    } catch (error) {
      logger.error('积分立即扣除请求异常，将在预览加载完成时重试', {
        error: error.message,
        stack: error.stack,
        operationId
      });
      return false;
    }
  }, [deductPoints, pointsCost, pointsDeducted, operationId, isOperationProcessed]);

  /**
   * 处理预览加载完成
   * 在预览加载完成时检查并扣除积分
   * @param {boolean} justGenerated - 是否刚刚生成
   * @returns {Promise<void>}
   */
  const handlePreviewLoaded = useCallback(async (justGenerated) => {
    logger.info('预览加载完成', {
      justGenerated,
      pointsDeducted,
      pointsCost,
      operationId
    });

    // 确保积分扣除 - 如果封面刚刚生成且积分还未扣除，在这里强制扣除积分
    // 作为备份机制，确保无论generateCover中的积分扣除是否成功，都会在预览加载完成后扣除
    if (justGenerated && !pointsDeducted && pointsCost > 0) {
      // 检查操作ID是否已处理
      if (operationId && isOperationProcessed(operationId)) {
        logger.info('该操作已处理过积分扣除，跳过重复扣除', { operationId });
        setPointsDeducted(true);
      } else {
        logger.info('预览加载完成，检测到积分未扣除，开始扣除积分', { operationId });
        await deductPoints('生成封面成功-预览加载完成时');
      }
    }

    // 只有在刚生成后才重置生成状态，避免离开提示
    if (justGenerated) {
      logger.info('重置生成状态，避免离开提示');
      // 封面生成成功，重置生成状态，避免离开提示
      setIsGenerating(false);
    }
  }, [deductPoints, pointsCost, pointsDeducted, operationId, isOperationProcessed]);

  // 检查是否有未完成的积分扣除任务
  useEffect(() => {
    const checkUnfinishedPointsTask = async () => {
      try {
        const unfinishedTask = localStorage.getItem('fengmian_unfinished_points_task');
        if (unfinishedTask) {
          const taskData = JSON.parse(unfinishedTask);

          // 检查任务是否有效
          if (!taskData || !taskData.points || taskData.points <= 0 || taskData.deducted) {
            logger.info('无效的未完成任务，清除任务', taskData);
            localStorage.removeItem('fengmian_unfinished_points_task');
            return;
          }

          // 检查操作ID是否已处理
          if (taskData.operationId && isOperationProcessed(taskData.operationId)) {
            logger.info('该操作已处理过积分扣除，跳过重复扣除', { operationId: taskData.operationId });
            localStorage.removeItem('fengmian_unfinished_points_task');
            return;
          }

          logger.info('检测到未完成的积分扣除任务', taskData);

          // 尝试取消任务
          try {
            logger.info('页面重新加载，尝试取消任务');
            // 直接调用cancelCoverGeneration
            await cancelCoverGeneration();
          } catch (error) {
            logger.error('取消任务失败', { error: error.message });
          }

          // 扣除积分
          const consumeResult = await consumePoints(
            taskData.points,
            taskData.reason || '生成封面(离开页面)',
            taskData.operationId // 传递操作ID
          );

          if (consumeResult.success) {
            logger.info('积分扣除成功', {
              points: taskData.points,
              operationId: taskData.operationId
            });

            // 记录已处理的操作ID
            if (taskData.operationId) {
              saveProcessedOperation(taskData.operationId);
            }
            
            // 更新用户积分
            if (updateUserPoints && typeof updateUserPoints === 'function') {
              updateUserPoints(consumeResult.data.points);
            }
          } else {
            logger.error('积分扣除失败', { error: consumeResult.message });
          }

          // 无论成功失败，都清除任务
          localStorage.removeItem('fengmian_unfinished_points_task');
        }
      } catch (error) {
        logger.error('处理未完成积分任务失败', { error: error.message });
        localStorage.removeItem('fengmian_unfinished_points_task');
      }
    };

    checkUnfinishedPointsTask();
  }, [consumePoints, isOperationProcessed, saveProcessedOperation, updateUserPoints]);

  // 在beforeunload事件中设置未完成的积分任务并取消任务
  useEffect(() => {
    const handleBeforeUnloadPoints = (e) => {
      // 检查当前页面是否是登录或注册页面，或者是否刚从登录/注册页面跳转过来
      const isAuthPage = window.location.pathname === '/auth' || 
                        window.location.pathname === '/login' || 
                        window.location.pathname === '/register' ||
                        window.location.pathname === '/forgot-password';
      
      // 检查是否是刚刚登录或注册成功
      const referrer = document.referrer;
      const isFromAuthPage = referrer && (
        referrer.includes('/auth') || 
        referrer.includes('/login') || 
        referrer.includes('/register') || 
        referrer.includes('/forgot-password')
      );
      
      // 如果是登录/注册页面或刚从这些页面跳转过来，不触发警告
      if (isAuthPage || isFromAuthPage) {
        return;
      }

      // 防抖处理：如果在短时间内多次触发，只处理最后一次
      const now = Date.now();
      if (now - lastBeforeUnloadTime.current < 500) {
        logger.info('短时间内重复触发beforeunload事件，跳过处理');
        return;
      }
      lastBeforeUnloadTime.current = now;

      if (isGenerating && !pointsDeducted && pointsCost > 0) {
        // 检查是否已经存在相同操作ID的任务
        try {
          const existingTask = localStorage.getItem('fengmian_unfinished_points_task');
          if (existingTask) {
            const taskData = JSON.parse(existingTask);
            if (operationId && taskData.operationId === operationId) {
              // 已经存在相同操作ID的任务，不再重复添加
              logger.info('已存在相同操作ID的未完成任务，跳过', { operationId });
              return;
            }
          }
        } catch (error) {
          // 解析错误，忽略
          logger.error('解析现有未完成任务失败', { error: error.message });
        }

        // 存储未完成的积分任务到localStorage
        logger.info('保存未完成的积分任务', { pointsCost, operationId });
        localStorage.setItem('fengmian_unfinished_points_task', JSON.stringify({
          timestamp: Date.now(),
          points: pointsCost,
          deducted: false,
          reason: '生成封面(离开页面)',
          operationId
        }));

        // 设置一个unload事件监听器，在页面真正卸载时才取消任务
        window.addEventListener('unload', function() {
          try {
            logger.info('页面真正卸载，尝试取消任务');
            // 使用sendBeacon API发送取消请求，这是在页面卸载时发送请求的最可靠方式
            const { cancelCoverGenerationBeacon } = require('../../services/aiService');
            cancelCoverGenerationBeacon();
          } catch (error) {
            // 在页面卸载时无法记录错误
            try {
              // 备用方案：直接使用sendBeacon
              const taskId = localStorage.getItem('current_task_id');
              if (taskId) {
                const token = localStorage.getItem('token');
                const data = JSON.stringify({ taskId, token });
                const blob = new Blob([data], { type: 'application/json' });
                navigator.sendBeacon('/api/cover/cancel', blob);
              }
            } catch (innerError) {
              // 忽略内部错误
            }
          }
        }, { once: true });
      }

      // 显示确认消息
      const message = '您的封面正在生成，请勿离开此页面，以免导致封面生成中断。';
      e.returnValue = message;
      return message;
    };

    window.addEventListener('beforeunload', handleBeforeUnloadPoints);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnloadPoints);
    };
  }, [isGenerating, pointsDeducted, pointsCost, operationId]);

  return {
    pointsCost,
    pointsDeducted,
    isGenerating,
    operationId,
    setPointsCost,
    setPointsDeducted,
    setIsGenerating,
    deductPoints,
    checkLoginAndPoints,
    handleGenerationSuccess,
    handlePreviewLoaded,
    isOperationProcessed,
    saveProcessedOperation
  };
};

export default usePointsManagement;

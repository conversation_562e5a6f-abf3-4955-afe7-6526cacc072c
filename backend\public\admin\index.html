<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>封面生成网站 - 管理后台</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <style>
    body {
      font-family: 'Microsoft YaHei', sans-serif;
      background-color: #f5f7fb;
    }
    .login-container {
      max-width: 400px;
      margin: 100px auto;
      padding: 30px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    .login-logo {
      text-align: center;
      margin-bottom: 30px;
    }
    .login-title {
      text-align: center;
      font-size: 24px;
      margin-bottom: 30px;
      color: #333;
    }
    .login-form {
      margin-bottom: 20px;
    }
    .form-group {
      margin-bottom: 20px;
    }
    .btn-login {
      width: 100%;
      padding: 10px;
      background-color: #1890ff;
      border: none;
    }
    .btn-login:hover {
      background-color: #0d80ef;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="login-container">
      <div class="login-logo">
        <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMjQgMjQiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzE4OTBmZiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxyZWN0IHg9IjMiIHk9IjMiIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCIgcng9IjIiIHJ5PSIyIj48L3JlY3Q+PGNpcmNsZSBjeD0iOC41IiBjeT0iOC41IiByPSIxLjUiPjwvY2lyY2xlPjxwb2x5bGluZSBwb2ludHM9IjIxIDEyIDEzLjUgNS41IDgudDogMTAgNiAxNSI+PC9wb2x5bGluZT48L3N2Zz4=" alt="Logo" width="80">
      </div>
      <h1 class="login-title">封面生成网站管理后台</h1>
      <div class="login-form">
        <div class="form-group">
          <label for="phone" class="form-label">手机号</label>
          <input type="text" class="form-control" id="phone" placeholder="请输入管理员手机号">
        </div>
        <div class="form-group">
          <label for="password" class="form-label">密码</label>
          <input type="password" class="form-control" id="password" placeholder="请输入密码">
        </div>
        <button type="button" class="btn btn-primary btn-login" id="loginBtn">登录</button>
      </div>
      <div class="alert alert-danger d-none" id="loginError"></div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const loginBtn = document.getElementById('loginBtn');
      const phoneInput = document.getElementById('phone');
      const passwordInput = document.getElementById('password');
      const loginError = document.getElementById('loginError');

      // 登录按钮点击事件
      loginBtn.addEventListener('click', function() {
        const phone = phoneInput.value.trim();
        const password = passwordInput.value;

        if (!phone) {
          showError('请输入手机号');
          return;
        }

        if (!password) {
          showError('请输入密码');
          return;
        }

        // 清除错误信息
        loginError.classList.add('d-none');

        // 设置按钮加载状态
        loginBtn.disabled = true;
        loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 登录中...';

        // 发送登录请求
        console.log('开始登录请求，账号:', phone);

        fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            phone,
            password
          })
        })
        .then(response => {
          console.log('登录响应状态码:', response.status);
          return response.json().then(data => {
            return { status: response.status, data };
          });
        })
        .then(({ status, data }) => {
          // 恢复按钮状态
          loginBtn.disabled = false;
          loginBtn.textContent = '登录';

          console.log('登录响应数据:', data);

          if (data.success) {
            console.log('登录成功，用户信息:', data.data.user);
            // 保存token到本地存储
            localStorage.setItem('token', data.data.token);

            // 跳转到管理后台主页面，而不是前端应用
            window.location.href = '/admin/dashboard.html';
          } else {
            // 显示错误信息
            const errorMsg = data.message || '登录失败，请检查账号和密码';
            console.error('登录失败:', errorMsg);
            showError(errorMsg);
          }
        })
        .catch(error => {
          // 恢复按钮状态
          loginBtn.disabled = false;
          loginBtn.textContent = '登录';

          // 显示错误信息
          console.error('登录请求失败:', error);
          showError('登录请求失败，请稍后再试');
        });
      });

      // 显示错误信息
      function showError(message) {
        loginError.textContent = message;
        loginError.classList.remove('d-none');
      }

      // 监听回车键
      passwordInput.addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
          loginBtn.click();
        }
      });
    });
  </script>
</body>
</html>

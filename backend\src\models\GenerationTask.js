const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 生成任务模型
 * 用于记录和管理封面生成任务的状态
 * 对应数据库中的generation_tasks表
 */
const GenerationTask = sequelize.define('GenerationTask', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '任务记录ID，唯一标识'
  },
  task_id: {
    type: DataTypes.STRING(64),
    allowNull: false,
    unique: true,
    comment: '任务唯一ID，用于前端跟踪和取消'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联用户表的用户ID，游客为null'
  },
  status: {
    type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'canceled', 'abnormal'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '任务状态：待处理、处理中、已完成、失败、已取消、非正常'
  },
  task_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'cover',
    comment: '任务类型，例如：封面生成、文章生成等'
  },
  start_time: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '任务开始时间'
  },
  end_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '任务结束时间'
  },
  duration_ms: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '任务持续时间（毫秒）'
  },
  parameters: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '任务参数，JSON格式'
  },
  result_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '生成结果ID，可关联到CoverRecord等表'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '错误信息，如果任务失败'
  },
  client_info: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '客户端信息，例如IP、浏览器等'
  },
  cover_type_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '使用的封面类型名称'
  },
  style_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '使用的风格名称'
  }
}, {
  tableName: 'generation_tasks',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true, // 使用下划线命名约定
  indexes: [
    {
      fields: ['task_id'],
      unique: true
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['task_type']
    }
  ]
});

// 定义模型关联 (关联已在 models/index.js 中定义)
GenerationTask.associate = (models) => {
  GenerationTask.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user'
  });
};

module.exports = GenerationTask;

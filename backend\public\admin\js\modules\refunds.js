/**
 * 退款管理模块
 * 功能：加载退款列表、筛选、分页、详情查看和状态更新
 */

// 定义退款管理模块命名空间
const refundsModule = (function() {
  // 模块内部变量
  let currentPage = 1;
  const pageSize = 10;
  let totalPages = 0;
  let totalRecords = 0;
  let currentFilters = {};

  // DOM元素
  const elements = {
    refundsTable: document.getElementById('refundsTable'),
    refundsPagination: document.getElementById('refundsPagination'),
    refundFilterForm: document.getElementById('refundFilterForm'),
    refundSearchBtn: document.getElementById('refundSearchBtn'),
    refundResetBtn: document.getElementById('refundResetBtn'),
    refundStatus: document.getElementById('refundStatus'),
    refundSearch: document.getElementById('refundSearch'),
    refundStartDate: document.getElementById('refundStartDate'),
    refundEndDate: document.getElementById('refundEndDate')
  };

  /**
   * 初始化退款管理模块
   */
  function init() {
    // 绑定事件
    if (elements.refundSearchBtn) {
      elements.refundSearchBtn.addEventListener('click', handleSearch);
    }
    
    if (elements.refundResetBtn) {
      elements.refundResetBtn.addEventListener('click', handleReset);
    }

    // 初始加载数据
    loadRefunds();

    // 监听DOM变化，当页面显示时重新加载数据
    observePageVisibility('refunds', () => {
      loadRefunds();
    });
  }

  /**
   * 加载退款列表
   */
  async function loadRefunds() {
    try {
      showLoading();
      
      // 构建请求参数
      const params = new URLSearchParams({
        page: currentPage,
        limit: pageSize,
        ...currentFilters
      });

      // 发送API请求
      const response = await fetch(`/api/admin/payment/refunds?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('加载退款列表失败');
      }

      const data = await response.json();
      
      if (data.success) {
        renderRefundsTable(data.data.refunds);
        renderPagination(data.data.pagination);
      } else {
        showToast('error', data.message || '加载退款列表失败');
      }
    } catch (error) {
      console.error('加载退款列表失败:', error);
      showToast('error', '加载退款列表失败，请重试');
      renderEmptyTable('加载失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 渲染退款列表表格
   * @param {Array} refunds 退款数据数组
   */
  function renderRefundsTable(refunds) {
    if (!elements.refundsTable) return;
    
    if (!refunds || refunds.length === 0) {
      renderEmptyTable('暂无退款记录');
      return;
    }

    // 表头
    const headerRow = `
      <tr>
        <th>退款单号</th>
        <th>订单号</th>
        <th>用户</th>
        <th>退款金额</th>
        <th>退款原因</th>
        <th>状态</th>
        <th>申请时间</th>
        <th>操作</th>
      </tr>
    `;

    // 表格内容
    const rows = refunds.map(refund => {
      // 状态样式
      const statusClass = {
        'pending': 'text-warning',
        'success': 'text-success',
        'failed': 'text-danger'
      }[refund.refund_status] || 'text-secondary';

      // 状态文本
      const statusText = {
        'pending': '处理中',
        'success': '已退款',
        'failed': '退款失败'
      }[refund.refund_status] || '未知状态';

      return `
        <tr>
          <td>${refund.refund_no}</td>
          <td>${refund.payment ? refund.payment.order_no : '-'}</td>
          <td>${refund.user ? (refund.user.nickname || refund.user.phone || '-') : '-'}</td>
          <td>¥${refund.refund_amount.toFixed(2)}</td>
          <td>${refund.refund_reason || '-'}</td>
          <td><span class="badge ${statusClass}">${statusText}</span></td>
          <td>${formatDateTime(refund.created_at)}</td>
          <td>
            <button class="btn btn-sm btn-outline-primary me-1" onclick="refundsModule.viewRefundDetail('${refund.id}')">
              <i class="bi bi-eye"></i>
            </button>
            ${refund.refund_status === 'pending' ? `
              <button class="btn btn-sm btn-outline-success me-1" onclick="refundsModule.updateRefundStatus('${refund.id}', 'success')">
                <i class="bi bi-check"></i>
              </button>
              <button class="btn btn-sm btn-outline-danger" onclick="refundsModule.updateRefundStatus('${refund.id}', 'failed')">
                <i class="bi bi-x"></i>
              </button>
            ` : ''}
          </td>
        </tr>
      `;
    }).join('');

    // 更新表格
    elements.refundsTable.innerHTML = `
      <thead>
        ${headerRow}
      </thead>
      <tbody>
        ${rows}
      </tbody>
    `;
  }

  /**
   * 渲染空表格
   * @param {string} message 显示的消息
   */
  function renderEmptyTable(message) {
    if (!elements.refundsTable) return;
    
    elements.refundsTable.innerHTML = `
      <thead>
        <tr>
          <th>退款单号</th>
          <th>订单号</th>
          <th>用户</th>
          <th>退款金额</th>
          <th>退款原因</th>
          <th>状态</th>
          <th>申请时间</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colspan="8" class="text-center">${message}</td>
        </tr>
      </tbody>
    `;
  }

  /**
   * 渲染分页控件
   * @param {Object} pagination 分页信息
   */
  function renderPagination(pagination) {
    if (!elements.refundsPagination) return;
    
    totalRecords = pagination.total;
    totalPages = pagination.total_pages;
    currentPage = pagination.page;

    // 计算显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);
    
    if (endPage - startPage < 4 && totalPages > 5) {
      startPage = Math.max(1, endPage - 4);
    }

    // 构建分页HTML
    let paginationHTML = '';
    
    // 上一页按钮
    paginationHTML += `
      <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${currentPage - 1}" aria-label="上一页">
          <span aria-hidden="true">&laquo;</span>
        </a>
      </li>
    `;

    // 页码按钮
    for (let i = startPage; i <= endPage; i++) {
      paginationHTML += `
        <li class="page-item ${i === currentPage ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `;
    }

    // 下一页按钮
    paginationHTML += `
      <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
        <a class="page-link" href="#" data-page="${currentPage + 1}" aria-label="下一页">
          <span aria-hidden="true">&raquo;</span>
        </a>
      </li>
    `;

    // 更新分页控件
    elements.refundsPagination.innerHTML = paginationHTML;

    // 绑定页码点击事件
    elements.refundsPagination.querySelectorAll('.page-link').forEach(link => {
      link.addEventListener('click', handlePageChange);
    });
  }

  /**
   * 处理页码变化
   * @param {Event} event 事件对象
   */
  function handlePageChange(event) {
    event.preventDefault();
    const page = parseInt(event.target.getAttribute('data-page') || event.target.parentElement.getAttribute('data-page'));
    
    if (page && page !== currentPage && page > 0 && page <= totalPages) {
      currentPage = page;
      loadRefunds();
    }
  }

  /**
   * 处理搜索
   */
  function handleSearch() {
    currentFilters = {};
    currentPage = 1;

    // 收集筛选条件
    if (elements.refundStatus && elements.refundStatus.value) {
      currentFilters.status = elements.refundStatus.value;
    }

    if (elements.refundSearch && elements.refundSearch.value) {
      currentFilters.search = elements.refundSearch.value;
    }

    if (elements.refundStartDate && elements.refundStartDate.value) {
      currentFilters.start_date = elements.refundStartDate.value;
    }

    if (elements.refundEndDate && elements.refundEndDate.value) {
      currentFilters.end_date = elements.refundEndDate.value;
    }

    // 重新加载数据
    loadRefunds();
  }

  /**
   * 处理重置
   */
  function handleReset() {
    // 重置表单
    if (elements.refundFilterForm) {
      elements.refundFilterForm.reset();
    }

    // 重置筛选条件
    currentFilters = {};
    currentPage = 1;

    // 重新加载数据
    loadRefunds();
  }

  /**
   * 查看退款详情
   * @param {string} id 退款记录ID
   */
  async function viewRefundDetail(id) {
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch(`/api/admin/payment/refunds/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('获取退款详情失败');
      }

      const data = await response.json();
      
      if (data.success) {
        showRefundDetailModal(data.data);
      } else {
        showToast('error', data.message || '获取退款详情失败');
      }
    } catch (error) {
      console.error('获取退款详情失败:', error);
      showToast('error', '获取退款详情失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 显示退款详情模态框
   * @param {Object} refundData 退款详情数据
   */
  function showRefundDetailModal(refundData) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'refundDetailModal';
    modal.tabIndex = '-1';
    modal.setAttribute('aria-labelledby', 'refundDetailModalLabel');
    modal.setAttribute('aria-hidden', 'true');

    // 状态样式和文本
    const statusClass = {
      'pending': 'text-warning',
      'success': 'text-success',
      'failed': 'text-danger'
    }[refundData.refund.refund_status] || 'text-secondary';

    const statusText = {
      'pending': '处理中',
      'success': '已退款',
      'failed': '退款失败'
    }[refundData.refund.refund_status] || '未知状态';

    // 订单信息
    const orderInfo = refundData.order ? `
      <div class="mb-3">
        <h6 class="fw-bold">订单信息</h6>
        <table class="table table-bordered">
          <tr>
            <th>订单号</th>
            <td>${refundData.order.order_no}</td>
            <th>商品类型</th>
            <td>${refundData.order.product_type === 'vip' ? '会员套餐' : '积分套餐'}</td>
          </tr>
          <tr>
            <th>订单金额</th>
            <td>¥${refundData.order.amount.toFixed(2)}</td>
            <th>支付方式</th>
            <td>${refundData.order.payment_type === 'wechat' ? '微信支付' : '支付宝'}</td>
          </tr>
          <tr>
            <th>订单状态</th>
            <td>${refundData.order.payment_status}</td>
            <th>订单时间</th>
            <td>${formatDateTime(refundData.order.created_at)}</td>
          </tr>
        </table>
      </div>
    ` : '';

    // 用户信息
    const userInfo = refundData.user ? `
      <div class="mb-3">
        <h6 class="fw-bold">用户信息</h6>
        <table class="table table-bordered">
          <tr>
            <th>用户ID</th>
            <td>${refundData.user.id}</td>
            <th>用户昵称</th>
            <td>${refundData.user.nickname || '-'}</td>
          </tr>
          <tr>
            <th>手机号</th>
            <td>${refundData.user.phone || '-'}</td>
            <th>用户角色</th>
            <td>${refundData.user.role || '-'}</td>
          </tr>
        </table>
      </div>
    ` : '';

    // 模态框内容
    modal.innerHTML = `
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="refundDetailModalLabel">退款详情</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <h6 class="fw-bold">退款信息</h6>
              <table class="table table-bordered">
                <tr>
                  <th width="20%">退款单号</th>
                  <td>${refundData.refund.refund_no}</td>
                  <th width="20%">退款金额</th>
                  <td>¥${refundData.refund.refund_amount.toFixed(2)}</td>
                </tr>
                <tr>
                  <th>退款状态</th>
                  <td><span class="badge ${statusClass}">${statusText}</span></td>
                  <th>申请时间</th>
                  <td>${formatDateTime(refundData.refund.created_at)}</td>
                </tr>
                <tr>
                  <th>退款原因</th>
                  <td colspan="3">${refundData.refund.refund_reason || '-'}</td>
                </tr>
              </table>
            </div>
            
            ${orderInfo}
            
            ${userInfo}
            
            ${refundData.refund.refund_status === 'pending' ? `
              <div class="d-flex justify-content-center mt-4">
                <button class="btn btn-success me-3" onclick="refundsModule.updateRefundStatus('${refundData.refund.id}', 'success', true)">
                  <i class="bi bi-check-circle"></i> 同意退款
                </button>
                <button class="btn btn-danger" onclick="refundsModule.updateRefundStatus('${refundData.refund.id}', 'failed', true)">
                  <i class="bi bi-x-circle"></i> 拒绝退款
                </button>
              </div>
            ` : ''}
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          </div>
        </div>
      </div>
    `;

    // 添加到页面并显示
    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    // 监听模态框关闭事件，移除DOM
    modal.addEventListener('hidden.bs.modal', function () {
      document.body.removeChild(modal);
    });
  }

  /**
   * 更新退款状态
   * @param {string} id 退款记录ID
   * @param {string} status 新状态
   * @param {boolean} closeModal 是否关闭模态框
   */
  async function updateRefundStatus(id, status, closeModal = false) {
    try {
      // 确认提示
      const confirmMessage = status === 'success' ? '确定要同意此退款请求吗？' : '确定要拒绝此退款请求吗？';
      if (!confirm(confirmMessage)) {
        return;
      }
      
      showLoading();
      
      // 发送API请求
      const response = await fetch(`/api/admin/payment/refunds/${id}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        },
        body: JSON.stringify({ status })
      });

      if (!response.ok) {
        throw new Error('更新退款状态失败');
      }

      const data = await response.json();
      
      if (data.success) {
        showToast('success', '退款状态更新成功');
        
        // 如果需要关闭模态框
        if (closeModal) {
          const modal = document.getElementById('refundDetailModal');
          if (modal) {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
              modalInstance.hide();
            }
          }
        }
        
        // 重新加载数据
        loadRefunds();
      } else {
        showToast('error', data.message || '更新退款状态失败');
      }
    } catch (error) {
      console.error('更新退款状态失败:', error);
      showToast('error', '更新退款状态失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 创建新退款
   * @param {Object} refundData 退款数据
   */
  async function createRefund(refundData) {
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch('/api/admin/payment/refunds', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        },
        body: JSON.stringify(refundData)
      });

      if (!response.ok) {
        throw new Error('创建退款失败');
      }

      const data = await response.json();
      
      if (data.success) {
        showToast('success', '退款创建成功');
        
        // 重新加载数据
        loadRefunds();
        
        return true;
      } else {
        showToast('error', data.message || '创建退款失败');
        return false;
      }
    } catch (error) {
      console.error('创建退款失败:', error);
      showToast('error', '创建退款失败，请重试');
      return false;
    } finally {
      hideLoading();
    }
  }

  // 辅助函数：格式化日期时间
  function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  }

  // 辅助函数：获取Token
  function getToken() {
    return localStorage.getItem('token') || '';
  }

  // 辅助函数：显示Loading
  function showLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.style.display = 'flex';
    }
  }

  // 辅助函数：隐藏Loading
  function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
  }

  // 辅助函数：显示提示消息
  function showToast(type, message) {
    if (window.utils && window.utils.showToast) {
      window.utils.showToast(type, message);
    } else {
      alert(message);
    }
  }

  // 辅助函数：监听页面可见性变化
  function observePageVisibility(pageId, callback) {
    // 创建一个观察器实例
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const page = document.getElementById(pageId);
          if (page && !page.classList.contains('d-none')) {
            callback();
          }
        }
      });
    });

    // 配置观察选项
    const config = { attributes: true };

    // 开始观察目标节点
    const page = document.getElementById(pageId);
    if (page) {
      observer.observe(page, config);
    }
  }

  // 暴露公共方法
  return {
    init,
    viewRefundDetail,
    updateRefundStatus,
    createRefund
  };
})();

// 初始化模块
document.addEventListener('DOMContentLoaded', function() {
  refundsModule.init();
}); 
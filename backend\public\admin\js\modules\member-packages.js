/**
 * 会员套餐管理模块
 * 功能：加载会员套餐列表、添加、编辑、删除、启用/禁用会员套餐
 */

// 定义会员套餐管理模块命名空间
const memberPackagesModule = (function() {
  // 模块内部变量
  let memberPackages = [];
  let currentEditId = null;

  // DOM元素
  const elements = {
    memberPackagesTable: document.getElementById('memberPackagesTable'),
    addMemberPackageBtn: document.getElementById('addMemberPackageBtn'),
    memberPackageModal: document.getElementById('memberPackageModal'),
    memberPackageForm: document.getElementById('memberPackageForm'),
    memberPackageModalLabel: document.getElementById('memberPackageModalLabel'),
    saveMemberPackageBtn: document.getElementById('saveMemberPackageBtn'),
    memberPackageId: document.getElementById('memberPackageId'),
    memberPackageName: document.getElementById('packageName'),
    memberPackageDuration: document.getElementById('packageDuration'),
    memberPackagePrice: document.getElementById('packagePrice'),
    memberPackageDiscountPrice: document.getElementById('packageDiscountPrice'),
    memberPackageDescription: document.getElementById('packageDescription'),
    memberPackageIsActive: document.getElementById('packageIsActive')
  };

  /**
   * 初始化会员套餐管理模块
   */
  function init() {
    // 绑定事件
    if (elements.addMemberPackageBtn) {
      elements.addMemberPackageBtn.addEventListener('click', showAddMemberPackageModal);
    }
    
    if (elements.saveMemberPackageBtn) {
      elements.saveMemberPackageBtn.addEventListener('click', saveMemberPackage);
    }

    // 初始加载数据
    loadMemberPackages();

    // 监听DOM变化，当页面显示时重新加载数据
    observePageVisibility('member-packages', () => {
      loadMemberPackages();
    });
  }

  /**
   * 加载会员套餐列表
   */
  async function loadMemberPackages() {
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch('/api/admin/payment/member-packages', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('加载会员套餐列表失败');
      }

      const data = await response.json();
      
      if (data.success) {
        memberPackages = data.data.packages || [];
        renderMemberPackagesTable(memberPackages);
      } else {
        showToast('error', data.message || '加载会员套餐列表失败');
        renderEmptyTable('加载失败: ' + data.message);
      }
    } catch (error) {
      console.error('加载会员套餐列表失败:', error);
      showToast('error', '加载会员套餐列表失败，请重试');
      renderEmptyTable('加载失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 渲染会员套餐表格
   * @param {Array} packages 会员套餐数组
   */
  function renderMemberPackagesTable(packages) {
    if (!elements.memberPackagesTable) return;
    
    if (!packages || packages.length === 0) {
      renderEmptyTable('暂无会员套餐数据');
      return;
    }

    // 表格内容
    const rows = packages.map(pkg => {
      return `
        <tr>
          <td>${pkg.id}</td>
          <td>${pkg.name}</td>
          <td>${pkg.duration}</td>
          <td>¥${pkg.price}</td>
          <td>${pkg.discount_price ? `¥${pkg.discount_price}` : '-'}</td>
          <td>${pkg.is_active ? '<span class="text-success">启用</span>' : '<span class="text-danger">停用</span>'}</td>
          <td>
            <button class="btn btn-sm btn-primary edit-member-package" data-id="${pkg.id}">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm ${pkg.is_active ? 'btn-warning' : 'btn-success'} toggle-member-package" data-id="${pkg.id}" data-active="${pkg.is_active}">
              <i class="bi ${pkg.is_active ? 'bi-pause' : 'bi-play'}"></i>
            </button>
            <button class="btn btn-sm btn-danger delete-member-package" data-id="${pkg.id}">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        </tr>
      `;
    }).join('');

    // 更新表格
    elements.memberPackagesTable.innerHTML = `
      <thead>
        <tr>
          <th>ID</th>
          <th>套餐名称</th>
          <th>有效期(天)</th>
          <th>价格(元)</th>
          <th>折扣价(元)</th>
          <th>状态</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        ${rows}
      </tbody>
    `;

    // 绑定事件
    document.querySelectorAll('.edit-member-package').forEach(btn => {
      btn.addEventListener('click', function() {
        const packageId = this.getAttribute('data-id');
        editMemberPackage(packageId);
      });
    });
    
    document.querySelectorAll('.toggle-member-package').forEach(btn => {
      btn.addEventListener('click', function() {
        const packageId = this.getAttribute('data-id');
        const isActive = this.getAttribute('data-active') === 'true';
        toggleMemberPackageStatus(packageId, !isActive);
      });
    });
    
    document.querySelectorAll('.delete-member-package').forEach(btn => {
      btn.addEventListener('click', function() {
        const packageId = this.getAttribute('data-id');
        deleteMemberPackage(packageId);
      });
    });
  }

  /**
   * 渲染空表格
   * @param {string} message 显示的消息
   */
  function renderEmptyTable(message) {
    if (!elements.memberPackagesTable) return;
    
    elements.memberPackagesTable.innerHTML = `
      <thead>
        <tr>
          <th>ID</th>
          <th>套餐名称</th>
          <th>有效期(天)</th>
          <th>价格(元)</th>
          <th>折扣价(元)</th>
          <th>状态</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td colspan="7" class="text-center">${message}</td>
        </tr>
      </tbody>
    `;
  }

  /**
   * 显示添加会员套餐模态框
   */
  function showAddMemberPackageModal() {
    if (!elements.memberPackageModal || !elements.memberPackageForm) return;
    
    // 重置表单
    elements.memberPackageForm.reset();
    elements.memberPackageId.value = '';
    elements.memberPackageModalLabel.textContent = '添加会员套餐';
    currentEditId = null;
    
    // 显示模态框
    const modal = new bootstrap.Modal(elements.memberPackageModal);
    modal.show();
  }

  /**
   * 编辑会员套餐
   * @param {string} packageId 会员套餐ID
   */
  async function editMemberPackage(packageId) {
    if (!elements.memberPackageModal || !elements.memberPackageForm) return;
    
    try {
      showLoading();
      
      // 发送API请求获取套餐详情
      const response = await fetch(`/api/admin/payment/member-packages/${packageId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('获取会员套餐详情失败');
      }

      const data = await response.json();
      
      if (data.success) {
        const packageData = data.data.package;
        
        // 填充表单
        elements.memberPackageId.value = packageData.id;
        elements.memberPackageName.value = packageData.name || '';
        elements.memberPackageDuration.value = packageData.duration || '';
        elements.memberPackagePrice.value = packageData.price || '';
        elements.memberPackageDiscountPrice.value = packageData.discount_price || '';
        elements.memberPackageDescription.value = packageData.description || '';
        if (elements.memberPackageIsActive) {
          elements.memberPackageIsActive.checked = packageData.is_active;
        }
        
        // 更新模态框标题
        elements.memberPackageModalLabel.textContent = '编辑会员套餐';
        currentEditId = packageData.id;
        
        // 显示模态框
        const modal = new bootstrap.Modal(elements.memberPackageModal);
        modal.show();
      } else {
        showToast('error', data.message || '获取会员套餐详情失败');
      }
    } catch (error) {
      console.error('获取会员套餐详情失败:', error);
      showToast('error', '获取会员套餐详情失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 保存会员套餐
   */
  async function saveMemberPackage() {
    if (!elements.memberPackageForm) return;
    
    // 检查表单有效性
    if (!elements.memberPackageForm.checkValidity()) {
      elements.memberPackageForm.reportValidity();
      return;
    }
    
    try {
      showLoading();
      
      // 收集表单数据
      const formData = {
        name: elements.memberPackageName.value,
        duration: parseInt(elements.memberPackageDuration.value),
        price: parseFloat(elements.memberPackagePrice.value),
        discount_price: elements.memberPackageDiscountPrice.value ? parseFloat(elements.memberPackageDiscountPrice.value) : null,
        description: elements.memberPackageDescription.value,
        is_active: elements.memberPackageIsActive ? elements.memberPackageIsActive.checked : true
      };
      
      let response;
      
      if (currentEditId) {
        // 更新现有套餐
        response = await fetch(`/api/admin/payment/member-packages/${currentEditId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(formData)
        });
      } else {
        // 创建新套餐
        response = await fetch('/api/admin/payment/member-packages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(formData)
        });
      }

      if (!response.ok) {
        throw new Error('保存会员套餐失败');
      }

      const data = await response.json();
      
      if (data.success) {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(elements.memberPackageModal);
        if (modal) {
          modal.hide();
        }
        
        // 重新加载数据
        loadMemberPackages();
        
        showToast('success', currentEditId ? '会员套餐更新成功' : '会员套餐创建成功');
      } else {
        showToast('error', data.message || '保存会员套餐失败');
      }
    } catch (error) {
      console.error('保存会员套餐失败:', error);
      showToast('error', '保存会员套餐失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 切换会员套餐状态
   * @param {string} packageId 会员套餐ID
   * @param {boolean} isActive 是否激活
   */
  async function toggleMemberPackageStatus(packageId, isActive) {
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch(`/api/admin/payment/member-packages/${packageId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        },
        body: JSON.stringify({ is_active: isActive })
      });

      if (!response.ok) {
        throw new Error('更新会员套餐状态失败');
      }

      const data = await response.json();
      
      if (data.success) {
        // 重新加载数据
        loadMemberPackages();
        
        showToast('success', isActive ? '会员套餐已启用' : '会员套餐已停用');
      } else {
        showToast('error', data.message || '更新会员套餐状态失败');
      }
    } catch (error) {
      console.error('更新会员套餐状态失败:', error);
      showToast('error', '更新会员套餐状态失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 删除会员套餐
   * @param {string} packageId 会员套餐ID
   */
  async function deleteMemberPackage(packageId) {
    // 确认删除
    if (!confirm('确定要删除这个会员套餐吗？此操作不可恢复。')) {
      return;
    }
    
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch(`/api/admin/payment/member-packages/${packageId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('删除会员套餐失败');
      }

      const data = await response.json();
      
      if (data.success) {
        // 重新加载数据
        loadMemberPackages();
        
        showToast('success', data.message || '会员套餐删除成功');
      } else {
        showToast('error', data.message || '删除会员套餐失败');
      }
    } catch (error) {
      console.error('删除会员套餐失败:', error);
      showToast('error', '删除会员套餐失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 获取token
   * @returns {string} token
   */
  function getToken() {
    return localStorage.getItem('token') || '';
  }

  /**
   * 显示加载中
   */
  function showLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.style.display = 'flex';
    }
  }

  /**
   * 隐藏加载中
   */
  function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
  }

  /**
   * 显示提示信息
   * @param {string} type 提示类型
   * @param {string} message 提示信息
   */
  function showToast(type, message) {
    window.showToast(type, message);
  }

  /**
   * 监听页面可见性变化
   * @param {string} pageId 页面ID
   * @param {Function} callback 回调函数
   */
  function observePageVisibility(pageId, callback) {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const page = document.getElementById(pageId);
          if (page && !page.classList.contains('d-none')) {
            callback();
          }
        }
      });
    });
    
    const page = document.getElementById(pageId);
    if (page) {
      observer.observe(page, { attributes: true });
    }
  }

  // 公开API
  return {
    init,
    loadMemberPackages,
    showAddMemberPackageModal,
    editMemberPackage,
    toggleMemberPackageStatus,
    deleteMemberPackage
  };
})();

// 初始化模块
memberPackagesModule.init(); 
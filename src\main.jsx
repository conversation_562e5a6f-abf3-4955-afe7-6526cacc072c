import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import App from './App'
import './index.css'
import ErrorBoundary from './components/ErrorBoundary'
import { AuthProvider } from './contexts/AuthContext'
import { ThemeProvider } from './contexts/ThemeContext'
import { FeatureProvider } from './contexts/FeatureContext'
import { UserProvider } from './contexts/UserContext'

// 组件顺序:
// 1. BrowserRouter (最外层确保路由可用)
// 2. ThemeProvider (主题无依赖)
// 3. AuthProvider (认证状态)
// 4. FeatureProvider (依赖认证状态)
// 5. UserProvider (依赖认证状态)
// 6. App (最内层，依赖以上所有上下文)

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ErrorBoundary>
      <BrowserRouter>
        <ThemeProvider>
          <AuthProvider>
            <FeatureProvider>
              <UserProvider>
        <App />
              </UserProvider>
            </FeatureProvider>
          </AuthProvider>
        </ThemeProvider>
      </BrowserRouter>
    </ErrorBoundary>
  </React.StrictMode>
)
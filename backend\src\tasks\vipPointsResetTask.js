const { User, PointRecord } = require('../models');
const logger = require('../utils/logger');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

const resetVipPoints = async () => {
  // 获取今天的日期（只包含年月日）
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const transaction = await sequelize.transaction();
  
  try {
    // 查找所有有效的VIP用户
    const vipUsers = await User.findAll({
      where: {
        role: 'vip',
        vip_expire_date: {
          [Op.gt]: new Date() // 未过期的VIP
        }
      },
      transaction
    });
    
    if (vipUsers.length === 0) {
      logger.info('没有发现有效VIP用户');
      await transaction.commit();
      return;
    }
    
    logger.info(`发现${vipUsers.length}个有效VIP用户，开始处理每日积分`);
    
    // 为每个VIP用户添加积分
    for (const user of vipUsers) {
      // 添加100积分
      user.points += 100;
      user.last_points_reset = new Date(); // 更新重置时间
      await user.save({ transaction });
      
      // 创建积分记录
      await PointRecord.create({
        user_id: user.id,
        points_change: 100,
        points_after: user.points,
        operation_type: 'daily_reward',
        description: 'VIP用户每日积分奖励'
      }, { transaction });
      
      logger.info(`用户${user.id}(${user.phone})获得每日VIP积分100点`);
    }
    
    await transaction.commit();
    logger.info(`成功为${vipUsers.length}个VIP用户发放每日积分`);
  } catch (error) {
    await transaction.rollback();
    logger.error('处理VIP每日积分任务失败:', error);
    throw error;
  }
};

module.exports = resetVipPoints;

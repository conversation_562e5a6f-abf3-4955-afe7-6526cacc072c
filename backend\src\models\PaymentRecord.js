const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 支付记录模型
 * 对应数据库中的payment_records表
 */
const PaymentRecord = sequelize.define('PaymentRecord', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '支付记录ID，唯一标识'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联用户表的用户ID'
  },
  order_no: {
    type: DataTypes.STRING(50),
    allowNull: false,
    // 移除unique约束，避免创建额外索引
    // unique: true,
    comment: '系统生成的唯一订单号'
  },
  payment_type: {
    type: DataTypes.ENUM('wechat', 'alipay'),
    allowNull: false,
    comment: '支付方式：微信支付、支付宝'
  },
  payment_type_detail: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '支付方式详情(Native/H5/PC/Mobile)'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '支付金额，精确到分'
  },
  payment_status: {
    type: DataTypes.ENUM('pending', 'success', 'failed', 'refunded', 'closed'),
    defaultValue: 'pending',
    comment: '支付状态：待支付、支付成功、支付失败、已退款、已关闭'
  },
  refund_status: {
    type: DataTypes.ENUM('none', 'partial', 'full'),
    defaultValue: 'none',
    comment: '退款状态'
  },
  transaction_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '第三方支付平台返回的交易ID'
  },
  payment_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '支付完成时间'
  },
  notify_data: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '支付回调原始数据'
  },
  client_ip: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '客户端IP地址'
  },
  product_type: {
    type: DataTypes.ENUM('vip', 'points'),
    allowNull: false,
    comment: '购买的产品类型：VIP会员、积分'
  },
  product_detail: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '产品的详细描述'
  },
  display_status: {
    type: DataTypes.ENUM('显示', '隐藏'),
    allowNull: false,
    defaultValue: '显示',
    comment: '订单显示状态，控制在用户个人中心的显示状态'
  }
}, {
  tableName: 'payment_records',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

// 添加关联关系方法
PaymentRecord.associate = (models) => {
  // 支付记录属于一个用户
  PaymentRecord.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user'
  });
};

module.exports = PaymentRecord;

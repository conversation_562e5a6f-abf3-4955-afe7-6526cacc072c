/**
 * 创建测试会员套餐和积分套餐数据
 * 运行方式: node scripts/create-test-packages.js
 */

// 导入必要的模块
const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// 读取数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'fengmian_db',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  dialect: 'mysql'
};

// 创建Sequelize实例
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: false
  }
);

// 定义会员套餐模型
const MemberPackage = sequelize.define('member_packages', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: Sequelize.STRING,
    allowNull: false
  },
  duration: {
    type: Sequelize.INTEGER,
    allowNull: false
  },
  price: {
    type: Sequelize.DECIMAL(10, 2),
    allowNull: false
  },
  discount_price: {
    type: Sequelize.DECIMAL(10, 2),
    allowNull: true
  },
  description: {
    type: Sequelize.TEXT,
    allowNull: true
  },
  is_active: {
    type: Sequelize.BOOLEAN,
    defaultValue: true
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  },
  updated_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'member_packages'
});

// 定义积分套餐模型
const PointPackage = sequelize.define('point_packages', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: Sequelize.STRING,
    allowNull: false
  },
  points: {
    type: Sequelize.INTEGER,
    allowNull: false
  },
  price: {
    type: Sequelize.DECIMAL(10, 2),
    allowNull: false
  },
  bonus_points: {
    type: Sequelize.INTEGER,
    allowNull: true,
    defaultValue: 0
  },
  description: {
    type: Sequelize.TEXT,
    allowNull: true
  },
  is_active: {
    type: Sequelize.BOOLEAN,
    defaultValue: true
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  },
  updated_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  }
}, {
  timestamps: false,
  tableName: 'point_packages'
});

// 会员套餐测试数据
const memberPackagesData = [
  {
    name: '月度会员',
    duration: 30,
    price: 19.90,
    discount_price: 19.90,
    description: '体验高级会员一个月',
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: '季度会员',
    duration: 90,
    price: 49.90,
    discount_price: 39.90,
    description: '连续三个月高级会员服务，比月度更优惠',
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: '年度会员',
    duration: 365,
    price: 199.90,
    discount_price: 149.90,
    description: '整年高级会员服务，最超值选择',
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
  }
];

// 积分套餐测试数据
const pointPackagesData = [
  {
    name: '小额充值',
    points: 100,
    price: 10.00,
    bonus_points: 0,
    description: '充值100积分',
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: '标准充值',
    points: 500,
    price: 50.00,
    bonus_points: 50,
    description: '充值500积分，赠送50积分',
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
  },
  {
    name: '大额充值',
    points: 1200,
    price: 100.00,
    bonus_points: 200,
    description: '充值1200积分，赠送200积分',
    is_active: true,
    created_at: new Date(),
    updated_at: new Date()
  }
];

// 创建测试数据的主函数
async function createTestData() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 清空现有数据
    await MemberPackage.destroy({ where: {} });
    await PointPackage.destroy({ where: {} });
    console.log('已清空现有套餐数据');

    // 创建会员套餐数据
    await MemberPackage.bulkCreate(memberPackagesData);
    console.log('会员套餐测试数据创建成功');

    // 创建积分套餐数据
    await PointPackage.bulkCreate(pointPackagesData);
    console.log('积分套餐测试数据创建成功');

    console.log('所有测试数据创建完成');
  } catch (error) {
    console.error('创建测试数据失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行创建测试数据的函数
createTestData(); 
/**
 * historyUtils.js
 * 编辑器历史记录管理工具函数
 * 提供获取、应用编辑器状态以及防抖记录状态的功能
 */

/**
 * 获取当前编辑器状态
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @returns {Object|null} 编辑器状态对象，包含HTML内容和元素位置信息，如果获取失败则返回null
 */
export const getCurrentEditorState = (iframe) => {
  const iframeDoc = iframe?.contentDocument;
  if (!iframeDoc) return null;

  const contentContainer = iframeDoc.querySelector('.content-container');
  if (!contentContainer) return null;

  // 创建一个临时克隆，以便我们可以在不影响原始DOM的情况下清理UI元素
  const tempContainer = contentContainer.cloneNode(true);
  
  // 清理所有UI相关元素，确保它们不会被保存到历史状态中
  // 1. 删除所有删除按钮
  const deleteButtons = tempContainer.querySelectorAll('.fengmian-delete-button');
  deleteButtons.forEach(button => button.remove());
  
  // 2. 删除其他编辑器UI元素
  const uiElements = tempContainer.querySelectorAll(
    '.resize-handle, .drag-handle, .text-editor-toolbar, .text-editor-toolbar-container, ' +
    '.color-picker, .color-picker-wrapper, .color-picker-popup, .editor-control, ' +
    '.text-editor-controls, .text-editor-panel, [data-editor-ui="true"]'
  );
  uiElements.forEach(el => el.remove());

  // 收集可拖动元素的位置信息
  const positions = {};
  const draggables = contentContainer.querySelectorAll('.draggable, [data-editable-fengmian="true"]');
  
  draggables.forEach(el => {
    if (!el.id) {
      el.id = `editable-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    positions[el.id] = el.style.transform || '';
  });

  return {
    html: tempContainer.innerHTML, // 使用清理后的HTML
    positions: positions
  };
};

/**
 * 应用编辑器状态
 * @param {Object} state - 要应用的编辑器状态
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {Object} textEditor - 文本编辑器实例
 * @param {Function} onHtmlContentChange - HTML内容变化时的回调函数
 * @returns {void}
 */
export const applyEditorState = (state, iframe, textEditor, onHtmlContentChange) => {
  if (!state) return;
  
  const iframeDoc = iframe?.contentDocument;
  if (!iframeDoc) return;

  const contentContainer = iframeDoc.querySelector('.content-container');
  if (!contentContainer) return;
  
  try {
    // 保存并临时禁用任何可能的mutation observer
    const tempObserver = textEditor?.mutationObserver;
    if (tempObserver) {
      tempObserver.disconnect();
    }
    
    // 保存当前删除模式状态
    let currentDeleteMode = false;
    if (textEditor && typeof textEditor.getDeleteMode === 'function') {
      currentDeleteMode = textEditor.getDeleteMode();
    }
    
    // 应用HTML内容
    contentContainer.innerHTML = state.html;
    
    // 清理可能出现的删除按钮和其他UI元素
    const deleteButtons = contentContainer.querySelectorAll('.fengmian-delete-button');
    deleteButtons.forEach(button => button.remove());
    
    const uiElements = contentContainer.querySelectorAll('[data-editor-ui="true"]');
    uiElements.forEach(el => el.remove());
    
    // 重新初始化编辑器
    if (textEditor && typeof textEditor.init === 'function') {
      textEditor.init();
    }
    
    // 应用元素位置
    Object.entries(state.positions).forEach(([id, transform]) => {
      const element = iframeDoc.getElementById(id);
      if (element) {
        element.style.transform = transform;
      }
    });

    // 通知内容变化
    if (onHtmlContentChange) {
      onHtmlContentChange(contentContainer.innerHTML);
    }
    
    // 恢复删除模式状态
    setTimeout(() => {
      if (textEditor && typeof textEditor.setDeleteMode === 'function') {
        // 先关闭再打开，确保UI完全刷新
        textEditor.setDeleteMode(false);
        if (currentDeleteMode) {
          textEditor.setDeleteMode(true);
        }
      }
      
      // 重新连接mutation observer
      if (tempObserver && contentContainer) {
        try {
          tempObserver.observe(contentContainer, { 
            attributes: true, 
            childList: true, 
            subtree: true, 
            characterData: true 
          });
        } catch (error) {
          // 静默处理错误
        }
      }
    }, 50);
    
    return true;
  } catch (error) {
    // 静默处理错误
    return false;
  }
};

/**
 * 创建一个防抖记录状态的函数
 * @param {Function} getState - 获取状态的函数
 * @param {Object} historyRef - 历史记录管理器的引用
 * @param {number} delay - 防抖延迟时间（毫秒）
 * @returns {Function} 防抖记录状态函数
 */
export const createDebouncedRecordState = (getState, historyRef, delay = 300) => {
  let timeoutId;
  let lastStateJSON = '';
  
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      try {
        const state = getState();
        if (state && historyRef.current) {
          // 将当前状态转换为JSON字符串以便比较
          const currentStateJSON = JSON.stringify(state);
          
          // 只有当状态发生实质性变化时才记录
          if (currentStateJSON !== lastStateJSON) {
            historyRef.current.record(state);
            lastStateJSON = currentStateJSON;
          }
        }
      } catch (error) {
        // 静默处理错误
      }
    }, delay);
  };
}; 
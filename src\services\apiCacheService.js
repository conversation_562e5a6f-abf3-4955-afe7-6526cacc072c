/**
 * API 缓存服务
 * 提供统一的缓存管理，减少重复的API请求
 */
import axios from 'axios';

// 缓存对象，使用Map便于管理
const apiCache = new Map();

// 缓存锁，防止同一时间重复请求
const requestLocks = new Map();

// 缓存时间配置（毫秒）
const DEFAULT_TTL = 5 * 60 * 1000; // 5分钟
const SHORT_TTL = 30 * 1000; // 30秒
const LONG_TTL = 30 * 60 * 1000; // 30分钟
const MEDIUM_TTL = 5 * 60 * 1000; // 5分钟

/**
 * 从缓存获取数据或发起API请求
 * @param {string} url - 请求URL
 * @param {Object} options - 请求配置
 * @param {Object} cacheOptions - 缓存配置
 * @returns {Promise<any>} - 请求结果
 */
export async function cachedRequest(url, options = {}, cacheOptions = {}) {
  // 构建缓存键
  const cacheKey = getCacheKey(url, options);
  const now = Date.now();

  // 缓存配置
  const {
    ttl = DEFAULT_TTL,
    bypassCache = false,
    forceRefresh = false
  } = cacheOptions;

  // 如果有请求锁，等待现有请求完成
  if (requestLocks.has(cacheKey)) {
    try {
      await requestLocks.get(cacheKey);
    } catch (error) {
      // 忽略错误，继续检查缓存或发起新请求
    }
  }

  // 检查缓存
  const cachedData = apiCache.get(cacheKey);
  if (!bypassCache && !forceRefresh && cachedData && now < cachedData.expiresAt) {
    return cachedData.data;
  }

  // 创建请求锁
  let resolvePromise, rejectPromise;
  const lockPromise = new Promise((resolve, reject) => {
    resolvePromise = resolve;
    rejectPromise = reject;
  });
  requestLocks.set(cacheKey, lockPromise);

  // 发起API请求
  try {
    const response = await axios({ url, ...options });
    const responseData = response.data;

    // 更新缓存
    apiCache.set(cacheKey, {
      data: responseData,
      timestamp: now,
      expiresAt: now + ttl
    });

    resolvePromise(responseData);
    requestLocks.delete(cacheKey);
    return responseData;
  } catch (error) {
    rejectPromise(error);
    requestLocks.delete(cacheKey);
    throw error;
  }
}

/**
 * 构建缓存键
 * @param {string} url - 请求URL
 * @param {Object} options - 请求配置
 * @returns {string} - 缓存键
 */
function getCacheKey(url, options = {}) {
  const { method = 'GET', params, data } = options;
  const paramsString = params ? JSON.stringify(params) : '';
  const dataString = data ? JSON.stringify(data) : '';
  return `${method}:${url}:${paramsString}:${dataString}`;
}

/**
 * 获取缓存项
 * @param {string} cacheKey - 缓存键
 * @returns {Object|null} - 缓存项或null
 */
export function getCacheItem(cacheKey) {
  return apiCache.get(cacheKey);
}

/**
 * 设置缓存项
 * @param {string} cacheKey - 缓存键
 * @param {any} data - 缓存数据
 * @param {number} ttl - 缓存时间（毫秒）
 */
export function setCacheItem(cacheKey, data, ttl = DEFAULT_TTL) {
  const now = Date.now();
  apiCache.set(cacheKey, {
    data,
    timestamp: now,
    expiresAt: now + ttl
  });
}

/**
 * 移除缓存项
 * @param {string|RegExp} keyPattern - 缓存键或正则表达式
 */
export function removeCacheItem(keyPattern) {
  if (typeof keyPattern === 'string') {
    apiCache.delete(keyPattern);
  } else if (keyPattern instanceof RegExp) {
    // 移除匹配正则的所有缓存
    for (const key of apiCache.keys()) {
      if (keyPattern.test(key)) {
        apiCache.delete(key);
      }
    }
  }
}

/**
 * 清空所有缓存
 */
export function clearCache() {
  apiCache.clear();
}

/**
 * 专门用于认证相关请求的缓存函数
 * @param {string} token - 认证令牌
 * @returns {Promise<boolean>} 认证结果
 */
export async function checkAuthToken(token) {
  if (!token) return false;
  
  try {
    // 使用较短的缓存时间
    const response = await cachedRequest('/api/auth/me', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` }
    }, { ttl: SHORT_TTL });
    
    return response.success;
  } catch (error) {
    return false;
  }
}

/**
 * 专门用于获取用户资料的缓存函数
 * @param {string} token - 认证令牌
 * @param {boolean} forceRefresh - 是否强制刷新缓存
 * @returns {Promise<Object>} 用户资料
 */
export async function fetchUserProfile(token, forceRefresh = false) {
  if (!token) {
    throw new Error('No token provided');
  }
  
  try {
    const response = await cachedRequest('/api/user/profile', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` }
    }, { 
      forceRefresh // 传递forceRefresh参数，强制刷新缓存
    });
    
    return response.data;
  } catch (error) {
    throw error;
  }
}

/**
 * 清除用户资料相关的缓存
 * 在需要立即更新用户信息的场景使用，如支付完成后
 */
export function invalidateUserProfileCache() {
  // 移除用户资料相关的缓存
  removeCacheItem(/^GET:\/api\/user\/profile/);
}

/**
 * 获取风格模板列表（带缓存）
 * @param {string} token - 认证令牌
 * @param {boolean} forceRefresh - 是否强制刷新缓存
 * @returns {Promise<Array>} 风格模板列表
 */
export async function fetchStyleTemplates(token, forceRefresh = false) {
  if (!token) {
    throw new Error('No token provided');
  }
  
  try {
    const response = await cachedRequest('/api/style', {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` }
    }, { 
      ttl: LONG_TTL, // 风格模板使用长缓存
      forceRefresh 
    });
    
    return response.data || [];
  } catch (error) {
    console.error('获取风格模板失败:', error);
    return [];
  }
}

/**
 * 获取基础提示词模板（带缓存）
 * @param {string} token - 认证令牌
 * @param {string} sizeType - 尺寸类型标识码
 * @param {boolean} forceRefresh - 是否强制刷新缓存
 * @returns {Promise<Object>} 基础提示词模板
 */
export async function fetchBasePrompts(token, sizeType = '', forceRefresh = false) {
  if (!token) {
    throw new Error('No token provided');
  }
  
  try {
    const url = sizeType ? `/api/cover/base-prompts?id_code=${sizeType}` : '/api/cover/base-prompts';
    const response = await cachedRequest(url, {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` }
    }, { 
      ttl: LONG_TTL, // 基础模板使用长缓存
      forceRefresh 
    });
    
    return response.data || {};
  } catch (error) {
    console.error('获取基础提示词模板失败:', error);
    return {};
  }
}

/**
 * 通过cover_code获取封面详情（带缓存）
 * @param {string} token - 认证令牌
 * @param {string} coverCode - 封面代码
 * @param {boolean} forceRefresh - 是否强制刷新缓存
 * @returns {Promise<Object>} 封面详情
 */
export async function fetchCoverByCode(token, coverCode, forceRefresh = false) {
  if (!token) {
    throw new Error('No token provided');
  }
  
  if (!coverCode) {
    throw new Error('No cover code provided');
  }
  
  try {
    const response = await cachedRequest(`/api/cover/code/${coverCode}/edit`, {
      method: 'GET',
      headers: { Authorization: `Bearer ${token}` }
    }, { 
      ttl: MEDIUM_TTL, // 用户内容使用中等缓存
      forceRefresh 
    });
    
    return response.data || {};
  } catch (error) {
    console.error('获取封面详情失败:', error);
    throw error;
  }
}

/**
 * 使对应URL的缓存失效
 * @param {string} urlPattern - URL模式
 */
export function invalidateCache(urlPattern) {
  removeCacheItem(new RegExp(urlPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')));
} 
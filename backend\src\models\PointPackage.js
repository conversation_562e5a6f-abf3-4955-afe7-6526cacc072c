const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 积分充值套餐模型
 */
const PointPackage = sequelize.define('point_package', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '套餐名称'
  },
  points: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '积分数量'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '价格'
  },
  bonus_points: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '赠送积分'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '套餐描述'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否激活'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'point_packages',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

module.exports = PointPackage; 
/**
 * 删除元素工具模块
 * 处理HTML预览区域的元素删除功能
 */

/**
 * 为元素添加删除按钮
 * @param {HTMLElement} element - 需要添加删除按钮的元素
 * @param {Document} doc - 文档对象
 * @param {Function} onHtmlContentChange - HTML内容变更回调函数
 * @param {boolean} isDeleteMode - 是否处于删除模式
 * @returns {HTMLElement} - 创建的删除按钮元素
 */
export const addDeleteButton = (element, doc, onHtmlContentChange, isDeleteMode = false) => {
  // 如果元素已经有删除按钮，则先移除
  removeDeleteButton(element);
  
  if (!isDeleteMode) return null;
  
  // 创建删除按钮
  const deleteButton = doc.createElement('div');
  deleteButton.className = 'fengmian-delete-button';
  deleteButton.title = '删除元素';
  
  // 设置样式
  Object.assign(deleteButton.style, {
    position: 'absolute',
    top: '-10px',
    right: '-10px',
    width: '20px',
    height: '20px',
    borderRadius: '50%',
    background: 'rgba(255, 0, 0, 0.8)',
    color: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: 'bold',
    zIndex: '9999',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
    border: '1px solid white',
    opacity: '0', // 默认不可见
    transition: 'opacity 0.2s ease',
    pointerEvents: 'auto' // 确保点击事件能被捕获
  });
  
  // 添加删除图标
  deleteButton.innerHTML = '×';
  
  // 添加鼠标悬停显示效果
  element.addEventListener('mouseenter', () => {
    if (isDeleteMode && deleteButton.parentElement) {
      deleteButton.style.opacity = '1';
    }
  });
  
  element.addEventListener('mouseleave', () => {
    if (deleteButton.parentElement) {
      deleteButton.style.opacity = '0';
    }
  });
  
  // 添加删除事件
  deleteButton.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    // 从DOM中移除元素
    element.remove();
    
    // 更新HTML内容
    const container = doc.querySelector('.content-container');
    if (container && onHtmlContentChange) {
      onHtmlContentChange(container.innerHTML);
    }
  });
  
  // 防止事件冒泡
  deleteButton.addEventListener('mousedown', (e) => {
    e.preventDefault();
    e.stopPropagation();
  });
  
  // 将删除按钮添加到元素中
  element.appendChild(deleteButton);
  
  // 确保元素有position:relative以便正确放置删除按钮
  const computedStyle = window.getComputedStyle(element);
  if (computedStyle.position === 'static') {
    element.style.position = 'relative';
  }
  
  // 添加删除按钮引用到元素的dataset
  element.dataset.hasDeleteButton = 'true';
  
  // 返回创建的删除按钮
  return deleteButton;
};

/**
 * 移除元素的删除按钮
 * @param {HTMLElement} element - 需要移除删除按钮的元素
 */
export const removeDeleteButton = (element) => {
  if (!element) return;
  
  const deleteButton = element.querySelector('.fengmian-delete-button');
  if (deleteButton) {
    deleteButton.remove();
  }
  
  delete element.dataset.hasDeleteButton;
};

/**
 * 更新所有元素的删除按钮显示状态
 * @param {Document} doc - 文档对象
 * @param {boolean} isDeleteMode - 是否处于删除模式
 * @param {Function} onHtmlContentChange - HTML内容变更回调函数
 */
export const updateAllDeleteButtons = (doc, isDeleteMode, onHtmlContentChange) => {
  // 查找所有可编辑元素
  const elements = doc.querySelectorAll('[data-editable-fengmian="true"], [contenteditable="true"]');
  
  elements.forEach(element => {
    if (isDeleteMode) {
      addDeleteButton(element, doc, onHtmlContentChange, true);
    } else {
      removeDeleteButton(element);
    }
  });
}; 
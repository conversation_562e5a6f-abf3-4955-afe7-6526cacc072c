import { Modal } from 'antd';
import { useCallback, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { cancelCoverGeneration, cancelCoverGenerationBeacon, getCurrentTaskId } from '../services/aiService';

/**
 * 页面离开警告工具 - 适用于React Router v6.4+
 * 提供在封面生成过程中阻止用户离开页面的功能
 */

/**
 * 处理beforeunload事件，防止用户关闭或刷新页面
 * @param {boolean} isGenerating - 是否正在生成封面
 * @returns {Function} 事件处理函数
 */
export const handleBeforeUnload = (isGenerating) => (e) => {
  // 检查当前页面是否是登录或注册页面，或者是否刚从登录/注册页面跳转过来
  const isAuthPage = window.location.pathname === '/auth' || 
                     window.location.pathname === '/login' || 
                     window.location.pathname === '/register' ||
                     window.location.pathname === '/forgot-password';
  
  // 检查是否是刚刚登录或注册成功
  const referrer = document.referrer;
  const isFromAuthPage = referrer && (
    referrer.includes('/auth') || 
    referrer.includes('/login') || 
    referrer.includes('/register') || 
    referrer.includes('/forgot-password')
  );
  
  // 如果是登录/注册页面或刚从这些页面跳转过来，不触发警告
  if (isAuthPage || isFromAuthPage) {
    return;
  }

  if (isGenerating) {
    // 不再调用cancelCoverGenerationBeacon()，避免与usePointsManagement.js中的cancelCoverGeneration()重复
    // 积分扣除和任务取消已在usePointsManagement.js中的handleBeforeUnloadPoints函数中处理

    const message = '您的封面正在生成，请勿离开此页面，以免导致封面生成中断。';
    e.returnValue = message;
    return message;
  }
};

/**
 * 设置beforeunload事件监听
 * @param {boolean} isGenerating - 是否正在生成封面
 */
export const setupBeforeUnloadListener = (isGenerating) => {
  // 创建一个唯一的事件处理函数引用，以便能够正确地移除它
  const handler = handleBeforeUnload(isGenerating);

  // 添加页面离开事件监听
  window.addEventListener('beforeunload', handler);

  // 返回清理函数
  return () => {
    window.removeEventListener('beforeunload', handler);
  };
};

/**
 * 处理应用内路由导航离开确认(新版React Router v6.4+)
 * 使用路由位置变化检测和历史状态管理实现导航拦截
 * @param {Object} options - 配置选项
 * @param {boolean} options.isGenerating - 是否正在生成封面
 * @param {Function} options.setPendingNavigation - 设置待处理导航的状态函数
 * @param {Object} options.logger - 日志记录工具
 * @returns {Object} 包含cleanup方法的对象
 */
export const setupNavigationBlocker = ({
  isGenerating,
  setPendingNavigation,
  logger = console
}) => {
  // 如果未提供必要参数或没有生成中，返回空函数
  if (!setPendingNavigation || !isGenerating) {
    return { cleanup: () => {} };
  }

  // 手动维护一个标志，表示拦截器是否处于活动状态
  let isActive = true;

  // 创建一个自定义历史状态，用于跟踪导航意图
  if (typeof window !== 'undefined') {
    // 添加自定义状态用于标记当前路径已被拦截器处理
    window.history.replaceState(
      { ...window.history.state, __blockerHandled: true },
      document.title
    );
  }

  // 监听popstate事件（浏览器后退/前进按钮）
  const handlePopState = (event) => {
    // 如果拦截器已禁用或状态已被处理，不进行拦截
    if (!isActive || (event.state && event.state.__blockerHandled)) {
      return;
    }

    // 阻止默认导航行为
    event.preventDefault();

    // 记录拦截的导航请求
    logger.info?.('拦截浏览器返回按钮导航请求');

    // 显示确认对话框
    Modal.confirm({
      title: '离开提示',
      content: '您的封面正在生成，请勿离开此页面，以免导致封面生成中断。',
      okText: '继续离开',
      cancelText: '等待完成',
      onOk: () => {
        // 暂时禁用拦截器允许导航
        isActive = false;
        logger.info?.('用户确认离开页面');

        // 修改状态标记已处理
        history.back();
      },
      onCancel: () => {
        logger.info?.('用户取消离开页面');
        // 用户选择留在当前页面，什么都不做
      }
    });
  };

  // 添加popstate事件监听
  if (typeof window !== 'undefined') {
    window.addEventListener('popstate', handlePopState);
  }

  // 返回清理函数
  return {
    cleanup: () => {
      logger.info?.('移除导航拦截');
      isActive = false;
      if (typeof window !== 'undefined') {
        window.removeEventListener('popstate', handlePopState);
      }
    }
  };
};

/**
 * 页面离开拦截器Hook - 适用于React Router v6.4+
 * 结合浏览器原生事件和React Router导航
 * @param {boolean} isGenerating - 是否在生成内容（需要阻止导航）
 * @param {Function} setPendingNavigation - 设置待处理导航的状态函数
 * @param {Object} logger - 日志工具
 * @param {number} pointsCost - 需要扣除的积分数量
 * @param {boolean} pointsDeducted - 是否已经扣除积分
 * @param {Function} setPointsDeducted - 设置积分扣除状态的函数
 * @param {Function} consumePoints - 扣除积分的函数
 * @param {boolean} disableBeforeUnload - 是否禁用beforeunload事件监听器
 * @returns {Object} 清理函数
 */
export const useNavigationGuard = (
  isGenerating,
  setPendingNavigation,
  logger = console,
  pointsCost = 0,
  pointsDeducted = false,
  setPointsDeducted = null,
  consumePoints = null,
  disableBeforeUnload = false
) => {
  const navigate = useNavigate();
  const location = useLocation();
  const lastLocationRef = useRef(location);

  // 通知当前的路径已变化
  useEffect(() => {
    lastLocationRef.current = location;
  }, [location]);

  // 扣除积分函数
  const deductPointsOnLeave = useCallback(async () => {
    // 如果没有提供所需函数或已扣除积分，则不执行
    if (!consumePoints || !setPointsDeducted || pointsDeducted || pointsCost <= 0) {
      return;
    }

    try {
      logger.info?.('用户离开页面，扣除积分', { pointsCost });

      // 扣除积分
      const result = await consumePoints(pointsCost, '生成封面(离开页面)');

      if (result.success) {
        logger.info?.('积分扣除成功', { points: pointsCost, remaining: result.data.points });
        setPointsDeducted(true);

        // 清除localStorage中可能存在的未完成任务
        if (typeof window !== 'undefined') {
          localStorage.removeItem('fengmian_unfinished_points_task');
        }
      } else {
        logger.error?.('积分扣除失败', { error: result.message });
      }
    } catch (error) {
      logger.error?.('积分扣除请求失败', { error: error.message });
    }
  }, [consumePoints, setPointsDeducted, pointsDeducted, pointsCost, logger]);

  // 取消生成任务函数
  const cancelGenerationOnLeave = useCallback(async () => {
    if (!isGenerating) {
      return;
    }

    try {
      logger.info?.('用户离开页面，取消生成任务');
      await cancelCoverGeneration(); // 使用普通取消
    } catch (error) {
      logger.error?.('取消生成任务失败', { error: error.message });
    }
  }, [isGenerating, logger]);

  // 设置beforeunload事件拦截（浏览器刷新/关闭）
  const cleanupBeforeUnload = disableBeforeUnload ? () => {} : setupBeforeUnloadListener(isGenerating);

  // 创建链接点击拦截处理函数
  const handleLinkClick = useCallback((e) => {
    // 只处理生成中且未被处理过的链接点击
    if (!isGenerating || e.__blockerHandled) {
      return;
    }

    // 查找最近的a标签
    let target = e.target;
    while (target && target.tagName !== 'A') {
      target = target.parentNode;
    }

    // 如果不是链接或者是外部链接，不处理
    if (!target || !target.href || target.target === '_blank' ||
        target.getAttribute('href')?.startsWith('#') ||
        target.getAttribute('href')?.startsWith('http')) {
      return;
    }

    // 检查链接是否指向同一站点内的路径（防止拦截外部链接）
    const url = new URL(target.href);
    if (url.origin !== window.location.origin) {
      return;
    }

    // 阻止默认导航
    e.preventDefault();
    e.stopPropagation();
    e.__blockerHandled = true;

    // 保存目标路径
    const targetPath = url.pathname + url.search + url.hash;

    // 显示确认对话框
    Modal.confirm({
      title: '离开提示',
      content: '您的封面正在生成，请勿离开此页面，以免导致封面生成中断。',
      okText: '继续离开',
      cancelText: '等待完成',
      onOk: async () => {
        // 用户选择继续离开，扣除积分并取消生成 - 情况2
        // 正确的积分扣除已由 usePointsManagement 的 beforeunload 逻辑处理
        // 此处不再调用 deductPointsOnLeave()，只取消生成任务
        await Promise.all([
          // deductPointsOnLeave(), // 注释掉此行以避免冗余和错误的API调用
          cancelGenerationOnLeave()
        ]);

        // 导航到目标路径
        navigate(targetPath);
      },
      onCancel: () => {
        // 用户选择留在当前页面
        logger.info?.('用户取消离开页面');
        if (setPendingNavigation) {
          setPendingNavigation(null);
        }
      }
    });
  }, [isGenerating, navigate, setPendingNavigation, logger, cancelGenerationOnLeave]);

  // 在全局监听点击事件，捕获所有导航链接的点击
  useEffect(() => {
    if (!isGenerating) return () => {};

    // 使用捕获阶段以便能够拦截事件
    document.addEventListener('click', handleLinkClick, true);

    return () => {
      document.removeEventListener('click', handleLinkClick, true);
    };
  }, [handleLinkClick, isGenerating]);

  // 设置popstate（浏览器前进/后退按钮）事件拦截
  const setupNavigationBlockerWithPoints = () => {
    // 如果未提供必要参数或没有生成中，返回空函数
    if (!setPendingNavigation || !isGenerating) {
      return { cleanup: () => {} };
    }

    // 手动维护一个标志，表示拦截器是否处于活动状态
    let isActive = true;

    // 创建一个自定义历史状态，用于跟踪导航意图
    if (typeof window !== 'undefined') {
      // 添加自定义状态用于标记当前路径已被拦截器处理
      window.history.replaceState(
        { ...window.history.state, __blockerHandled: true },
        document.title
      );
    }

    // 监听popstate事件（浏览器后退/前进按钮）
    const handlePopState = (event) => {
      // 如果拦截器已禁用或状态已被处理，不进行拦截
      if (!isActive || (event.state && event.state.__blockerHandled)) {
        return;
      }

      // 阻止默认导航行为
      event.preventDefault();

      // 记录拦截的导航请求
      logger.info?.('拦截浏览器返回按钮导航请求');

      // 显示确认对话框
      Modal.confirm({
        title: '离开提示',
        content: '您的封面正在生成，请勿离开此页面，以免导致封面生成中断。',
        okText: '继续离开',
        cancelText: '等待完成',
        onOk: async () => {
          // 用户选择继续离开，扣除积分并取消生成 - 情况2
          // 正确的积分扣除已由 usePointsManagement 的 beforeunload 逻辑处理
          // 此处不再调用 deductPointsOnLeave()，只取消生成任务
          await Promise.all([
            // deductPointsOnLeave(), // 注释掉此行以避免冗余和错误的API调用
            cancelGenerationOnLeave()
          ]);

          // 导航到目标路径
          const targetPath = window.history.state?.usr?.from || '/';
          navigate(targetPath);
        },
        onCancel: () => {
          logger.info?.('用户取消离开页面');
          // 用户选择留在当前页面，什么都不做
        }
      });
    };

    // 添加popstate事件监听
    if (typeof window !== 'undefined') {
      window.addEventListener('popstate', handlePopState);
    }

    // 返回清理函数
    return {
      cleanup: () => {
        logger.info?.('移除导航拦截');
        isActive = false;
        if (typeof window !== 'undefined') {
          window.removeEventListener('popstate', handlePopState);
        }
      }
    };
  };

  // 设置popstate（浏览器前进/后退按钮）事件拦截
  const { cleanup: cleanupPopState } = setupNavigationBlockerWithPoints();

  // 返回一个清理函数，在组件卸载时清理所有事件监听
  return {
    cleanup: () => {
      cleanupBeforeUnload();
      cleanupPopState();
    }
  };
};
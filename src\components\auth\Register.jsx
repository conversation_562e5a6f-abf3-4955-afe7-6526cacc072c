import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Smartphone, KeyRound, User, Lock, UserPlus, Loader2 } from 'lucide-react';
import axios from 'axios';
import { useToast } from '@/components/ui/use-toast';

// API调用函数
const sendSmsCode = async (phone, purpose) => {
  try {
    const response = await axios.post('/api/sms/send', { phone, purpose });
    return response.data;
  } catch (error) {
    console.error('发送验证码失败:', error);
    throw error;
  }
};

const register = async (data) => {
  try {
    const response = await axios.post('/api/auth/register', data);
    return response.data;
  } catch (error) {
    console.error('注册失败:', error);
    if (error.response) {
      const errorData = {
        status: error.response.status,
        message: error.response.data?.message || '注册失败，请重试'
      };
      throw errorData;
    }
    throw error;
  }
};

const registerByVerifyCode = async (data) => {
  try {
    const response = await axios.post('/api/auth/register/verify-code', data);
    return response.data;
  } catch (error) {
    console.error('验证码注册失败:', error);
     if (error.response) {
       const errorData = {
         status: error.response.status,
         message: error.response.data?.message || '注册失败，请重试'
       };
       throw errorData;
     }
    throw error;
  }
};

const Register = () => {
  const [phone, setPhone] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [nickname, setNickname] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [sendingCode, setSendingCode] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();

  // 发送验证码
  const handleSendCode = async () => {
    if (!phone) {
      setErrorMessage('请输入手机号');
      return;
    }
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      setErrorMessage('请输入有效的手机号');
      return;
    }

    setErrorMessage('');
      setSendingCode(true);
    try {
      const response = await sendSmsCode(phone, 'register');

      if (response.success) {
        setErrorMessage('');
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setErrorMessage(response.message || '验证码发送失败');
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
       if (error.response && error.response.data) {
         setErrorMessage(error.response.data.message || '验证码发送失败');
       } else {
         setErrorMessage('验证码发送失败，请检查网络连接或手机号');
       }
    } finally {
      setSendingCode(false);
    }
  };

  // 表单提交
  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');
    setLoading(true);

    // 验证手机号
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      setErrorMessage('请输入有效的手机号');
      setLoading(false);
      return;
    }

    // 验证验证码
    if (!verifyCode || !/^\d{6}$/.test(verifyCode)) {
      setErrorMessage('请输入有效的6位验证码');
      setLoading(false);
      return;
    }

    // 验证密码（如果提供）
    if (password && !/^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/.test(password)) {
      setErrorMessage('密码必须包含字母和数字，长度必须在8-32个字符之间');
      setLoading(false);
      return;
    }

    try {
      const response = await registerByVerifyCode({
        phone,
        verifyCode,
        nickname: nickname || `用户${phone.substring(7)}`,
        password: password || '' // 可选密码
      });

      if (response.success) {
        setErrorMessage('');

        // 使用toast通知代替内部成功消息
        toast({
          title: '注册成功',
          description: '即将跳转到首页',
          variant: 'default'
        });

        // 保存token和用户信息，实现自动登录
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.user));

        // 获取URL参数中的redirect参数
        const urlParams = new URLSearchParams(window.location.search);
        const redirectUrl = urlParams.get('redirect');

        // 如果有重定向地址，则重定向到该地址，否则跳转到首页
        setTimeout(() => {
          if (redirectUrl) {
            window.location.href = redirectUrl;
          } else {
            window.location.href = '/';
    }
        }, 800);
      } else {
        setErrorMessage(response.message || '注册失败，请重试');
      }
    } catch (error) {
      console.error('注册失败:', error);
      if (error.message) {
        setErrorMessage(error.message);
      } else if (error.response && error.response.data) {
        setErrorMessage(error.response.data.message || '注册失败，请重试');
      } else {
        setErrorMessage('注册失败，请检查网络连接');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full flex flex-col items-center">
      <div className="w-full bg-card shadow-lg border border-border/20 rounded-xl">
        <div className="text-center pt-8 pb-4 sm:pt-6 sm:pb-4">
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
            注册账号
          </h1>
        </div>

        <div className="space-y-6 px-6 sm:px-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="register_phone" className="text-sm font-medium text-muted-foreground">手机号码</label>
              <div className="relative">
                <Smartphone className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <input
                  id="register_phone"
                  type="tel"
                  placeholder="请输入您的手机号"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  required
                  className="pl-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="register_verifyCode" className="text-sm font-medium text-muted-foreground">验证码</label>
              <div className="flex space-x-2">
                <div className="relative flex-grow">
                  <KeyRound className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <input
                    id="register_verifyCode"
                    type="text"
                    placeholder="请输入6位验证码"
                    value={verifyCode}
                    onChange={(e) => setVerifyCode(e.target.value)}
                    required
                    className="pl-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          />
                </div>
                <button
                  type="button"
            onClick={handleSendCode}
                  disabled={sendingCode || countdown > 0}
                  className="shrink-0 h-10 sm:h-11 border-primary text-primary hover:bg-primary/10 disabled:opacity-70 rounded-md border px-3 py-2 text-sm font-medium"
          >
                  {sendingCode ? <Loader2 className="h-4 w-4 animate-spin" /> : (countdown > 0 ? `${countdown}秒` : '获取验证码')}
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="register_nickname" className="text-sm font-medium text-muted-foreground">
                昵称 <span className="text-xs text-muted-foreground">(选填，默认自动生成)</span>
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <input
                  id="register_nickname"
                  type="text"
                  placeholder="请输入您的昵称"
                  value={nickname}
                  onChange={(e) => setNickname(e.target.value)}
                  className="pl-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="register_password" className="text-sm font-medium text-muted-foreground">
                密码 <span className="text-xs text-muted-foreground">(选填，可以在登录后-个人资料界面设置密码)</span>
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <input
                  id="register_password"
                  type="password"
                  placeholder="设置密码（8-32位，包含字母和数字）"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                />
              </div>
              {password && password.length > 0 && !/^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/.test(password) && (
                <p className="text-xs text-red-500">密码必须包含字母和数字，长度必须在8-32个字符之间</p>
              )}
            </div>

            {errorMessage && (
              <div className="text-sm text-red-500 text-center">{errorMessage}</div>
            )}

            <button
              type="submit"
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90 text-base py-2.5 sm:py-3 h-auto shadow-md hover:shadow-lg transition-all duration-300 rounded-md flex items-center justify-center"
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              ) : (
                <UserPlus className="mr-2 h-5 w-5" />
              )}
              {loading ? '注册中...' : '立即注册'}
            </button>
          </form>
        </div>

        <div className="text-center pb-8 sm:pb-10 px-6 sm:px-8 mt-6">
          <p className="text-sm text-muted-foreground w-full">
            已有账户？{' '}
            <Link to="/auth" className="font-semibold text-primary hover:underline hover:text-primary/80">
              立即登录
            </Link>
          </p>
          <p className="text-xs text-muted-foreground mt-2">
            注册即登录，视为您已阅读并同意本站的{' '}
            <Link to="/policies/agreement" target="_blank" className="text-primary hover:underline">
              用户协议
            </Link>
            {' '}和{' '}
            <Link to="/policies/privacy" target="_blank" className="text-primary hover:underline">
              隐私政策
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Register;
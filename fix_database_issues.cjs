/**
 * 修复数据库问题
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, 'backend/.env') });
const { sequelize } = require('./backend/src/config/database');

async function fixDatabaseIssues() {
  try {
    console.log('🔧 修复数据库问题...');
    
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 1. 检查并创建html_security_violations表
    console.log('\n📋 检查html_security_violations表...');
    try {
      const [tables] = await sequelize.query("SHOW TABLES LIKE 'html_security_violations'");
      
      if (tables.length === 0) {
        console.log('❌ html_security_violations表不存在，正在创建...');
        
        await sequelize.query(`
          CREATE TABLE html_security_violations (
            id INT PRIMARY KEY AUTO_INCREMENT COMMENT '违规记录ID，唯一标识',
            user_id INT NOT NULL COMMENT '用户ID',
            user_phone VARCHAR(20) COMMENT '用户手机号',
            file_name VARCHAR(255) COMMENT '原始文件名',
            upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
            risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') COMMENT '风险等级',
            violation_reasons JSON COMMENT '违规原因详情',
            detected_threats JSON COMMENT '检测到的威胁列表',
            file_size INT COMMENT '文件大小（字节）',
            file_hash VARCHAR(64) COMMENT '文件内容SHA256哈希',
            detection_engine_version VARCHAR(50) COMMENT '检测引擎版本',
            ip_address VARCHAR(45) COMMENT '上传IP地址',
            user_agent TEXT COMMENT '用户代理信息',
            INDEX idx_user_id (user_id),
            INDEX idx_upload_time (upload_time),
            INDEX idx_risk_level (risk_level)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='HTML安全违规记录表'
        `);
        
        console.log('✅ html_security_violations表创建成功');
        
        // 插入测试数据
        await sequelize.query(`
          INSERT INTO html_security_violations 
          (user_id, file_name, risk_level, violation_reasons, detected_threats, file_size, ip_address) 
          VALUES 
          (1, 'test1.html', 'HIGH', 
           JSON_OBJECT('xss_detected', true, 'script_tags', 2), 
           JSON_ARRAY('XSS攻击', '恶意脚本'), 
           1024, '127.0.0.1'),
          (1, 'test2.html', 'MEDIUM', 
           JSON_OBJECT('suspicious_code', true, 'eval_calls', 1), 
           JSON_ARRAY('可疑代码', 'eval调用'), 
           2048, '127.0.0.1'),
          (1, 'test3.html', 'LOW', 
           JSON_OBJECT('iframe_detected', true), 
           JSON_ARRAY('iframe标签'), 
           512, '127.0.0.1')
        `);
        
        console.log('✅ 测试数据插入成功');
      } else {
        console.log('✅ html_security_violations表已存在');
        
        // 检查数据数量
        const [count] = await sequelize.query('SELECT COUNT(*) as count FROM html_security_violations');
        console.log(`📊 当前违规记录数: ${count[0].count}`);
        
        if (count[0].count === 0) {
          console.log('⚠️ 表中无数据，插入测试数据...');
          await sequelize.query(`
            INSERT INTO html_security_violations 
            (user_id, file_name, risk_level, violation_reasons, detected_threats, file_size, ip_address) 
            VALUES 
            (1, 'test1.html', 'HIGH', 
             JSON_OBJECT('xss_detected', true, 'script_tags', 2), 
             JSON_ARRAY('XSS攻击', '恶意脚本'), 
             1024, '127.0.0.1'),
            (1, 'test2.html', 'MEDIUM', 
             JSON_OBJECT('suspicious_code', true, 'eval_calls', 1), 
             JSON_ARRAY('可疑代码', 'eval调用'), 
             2048, '127.0.0.1')
          `);
          console.log('✅ 测试数据插入成功');
        }
      }
    } catch (error) {
      console.log('❌ 处理html_security_violations表失败:', error.message);
    }
    
    // 2. 验证html_security_configs表数据
    console.log('\n📋 验证html_security_configs表数据...');
    try {
      const [configs] = await sequelize.query('SELECT * FROM html_security_configs');
      console.log(`配置数量: ${configs.length}`);
      
      configs.forEach((config, index) => {
        console.log(`\n配置 ${index + 1}:`);
        console.log(`  config_key: ${config.config_key}`);
        console.log(`  description: ${config.description}`);
        console.log(`  config_value类型: ${typeof config.config_value}`);
        
        // 验证JSON数据
        if (typeof config.config_value === 'object') {
          console.log(`  ✅ config_value是对象，无需JSON.parse`);
        } else if (typeof config.config_value === 'string') {
          try {
            JSON.parse(config.config_value);
            console.log(`  ✅ config_value是有效JSON字符串`);
          } catch (e) {
            console.log(`  ❌ config_value JSON格式错误: ${e.message}`);
          }
        }
      });
    } catch (error) {
      console.log('❌ 验证html_security_configs表失败:', error.message);
    }
    
    await sequelize.close();
    console.log('\n✅ 数据库问题修复完成');
    
  } catch (error) {
    console.log('❌ 修复失败:', error.message);
    console.log('错误详情:', error);
  }
}

fixDatabaseIssues();

/**
 * 文本编辑器工具模块 - 聊天风格布局版本
 * 用于封面编辑器中的文本编辑功能
 *
 * 这个文件是为了保持向后兼容性，实际功能已拆分到以下文件：
 * - textEditorCore.js: 核心功能和共享状态
 * - dragMode.js: 拖拽模式相关功能
 * - editMode.js: 编辑模式相关功能
 * - textEditorMiddleware.js: 模式切换中间件
 */

import { getTextEditorStyles, getKeyboardNavigationScript, getTextElementInitScript, createTextEditor } from './textEditorCore';

// 导出所有函数，保持向后兼容性
export {
  getTextEditorStyles,
  getKeyboardNavigationScript,
  getTextElementInitScript,
  createTextEditor
};
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chart.js测试</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
  <div class="container mt-5">
    <h1>Chart.js测试</h1>
    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title">测试图表</h5>
          </div>
          <div class="card-body">
            <canvas id="testChart" height="250"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      console.log('页面加载完成');
      
      // 检查Chart对象是否存在
      if (typeof Chart === 'undefined') {
        console.error('Chart.js未正确加载');
        document.body.innerHTML += '<div class="alert alert-danger mt-3">Chart.js未正确加载</div>';
        return;
      }
      
      console.log('Chart.js已加载，版本:', Chart.version);
      document.body.innerHTML += `<div class="alert alert-success mt-3">Chart.js已加载，版本: ${Chart.version}</div>`;
      
      try {
        // 获取Canvas元素
        const ctx = document.getElementById('testChart').getContext('2d');
        
        // 创建一个简单的图表
        const chart = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
            datasets: [{
              label: '测试数据',
              data: [12, 19, 3, 5, 2, 3],
              backgroundColor: [
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 206, 86, 0.2)',
                'rgba(75, 192, 192, 0.2)',
                'rgba(153, 102, 255, 0.2)',
                'rgba(255, 159, 64, 0.2)'
              ],
              borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)'
              ],
              borderWidth: 1
            }]
          },
          options: {
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
        
        console.log('图表创建成功');
        document.body.innerHTML += '<div class="alert alert-success mt-3">图表创建成功</div>';
      } catch (error) {
        console.error('创建图表时出错:', error);
        document.body.innerHTML += `<div class="alert alert-danger mt-3">创建图表时出错: ${error.message}</div>`;
      }
    });
  </script>
</body>
</html>

/**
 * 支付工具函数库，提供证书获取、缓存和管理功能
 */
const axios = require('axios');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { sequelize } = require('../models');
const { PaymentConfig } = require('../models');
const { decryptPaymentKey } = require('./encryption');
const logger = require('./logger');

// 证书缓存，避免频繁请求微信支付API
let wechatpayCertificates = null;
let certificatesExpireTime = null;

/**
 * 获取微信支付平台证书，用于验证签名
 * @param {boolean} forceRefresh 是否强制刷新证书
 * @returns {Promise<Array>} 微信支付平台证书列表
 */
const getWechatPayCertificates = async (forceRefresh = false) => {
  try {
    // 如果证书已经缓存且未过期且不强制刷新，则直接返回缓存的证书
    const now = Date.now();
    if (
      !forceRefresh && 
      wechatpayCertificates && 
      certificatesExpireTime && 
      now < certificatesExpireTime
    ) {
      logger.info('使用缓存的微信支付平台证书');
      return wechatpayCertificates;
    }

    // 从数据库获取微信支付配置
    const config = await PaymentConfig.findOne({
      where: {
        payment_type: 'wechat',
        config_key: 'apiv3_key',
        is_active: true
      }
    });

    if (!config) {
      throw new Error('未找到APIv3密钥配置');
    }

    // 解密APIv3密钥
    const apiV3Key = config.is_encrypted 
      ? decryptPaymentKey(config.config_value) 
      : config.config_value;

    // 获取商户私钥和证书序列号，用于请求微信支付API
    const privateKey = await getWechatPayPrivateKey();
    const serialNo = await getWechatPaySerialNo();

    // 获取签名
    const method = 'GET';
    const url = '/v3/certificates';
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const nonceStr = generateNonceStr();
    const message = `${method}\\n${url}\\n${timestamp}\\n${nonceStr}\\n\\n`;
    
    // 使用商户私钥对消息进行签名
    const signature = crypto.createSign('RSA-SHA256')
      .update(message)
      .sign(privateKey, 'base64');
    
    // 商户号
    const mchid = await getMerchantId();

    // 请求微信支付平台，获取证书列表
    const response = await axios.get('https://api.mch.weixin.qq.com/v3/certificates', {
      headers: {
        'Authorization': `WECHATPAY2-SHA256-RSA2048 mchid="${mchid}",nonce_str="${nonceStr}",signature="${signature}",timestamp="${timestamp}",serial_no="${serialNo}"`,
        'Accept': 'application/json',
        'User-Agent': 'Mozilla/5.0 (NodeJS) WechatPay/V3'
      }
    });

    if (!response.data || !response.data.data || !Array.isArray(response.data.data)) {
      throw new Error('获取微信支付平台证书失败：响应格式异常');
    }
    
    // 处理返回的证书数据
    const certificates = [];
    for (const item of response.data.data) {
      const cert = item.encrypt_certificate;
      
      // 使用AES-256-GCM解密证书内容
      const ciphertext = Buffer.from(cert.ciphertext, 'base64');
      const authTag = ciphertext.slice(ciphertext.length - 16);
      const data = ciphertext.slice(0, ciphertext.length - 16);
      const decipher = crypto.createDecipheriv(
        'aes-256-gcm',
        Buffer.from(apiV3Key, 'utf8'),
        Buffer.from(cert.nonce, 'utf8')
      );
      
      decipher.setAuthTag(authTag);
      decipher.setAAD(Buffer.from(cert.associated_data, 'utf8'));
      
      const decrypted = Buffer.concat([
        decipher.update(data),
        decipher.final()
      ]);
      
      certificates.push({
        serial_no: item.serial_no,
        effective_time: item.effective_time,
        expire_time: item.expire_time,
        certificate: decrypted.toString('utf8')
      });
    }
    
    // 缓存证书，有效期设为8小时
    wechatpayCertificates = certificates;
    certificatesExpireTime = now + 8 * 60 * 60 * 1000;
    
    // 返回证书列表
    return certificates;
  } catch (error) {
    logger.error('获取微信支付平台证书失败:', error);
    throw error;
  }
};

/**
 * 获取商户私钥
 * @returns {Promise<string>} 商户私钥
 */
const getWechatPayPrivateKey = async () => {
  try {
    // 从数据库获取商户私钥配置
    const config = await PaymentConfig.findOne({
      where: {
        payment_type: 'wechat',
        config_key: 'private_key',
        is_active: true
      }
    });

    if (!config) {
      throw new Error('未找到商户私钥配置');
    }

    return config.is_encrypted 
      ? decryptPaymentKey(config.config_value) 
      : config.config_value;
  } catch (error) {
    logger.error('获取商户私钥失败:', error);
    throw error;
  }
};

/**
 * 获取证书序列号
 * @returns {Promise<string>} 证书序列号
 */
const getWechatPaySerialNo = async () => {
  try {
    // 从数据库获取证书序列号配置
    const config = await PaymentConfig.findOne({
      where: {
        payment_type: 'wechat',
        config_key: 'serial_no',
        is_active: true
      }
    });

    if (!config) {
      throw new Error('未找到证书序列号配置');
    }

    return config.config_value;
  } catch (error) {
    logger.error('获取证书序列号失败:', error);
    throw error;
  }
};

/**
 * 获取商户号
 * @returns {Promise<string>} 商户号
 */
const getMerchantId = async () => {
  try {
    // 从数据库获取商户号配置
    const config = await PaymentConfig.findOne({
      where: {
        payment_type: 'wechat',
        config_key: 'mchid',
        is_active: true
      }
    });

    if (!config) {
      throw new Error('未找到商户号配置');
    }

    return config.config_value;
  } catch (error) {
    logger.error('获取商户号失败:', error);
    throw error;
  }
};

/**
 * 生成随机字符串
 * @param {number} length 随机字符串长度
 * @returns {string} 随机字符串
 */
const generateNonceStr = (length = 32) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

module.exports = {
  getWechatPayCertificates,
  getWechatPayPrivateKey,
  getWechatPaySerialNo,
  getMerchantId,
  generateNonceStr
}; 
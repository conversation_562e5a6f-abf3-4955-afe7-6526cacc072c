
import React from "react";
import { ArrowUpOutlined, ArrowDownOutlined } from "@ant-design/icons";
import { Button, Tooltip, Space } from "antd";

const LayerControlToolbar = ({ selectedElement, onMoveUp, onMoveDown, scale }) => {
  if (!selectedElement) {
    return null;
  }

  const rect = selectedElement.getBoundingClientRect();
  const toolbarStyle = {
    position: "absolute",
    top: `${rect.top - 40}px`, // Position above the element
    left: `${rect.left}px`,
    zIndex: 10001, // Ensure it is on top of other elements
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    padding: "4px 8px",
    borderRadius: "6px",
    display: "flex",
    alignItems: "center",
    transform: `scale(${1 / scale})`, // Counteract the parent's scale
    transformOrigin: "top left",
  };

  return (
    <div style={toolbarStyle}>
      <Space>
        <Tooltip title="上移一层">
          <Button
            type="primary"
            icon={<ArrowUpOutlined />}
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onMoveUp();
            }}
          />
        </Tooltip>
        <Tooltip title="下移一层">
          <Button
            type="primary"
            icon={<ArrowDownOutlined />}
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onMoveDown();
            }}
          />
        </Tooltip>
      </Space>
    </div>
  );
};

export default LayerControlToolbar;

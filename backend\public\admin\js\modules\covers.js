// covers.js - 封面管理模块

// 封面管理模块命名空间
window.coverModule = (function() {
  // 初始化函数
  function init() {
    console.log('初始化封面管理模块...');

    // 绑定事件
    bindEvents();

    // 加载封面列表
    fetchCovers();
  }

  // 绑定事件处理函数
  function bindEvents() {
    // 添加封面按钮
    document.getElementById('addCoverBtn').addEventListener('click', function() {
      // 重置表单
      document.getElementById('coverForm').reset();
      document.getElementById('coverId').value = '';

      // 新增时确保封面标识码字段可编辑
      document.getElementById('coverIdCode').disabled = false;

      // 设置模态框标题
      document.getElementById('coverModalLabel').textContent = '添加封面模板';

      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('coverModal'));
      modal.show();
    });

    // 保存封面按钮
    document.getElementById('saveCoverBtn').addEventListener('click', function() {
      saveCover();
    });
  }

  // 获取封面列表
  function fetchCovers() {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch('/api/admin/base-prompts', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // 处理后端返回的数据结构
        // 检查data.data.basePrompts是否存在
        const covers = data.data && data.data.basePrompts ? data.data.basePrompts :
                      (Array.isArray(data.data) ? data.data : []);
        console.log('处理后的封面数据:', covers);
        renderCovers(covers);
      } else {
        console.error('获取封面模板失败:', data.message);
        renderCovers([]);
      }
    })
    .catch(error => {
      console.error('获取封面模板失败:', error);
      renderCovers([]);
    });
  }

  // 渲染封面列表
  function renderCovers(covers) {
    const tbody = document.querySelector('#coversTable tbody');
    tbody.innerHTML = '';

    if (covers.length === 0) {
      const tr = document.createElement('tr');
      tr.innerHTML = '<td colspan="6" class="text-center">暂无数据</td>';
      tbody.appendChild(tr);
      return;
    }

    covers.forEach(cover => {
      const tr = document.createElement('tr');
      tr.innerHTML = `
        <td>${cover.id || '-'}</td>
        <td>${cover.id_code || '-'}</td>
        <td>${cover.cover_type || '-'}</td>
        <td>${cover.cover_size || '-'}</td>
        <td>${cover.prompt_content ? (cover.prompt_content.substring(0, 50) + (cover.prompt_content.length > 50 ? '...' : '')) : '-'}</td>
        <td>
          <button class="btn btn-sm btn-primary edit-cover" data-id="${cover.id}" data-content="${encodeURIComponent(cover.prompt_content)}">编辑</button>
          <button class="btn btn-sm btn-danger delete-cover" data-id="${cover.id}" data-type="${cover.cover_type}">删除</button>
        </td>
      `;

      // 绑定编辑按钮事件
      tr.querySelector('.edit-cover').addEventListener('click', function() {
        editCover(cover.id);
      });

      // 绑定删除按钮事件
      tr.querySelector('.delete-cover').addEventListener('click', function() {
        confirmDeleteCover(cover.id, cover.cover_type);
      });

      tbody.appendChild(tr);
    });
  }

  // 编辑封面
  function editCover(coverId) {
    const token = localStorage.getItem('token');
    if (!token) return;

    // 从已加载的数据中查找对应的封面模板
    const tbody = document.querySelector('#coversTable tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    let coverData = null;

    for (let row of rows) {
      // 跳过"暂无数据"行
      if (row.children.length < 3) continue;

      // 获取编辑按钮并检查其data-id属性
      const editBtn = row.querySelector('.edit-cover');
      if (editBtn && editBtn.getAttribute('data-id') == coverId) {
        // 找到匹配的行，提取数据
        coverData = {
          id: coverId,
          id_code: row.children[1].textContent.trim(),
          cover_type: row.children[2].textContent.trim(),
          cover_size: row.children[3].textContent.trim(),
          prompt_content: decodeURIComponent(row.querySelector('.edit-cover').getAttribute('data-content') || '')
        };
        break;
      }
    }

    if (coverData) {
      // 使用找到的数据填充表单
      document.getElementById('coverModalLabel').textContent = '编辑封面模板';
      document.getElementById('coverId').value = coverData.id;
      document.getElementById('coverIdCode').value = coverData.id_code;
      document.getElementById('coverIdCode').disabled = true; // 编辑时设置为禁用状态（灰色效果）
      document.getElementById('coverTypeMgmt').value = coverData.cover_type;
      document.getElementById('coverSize').value = coverData.cover_size;
      document.getElementById('promptContentMgmt').value = coverData.prompt_content;

      // 显示模态框
      const modal = new bootstrap.Modal(document.getElementById('coverModal'));
      modal.show();
    } else {
      // 如果在本地数据中找不到，尝试从服务器获取
      fetch(`/api/admin/base-prompts/${coverId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const cover = data.data.basePrompt || data.data;

          // 设置模态框标题
          document.getElementById('coverModalLabel').textContent = '编辑封面模板';

          // 填充表单
          document.getElementById('coverId').value = cover.id;
          document.getElementById('coverIdCode').value = cover.id_code;
          document.getElementById('coverIdCode').disabled = true; // 编辑时设置为禁用状态（灰色效果）
          document.getElementById('coverTypeMgmt').value = cover.cover_type;
          document.getElementById('coverSize').value = cover.cover_size;
          document.getElementById('promptContentMgmt').value = cover.prompt_content;

          // 显示模态框
          const modal = new bootstrap.Modal(document.getElementById('coverModal'));
          modal.show();
        } else {
          alert('获取封面模板详情失败: ' + (data.message || '未知错误'));
        }
      })
      .catch(error => {
        console.error('获取封面模板详情失败:', error);
        alert('获取封面模板详情失败，请稍后再试');
      });
    }
  }

  // 确认删除封面
  function confirmDeleteCover(coverId, coverType) {
    // 填充确认对话框
    document.getElementById('deleteCoverId').value = coverId;
    document.getElementById('deleteCoverType').textContent = coverType;

    // 显示确认对话框
    const modal = new bootstrap.Modal(document.getElementById('deleteCoverModal'));
    modal.show();

    // 绑定确认删除按钮事件
    document.getElementById('confirmDeleteCoverBtn').onclick = function() {
      deleteCover(coverId);
    };
  }

  // 删除封面
  function deleteCover(coverId) {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`/api/admin/base-prompts/${coverId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      // 关闭确认对话框
      bootstrap.Modal.getInstance(document.getElementById('deleteCoverModal')).hide();

      if (data.success) {
        // 刷新封面列表
        fetchCovers();
        alert('删除封面模板成功');
      } else {
        alert('删除封面模板失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('删除封面模板失败:', error);

      // 关闭确认对话框
      bootstrap.Modal.getInstance(document.getElementById('deleteCoverModal')).hide();

      alert('删除封面模板失败，请稍后再试');
    });
  }

  // 保存封面前检查唯一性
  function isDuplicateCover(coverType, coverSize, covers) {
    return covers.some(cover => cover.cover_type === coverType && cover.cover_size === coverSize);
  }

  // 保存封面
  function saveCover() {
    const form = document.getElementById('coverForm');
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }

    const coverId = document.getElementById('coverId').value;
    const idCode = document.getElementById('coverIdCode').value;
    const coverType = document.getElementById('coverTypeMgmt').value;
    const coverSize = document.getElementById('coverSize').value;
    const promptContent = document.getElementById('promptContentMgmt').value;

    // 前端唯一性校验（仅新增时）
    if (!coverId) {
      // 获取当前表格已加载的covers
      const tbody = document.querySelector('#coversTable tbody');
      const rows = Array.from(tbody.querySelectorAll('tr'));
      for (let row of rows) {
        // 跳过“暂无数据”行
        if (row.children.length < 3) continue;
        const code = row.children[1].textContent.trim();
        if (code === idCode) {
          alert('该标识码已存在，请使用不同的标识码！');
          return;
        }
      }
    }

    const coverData = {
      id_code: idCode,
      cover_type: coverType,
      cover_size: coverSize,
      prompt_content: promptContent
    };

    const token = localStorage.getItem('token');
    if (!token) return;

    const url = coverId ? `/api/admin/base-prompts/${coverId}` : '/api/admin/base-prompts';
    const method = coverId ? 'PUT' : 'POST';

    const saveBtn = document.getElementById('saveCoverBtn');
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';

    fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(coverData)
    })
    .then(response => response.json())
    .then(data => {
      saveBtn.disabled = false;
      saveBtn.innerHTML = '保存';

      if (data.success) {
        bootstrap.Modal.getInstance(document.getElementById('coverModal')).hide();
        fetchCovers();
        alert(coverId ? '更新封面模板成功' : '添加封面模板成功');
      } else {
        alert(data.message || '操作失败');
      }
    })
    .catch(error => {
      console.error('保存封面模板失败:', error);
      saveBtn.disabled = false;
      saveBtn.innerHTML = '保存';
      alert('操作失败，请稍后再试');
    });
  }

  // 公开API
  return {
    init: init
  };
})();

/**
 * 积分记录管理模块
 * 依赖utils.js中的getAuthHeaders函数进行API请求认证
 */
window.pointRecordsModule = (function() {
  // 页面元素
  const $pointRecordsTable = document.getElementById('pointRecordsTable');
  const $pointRecordsPagination = document.getElementById('pointRecordsPagination');
  const $pointKeyword = document.getElementById('pointKeyword');
  const $pointOperationType = document.getElementById('pointOperationType');
  const $pointStartDate = document.getElementById('pointStartDate');
  const $pointEndDate = document.getElementById('pointEndDate');
  const $searchPointRecordsBtn = document.getElementById('searchPointRecordsBtn');
  const $resetPointRecordsBtn = document.getElementById('resetPointRecordsBtn');

  // 分页信息
  let currentPage = 1;
  let totalPages = 1;
  const pageSize = 10;

  // 初始化
  function init() {
    loadPointRecords();
    bindEvents();
  }

  // 绑定事件
  function bindEvents() {
    if ($searchPointRecordsBtn) {
      $searchPointRecordsBtn.addEventListener('click', function() {
        currentPage = 1;
        loadPointRecords();
      });
    }

    if ($resetPointRecordsBtn) {
      $resetPointRecordsBtn.addEventListener('click', function() {
        $pointKeyword.value = '';
        $pointOperationType.value = '';
        $pointStartDate.value = '';
        $pointEndDate.value = '';
        currentPage = 1;
        loadPointRecords();
      });
    }
  }

  // 加载积分记录
  function loadPointRecords() {
    showLoading($pointRecordsTable);

    // 构建查询参数
    const params = new URLSearchParams();
    params.append('page', currentPage);
    params.append('limit', pageSize);
    
    if ($pointKeyword.value) {
      params.append('keyword', $pointKeyword.value);
    }
    
    if ($pointOperationType.value) {
      params.append('operation_type', $pointOperationType.value);
    }
    
    if ($pointStartDate.value) {
      params.append('start_date', $pointStartDate.value);
    }
    
    if ($pointEndDate.value) {
      params.append('end_date', $pointEndDate.value);
    }

    // 获取认证令牌
    const token = localStorage.getItem('token');
    if (!token) {
      showError($pointRecordsTable, '未登录或会话已过期，请重新登录');
      return;
    }

    // 发送请求 - 使用公共的getAuthHeaders函数
    fetch(`/api/admin/point-records?${params.toString()}`, {
      headers: getAuthHeaders()
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          renderPointRecords(data.data);
          renderPagination(data.pagination);
        } else {
          showError($pointRecordsTable, data.message || '获取积分记录失败');
        }
      })
      .catch(error => {
        console.error('获取积分记录失败:', error);
        showError($pointRecordsTable, '获取积分记录失败');
      });
  }

  // 渲染积分记录
  function renderPointRecords(records) {
    const tbody = $pointRecordsTable.querySelector('tbody');
    tbody.innerHTML = '';

    if (!records || records.length === 0) {
      const tr = document.createElement('tr');
      tr.innerHTML = '<td colspan="9" class="text-center">暂无积分记录</td>';
      tbody.appendChild(tr);
      return;
    }

    records.forEach(record => {
      const tr = document.createElement('tr');
      
      // 定义操作类型文本
      const operationTypeText = {
        'register': '注册奖励',
        'daily_reward': '每日奖励',
        'generate': '生成封面',
        'admin_adjust': '管理员调整',
        'vip_upgrade': 'VIP升级'
      };
      
      // 定义角色文本和样式
      const roleText = {
        'admin': '<span class="badge bg-danger">管理员</span>',
        'vip': '<span class="badge bg-warning text-dark">VIP用户</span>',
        'user': '<span class="badge bg-success">普通用户</span>'
      };
      
      // 格式化积分变动，添加颜色
      const pointsChangeFormatted = record.points_change > 0 
        ? `<span class="text-success">+${record.points_change}</span>` 
        : `<span class="text-danger">${record.points_change}</span>`;
      
      // 格式化时间
      const createdAt = new Date(record.operation_time || record.created_at).toLocaleString();
      
      // 设置表格行内容
      tr.innerHTML = `
        <td>${record.id}</td>
        <td>${record.phone || '-'}</td>
        <td>${record.nickname || '-'}</td>
        <td>${roleText[record.role] || '-'}</td>
        <td>${pointsChangeFormatted}</td>
        <td>${record.points_after}</td>
        <td>${operationTypeText[record.operation_type] || record.operation_type}</td>
        <td>${record.description || '-'}</td>
        <td>${createdAt}</td>
      `;
      
      tbody.appendChild(tr);
    });
  }

  // 渲染分页控件
  function renderPagination(pagination) {
    if (!pagination) return;
    
    totalPages = pagination.total_pages;
    currentPage = pagination.page;
    
    $pointRecordsPagination.innerHTML = '';
    
    // 如果只有一页，不显示分页
    if (totalPages <= 1) return;
    
    // 上一页按钮
    const prevLi = document.createElement('li');
    prevLi.className = `page-item${currentPage === 1 ? ' disabled' : ''}`;
    prevLi.innerHTML = '<a class="page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>';
    if (currentPage > 1) {
      prevLi.addEventListener('click', function(e) {
        e.preventDefault();
        goToPage(currentPage - 1);
      });
    }
    $pointRecordsPagination.appendChild(prevLi);
    
    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);
    
    for (let i = startPage; i <= endPage; i++) {
      const pageLi = document.createElement('li');
      pageLi.className = `page-item${i === currentPage ? ' active' : ''}`;
      pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;
      
      if (i !== currentPage) {
        pageLi.addEventListener('click', function(e) {
          e.preventDefault();
          goToPage(i);
        });
      }
      
      $pointRecordsPagination.appendChild(pageLi);
    }
    
    // 下一页按钮
    const nextLi = document.createElement('li');
    nextLi.className = `page-item${currentPage === totalPages ? ' disabled' : ''}`;
    nextLi.innerHTML = '<a class="page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>';
    if (currentPage < totalPages) {
      nextLi.addEventListener('click', function(e) {
        e.preventDefault();
        goToPage(currentPage + 1);
      });
    }
    $pointRecordsPagination.appendChild(nextLi);
  }

  // 跳转到指定页
  function goToPage(page) {
    currentPage = page;
    loadPointRecords();
  }

  // 显示加载中
  function showLoading(table) {
    const tbody = table.querySelector('tbody');
    tbody.innerHTML = '<tr><td colspan="9" class="text-center">加载中...</td></tr>';
  }

  // 显示错误
  function showError(table, message) {
    const tbody = table.querySelector('tbody');
    tbody.innerHTML = `<tr><td colspan="9" class="text-center text-danger">${message}</td></tr>`;
  }

  // 监听页面导航事件
  document.addEventListener('pageNavigation', function(e) {
    if (e.detail.page === 'point-records') {
      // 当切换到积分记录页面时刷新数据
      currentPage = 1;
      loadPointRecords();
    }
  });

  // 返回公共接口
  return {
    init: init,
    goToPage: goToPage
  };
})();

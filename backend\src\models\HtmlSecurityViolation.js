const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * HTML安全违规记录模型
 * 对应数据库中的html_security_violations表
 */
const HtmlSecurityViolation = sequelize.define('HtmlSecurityViolation', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '违规记录ID，唯一标识'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  user_phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    comment: '用户手机号'
  },
  file_name: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '原始文件名'
  },
  upload_time: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '上传时间'
  },
  risk_level: {
    type: DataTypes.ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL'),
    allowNull: true,
    comment: '风险等级'
  },
  violation_reasons: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '违规原因详情'
  },
  detected_threats: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '检测到的威胁列表'
  },
  file_size: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '文件大小（字节）'
  },
  file_hash: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '文件内容SHA256哈希'
  },
  detection_engine_version: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '检测引擎版本'
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: '上传IP地址'
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '用户代理信息'
  }
}, {
  tableName: 'html_security_violations',
  timestamps: false, // 使用自定义的upload_time字段
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['upload_time']
    },
    {
      fields: ['risk_level']
    }
  ],
  comment: 'HTML安全违规记录表'
});

// 定义关联关系
HtmlSecurityViolation.associate = function(models) {
  // 与User模型的关联
  HtmlSecurityViolation.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user'
  });
};

module.exports = HtmlSecurityViolation;

import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { fetchUserProfile } from '../services/apiCacheService';

// 创建上下文
const UserContext = createContext(null);

// 缓存过期时间（默认5分钟）
const CACHE_EXPIRY_TIME = 5 * 60 * 1000;

export const UserProvider = ({ children }) => {
  // 用户资料状态
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastFetchTime, setLastFetchTime] = useState(0);

  // 获取用户资料的函数，使用apiCacheService
  const fetchUserProfileData = async (forceRefresh = false) => {
    const now = Date.now();
    
    // 如果资料已存在且未过期，除非强制刷新，否则不重新获取
    if (!forceRefresh && userProfile && now - lastFetchTime < CACHE_EXPIRY_TIME) {
      return userProfile;
    }
    
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      if (!token) {
        setUserProfile(null);
        setError('未登录');
        setLoading(false);
        return null;
      }
      
      // 使用apiCacheService的fetchUserProfile函数
      const profileData = await fetchUserProfile(token);
      
      if (profileData) {
        setUserProfile(profileData);
        setLastFetchTime(now);
        setError(null);
        setLoading(false);
        return profileData;
      } else {
        setError('获取用户资料失败');
        setLoading(false);
        return null;
      }
    } catch (error) {
      console.error('获取用户资料失败:', error);
      setError('获取用户资料失败');
      setLoading(false);
      return null;
    }
  };

  // 更新用户资料的函数
  const updateUserProfile = async (updatedData) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      if (!token) {
        setError('未登录');
        setLoading(false);
        return false;
      }
      
      // 直接使用axios进行更新请求，不使用缓存
      const axios = (await import('axios')).default;
      const response = await axios.put('/api/user/profile', updatedData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        // 更新本地存储的用户信息
        const updatedProfile = { ...userProfile, ...response.data.data };
        setUserProfile(updatedProfile);
        setLastFetchTime(Date.now());
        
        // 更新localStorage中的user信息
        const currentUserData = JSON.parse(localStorage.getItem('user') || '{}');
        localStorage.setItem('user', JSON.stringify({ 
          ...currentUserData, 
          ...response.data.data 
        }));
        
        setLoading(false);
        return true;
      } else {
        setError(response.data.message || '更新用户资料失败');
        setLoading(false);
        return false;
      }
    } catch (error) {
      console.error('更新用户资料失败:', error);
      setError('更新用户资料失败');
      setLoading(false);
      return false;
    }
  };

  // 组件挂载时初始化用户资料，不设置额外的定时刷新
  // AuthContext已经有定时刷新机制，避免重复请求
  useEffect(() => {
    fetchUserProfileData();
  }, []);

  // 提供Context值
  const contextValue = useMemo(() => ({
    userProfile,
    loading,
    error,
    fetchUserProfile: fetchUserProfileData,
    updateUserProfile
  }), [userProfile, loading, error]);
  
  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

// 自定义hook便于组件使用
export const useUserProfile = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUserProfile必须在UserProvider内使用');
  }
  return context;
};

export default UserContext; 
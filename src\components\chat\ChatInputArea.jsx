import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { Button, message } from 'antd';
import { ArrowDown, Trash2, Settings2, ImageUp, ChevronDown, Tags, Edit3, SlidersHorizontal, <PERSON>lette, Wand2, CheckSquare, UploadCloud, Loader2, AlertCircle, Crown, ChevronLeft, ChevronRight } from 'lucide-react';
import axios from 'axios';
import { checkFeatureAvailability } from '../../services/featureService';
import PermissionDialog from '../../components/common/PermissionDialog';
import { getSizeTypes, getCoverSizeConfig } from '../../services/templateService';

// 设定每行最多显示的标签数
const TAGS_PER_ROW = 7;
// 页面标签导航逻辑
const TAGS_PER_PAGE = 6;

// 简易的自定义Tooltip组件
const SimpleTooltip = ({ children, content }) => {
  const [isVisible, setIsVisible] = useState(false);
  
  return (
    <div
      className="relative inline-block"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && content && (
        <div
          className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black bg-opacity-80 text-white text-xs rounded-md shadow-lg z-50 whitespace-nowrap"
        >
          {content}
        </div>
      )}
    </div>
  );
};

const ChatInputArea = forwardRef((props, ref) => {
  const {
    coverData: initialCoverData = { title: '', subtitle: '', accountName: '' },
    onInputChange,
    selectedStyleId = null,
    onStyleChange,
    selectedSizeId: initialSizeId = null,
    onSizeChange,
    isPreviewLoaded = false,
    isGenerating = false,
    onFormSubmit = () => {},
    onShowStyleExample = () => {},
    isCollapsed: isInputAreaCollapsedProp = false,
    toggleCollapse = () => {},
    coverId,
    coverCode,
    setFeedbackText,
  } = props;

  const textareaRef = useRef(null);
  const [availableSizes, setAvailableSizes] = useState([]);
  const [availableStylesDemo, setAvailableStylesDemo] = useState([]);
  
  // Local state for form inputs
  const [coverText, setCoverText] = useState(initialCoverData.title || '');
  const [subTitle, setSubTitle] = useState(initialCoverData.subtitle || '');
  const [accountName, setAccountName] = useState(initialCoverData.accountName || '');
  const [selectedSize, setSelectedSize] = useState(initialSizeId);
  const [internalSelectedStyle, setInternalSelectedStyle] = useState(selectedStyleId);
  
  const [showSubtitleInput, setShowSubtitleInput] = useState(!!initialCoverData.subtitle);
  const [showAccountNameInput, setShowAccountNameInput] = useState(!!initialCoverData.accountName);
  
  const [isStylesExpanded, setIsStylesExpanded] = useState(false);
  const [currentStylePage, setCurrentStylePage] = useState(0);
  const [showImageUploadArea, setShowImageUploadArea] = useState(false);
  const [customImageType, setCustomImageType] = useState('background');
  const [aiRefineEnabled, setAiRefineEnabled] = useState(false);

  const [hasBeenManuallyExpanded, setHasBeenManuallyExpanded] = useState(false);
  
  const [customImageUrl, setCustomImageUrl] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  const [showPermissionDialog, setShowPermissionDialog] = useState(false);
  const [permissionDialogInfo, setPermissionDialogInfo] = useState({ featureName: '', reason: '' });

  // 添加一个状态标志控制初始化行为
  const [hasInitialized, setHasInitialized] = useState(false);
  // 添加引用来追踪之前的值，避免不必要的更新
  const prevStyleIdRef = useRef(selectedStyleId);
  const prevSizeIdRef = useRef(initialSizeId);

  // 通过ref暴露方法
  useImperativeHandle(ref, () => ({
    resetExpandedState: () => {
      setHasBeenManuallyExpanded(true);
    }
  }));

  // Fetch initial data for sizes and styles
  useEffect(() => {
    // 如果已初始化，不再执行
    if (hasInitialized) return;
    
    const fetchSizeTypes = async () => {
      try {
        const response = await axios.get(`/api/cover/base-prompts?t=${new Date().getTime()}`);
        if (response.data.success && Array.isArray(response.data.data.basePrompts) && response.data.data.basePrompts.length > 0) {
          const sizeTypesArray = response.data.data.basePrompts.map(p => ({ 
              id: p.id_code || p.cover_type, 
              name: p.cover_type_name || p.cover_type,
              width: p.width,
              height: p.height,
            }));
          setAvailableSizes(sizeTypesArray);
          if (!initialSizeId && sizeTypesArray.length > 0) {
            const firstSizeId = sizeTypesArray[0].id;
            setSelectedSize(firstSizeId);
            // 初始化阶段不调用回调，避免触发更新循环
          }
        } else {
            // Handle cases where the API call succeeds but returns no data or success:false
            message.error('无法从服务器获取尺寸数据，请稍后重试。');
            console.error('获取尺寸数据失败，API响应:', response.data);
        }
      } catch (error) {
        // This block now ONLY handles network/request errors. No fallback logic.
        console.error('请求尺寸数据API时出错:', error);
        message.error('网络错误：无法连接到服务器获取尺寸数据。');
      }
    };

    const fetchStyles = async () => {
      try {
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
        const response = await axios.get(`/api/style?t=${new Date().getTime()}`, { headers });
        if (response.data.success && Array.isArray(response.data.data)) {
          const stylesData = response.data.data.map(style => ({ id: style.id_code, name: style.style_name }));
          setAvailableStylesDemo(stylesData);
          if (!selectedStyleId && stylesData.length > 0) {
            setInternalSelectedStyle(stylesData[0].id);
            // 初始化阶段不调用回调，避免触发更新循环
          }
        }
      } catch (error) {
        console.error('获取风格数据失败:', error);
      }
    };

    fetchSizeTypes();
    fetchStyles();
    setHasInitialized(true);
  }, [initialSizeId, selectedStyleId, hasInitialized]); // 添加依赖项，控制初始化行为

  // Corrected useEffect to sync state from props ONLY when a new cover is loaded
  useEffect(() => {
    // This effect updates local state when a new cover is loaded for editing,
    // detected by a change in coverId or coverCode.
    // It avoids overwriting user input on every re-render.
    setCoverText(initialCoverData.title || '');
    setSubTitle(initialCoverData.subtitle || '');
    setAccountName(initialCoverData.accountName || '');
    setShowSubtitleInput(!!initialCoverData.subtitle);
    setShowAccountNameInput(!!initialCoverData.accountName);
    setInternalSelectedStyle(selectedStyleId);
    setSelectedSize(initialSizeId);
  }, [coverId, coverCode]);

  // Dynamic textarea height adjustment
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const scrollHeight = textareaRef.current.scrollHeight;
      const maxHeight = 260;
      textareaRef.current.style.height = `${Math.min(scrollHeight, maxHeight)}px`;
      textareaRef.current.style.overflowY = scrollHeight > maxHeight ? 'auto' : 'hidden';
    }
  }, [coverText]);

  // Auto-collapse logic
  useEffect(() => {
    if (isGenerating && !isInputAreaCollapsedProp) {
        toggleCollapse(true);
    } else if (isPreviewLoaded && !hasBeenManuallyExpanded && !isStylesExpanded && !isInputAreaCollapsedProp) {
        toggleCollapse(true);
    }
  }, [isPreviewLoaded, isGenerating, hasBeenManuallyExpanded, isStylesExpanded, toggleCollapse, isInputAreaCollapsedProp]);

  // 修改样式同步逻辑
  useEffect(() => {
    // 避免在组件初始渲染时触发不必要的更新
    if (selectedStyleId !== prevStyleIdRef.current && selectedStyleId !== internalSelectedStyle) {
      setInternalSelectedStyle(selectedStyleId);
      prevStyleIdRef.current = selectedStyleId;
    }
  }, [selectedStyleId, internalSelectedStyle]);

  // 修改尺寸同步逻辑
  useEffect(() => {
    // 避免在组件初始渲染时触发不必要的更新
    if (initialSizeId !== prevSizeIdRef.current && initialSizeId !== selectedSize) {
      setSelectedSize(initialSizeId);
      prevSizeIdRef.current = initialSizeId;
    }
  }, [initialSizeId, selectedSize]);

  // Input change handlers
  const handleLocalInputChange = (field, value) => {
    if (field === 'title') setCoverText(value);
    if (field === 'subtitle') setSubTitle(value);
    if (field === 'accountName') setAccountName(value);
    if (onInputChange) {
      onInputChange(field, value);
    }
  };
  
  const handleSizeSelection = (e) => {
    const newSizeId = e.target.value;
    setSelectedSize(newSizeId);
    if(onSizeChange) onSizeChange(newSizeId);
  };

  const handleStyleSelection = (styleId, skipStyleExample = false) => {
    setInternalSelectedStyle(styleId);
    if (onStyleChange) {
      onStyleChange(styleId);
    }
    // 仅在不跳过风格示例的情况下加载风格示例
    if (!skipStyleExample) {
      fetchStyleExampleHtml(styleId);
    }
  };
  
  const fetchStyleExampleHtml = async (styleId) => {
    if (!styleId || !setFeedbackText) return;
    try {
      setFeedbackText('正在加载风格示例...');
      const token = localStorage.getItem('token');
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {};
      const response = await axios.get(`/api/style/${styleId}?t=${new Date().getTime()}`, { headers });
      if (response.data.success && response.data.data.stylePrompt?.example_html) {
        onShowStyleExample(response.data.data.stylePrompt.example_html);
      }
    } catch (error) {
      console.error('获取风格示例失败:', error);
    } finally {
        setFeedbackText('');
    }
  };

  const handleGenerateCover = () => {
    if (!coverText.trim()) {
      message.warning('请输入封面主要文字内容');
      return;
    }
    if (isGenerating) {
      message.warning('正在生成中，请稍候...');
      return;
    }
    const formData = {
      coverText: coverText.trim(),
      subtitle: subTitle.trim(),
      accountName: accountName.trim(),
      showAccountName: !!accountName.trim(),
      showSubtitle: !!subTitle.trim(),
      autoOptimize: aiRefineEnabled,
      customImageUrl,
      customImageType: showImageUploadArea ? customImageType : null,
    };
    onFormSubmit(formData, selectedSize, internalSelectedStyle);
  };

  const handleClearFields = () => {
    setCoverText('');
    setSubTitle('');
    setAccountName('');
    setShowSubtitleInput(false);
    setShowAccountNameInput(false);
    setShowImageUploadArea(false);
    setCustomImageUrl('');
    if (onInputChange) {
      onInputChange('title', '');
      onInputChange('subtitle', '');
      onInputChange('accountName', '');
    }
  };

  const handleCollapsedInputClick = () => {
    setHasBeenManuallyExpanded(true);
    toggleCollapse(false); 
    setTimeout(() => textareaRef.current?.focus(), 50);
  };
  
  const handleManualCollapseToggle = () => {
    if (isInputAreaCollapsedProp) {
        setHasBeenManuallyExpanded(true);
    }
    toggleCollapse(!isInputAreaCollapsedProp);
  };

  const showPermissionError = (featureName, reason) => {
    setPermissionDialogInfo({ featureName, reason });
    setShowPermissionDialog(true);
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    if (file.size > 5 * 1024 * 1024) {
      message.error('图片大小不能超过5MB');
      return;
    }
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('type', customImageType);
      const token = localStorage.getItem('token');
      const response = await axios.post('/api/cover/upload-image', formData, {
        headers: { 'Content-Type': 'multipart/form-data', 'Authorization': `Bearer ${token}` }
      });
      if (response.data.success) {
        let imageUrl = response.data.data.imageUrl;
        if (imageUrl && !imageUrl.startsWith('http')) {
          imageUrl = '/uploads/' + imageUrl;
        }
        setCustomImageUrl(imageUrl);
        if (onInputChange) {
          onInputChange('customImageUrl', imageUrl);
          onInputChange('customImageType', customImageType);
        }
        message.success('图片上传成功');
      } else {
        message.error(response.data.message || '图片上传失败');
      }
    } catch (error) {
      message.error('图片上传失败，请稍后再试');
    } finally {
      setIsUploading(false);
    }
  };

  const toggleImageUploadArea = async () => {
    const result = await checkFeatureAvailability('自定义图片');
    if (!result.available) {
      showPermissionError('自定义图片', result.reason || '您的账号权限不足');
      return;
    }
    setShowImageUploadArea(prev => !prev);
  };

  // Pagination and display logic for styles
  const totalPages = Math.ceil(availableStylesDemo.length / TAGS_PER_PAGE);
  const stylesToDisplay = isStylesExpanded 
    ? availableStylesDemo 
    : availableStylesDemo.slice(currentStylePage * TAGS_PER_PAGE, (currentStylePage + 1) * TAGS_PER_PAGE);
  
  const handleStyleSelectionWithFeedback = (styleId, event) => {
    event.stopPropagation();
    handleStyleSelection(styleId);
    if (isStylesExpanded) setIsStylesExpanded(false);
    const selectedIndex = availableStylesDemo.findIndex(style => style.id === styleId);
    if (selectedIndex !== -1) {
      setCurrentStylePage(Math.floor(selectedIndex / TAGS_PER_PAGE));
    }
    if (isInputAreaCollapsedProp) {
      setHasBeenManuallyExpanded(true);
      toggleCollapse(false);
    }
  };

  // JSX Rendering
  if (isInputAreaCollapsedProp) {
    return (
      <div className="relative w-full transition-all duration-300 ease-in-out">
        <div
          onClick={handleCollapsedInputClick}
          className="relative flex items-center w-full p-3 border-2 border-purple-500 rounded-md cursor-text hover:border-purple-600 bg-white min-h-[50px] shadow-md transition-all duration-300 ease-in-out"
          title="点击展开输入区域"
        >
          <span className="flex-grow text-gray-600 truncate text-sm">{coverText || "点击输入主要的封面文字内容..."}</span>
          <Wand2 size={18} className="text-purple-500 ml-2 flex-shrink-0" title="生成封面提示"/>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full bg-white rounded-xl shadow-lg border border-gray-200 flex flex-col transition-all duration-300 ease-in-out px-2 pt-3 pb-1.5 md:px-3 md:pt-4 md:pb-2">
      <button
        onClick={handleManualCollapseToggle}
        className="absolute top-0 left-1/2 -translate-x-1/2 z-20 h-5 px-4 bg-slate-100 hover:bg-slate-200 border border-slate-300 rounded-lg shadow-sm cursor-pointer group"
        title="收起/展开输入区域"
      >
        <ChevronDown size={16} className={`text-slate-600 group-hover:translate-y-0.5 transition-transform ${isInputAreaCollapsedProp ? 'rotate-180' : ''}`} />
      </button>

      <div className="relative px-2 py-3 md:px-3 flex-grow flex flex-col">
        {/* Sizes and Styles */}
        <div className="mb-2 px-1 py-2 bg-gray-50/70 border border-gray-200 rounded-lg">
          <div className="flex items-center gap-1">
            <div className="flex items-center shrink-0 gap-1">
              <SimpleTooltip content="选择不同平台的封面尺寸类型">
                <div className="flex items-center space-x-1.5">
                  <SlidersHorizontal size={16} className="text-slate-500" />
                  <span className="text-sm font-semibold text-slate-700 whitespace-nowrap">尺寸类型：</span>
                </div>
              </SimpleTooltip>
              <select value={selectedSize || ''} onChange={handleSizeSelection} className="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full border-2 border-gray-400 rounded-md py-1.5 px-2 text-xs h-[36px] bg-white hover:bg-gray-50 min-w-[130px]">
                {availableSizes.map(size => <option key={size.id} value={size.id}>{size.name}</option>)}
              </select>
            </div>
            <div className="flex-1 flex items-center min-w-0 gap-1">
                <label className="text-sm font-semibold text-gray-700 flex items-center px-2 py-1 bg-white rounded-md shrink-0"><Palette size={16} className="mr-1.5 text-gray-500" />风格:</label>
                <div className="flex-1 relative flex items-center">
                    {!isStylesExpanded && currentStylePage > 0 && (<button type="button" onClick={() => setCurrentStylePage(p => p - 1)} className="shrink-0 p-1 rounded-full text-blue-600 hover:bg-blue-100"><ChevronLeft size={20} /></button>)}
                    <div className={`flex-1 min-w-0 transition-all duration-300 ${isStylesExpanded ? 'overflow-y-auto max-h-[160px]' : 'overflow-hidden'}`}>
                      {isStylesExpanded ? (
                        availableStylesDemo.map(style => (
                          <button key={style.id} type="button" onClick={(e) => handleStyleSelectionWithFeedback(style.id, e)} className={`m-1 px-2 py-1.5 text-xs rounded-md ${internalSelectedStyle === style.id ? 'bg-gradient-primary text-white' : 'text-gray-700 border border-gray-400 hover:bg-gray-100'}`}>{style.name}</button>
                        ))
                      ) : (
                        <div className="flex flex-nowrap items-center gap-1.5 overflow-x-hidden">{stylesToDisplay.map(style => (<button key={style.id} type="button" onClick={(e) => handleStyleSelectionWithFeedback(style.id, e)} className={`px-2 py-1.5 text-xs rounded-md shrink-0 ${internalSelectedStyle === style.id ? 'bg-gradient-primary text-white' : 'text-gray-700 border border-gray-400 hover:bg-gray-100'}`}>{style.name}</button>))}</div>
                      )}
                    </div>
                    {!isStylesExpanded && currentStylePage < totalPages - 1 && (<button type="button" onClick={() => setCurrentStylePage(p => p + 1)} className="shrink-0 p-1 rounded-full text-blue-600 hover:bg-blue-100"><ChevronRight size={20} /></button>)}
                </div>
                {availableStylesDemo.length > TAGS_PER_PAGE && (<button onClick={() => setIsStylesExpanded(v => !v)} className="px-2 py-1.5 text-xs rounded-md flex items-center shrink-0 text-blue-600 hover:text-blue-700 bg-transparent border border-blue-200 hover:bg-blue-50">{isStylesExpanded ? "收起" : "更多"}<ChevronDown size={14} className={`ml-0.5 transition-transform ${isStylesExpanded ? 'rotate-180' : ''}`} /></button>)}
            </div>
          </div>
        </div>

        {/* Main Text Input */}
        <div className="relative w-full">
          <textarea ref={textareaRef} value={coverText} onChange={(e) => handleLocalInputChange('title', e.target.value)} placeholder="请输入封面主要文字，Shift+Enter可换行" className="w-full p-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-base resize-none" style={{ minHeight: '120px' }} rows={4} />
        </div>

        {/* Bottom Controls */}
        <div className="mt-3 border-t border-gray-200 pt-3">
          <div className="flex items-center justify-between">
            <div className="flex flex-wrap items-center gap-2">
              <button title={showSubtitleInput ? "隐藏副标题" : "添加副标题"} onClick={() => setShowSubtitleInput(p => !p)} className={`relative px-3 py-1.5 text-sm rounded-full flex items-center ${showSubtitleInput ? 'bg-gradient-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}><Edit3 size={14} className="mr-1.5" />副标题{!showSubtitleInput && subTitle.trim() && (<AlertCircle size={12} className="absolute -top-1 -right-1 text-orange-500 bg-white rounded-full" />)}</button>
              <button title={showAccountNameInput ? "隐藏账号信息" : "添加账号信息"} onClick={() => setShowAccountNameInput(p => !p)} className={`relative px-3 py-1.5 text-sm rounded-full flex items-center ${showAccountNameInput ? 'bg-gradient-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}><Tags size={14} className="mr-1.5" />账号信息{!showAccountNameInput && accountName.trim() && (<AlertCircle size={12} className="absolute -top-1 -right-1 text-orange-500 bg-white rounded-full" />)}</button>
              <button title="设置自定义图片" onClick={toggleImageUploadArea} className={`relative px-3 py-1.5 text-sm rounded-full flex items-center ${showImageUploadArea ? 'bg-gradient-primary text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}><ImageUp size={14} className="mr-1.5" />自定义图片<Crown size={10} className="absolute -top-1 -right-1 text-orange-500" /></button>
            </div>
            <div className="flex items-center gap-2.5">
              <SimpleTooltip content="开启后，AI将自动提炼和优化您的文案"><div className="flex items-center space-x-1.5"><div onClick={() => setAiRefineEnabled(!aiRefineEnabled)} className={`w-10 h-5 flex items-center rounded-full p-0.5 cursor-pointer ${aiRefineEnabled ? 'bg-purple-600' : 'bg-gray-300'}`}><div className={`w-4 h-4 bg-white rounded-full shadow-md transform transition-transform ${aiRefineEnabled ? 'translate-x-5' : 'translate-x-0'}`} /></div><span className="text-sm font-semibold text-slate-700 select-none">AI提炼</span></div></SimpleTooltip>
              <button type="button" title="清空所有输入" className="flex items-center justify-center px-3 py-1.5 border border-gray-300 text-sm rounded-md text-gray-700 hover:bg-gray-100" onClick={handleClearFields}><Trash2 size={14} className="mr-1.5" />清空</button>
              <button type="button" title="生成封面" className="inline-flex items-center justify-center px-4 py-1.5 border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient-primary hover:opacity-90 disabled:opacity-50" onClick={handleGenerateCover} disabled={isGenerating}>{isGenerating ? (<><Loader2 size={14} className="mr-1.5 animate-spin" />生成中...</>) : (<><Settings2 size={14} className="mr-1.5" />生成</>)}</button>
            </div>
          </div>
          <div className="space-y-2 mt-2.5">
            {showSubtitleInput && (<div><input type="text" placeholder="副标题 (可选)" value={subTitle} onChange={(e) => handleLocalInputChange('subtitle', e.target.value)} className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border border-gray-300 rounded-md p-2 text-base" /></div>)}
            {showAccountNameInput && (<div><input type="text" placeholder="账号名称/署名 (可选)" value={accountName} onChange={(e) => handleLocalInputChange('accountName', e.target.value)} className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border border-gray-300 rounded-md p-2 text-base" /></div>)}
          </div>
        </div>
        
        {/* Custom Image Upload Area */}
        {showImageUploadArea && (
          <div className="mt-3 p-3 border border-purple-300 rounded-lg bg-purple-50/30">
            <h3 className="text-md font-semibold text-purple-700 flex items-center"><CheckSquare size={20} className="text-purple-600 mr-2" />自定义图片</h3>
            <div className="my-3 flex items-center space-x-4">
              <label className="flex items-center text-sm"><input type="radio" name="customImageType" value="background" checked={customImageType === 'background'} onChange={(e) => setCustomImageType(e.target.value)} className="form-radio h-4 w-4 text-purple-600" /><span className="ml-2">背景图</span></label>
              <label className="flex items-center text-sm"><input type="radio" name="customImageType" value="illustration" checked={customImageType === 'illustration'} onChange={(e) => setCustomImageType(e.target.value)} className="form-radio h-4 w-4 text-purple-600" /><span className="ml-2">插图</span></label>
            </div>
            <input type="file" id="customImageUpload" className="hidden" accept="image/jpeg,image/png,image/gif,image/webp" onChange={handleImageUpload} />
            <label htmlFor="customImageUpload" className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer">
              {isUploading ? <><Loader2 size={18} className="mr-2 animate-spin" />上传中...</> : <><UploadCloud size={18} className="mr-2" />上传图片</>}
            </label>
            {customImageUrl && (
              <div className="relative mt-2 border rounded-md overflow-hidden bg-white p-2">
                <img src={customImageUrl} alt="自定义图片预览" className="max-w-full max-h-[110px] object-contain mx-auto" />
                <button onClick={() => setCustomImageUrl('')} className="absolute top-1 right-1 bg-black bg-opacity-50 text-white rounded-full p-1 hover:bg-opacity-70"><Trash2 size={14} /></button>
              </div>
            )}
            <p className="text-xs text-gray-500 mt-2 text-center">支持JPG/PNG/GIF/WEBP，不超过5MB</p>
          </div>
        )}
      </div>

      <PermissionDialog open={showPermissionDialog} onClose={() => setShowPermissionDialog(false)} featureName={permissionDialogInfo.featureName} reason={permissionDialogInfo.reason} />
    </div>
  );
});

export default ChatInputArea;
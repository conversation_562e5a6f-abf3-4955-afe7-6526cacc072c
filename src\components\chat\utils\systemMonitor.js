/**
 * 系统监控器 - 轻量级性能监控和优化建议
 * 第二十阶段：集成测试与优化
 */

import logger from '../../../services/logs/frontendLogger';

/**
 * 性能指标类型
 */
export const PERFORMANCE_METRICS = {
  LOAD_TIME: 'load_time',
  RENDER_TIME: 'render_time',
  MEMORY_USAGE: 'memory_usage',
  ERROR_RATE: 'error_rate',
  USER_INTERACTION: 'user_interaction'
};

/**
 * 系统监控器类
 */
class SystemMonitor {
  constructor() {
    this.metrics = new Map();
    this.startTime = Date.now();
    this.errorCount = 0;
    this.interactionCount = 0;
    this.isMonitoring = false;
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.startTime = Date.now();
    
    // 监控页面性能
    this.monitorPagePerformance();
    
    // 监控错误
    this.monitorErrors();
    
    // 监控用户交互
    this.monitorUserInteractions();
    
    logger.info('系统监控已启动');
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.isMonitoring = false;
    logger.info('系统监控已停止');
  }

  /**
   * 监控页面性能
   */
  monitorPagePerformance() {
    if (!window.performance) return;

    // 监控页面加载时间
    window.addEventListener('load', () => {
      const loadTime = Date.now() - this.startTime;
      this.recordMetric(PERFORMANCE_METRICS.LOAD_TIME, loadTime);
    });

    // 监控内存使用（如果支持）
    if (window.performance.memory) {
      setInterval(() => {
        const memoryInfo = {
          used: window.performance.memory.usedJSHeapSize,
          total: window.performance.memory.totalJSHeapSize,
          limit: window.performance.memory.jsHeapSizeLimit
        };
        this.recordMetric(PERFORMANCE_METRICS.MEMORY_USAGE, memoryInfo);
      }, 30000); // 每30秒检查一次
    }
  }

  /**
   * 监控错误
   */
  monitorErrors() {
    window.addEventListener('error', (event) => {
      this.errorCount++;
      this.recordMetric(PERFORMANCE_METRICS.ERROR_RATE, {
        count: this.errorCount,
        lastError: {
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          timestamp: Date.now()
        }
      });
    });

    // 监控Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      this.errorCount++;
      this.recordMetric(PERFORMANCE_METRICS.ERROR_RATE, {
        count: this.errorCount,
        lastError: {
          message: event.reason?.message || 'Unhandled Promise Rejection',
          timestamp: Date.now()
        }
      });
    });
  }

  /**
   * 监控用户交互
   */
  monitorUserInteractions() {
    const interactionEvents = ['click', 'keydown', 'scroll', 'resize'];
    
    interactionEvents.forEach(eventType => {
      document.addEventListener(eventType, () => {
        this.interactionCount++;
        if (this.interactionCount % 10 === 0) { // 每10次交互记录一次
          this.recordMetric(PERFORMANCE_METRICS.USER_INTERACTION, {
            count: this.interactionCount,
            timestamp: Date.now()
          });
        }
      }, { passive: true });
    });
  }

  /**
   * 记录性能指标
   * @param {string} type - 指标类型
   * @param {*} value - 指标值
   */
  recordMetric(type, value) {
    if (!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }
    
    const metrics = this.metrics.get(type);
    metrics.push({
      value,
      timestamp: Date.now()
    });

    // 保持最近100条记录
    if (metrics.length > 100) {
      metrics.shift();
    }

    logger.debug('性能指标记录', { type, value });
  }

  /**
   * 获取性能报告
   * @returns {Object} 性能报告
   */
  getPerformanceReport() {
    const report = {
      monitoringDuration: Date.now() - this.startTime,
      totalErrors: this.errorCount,
      totalInteractions: this.interactionCount,
      metrics: {}
    };

    // 处理各种指标
    this.metrics.forEach((values, type) => {
      if (values.length === 0) return;

      switch (type) {
        case PERFORMANCE_METRICS.LOAD_TIME:
          report.metrics.loadTime = {
            average: this.calculateAverage(values),
            latest: values[values.length - 1].value
          };
          break;

        case PERFORMANCE_METRICS.MEMORY_USAGE:
          const latestMemory = values[values.length - 1].value;
          report.metrics.memoryUsage = {
            current: latestMemory,
            usagePercentage: (latestMemory.used / latestMemory.total * 100).toFixed(2)
          };
          break;

        case PERFORMANCE_METRICS.ERROR_RATE:
          report.metrics.errorRate = {
            total: this.errorCount,
            rate: (this.errorCount / (Date.now() - this.startTime) * 60000).toFixed(2) // 每分钟错误数
          };
          break;

        case PERFORMANCE_METRICS.USER_INTERACTION:
          report.metrics.userInteraction = {
            total: this.interactionCount,
            rate: (this.interactionCount / (Date.now() - this.startTime) * 60000).toFixed(2) // 每分钟交互数
          };
          break;
      }
    });

    return report;
  }

  /**
   * 计算平均值
   * @param {Array} values - 数值数组
   * @returns {number} 平均值
   */
  calculateAverage(values) {
    if (values.length === 0) return 0;
    const sum = values.reduce((acc, item) => acc + (typeof item.value === 'number' ? item.value : 0), 0);
    return (sum / values.length).toFixed(2);
  }

  /**
   * 生成优化建议
   * @returns {Array} 优化建议列表
   */
  generateOptimizationSuggestions() {
    const suggestions = [];
    const report = this.getPerformanceReport();

    // 加载时间建议
    if (report.metrics.loadTime && report.metrics.loadTime.average > 3000) {
      suggestions.push({
        type: 'performance',
        priority: 'high',
        message: '页面加载时间较长，建议优化资源加载策略',
        suggestion: '考虑使用懒加载或减少初始资源大小'
      });
    }

    // 内存使用建议
    if (report.metrics.memoryUsage && report.metrics.memoryUsage.usagePercentage > 80) {
      suggestions.push({
        type: 'memory',
        priority: 'medium',
        message: '内存使用率较高，可能存在内存泄漏',
        suggestion: '检查是否有未清理的事件监听器或定时器'
      });
    }

    // 错误率建议
    if (report.metrics.errorRate && report.metrics.errorRate.rate > 1) {
      suggestions.push({
        type: 'stability',
        priority: 'high',
        message: '错误率较高，影响用户体验',
        suggestion: '检查并修复频繁出现的错误'
      });
    }

    // 交互率建议
    if (report.metrics.userInteraction && report.metrics.userInteraction.rate < 5) {
      suggestions.push({
        type: 'usability',
        priority: 'low',
        message: '用户交互率较低',
        suggestion: '考虑改进用户界面的易用性'
      });
    }

    return suggestions;
  }

  /**
   * 检查系统健康状态
   * @returns {Object} 健康状态报告
   */
  checkSystemHealth() {
    const report = this.getPerformanceReport();
    const suggestions = this.generateOptimizationSuggestions();
    
    let healthScore = 100;
    let status = 'excellent';

    // 根据各项指标计算健康分数
    if (report.metrics.errorRate && report.metrics.errorRate.rate > 1) {
      healthScore -= 30;
    }
    
    if (report.metrics.loadTime && report.metrics.loadTime.average > 3000) {
      healthScore -= 20;
    }
    
    if (report.metrics.memoryUsage && report.metrics.memoryUsage.usagePercentage > 80) {
      healthScore -= 25;
    }

    // 确定状态
    if (healthScore >= 90) status = 'excellent';
    else if (healthScore >= 70) status = 'good';
    else if (healthScore >= 50) status = 'fair';
    else status = 'poor';

    return {
      score: Math.max(0, healthScore),
      status,
      report,
      suggestions,
      timestamp: Date.now()
    };
  }
}

// 创建全局监控实例
const systemMonitor = new SystemMonitor();

// 导出监控器实例和相关函数
export default systemMonitor;

/**
 * 启动系统监控
 */
export const startSystemMonitoring = () => {
  systemMonitor.startMonitoring();
};

/**
 * 停止系统监控
 */
export const stopSystemMonitoring = () => {
  systemMonitor.stopMonitoring();
};

/**
 * 获取系统健康报告
 * @returns {Object} 健康报告
 */
export const getSystemHealthReport = () => {
  return systemMonitor.checkSystemHealth();
};

/**
 * 获取性能报告
 * @returns {Object} 性能报告
 */
export const getPerformanceReport = () => {
  return systemMonitor.getPerformanceReport();
};

/**
 * 获取优化建议
 * @returns {Array} 优化建议
 */
export const getOptimizationSuggestions = () => {
  return systemMonitor.generateOptimizationSuggestions();
};

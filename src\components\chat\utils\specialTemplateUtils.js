/**
 * specialTemplateUtils.js
 * 
 * 特殊模板处理工具函数集合
 * 该文件包含检测和处理特殊模板结构的函数
 */

/**
 * 检测是否是特殊模板结构
 * @param {Document|HTMLElement} docOrElement - iframe的document对象或HTML元素
 * @returns {boolean} 是否是特殊模板
 */
export const detectSpecialTemplate = (docOrElement) => {
  try {
    // 根据传入参数类型确定如何处理
    let doc;
    let element;
    
    if (docOrElement instanceof HTMLElement) {
      element = docOrElement;
      doc = element.ownerDocument;
      
      // 1. 检查元素自身是否有特殊样式
      if (element.style && element.style.overflowWrap === 'break-word' && element.style.wordBreak === 'break-all') {
        return true;
      }
      
      // 2. 检查是否在#main-container内
      const mainContainer = element.closest('#main-container');
      if (mainContainer) {
        return true;
      }
      
      // 3. 检查是否有特定的类名
      const identitySection = element.closest('.identity-section');
      const linksSection = element.closest('.links-section');
      if (identitySection || linksSection) {
        return true;
      }
    } else {
      doc = docOrElement;
    }
    
    if (!doc) return false;
    
    // 检查是否包含特定的CSS动画
    const styleElements = doc.querySelectorAll('style');
    let hasAnimation = false;
    
    for (const style of styleElements) {
      if (style.textContent.includes('@keyframes') || 
          style.textContent.includes('animation') || 
          style.textContent.includes('flow')) {
        hasAnimation = true;
        break;
      }
    }
    
    // 检查是否包含大量使用overflow-wrap和word-break的元素
    const elementsWithSpecialStyles = doc.querySelectorAll('[style*="overflow-wrap"][style*="word-break"]');
    if (elementsWithSpecialStyles.length > 5) {
      return true;
    }
    
    // 检查是否有特定的结构模式，例如带有特定类名的元素
    const mainContainer = doc.querySelector('#main-container');
    if (mainContainer && hasAnimation) {
      return true;
    }
    
    // 检查是否有带有特定类名的元素
    const identitySection = doc.querySelector('.identity-section');
    const linksSection = doc.querySelector('.links-section');
    if (identitySection && linksSection) {
      return true;
    }
    
    // 检查是否有特定的CSS结构，例如使用了radial-gradient
    const elementsWithGradient = doc.querySelectorAll('[style*="radial-gradient"]');
    if (elementsWithGradient.length > 0 && hasAnimation) {
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('检测特殊模板结构失败:', error);
    return false;
  }
};

/**
 * 为特殊模板中的元素应用额外处理
 * @param {HTMLElement} element - 需要处理的元素
 * @param {HTMLElement} container - 父容器元素
 */
export const applySpecialTemplateHandling = (element, container) => {
  try {
    if (!element || !element.style) return;
    
    // 1. 确保元素有正确的position和z-index
    element.style.position = 'absolute';
    element.style.zIndex = '100'; // 使用更高的z-index确保在动画元素上方
    
    // 2. 移除可能干扰拖拽的样式
    element.style.overflowWrap = '';
    element.style.wordBreak = '';
    
    // 3. 暂停可能的动画，以免干扰拖拽
    element.style.animation = 'none';
    element.style.transition = 'none';
    
    // 4. 确保元素可以接收鼠标事件
    element.style.pointerEvents = 'auto';
    
    // 5. 处理特殊模板中可能的嵌套contenteditable元素
    const nestedEditableElements = element.querySelectorAll('[contenteditable="true"]');
    nestedEditableElements.forEach(nestedEl => {
      if (nestedEl !== element) {
        // 禁用嵌套contenteditable元素的鼠标事件处理，避免事件冲突
        nestedEl.style.pointerEvents = 'none';
      }
    });
    
    // 6. 在父容器上添加相对定位，确保绝对定位的子元素能够正确定位
    if (container && container.style) {
      // 检查容器的position属性
      const containerStyle = window.getComputedStyle(container);
      if (containerStyle.position === 'static') {
        container.style.position = 'relative';
      }
    }
  } catch (error) {
    console.error('应用特殊模板处理失败:', error);
  }
};

/**
 * 处理特殊模板中的样式冲突
 * @param {HTMLElement} element - 需要处理的元素
 * @param {string} phase - 处理阶段，'start'或'end'
 */
export const handleSpecialTemplateStyles = (element, phase) => {
  if (!element) return;
  
  try {
    // 检测是否是特殊模板
    const isSpecialTemplate = detectSpecialTemplate(element);
    
    if (!isSpecialTemplate) return;
    
    // 根据拖拽阶段应用不同处理
    switch (phase) {
      case 'start':
        // 拖拽开始时的处理
        
        // 1. 保存原始样式
        if (!element.dataset.originalOverflowWrap && element.style.overflowWrap) {
          element.dataset.originalOverflowWrap = element.style.overflowWrap;
        }
        if (!element.dataset.originalWordBreak && element.style.wordBreak) {
          element.dataset.originalWordBreak = element.style.wordBreak;
        }
        // 保存原始z-index（如果尚未保存）
        if (!element.hasAttribute('data-original-zindex') && element.style.zIndex) {
          element.setAttribute('data-original-zindex', element.style.zIndex);
        }
        
        // 2. 临时移除可能干扰拖拽的样式
        element.style.overflowWrap = '';
        element.style.wordBreak = '';
        
        // 3. 确保元素有正确的z-index
        if (!element.style.zIndex || parseInt(element.style.zIndex) < 10) {
          element.style.zIndex = '10';
        }
        
        // 4. 处理嵌套元素
        const nestedElements = element.querySelectorAll('*');
        nestedElements.forEach(nested => {
          if (nested.style) {
            // 保存原始样式
            if (nested.style.overflowWrap) {
              nested.dataset.originalOverflowWrap = nested.style.overflowWrap;
              nested.style.overflowWrap = '';
            }
            if (nested.style.wordBreak) {
              nested.dataset.originalWordBreak = nested.style.wordBreak;
              nested.style.wordBreak = '';
            }
          }
        });
        break;
        
      case 'end':
        // 拖拽结束时的处理
        
        // 1. 恢复原始样式，但不恢复z-index（由handleDragEnd处理）
        if (element.dataset.originalOverflowWrap) {
          element.style.overflowWrap = element.dataset.originalOverflowWrap;
          delete element.dataset.originalOverflowWrap;
        }
        if (element.dataset.originalWordBreak) {
          element.style.wordBreak = element.dataset.originalWordBreak;
          delete element.dataset.originalWordBreak;
        }
        
        // 2. 恢复嵌套元素的原始样式
        const nestedElementsEnd = element.querySelectorAll('*');
        nestedElementsEnd.forEach(nested => {
          if (nested.dataset.originalOverflowWrap) {
            nested.style.overflowWrap = nested.dataset.originalOverflowWrap;
            delete nested.dataset.originalOverflowWrap;
          }
          if (nested.dataset.originalWordBreak) {
            nested.style.wordBreak = nested.dataset.originalWordBreak;
            delete nested.dataset.originalWordBreak;
          }
        });
        break;
    }
  } catch (error) {
    console.error('处理特殊模板样式失败:', error);
  }
};

/**
 * 为特殊模板保留关键样式
 * @param {HTMLElement} element - HTML元素
 * @param {CSSStyleDeclaration} computedStyle - 计算样式对象
 */
export const preserveKeyStylesForSpecialTemplate = (element, computedStyle) => {
  if (!element || !element.style) return;
  
  try {
    // 如果有内联样式，检查是否包含overflow-wrap和word-break
    if (element.hasAttribute('style')) {
      const style = element.getAttribute('style');
      
      // 如果同时包含overflow-wrap和word-break，保留这些样式
      if (style.includes('overflow-wrap') && style.includes('word-break')) {
        // 不移除这些样式，保持原样
        return;
      }
    }
    
    // 如果元素有计算样式，检查是否需要保留动画相关样式
    if (computedStyle) {
      // 检查是否有动画
      if (computedStyle.animation && computedStyle.animation !== 'none') {
        // 保留动画相关样式
        element.style.animation = computedStyle.animation;
        element.style.animationPlayState = 'running';
      }
      
      // 检查是否有transform
      if (computedStyle.transform && computedStyle.transform !== 'none') {
        // 保留transform样式
        element.style.transform = computedStyle.transform;
      }
      
      // 检查是否有position
      if (computedStyle.position && computedStyle.position !== 'static') {
        // 保留position样式
        element.style.position = computedStyle.position;
      }
    }
  } catch (error) {
    console.error('保留特殊模板样式失败:', error);
  }
};

/**
 * 恢复特殊模板中的关键动画样式
 * @param {Document} doc - HTML文档对象
 */
export const restoreKeyAnimations = (doc) => {
  try {
    // 查找所有可能包含动画的元素
    const elementsWithAnimation = doc.querySelectorAll('[data-original-animation]');
    elementsWithAnimation.forEach(el => {
      const originalAnimation = el.getAttribute('data-original-animation');
      if (originalAnimation) {
        el.style.animation = originalAnimation;
        el.style.animationPlayState = 'running';
        el.removeAttribute('data-original-animation');
      }
    });
    
    // 查找所有可能包含过渡效果的元素
    const elementsWithTransition = doc.querySelectorAll('[data-original-transition]');
    elementsWithTransition.forEach(el => {
      const originalTransition = el.getAttribute('data-original-transition');
      if (originalTransition) {
        el.style.transition = originalTransition;
        el.removeAttribute('data-original-transition');
      }
    });
    
    // 查找所有可能包含transform的元素
    const elementsWithTransform = doc.querySelectorAll('[data-original-transform]');
    elementsWithTransform.forEach(el => {
      const originalTransform = el.getAttribute('data-original-transform');
      if (originalTransform) {
        el.style.transform = originalTransform;
        el.removeAttribute('data-original-transform');
      }
    });
    
    // 特殊处理：查找main-container元素，确保其overflow属性正确设置
    const mainContainer = doc.querySelector('#main-container');
    if (mainContainer) {
      // 如果有overflow-wrap和word-break内联样式，确保它们被保留
      if (mainContainer.style.overflowWrap === 'break-word' && mainContainer.style.wordBreak === 'break-all') {
        // 确保这些样式被保留
      } else {
        // 如果没有这些内联样式，但需要它们，则添加
        mainContainer.style.overflowWrap = 'break-word';
        mainContainer.style.wordBreak = 'break-all';
      }
    }
  } catch (error) {
    console.error('恢复关键动画样式失败:', error);
  }
}; 
const jwt = require('jsonwebtoken');
const { User } = require('../models');
const logger = require('../utils/logger');

/**
 * JWT认证中间件
 * 验证请求中的JWT token，并将用户信息添加到req对象中
 */
const auth = async (req, res, next) => {
  try {
    // 获取Authorization头
    const authHeader = req.header('Authorization');

    if (!authHeader) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }

    // 检查Authorization头格式
    if (!authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '认证令牌格式不正确'
      });
    }

    // 提取token部分
    const token = authHeader.replace('Bearer ', '');

    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 查找用户
    const user = await User.findByPk(decoded.id);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '无效的用户令牌'
      });
    }

    // 检查用户是否被锁定
    if (user.isLocked()) {
      // 计算剩余锁定时间
      const remainingTime = user.lock_expires_at ? Math.ceil((new Date(user.lock_expires_at) - new Date()) / (60 * 60 * 1000)) : 24;
      return res.status(403).json({
        success: false,
        message: `账号已被锁定，请${remainingTime}小时后再试或联系客服`
      });
    }

    // 检查并更新VIP状态
    if (user.role === 'vip') {
      const vipStatusUpdated = await user.checkAndUpdateVipStatus();
      if (vipStatusUpdated) {
        logger.info(`用户${user.id}(${user.phone})的VIP已过期，已自动转为普通用户`);
      }
    }

    // 将用户信息添加到请求对象
    req.user = user;
    req.token = token;

    next();
  } catch (error) {
    logger.error('JWT验证失败:', error);

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '认证令牌已过期，请重新登录'
      });
    }

    res.status(401).json({
      success: false,
      message: '请先登录'
    });
  }
};

/**
 * 管理员权限中间件
 * 验证用户是否具有管理员权限
 */
const adminAuth = async (req, res, next) => {
  try {
    // 检查用户是否已通过认证
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '请先登录'
      });
    }

    // 检查用户是否为管理员
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '您没有管理员权限'
      });
    }

    next();
  } catch (error) {
    logger.error('管理员权限验证失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
};

module.exports = {
  auth,
  adminAuth
};

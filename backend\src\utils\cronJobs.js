const cron = require('node-cron');
const logger = require('./logger');

// 任务注册表
const scheduledTasks = {};

// 注册定时任务
const registerTask = (name, schedule, task) => {
  if (scheduledTasks[name]) {
    logger.warn(`任务${name}已存在，将被覆盖`);
    scheduledTasks[name].stop();
  }
  
  try {
    const cronTask = cron.schedule(schedule, async () => {
      logger.info(`开始执行定时任务: ${name}`);
      const startTime = Date.now();
      
      try {
        await task();
        logger.info(`定时任务${name}执行完成，耗时: ${Date.now() - startTime}ms`);
      } catch (error) {
        logger.error(`定时任务${name}执行失败:`, error);
      }
    }, {
      scheduled: true,
      timezone: "Asia/Shanghai" // 使用中国时区
    });
    
    scheduledTasks[name] = cronTask;
    logger.info(`定时任务${name}已注册，执行计划: ${schedule}`);
    return true;
  } catch (error) {
    logger.error(`注册定时任务${name}失败:`, error);
    return false;
  }
};

module.exports = {
  registerTask,
  scheduledTasks
};

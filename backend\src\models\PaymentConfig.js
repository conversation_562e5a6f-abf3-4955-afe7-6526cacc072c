const { DataTypes } = require('sequelize');

/**
 * 支付配置模型
 * 用于管理不同支付方式的配置参数
 */
module.exports = (sequelize) => {
  const PaymentConfig = sequelize.define('PaymentConfig', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    payment_type: {
      type: DataTypes.ENUM('wechat', 'alipay'),
      allowNull: false,
      comment: '支付类型'
    },
    config_name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '配置名称'
    },
    config_key: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '配置键'
    },
    config_value: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '配置值'
    },
    is_encrypted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否加密'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否激活'
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '描述'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'payment_configs',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        name: 'idx_payment_type',
        fields: ['payment_type']
      },
      {
        name: 'idx_config_key',
        fields: ['config_key']
      },
      {
        name: 'idx_is_active',
        fields: ['is_active']
      }
    ]
  });

  return PaymentConfig;
}; 
/**
 * 日志路由
 */
const express = require('express');
const router = express.Router();
const logController = require('../controllers/logController');
const { auth, adminAuth } = require('../middlewares/authMiddleware');

// 环境检查中间件，只允许在开发环境中访问
const developmentOnly = (req, res, next) => {
  if (process.env.NODE_ENV !== 'development') {
    return res.status(403).json({
      success: false,
      message: '此功能仅在开发环境可用'
    });
  }
  next();
};

/**
 * @route GET /api/admin/logs
 * @desc 获取日志列表
 * @access Private (Admin)
 */
router.get('/', auth, adminAuth, logController.getLogs);

/**
 * @route GET /api/admin/logs/stats
 * @desc 获取日志统计数据
 * @access Private (Admin)
 */
router.get('/stats', auth, adminAuth, logController.getLogStats);

/**
 * @route GET /api/admin/logs/:id
 * @desc 获取日志详情
 * @access Private (Admin)
 */
router.get('/:id', auth, adminAuth, logController.getLogDetail);

/**
 * @route DELETE /api/admin/logs/cleanup
 * @desc 清理过期日志
 * @access Private (Admin)
 */
router.delete('/cleanup', auth, adminAuth, logController.cleanupLogs);

/**
 * @route POST /api/admin/logs/test
 * @desc 创建测试日志记录 (仅开发环境)
 * @access Private (Admin)
 */
router.post('/test', auth, adminAuth, developmentOnly, logController.createTestLogs);

module.exports = router; 
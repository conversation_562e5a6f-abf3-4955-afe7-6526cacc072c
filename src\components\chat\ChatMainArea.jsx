import React from 'react';
import ChatPreview from './ChatPreview';
import ProfileContent from './ProfileContent';
import AuthContent from './AuthContent';
import MembershipPage from '../membership/MembershipPage';
import FileUploadView from './FileUploadView';
import CodePasteView from './CodePasteView';

// ChatMainArea 组件可以根据activeView展示不同内容
const ChatMainArea = (props) => {
  const {
    chatPreviewProps,   // 包含 isPreviewActive, coverImageUrl 等
    previewRef,         // 从props中获取ref
    activeView = 'chat', // 用于控制显示的内容类型
  } = props;

  // 根据activeView显示不同内容
  const renderContent = () => {
    // 如果是聊天/生成封面模式
    if (activeView === 'chat') {
      // 根据预览区域是否激活来决定容器样式，为编辑台固定定位预留空间
      const containerClassName = chatPreviewProps.isPreviewActive
        ? "w-full h-full flex flex-col items-center justify-center overflow-hidden pr-80" // 预留编辑台宽度
        : "w-full h-full flex items-center justify-center overflow-hidden"; // 添加overflow-hidden防止滚动条出现

      return (
        <div className={containerClassName}>
          <ChatPreview {...chatPreviewProps} ref={previewRef} />
        </div>
      );
    }
    
    // 如果是登录/注册页面
    if (activeView === 'auth') {
      return <AuthContent />;
    }
    
    // 如果是会员中心页面
    if (activeView === 'membership') {
      return (
        <div className="w-full h-full overflow-auto p-4 md:p-6">
          <MembershipPage />
        </div>
      );
    }
    
    // 如果是个人中心相关页面
    if (activeView === 'profile' || activeView === 'points' || activeView === 'covers' || activeView === 'orders' || activeView === 'my-creations') {
      return <ProfileContent contentType={activeView} />;
    }
    
    // 如果是文件上传页面
    if (activeView === 'file-upload') {
      return <FileUploadView />;
    }
    
    // 如果是代码粘贴页面
    if (activeView === 'code-paste') {
      return <CodePasteView />;
    }
    
    // 默认返回聊天预览
    return (
      <div className="w-full h-full flex flex-col items-center justify-center overflow-hidden">
        <ChatPreview {...chatPreviewProps} ref={previewRef} />
      </div>
    );
  };

  return (
    <div className="w-full h-full">
      {renderContent()}
    </div>
  );
};

export default ChatMainArea;
/**
 * 封面生成网站 - 后端服务启动脚本
 */
const logger = require('./src/utils/logger');
const initDatabase = require('./src/utils/initDatabase');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// 确保上传和日志目录存在
const dirs = ['logs', 'uploads', 'public/admin'];
dirs.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    logger.info(`创建目录: ${dirPath}`);
  }
});

// 初始化环境变量文件
const envPath = path.join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  logger.info('未找到.env文件，创建默认配置');
  // 复制示例配置
  fs.copyFileSync(
    path.join(__dirname, '.env.example') || path.join(__dirname, '.env'),
    envPath
  );
}

// 启动前准备
const startServer = async () => {
  logger.info('=============================================');
  logger.info('  封面生成网站 - 后端服务启动中');
  logger.info('=============================================');
  
  // 初始化数据库
  logger.info('开始初始化数据库...');
  const dbInitResult = await initDatabase();
  
  if (!dbInitResult) {
    logger.error('数据库初始化失败，请检查数据库配置');
    process.exit(1);
  }
  
  // 检查是否安装了所有依赖
  if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
    logger.info('正在安装依赖包，请稍候...');
    try {
      // 执行npm install
      exec('npm install', { cwd: __dirname }, (error, stdout, stderr) => {
        if (error) {
          logger.error('依赖安装失败:', error);
          return;
        }
        logger.info('依赖安装完成，正在启动服务...');
        startApp();
      });
    } catch (error) {
      logger.error('依赖安装失败:', error);
    }
  } else {
    startApp();
  }
};

// 启动应用
const startApp = () => {
  try {
    // 导入应用
    const app = require('./src/app');
    logger.info('服务启动成功！');
    logger.info('=============================================');
    logger.info('默认管理员账号: 13800000000');
    logger.info('默认管理员密码: admin123456');
    logger.info('请及时修改默认密码！');
    logger.info('=============================================');
  } catch (error) {
    logger.error('服务启动失败:', error);
  }
};

// 开始启动流程
startServer().catch(err => {
  logger.error('启动过程中出错:', err);
});

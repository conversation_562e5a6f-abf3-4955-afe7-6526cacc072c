<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础功能测试 - 架构差异对比</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .title {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .test-title {
            color: #444;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .test-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background: #667eea;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .editable-text {
            background: #fff;
            border: 2px dashed #ccc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            cursor: text;
            transition: all 0.3s ease;
            min-height: 50px;
            display: flex;
            align-items: center;
        }
        
        .editable-text:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .editable-text.editing {
            border-color: #667eea;
            background: #fff;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }
        
        .interactive-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .interactive-button:active {
            transform: translateY(0);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        .status-safe {
            background: #4CAF50;
        }
        
        .status-warning {
            background: #FF9800;
        }
        
        .status-danger {
            background: #F44336;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .architecture-info {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .result-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .result-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 基础功能测试</h1>
            <p class="subtitle">用于测试新旧架构在基础功能上的差异表现</p>
            <div class="architecture-info">
                <strong>测试说明：</strong>此HTML文件包含基础的文本编辑、按钮交互和样式功能，用于验证两种架构的兼容性和稳定性。
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">1</span>
                文本编辑功能测试
            </div>
            <div class="editable-text" contenteditable="true">
                点击这里编辑文本 - 测试文本编辑器功能是否正常工作
            </div>
            <div class="editable-text" contenteditable="true">
                第二个可编辑区域 - 测试多个编辑区域的切换和焦点管理
            </div>
            <div class="test-result" id="textEditResult">
                <span class="status-indicator status-safe"></span>
                等待测试...
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">2</span>
                交互功能测试
            </div>
            <button class="interactive-button" onclick="testButtonClick(this)">
                点击测试按钮交互
            </button>
            <button class="interactive-button" onclick="testStyleChange()">
                测试样式动态修改
            </button>
            <button class="interactive-button" onclick="testDOMManipulation()">
                测试DOM操作
            </button>
            <div class="test-result" id="interactionResult">
                <span class="status-indicator status-warning"></span>
                等待交互测试...
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">3</span>
                安全性测试
            </div>
            <p>此区域用于测试架构的安全隔离能力：</p>
            <div id="securityTestArea" style="padding: 15px; background: #f5f5f5; border-radius: 5px;">
                <span class="status-indicator status-safe"></span>
                安全测试区域 - 正常显示表示安全隔离有效
            </div>
            <div class="test-result" id="securityResult">
                <span class="status-indicator status-safe"></span>
                安全测试通过
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">4</span>
                架构差异检测
            </div>
            <p>当前架构信息：</p>
            <div id="architectureInfo" style="font-family: monospace; background: #f0f0f0; padding: 15px; border-radius: 5px;">
                正在检测架构类型...
            </div>
            <div class="test-result" id="architectureResult">
                <span class="status-indicator status-warning"></span>
                架构检测中...
            </div>
        </div>
    </div>

    <script>
        // 测试按钮点击功能
        function testButtonClick(button) {
            const originalText = button.textContent;
            button.textContent = '✓ 点击成功！';
            button.style.background = '#4CAF50';
            
            document.getElementById('interactionResult').innerHTML = 
                '<span class="status-indicator status-safe"></span>按钮交互测试通过';
            document.getElementById('interactionResult').className = 'test-result result-success';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '';
            }, 2000);
        }
        
        // 测试样式动态修改
        function testStyleChange() {
            const container = document.querySelector('.container');
            const originalBg = container.style.background;
            
            container.style.background = 'linear-gradient(45deg, #ff6b6b, #feca57)';
            container.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                container.style.background = originalBg;
            }, 2000);
            
            document.getElementById('interactionResult').innerHTML = 
                '<span class="status-indicator status-safe"></span>样式修改测试通过';
            document.getElementById('interactionResult').className = 'test-result result-success';
        }
        
        // 测试DOM操作
        function testDOMManipulation() {
            const testArea = document.getElementById('securityTestArea');
            const newElement = document.createElement('div');
            newElement.innerHTML = '✓ DOM操作测试成功 - 新元素已添加';
            newElement.style.color = '#4CAF50';
            newElement.style.fontWeight = 'bold';
            newElement.style.marginTop = '10px';
            
            testArea.appendChild(newElement);
            
            setTimeout(() => {
                testArea.removeChild(newElement);
            }, 3000);
            
            document.getElementById('interactionResult').innerHTML = 
                '<span class="status-indicator status-safe"></span>DOM操作测试通过';
            document.getElementById('interactionResult').className = 'test-result result-success';
        }
        
        // 检测架构类型
        function detectArchitecture() {
            const info = {
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                windowLocation: window.location.href,
                parentExists: window.parent !== window,
                topExists: window.top !== window,
                frameElement: !!window.frameElement,
                sandboxed: window.frameElement ? window.frameElement.hasAttribute('sandbox') : false
            };
            
            document.getElementById('architectureInfo').innerHTML = 
                JSON.stringify(info, null, 2);
            
            let architectureType = '传统架构';
            let statusClass = 'result-warning';
            let statusIcon = 'status-warning';
            
            if (info.sandboxed) {
                architectureType = '沙箱架构';
                statusClass = 'result-success';
                statusIcon = 'status-safe';
            }
            
            document.getElementById('architectureResult').innerHTML = 
                `<span class="status-indicator ${statusIcon}"></span>检测到：${architectureType}`;
            document.getElementById('architectureResult').className = `test-result ${statusClass}`;
        }
        
        // 页面加载完成后执行检测
        window.addEventListener('load', function() {
            detectArchitecture();
            
            // 监听文本编辑
            const editableElements = document.querySelectorAll('.editable-text');
            editableElements.forEach(element => {
                element.addEventListener('focus', function() {
                    this.classList.add('editing');
                    document.getElementById('textEditResult').innerHTML = 
                        '<span class="status-indicator status-safe"></span>文本编辑功能正常';
                    document.getElementById('textEditResult').className = 'test-result result-success';
                });
                
                element.addEventListener('blur', function() {
                    this.classList.remove('editing');
                });
            });
        });
    </script>
</body>
</html>

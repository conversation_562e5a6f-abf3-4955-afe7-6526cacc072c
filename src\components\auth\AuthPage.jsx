import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Login from './Login';
import Register from './Register';
import ForgotPassword from './ForgotPassword';
import ChatSidebar from '../chat/ChatSidebar';
import { cn } from '@/lib/utils';

const AuthPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('login');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  
  // 检查URL参数，确定显示哪个组件
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get('tab');
    
    if (tab === 'register') {
      setActiveTab('register');
    } else if (tab === 'forgot-password') {
      setActiveTab('forgot-password');
    } else {
      setActiveTab('login');
    }
  }, [location.search]);

  // 处理注册成功的回调
  const handleRegisterSuccess = () => {
    // 注册成功后，重定向到首页
    navigate('/');
  };

  // 处理侧边栏折叠切换
  const toggleSidebarCollapse = () => {
    setIsSidebarCollapsed(prev => !prev);
  };

  // 根据activeTab渲染不同的组件
  const renderAuthComponent = () => {
    switch (activeTab) {
      case 'register':
        return <Register />;
      case 'forgot-password':
        return <ForgotPassword />;
      case 'login':
      default:
        return <Login />;
    }
  };

  // 模拟handleNavItemClick函数，实际上什么都不做
  const handleNavItemClick = () => {};

  return (
    <div className="flex h-screen w-screen bg-gray-100 overflow-hidden">
      {/* 1. 左侧边栏 */}
      <div
        className={`bg-white shadow-lg overflow-y-auto flex-none z-20 transition-all duration-300 ease-in-out ${isSidebarCollapsed ? 'w-16' : 'w-64'}`}
      >
        <ChatSidebar
          isCollapsed={isSidebarCollapsed}
          toggleCollapse={toggleSidebarCollapse}
          userInfo={null}
          onLoadCoverData={() => {}}
          onNavItemClick={handleNavItemClick}
          activeView="chat"
          onResetDesign={() => {}}
        />
      </div>
      
      {/* 2. 右侧区域 - 显示认证组件 */}
      <div className="flex-1 flex flex-col overflow-auto bg-gray-100 p-4 md:p-6 lg:p-8">
        <div className="max-w-md mx-auto w-full">
      {renderAuthComponent()}
        </div>
      </div>
    </div>
  );
};

export default AuthPage;
const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 封面生成记录模型
 * 对应数据库中的cover_records表
 */
const CoverRecord = sequelize.define('CoverRecord', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '封面记录ID，唯一标识'
  },
  sequence_number: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '序号'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联用户表的用户ID'
  },
  cover_code: {
    type: DataTypes.STRING(36),
    allowNull: true,
    unique: true,
    comment: '用户可见的唯一封面编码 (例如 UUID)'
  },
  cover_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '封面类型，如：xiaohongshu、wechat或自定义类型'
  },
  cover_type_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '封面类型显示名称'
  },
  cover_style: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '用户选择的封面风格'
  },
  cover_style_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '封面风格显示名称'
  },
  cover_text: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '用户输入的封面文案内容'
  },
  account_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '用户输入的账号名称'
  },
  subtitle: {
    type: DataTypes.STRING(200),
    allowNull: true,
    comment: '用户输入的副标题'
  },
  auto_title: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否启用自动标题功能'
  },
  image_url: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '生成的封面图片URL'
  },
  html_content: {
    type: DataTypes.TEXT('long'),
    allowNull: true,
    comment: '封面生成时的HTML代码，用于预览和再编辑'
  },
  edited_html_content: {
    type: DataTypes.TEXT('long'),
    allowNull: true,
    comment: '用户编辑后的HTML内容，用于保存用户的编辑结果'
  },
  version: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    comment: '封面版本号，每次编辑保存时递增'
  },
  last_edited_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后编辑时间'
  },
  prompt_used: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '生成封面时使用的完整提示词'
  },
  ai_service: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '使用的AI服务信息，JSON格式'
  },
  status: {
    type: DataTypes.ENUM('显示', '隐藏'),
    allowNull: false,
    defaultValue: '显示',
    comment: '封面记录状态，控制在用户个人中心的显示状态'
  },
  record_type: {
    type: DataTypes.ENUM('AI生成', '文件上传', '代码粘贴'),
    allowNull: false,
    defaultValue: 'AI生成',
    comment: '记录类型，标识封面的来源'
  },
  // 新增原始HTML安全保留相关字段
  original_html_content: {
    type: DataTypes.TEXT('long'),
    allowNull: true,
    comment: '用户上传的原始HTML内容，不做任何修改'
  },
  original_html_hash: {
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '原始HTML的SHA256哈希值，用于完整性验证'
  },
  security_scan_result: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '安全扫描结果，包含风险等级和检测详情'
  },
  isolation_config: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '隔离配置参数，控制沙箱行为'
  },
  sandbox_permissions: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '沙箱权限配置，精确控制允许的功能'
  },
  content_source_type: {
    type: DataTypes.ENUM('upload', 'paste', 'ai_generated'),
    allowNull: false,
    defaultValue: 'ai_generated',
    comment: '内容来源类型'
  }
}, {
  tableName: 'cover_records',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

// 在模型导出前添加关联关系的设置
CoverRecord.associate = (models) => {
  CoverRecord.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user'
  });
  
  CoverRecord.belongsTo(models.StylePrompt, {
    foreignKey: 'cover_style',
    targetKey: 'style_name',
    as: 'stylePrompt'
  });
};

module.exports = CoverRecord;

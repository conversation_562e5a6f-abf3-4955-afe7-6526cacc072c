<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>系统设置 - 封面生成网站管理后台</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link rel="stylesheet" href="css/dashboard.css">
  <link rel="stylesheet" href="css/custom.css">
</head>
<body>
  <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
    <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">封面生成网站管理后台</a>
    <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="w-100"></div>
    <div class="navbar-nav">
      <div class="nav-item text-nowrap">
        <a class="nav-link px-3" href="#" id="logoutBtn">退出登录</a>
      </div>
    </div>
  </nav>

  <div class="container-fluid">
    <div class="row">
      <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
        <div class="sidebar-sticky">
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="dashboard.html">
                <i class="bi bi-speedometer"></i>控制台概览
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="statistics.html">
                <i class="bi bi-bar-chart"></i>数据统计
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="users.html">
                <i class="bi bi-people"></i>用户管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="covers.html">
                <i class="bi bi-images"></i>封面管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="cover-records.html">
                <i class="bi bi-clock-history"></i>封面记录
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="tasks.html">
                <i class="bi bi-hdd-stack"></i>任务管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="point-records.html">
                <i class="bi bi-credit-card-2-front"></i>积分记录
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="styles.html">
                <i class="bi bi-palette"></i>风格管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="features.html">
                <i class="bi bi-toggles"></i>功能控制
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="ai-services.html">
                <i class="bi bi-robot"></i>AI服务管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="settings.html">
                <i class="bi bi-gear"></i>系统设置
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="logs.html">
                <i class="bi bi-journal-text"></i>系统日志
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <main class="col-md-9 offset-md-3 col-lg-10 offset-lg-2 px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2">系统设置</h1>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="card dashboard-card">
              <div class="card-header">
                <h5 class="card-title mb-0">积分设置</h5>
              </div>
              <div class="card-body">
                <form id="pointsSettingsForm">
                  <div class="mb-3">
                    <label for="newUserPoints" class="form-label">新用户初始积分</label>
                    <input type="number" class="form-control" id="newUserPoints" min="0" step="1">
                    <div class="form-text">新用户注册后自动获得的积分数量</div>
                  </div>
                  <div class="mb-3">
                    <label for="coverPointsCost" class="form-label">生成封面所需积分</label>
                    <input type="number" class="form-control" id="coverPointsCost" min="0" step="1">
                    <div class="form-text">用户每次生成封面消耗的积分数量</div>
                  </div>
                  <div class="mb-3">
                    <label for="verifyCodeStorageType" class="form-label">验证码存储方式</label>
                    <select class="form-select" id="verifyCodeStorageType">
                      <option value="memory">内存存储（本地测试环境）</option>
                      <option value="database">数据库存储（云服务器部署）</option>
                    </select>
                    <div class="form-text">内存存储适用于本地测试环境，数据库存储适用于云服务器部署</div>
                  </div>
                  <button type="submit" class="btn btn-primary" id="saveSettingsBtn">保存系统设置</button>
                </form>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card dashboard-card">
              <div class="card-header">
                <h5 class="card-title mb-0">网站信息设置</h5>
              </div>
              <div class="card-body">
                <form id="siteSettingsForm">
                  <div class="mb-3">
                    <label for="siteName" class="form-label">网站名称</label>
                    <input type="text" class="form-control" id="siteName">
                  </div>
                  <div class="mb-3">
                    <label for="siteDescription" class="form-label">网站描述</label>
                    <textarea class="form-control" id="siteDescription" rows="2"></textarea>
                  </div>
                  <div class="mb-3">
                    <label for="contactEmail" class="form-label">联系邮箱</label>
                    <input type="email" class="form-control" id="contactEmail">
                  </div>
                  <div class="mb-3">
                    <label for="icp" class="form-label">ICP备案号</label>
                    <input type="text" class="form-control" id="icp">
                  </div>
                  <button type="submit" class="btn btn-primary" id="siteSettingsSaveBtn">保存网站信息</button>
                </form>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-md-12">
            <div class="card dashboard-card">
              <div class="card-header">
                <h5 class="card-title mb-0">隐私政策和用户协议</h5>
              </div>
              <div class="card-body">
                <ul class="nav nav-tabs" id="policyTabs" role="tablist">
                  <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="privacy-tab" data-bs-toggle="tab" data-bs-target="#privacy" type="button" role="tab" aria-controls="privacy" aria-selected="true">隐私政策</button>
                  </li>
                  <li class="nav-item" role="presentation">
                    <button class="nav-link" id="agreement-tab" data-bs-toggle="tab" data-bs-target="#agreement" type="button" role="tab" aria-controls="agreement" aria-selected="false">用户协议</button>
                  </li>
                </ul>
                <div class="tab-content mt-3" id="policyTabsContent">
                  <div class="tab-pane fade show active" id="privacy" role="tabpanel" aria-labelledby="privacy-tab">
                    <form id="privacyPolicyForm">
                      <div class="mb-3">
                        <label for="privacyPolicyVersion" class="form-label">版本号</label>
                        <input type="text" class="form-control" id="privacyPolicyVersion" placeholder="例如: 1.0">
                      </div>
                      <div class="mb-3">
                        <label for="privacyPolicyContent" class="form-label">隐私政策内容</label>
                        <textarea class="form-control" id="privacyPolicyContent" rows="15" placeholder="请输入隐私政策内容，支持HTML格式"></textarea>
                      </div>
                      <button type="submit" class="btn btn-primary" id="privacyPolicySaveBtn">保存隐私政策</button>
                    </form>
                  </div>
                  <div class="tab-pane fade" id="agreement" role="tabpanel" aria-labelledby="agreement-tab">
                    <form id="userAgreementForm">
                      <div class="mb-3">
                        <label for="userAgreementVersion" class="form-label">版本号</label>
                        <input type="text" class="form-control" id="userAgreementVersion" placeholder="例如: 1.0">
                      </div>
                      <div class="mb-3">
                        <label for="userAgreementContent" class="form-label">用户协议内容</label>
                        <textarea class="form-control" id="userAgreementContent" rows="15" placeholder="请输入用户协议内容，支持HTML格式"></textarea>
                      </div>
                      <button type="submit" class="btn btn-primary" id="userAgreementSaveBtn">保存用户协议</button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-md-12">
            <div class="card dashboard-card">
              <div class="card-header">
                <h5 class="card-title mb-0">数据库备份与恢复</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <h6>备份数据库</h6>
                    <p class="text-muted">将当前数据库导出为JSON文件，用于备份或迁移。</p>
                    <button class="btn btn-primary" id="backupDatabaseBtn">
                      <i class="bi bi-download"></i> 备份数据库
                    </button>
                  </div>
                  <div class="col-md-6">
                    <h6>恢复数据库</h6>
                    <p class="text-muted">从备份文件恢复数据库。此操作将覆盖现有数据，请谨慎操作。</p>
                    <div class="input-group mb-3">
                      <input type="file" class="form-control" id="restoreFileInput" accept=".json">
                      <button class="btn btn-warning" type="button" id="restoreDatabaseBtn">
                        <i class="bi bi-upload"></i> 恢复
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- JavaScript -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="js/utils.js"></script>
  <script src="js/modules/settings.js"></script>
  <script src="js/dashboard.js"></script>
</body>
</html>

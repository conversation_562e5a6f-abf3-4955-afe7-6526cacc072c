/**
 * 设置数据库并测试API
 */

const mysql = require('mysql2/promise');
const http = require('http');
const jwt = require('jsonwebtoken');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'TUWENbo19890120',
  database: 'fengmian_db'
};

// 创建JWT令牌
function createTestToken() {
  const payload = {
    userId: 1,
    phone: '13800138000',
    role: 'admin'
  };
  return jwt.sign(payload, 'your_jwt_secret_key', { expiresIn: '1h' });
}

// 测试API接口
function testAPI(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function setupDatabaseAndTest() {
  let connection;
  
  try {
    console.log('🔍 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查并创建html_security_violations表
    console.log('\n📋 检查html_security_violations表...');
    const [tables] = await connection.execute("SHOW TABLES LIKE 'html_security_violations'");
    
    if (tables.length === 0) {
      console.log('❌ html_security_violations表不存在，正在创建...');
      
      // 创建表
      await connection.execute(`
        CREATE TABLE html_security_violations (
          id INT PRIMARY KEY AUTO_INCREMENT COMMENT '违规记录ID，唯一标识',
          user_id INT NOT NULL COMMENT '用户ID',
          user_phone VARCHAR(20) COMMENT '用户手机号',
          file_name VARCHAR(255) COMMENT '原始文件名',
          upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
          risk_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') COMMENT '风险等级',
          violation_reasons JSON COMMENT '违规原因详情',
          detected_threats JSON COMMENT '检测到的威胁列表',
          file_size INT COMMENT '文件大小（字节）',
          file_hash VARCHAR(64) COMMENT '文件内容SHA256哈希',
          detection_engine_version VARCHAR(50) COMMENT '检测引擎版本',
          ip_address VARCHAR(45) COMMENT '上传IP地址',
          user_agent TEXT COMMENT '用户代理信息',
          INDEX idx_user_id (user_id),
          INDEX idx_upload_time (upload_time),
          INDEX idx_risk_level (risk_level)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='HTML安全违规记录表'
      `);
      
      console.log('✅ html_security_violations表创建成功');
      
      // 插入测试数据
      await connection.execute(`
        INSERT INTO html_security_violations 
        (user_id, file_name, risk_level, violation_reasons, detected_threats, file_size, ip_address) 
        VALUES 
        (1, 'test1.html', 'HIGH', 
         JSON_OBJECT('xss_detected', true, 'script_tags', 2), 
         JSON_ARRAY('XSS攻击', '恶意脚本'), 
         1024, '127.0.0.1'),
        (1, 'test2.html', 'MEDIUM', 
         JSON_OBJECT('suspicious_code', true, 'eval_calls', 1), 
         JSON_ARRAY('可疑代码', 'eval调用'), 
         2048, '127.0.0.1'),
        (1, 'test3.html', 'LOW', 
         JSON_OBJECT('iframe_detected', true), 
         JSON_ARRAY('iframe标签'), 
         512, '127.0.0.1')
      `);
      
      console.log('✅ 测试数据插入成功');
    } else {
      console.log('✅ html_security_violations表已存在');
    }

    // 检查数据
    const [data] = await connection.execute('SELECT COUNT(*) as count FROM html_security_violations');
    console.log(`📊 违规记录数量: ${data[0].count}`);

    await connection.end();
    console.log('✅ 数据库设置完成');

    // 测试API接口
    console.log('\n🧪 测试API接口...');
    const token = createTestToken();
    console.log(`🔑 生成测试令牌: ${token.substring(0, 50)}...`);

    // 等待后端启动
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试获取渲染方式配置
    console.log('\n1️⃣ 测试 GET /api/admin/config/rendering-mode');
    const renderingResult = await testAPI('/api/admin/config/rendering-mode', 'GET', null, token);
    console.log(`   状态码: ${renderingResult.statusCode}`);
    if (renderingResult.statusCode === 200) {
      console.log('   ✅ 成功');
      console.log(`   响应: ${renderingResult.body.substring(0, 100)}...`);
    } else {
      console.log('   ❌ 失败');
      console.log(`   错误: ${renderingResult.body}`);
    }

    // 测试获取安全规则配置
    console.log('\n2️⃣ 测试 GET /api/admin/config/security-rules');
    const securityResult = await testAPI('/api/admin/config/security-rules', 'GET', null, token);
    console.log(`   状态码: ${securityResult.statusCode}`);
    if (securityResult.statusCode === 200) {
      console.log('   ✅ 成功');
      console.log(`   响应: ${securityResult.body.substring(0, 100)}...`);
    } else {
      console.log('   ❌ 失败');
      console.log(`   错误: ${securityResult.body}`);
    }

    // 测试获取安全违规记录
    console.log('\n3️⃣ 测试 GET /api/admin/security/violations');
    const violationsResult = await testAPI('/api/admin/security/violations', 'GET', null, token);
    console.log(`   状态码: ${violationsResult.statusCode}`);
    if (violationsResult.statusCode === 200) {
      console.log('   ✅ 成功');
      console.log(`   响应: ${violationsResult.body.substring(0, 100)}...`);
    } else {
      console.log('   ❌ 失败');
      console.log(`   错误: ${violationsResult.body}`);
    }

    console.log('\n✅ 所有测试完成');

  } catch (error) {
    console.log('❌ 操作失败:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 提示: 数据库或后端服务可能未启动');
    }
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

setupDatabaseAndTest();

const { User } = require('../models');
const logger = require('../utils/logger');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 检查VIP过期状态并更新用户角色
 * 每天凌晨2:00执行
 */
const checkVipExpiration = async () => {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('开始执行VIP过期检查任务');
    
    // 查找所有已过期的VIP用户
    const expiredVipUsers = await User.findAll({
      where: {
        role: 'vip',
        vip_expire_date: {
          [Op.lt]: new Date() // 过期时间早于当前时间
        }
      },
      transaction
    });
    
    if (expiredVipUsers.length === 0) {
      logger.info('没有发现过期VIP用户');
      await transaction.commit();
      return;
    }
    
    logger.info(`发现${expiredVipUsers.length}个过期VIP用户，开始处理`);
    
    // 更新过期用户角色
    for (const user of expiredVipUsers) {
      user.role = 'user'; // 转为普通用户
      // 注意：不清零积分
      await user.save({ transaction });
      logger.info(`用户${user.id}(${user.phone})的VIP已过期，已转为普通用户，过期时间: ${user.vip_expire_date}`);
    }
    
    await transaction.commit();
    logger.info(`成功处理${expiredVipUsers.length}个过期VIP用户`);
  } catch (error) {
    await transaction.rollback();
    logger.error('处理VIP过期任务失败:', error);
    throw error;
  }
};

module.exports = checkVipExpiration;

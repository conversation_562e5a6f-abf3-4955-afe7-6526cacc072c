const { sequelize } = require('../config/database');
const { AIServiceConfig } = require('../models');
const logger = require('../utils/logger');
const crypto = require('crypto');

// 加密API密钥
const encryptApiKey = (apiKey) => {
  // 在实际生产环境中，应该使用更安全的加密方式和环境变量存储密钥
  const algorithm = 'aes-256-ctr';
  const secretKey = process.env.API_KEY_SECRET || 'your-secret-key-should-be-in-env-file';
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipheriv(algorithm, secretKey, iv);
  const encrypted = Buffer.concat([cipher.update(apiKey), cipher.final()]);

  return {
    iv: iv.toString('hex'),
    content: encrypted.toString('hex')
  };
};

// 初始AI服务配置
const initialAIServices = [
  {
    service_name: 'DeepSeek',
    base_url: 'https://api.deepseek.com/v1',
    api_key: '***********************************',
    model_name: 'deepseek-chat',
    is_active: true,
    request_format: JSON.stringify({
      "messages": [{"role": "user", "content": "${prompt}"}],
      "temperature": 0.7,
      "max_tokens": 8192
    }),
    response_format: JSON.stringify({
      "path": "data.message.content"
    })
  },
  {
    service_name: 'xAI',
    base_url: 'https://api.xai.com',
    api_key: '************************************************************************************',
    model_name: 'grok-1',
    is_active: false,
    request_format: JSON.stringify({
      "messages": [{"role": "user", "content": "${prompt}"}],
      "temperature": 0.7,
      "max_tokens": 8192
    }),
    response_format: JSON.stringify({
      "choices": [{"message": {"content": ""}}]
    })
  }
];

// 执行种子数据初始化
async function runSeeds() {
  try {
    logger.info('开始执行种子数据初始化...');

    // 确保数据库连接
    await sequelize.authenticate();
    logger.info('数据库连接成功');

    // 检查是否已有AI服务配置
    const existingServices = await AIServiceConfig.count();

    if (existingServices > 0) {
      logger.info('AI服务配置已存在，跳过初始化');
      return;
    }

    // 开始事务
    const transaction = await sequelize.transaction();

    try {
      // 创建初始AI服务配置
      for (const service of initialAIServices) {
        // 加密API密钥
        const encryptedKey = encryptApiKey(service.api_key);

        await AIServiceConfig.create({
          service_name: service.service_name,
          base_url: service.base_url,
          api_key: JSON.stringify(encryptedKey),
          model_name: service.model_name,
          is_active: service.is_active,
          request_format: service.request_format,
          response_format: service.response_format
        }, { transaction });

        logger.info(`创建AI服务配置: ${service.service_name}`);
      }

      // 提交事务
      await transaction.commit();
      logger.info('种子数据初始化完成');
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      logger.error('种子数据初始化失败:', error);
      throw error;
    }
  } catch (error) {
    logger.error('执行种子数据初始化时出错:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行种子数据初始化
runSeeds();

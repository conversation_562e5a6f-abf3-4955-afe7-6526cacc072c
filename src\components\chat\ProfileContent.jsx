import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import axios from 'axios';
import { useNavigate, useSearchParams } from 'react-router-dom';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { message } from 'antd';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { 
  Award, Bell, Camera, CircleDollarSign, Copy, Crown, Edit, Eye, 
  History, LinkIcon, Lock, LogOut, Mail, RefreshCw, Save, Share2, 
  Smartphone, Trash2, Upload, UserCircle2, Wand2, X, Briefcase, Loader2,
  Ban
} from 'lucide-react';
import { cn, maskPhoneNumber } from '@/lib/utils';
import WorkCard from '../myworks/WorkCard';
import PasswordDialog from './PasswordDialog';
import { cachedRequest } from '@/services/apiCacheService';

// 加载中的spinner组件
const LoadingSpinner = ({ className }) => (
  <div className={cn("animate-spin rounded-full h-8 w-8 border-b-2 border-primary", className)}></div>
);

const ProfileContent = ({ contentType = 'profile' }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const initialPage = parseInt(searchParams.get('page') || '1', 10);
  const navigate = useNavigate();

  // 状态变量
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState(null);
  const [editing, setEditing] = useState(false);
  const [editNickname, setEditNickname] = useState('');
  const [editEmail, setEditEmail] = useState('');
  
  // 头像上传相关状态
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarUploading, setAvatarUploading] = useState(false);

  // 密码对话框状态
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);

  // 积分记录状态
  const [pointRecords, setPointRecords] = useState([]);
  const [pointsLoading, setPointsLoading] = useState(false);
  const [pointsCurrentPage, setPointsCurrentPage] = useState(initialPage);
  const [pointsTotalPages, setPointsTotalPages] = useState(0);

  // 封面记录状态
  const [covers, setCovers] = useState([]);
  const [coversLoading, setCoversLoading] = useState(false);
  const [coversCurrentPage, setCoversCurrentPage] = useState(initialPage);
  const [coversTotalPages, setCoversTotalPages] = useState(0);

  // 预览状态
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [currentCover, setCurrentCover] = useState(null);
  const [dataForEdit, setDataForEdit] = useState(null);
  const [previewHtml, setPreviewHtml] = useState('');
  const [currentPreviewRecordId, setCurrentPreviewRecordId] = useState(null);
  const [shareLinkDialogOpen, setShareLinkDialogOpen] = useState(false);
  const [shareUrl, setShareUrl] = useState('');

  // 删除封面状态
  const [deletingCoverId, setDeletingCoverId] = useState(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 订单状态映射表
  const orderStatusMap = {
    'pending': '待支付',
    'success': '支付成功',
    'failed': '支付失败',
    'refunded': '已退款',
    'closed': '已关闭'
  };

  // 产品类型映射表
  const productTypeMap = {
    'vip': 'VIP会员',
    'points': '积分充值'
  };

  // 支付方式映射表
  const paymentTypeMap = {
    'wechat': '微信支付',
    'alipay': '支付宝'
  };

  // 订单记录状态
  const [orders, setOrders] = useState([]);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [ordersCurrentPage, setOrdersCurrentPage] = useState(initialPage);
  const [ordersTotalPages, setOrdersTotalPages] = useState(0);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [isOrderDetailOpen, setIsOrderDetailOpen] = useState(false);
  const [isOrderDeleteConfirmOpen, setIsOrderDeleteConfirmOpen] = useState(false);
  const [isOrderCloseConfirmOpen, setIsOrderCloseConfirmOpen] = useState(false);
  const [deletingOrderId, setDeletingOrderId] = useState(null);
  const [closingOrderId, setClosingOrderId] = useState(null);
  const [orderDeleteLoading, setOrderDeleteLoading] = useState(false);
  const [orderCloseLoading, setOrderCloseLoading] = useState(false);

  // 操作类型映射
  const operationTypeMap = {
    'register': '注册奖励',
    'daily_reward': '每日奖励',
    'generate': '生成封面',
    'admin_adjust': '管理员调整',
    'vip_upgrade': 'VIP升级',
    'recharge': '购买积分',
    'daily_points_normal': '平台赠送',
    'daily_points_advanced': '会员赠送'
  };

  // 描述文本映射
  const descriptionMap = {
    '每日积分（普通）重置': '平台赠送积分',
    '每日积分（普通）手动更新': '平台赠送积分',
    '每日积分（高级）重置': '每日会员赠送',
    '每日积分（高级）手动更新': '每日会员赠送'
  };

  // 添加iframe引用
  const iframeRef = useRef(null);

  // 根据contentType加载对应数据
  useEffect(() => {
    if (contentType === 'profile') {
      fetchUserProfile();
    } else if (contentType === 'points') {
      fetchPointRecords();
    } else if (contentType === 'covers') {
      fetchCoverRecords();
    } else if (contentType === 'my-creations') {
      fetchCoverRecords();
    } else if (contentType === 'orders') {
      fetchUserOrders();
    }
  }, [contentType]);

  // 获取用户个人资料
  const fetchUserProfile = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        return;
      }
      // 使用缓存的用户资料请求
      const response = await cachedRequest('/api/user/profile', {
        headers: { Authorization: `Bearer ${token}` }
      }, { ttl: 5 * 60 * 1000 }); // 5分钟缓存
      
      if (response.success) {
        setUserProfile(response.data);
        setEditNickname(response.data.nickname);
        setEditEmail(response.data.email || '');
        
        // 获取每日积分信息
        fetchDailyPoints();
      }
    } catch (error) {
      console.error('获取个人资料失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取每日积分信息
  const fetchDailyPoints = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) { return; }
      
      const response = await axios.get('/api/user/daily-points', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        // 更新用户资料中的每日积分信息
        setUserProfile(prev => ({
          ...prev,
          daily_points: response.data.data.daily_points || 0,
          last_daily_points_update: response.data.data.last_update
        }));
        
        // 更新本地存储中的用户信息，添加daily_points
        const userStr = localStorage.getItem('user');
        if (userStr) {
          try {
            const currentUser = JSON.parse(userStr);
            // 只有当每日积分发生变化时才更新
            if (currentUser.daily_points !== response.data.data.daily_points) {
              const updatedUser = {
                ...currentUser,
                daily_points: response.data.data.daily_points || 0
              };
              localStorage.setItem('user', JSON.stringify(updatedUser));
              
              // 触发每日积分更新事件，通知其他组件（如ChatSidebar）
              window.dispatchEvent(new CustomEvent('dailyPointsUpdated'));
            }
          } catch (error) {
            console.error('解析用户数据失败:', error);
          }
        }
      }
    } catch (error) {
      console.error('获取每日积分信息失败:', error);
    }
  };

  // 获取积分记录
  const fetchPointRecords = async (page = 1, limit = 10, forceRefresh = false) => {
    try {
      setPointsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { return; }
      
      // 使用缓存的积分记录请求
      const timestamp = new Date().getTime();
      const url = `/api/user/point-records?page=${page}&limit=${limit}&_t=${timestamp}`;
      const response = await cachedRequest(url, {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Cache-Control': 'no-cache' 
        }
      }, { 
        ttl: 2 * 60 * 1000, // 2分钟缓存
        forceRefresh: forceRefresh // 手动刷新时强制请求
      });

      if (response.success) {
        setPointRecords(response.data);
        const total = response.pagination.total;
        const calculatedTotalPages = Math.ceil(total / limit);
        setPointsTotalPages(response.pagination.totalPages || calculatedTotalPages);
        setPointsCurrentPage(page);
      } else {
        setPointRecords([]);
        setPointsTotalPages(0);
        setPointsCurrentPage(1);
      }
    } catch (error) {
      console.error('获取积分记录失败:', error);
      setPointRecords([]);
      setPointsTotalPages(0);
      setPointsCurrentPage(1);
    } finally { 
      setPointsLoading(false);
      // 不需要每次都更新用户资料，仅首次加载或强制刷新时更新
      if (forceRefresh || !userProfile) {
        fetchUserProfile();
      }
    }
  };

  // 获取封面记录
  const fetchCoverRecords = async (page = 1, limit = 12, forceRefresh = false) => {
    try {
      setCoversLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { return; }
      
      const timestamp = new Date().getTime();
      const url = `/api/user/covers?page=${page}&limit=${limit}&_t=${timestamp}`;
      // 使用缓存的封面记录请求
      const response = await cachedRequest(url, {
        headers: { 'Authorization': `Bearer ${token}`, 'Cache-Control': 'no-cache' }
      }, { 
        ttl: 2 * 60 * 1000, // 2分钟缓存
        forceRefresh: forceRefresh // 手动刷新时强制请求
      });
      
      if (response.success) {
        setCovers(response.data);
        const total = response.pagination.total;
        const calculatedTotalPages = Math.ceil(total / limit);
        setCoversTotalPages(response.pagination.totalPages || calculatedTotalPages);
        setCoversCurrentPage(page);
      } else {
        setCovers([]);
        setCoversTotalPages(0);
        setCoversCurrentPage(1);
      }
    } catch (error) {
      console.error('获取封面记录失败:', error);
      setCovers([]);
      setCoversTotalPages(0);
      setCoversCurrentPage(1);
    } finally { 
      setCoversLoading(false); 
      // 不需要每次都更新用户资料，仅首次加载或强制刷新时更新
      if (forceRefresh || !userProfile) {
        fetchUserProfile();
      }
    }
  };

  // 获取用户订单列表
  const fetchUserOrders = async (page = 1, limit = 10, forceRefresh = false) => {
    try {
      setOrdersLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { return; }
      
      const timestamp = new Date().getTime();
      const url = `/api/payment/orders?page=${page}&limit=${limit}&_t=${timestamp}`;
      // 使用缓存的订单记录请求
      const response = await cachedRequest(url, {
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Cache-Control': 'no-cache' 
        }
      }, { 
        ttl: 2 * 60 * 1000, // 2分钟缓存
        forceRefresh: forceRefresh // 手动刷新时强制请求
      });
      
      if (response.success) {
        setOrders(response.data.orders);
        const total = response.data.pagination.total;
        const calculatedTotalPages = Math.ceil(total / limit);
        setOrdersTotalPages(response.data.pagination.total_pages || calculatedTotalPages);
        setOrdersCurrentPage(page);
      } else {
        setOrders([]);
        setOrdersTotalPages(0);
        setOrdersCurrentPage(1);
      }
    } catch (error) {
      console.error('获取订单记录失败:', error);
      setOrders([]);
      setOrdersTotalPages(0);
      setOrdersCurrentPage(1);
    } finally { 
      setOrdersLoading(false); 
    }
  };

  // 更新用户资料
  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const values = { nickname: editNickname, email: editEmail };

      if (!values.nickname) {
        message.error('昵称不能为空');
        setLoading(false);
        return;
      }

      const response = await axios.put('/api/user/profile', values, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setUserProfile(prev => ({ ...prev, ...response.data.data }));
        setEditing(false);
        message.success('个人资料更新成功');
        const currentUserData = JSON.parse(localStorage.getItem('user') || '{}');
        localStorage.setItem('user', JSON.stringify({ 
          ...currentUserData, 
          nickname: response.data.data.nickname, 
          email: response.data.data.email 
        }));
      } else {
        message.error(response.data.message || '更新个人资料失败');
      }
    } catch (error) {
      console.error('更新个人资料失败:', error);
      message.error('更新个人资料失败，请检查网络或联系管理员');
    } finally {
      setLoading(false);
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditing(false);
    setEditNickname(userProfile?.nickname || '');
    setEditEmail(userProfile?.email || '');
  };
  
  // 处理头像选择
  const handleAvatarSelect = (e) => {
    if (e.target.files && e.target.files[0]) {
      setAvatarFile(e.target.files[0]);
      handleAvatarUpload(e.target.files[0]);
    }
  };
  
  // 处理头像上传
  const handleAvatarUpload = async (file) => {
    if (!file) return;
    
    setAvatarUploading(true);
    const formData = new FormData();
    formData.append('avatar', file);
    
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post('/api/user/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${token}`
        }
      });
      
      if (response.data.success) {
        // 更新用户头像
        setUserProfile({
          ...userProfile,
          avatar: response.data.data.avatar
        });
        
        // 更新本地存储的用户信息
        const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
        userInfo.avatar = response.data.data.avatar;
        localStorage.setItem('user', JSON.stringify(userInfo));
        
        message.success('头像上传成功');
      }
    } catch (error) {
      console.error('上传头像失败:', error);
      message.error('上传头像失败，请重试');
    } finally {
      setAvatarUploading(false);
    }
  };

  // 删除封面
  const handleDeleteCover = async (coverId) => {
    try {
      setDeleteLoading(true);
      const token = localStorage.getItem('token');
      if (!token) return;
      
      const response = await axios.put(`/api/user/covers/${coverId}/hide`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        // 成功隐藏记录后更新列表
        fetchCoverRecords(coversCurrentPage);
        setIsDeleteConfirmOpen(false);
        setDeletingCoverId(null);
      } else {
        message.error(response.data.message || '删除封面失败');
      }
    } catch (error) {
      console.error('隐藏封面记录失败:', error);
      message.error('删除封面失败，请稍后重试');
    } finally {
      setDeleteLoading(false);
    }
  };

  // 打开删除确认框
  const openDeleteConfirm = (coverId, e) => {
    if (e) e.preventDefault();
    setDeletingCoverId(coverId);
    setIsDeleteConfirmOpen(true);
  };

  // 处理分页变化
  const handlePageChange = (newPage, type, e) => {
    // 阻止默认链接行为，防止页面跳转
    if (e) e.preventDefault();
    
    // 保存当前的URL参数
    const currentUrl = new URL(window.location.href);
    const searchParams = new URLSearchParams(currentUrl.search);
    
    // 更新页码参数
    searchParams.set('page', newPage.toString());
    
    // 确保保留view参数
    if (!searchParams.has('view')) {
      searchParams.set('view', type);
    }
    
    // 使用history.replaceState更新URL而不导致页面跳转
    window.history.replaceState(
      {}, 
      '', 
      `${window.location.pathname}?${searchParams.toString()}`
    );
    
    // 更新状态并获取数据，不使用强制刷新，利用缓存提高性能
    if (type === 'points') {
      setPointsCurrentPage(newPage);
      fetchPointRecords(newPage, 10, false);
    } else if (type === 'covers') {
      setCoversCurrentPage(newPage);
      fetchCoverRecords(newPage, 12, false);
    } else if (type === 'my-creations') {
      setCoversCurrentPage(newPage);
      fetchCoverRecords(newPage, 12, false);
    } else if (type === 'orders') {
      setOrdersCurrentPage(newPage);
      fetchUserOrders(newPage, 10, false);
    }
  };

  // 生成分页号
  const generatePageNumbers = (currentPage, totalPages, pageNeighbours = 1) => {
    if (totalPages <= 5) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const startPage = Math.max(2, currentPage - pageNeighbours);
    const endPage = Math.min(totalPages - 1, currentPage + pageNeighbours);
    
    let pages = [1];
    
    if (startPage > 2) {
      pages.push('ellipsis');
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    if (endPage < totalPages - 1) {
      pages.push('ellipsis');
    }
    
    pages.push(totalPages);
    
    return pages;
  };

  // 查看封面HTML
  const handleLoadCover = async (record, e) => {
    if (e) e.preventDefault();
    
    const coverCode = record?.cover_code;
    const coverId = record?.id;

    if (!coverCode && !coverId) {
      message.error('封面记录无效，无法查看');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      // 打开预览弹出层并显示加载动画
      setIsPreviewOpen(true);
      setDataForEdit(null);
      setCurrentCover(record);
      setPreviewHtml(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            html, body { margin: 0; padding: 0; height: 100%; }
            body { display: flex; justify-content: center; align-items: center; height: 100vh; }
            .loading { display: flex; justify-content: center; align-items: center; height: 100%; }
            .spinner { animation: spin 1s linear infinite; height: 2rem; width: 2rem; border-radius: 50%; border: 2px solid #f3f3f3; border-top-color: #3498db; }
            @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
          </style>
        </head>
        <body>
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </body>
        </html>
      `);
      
      if (coverId) setCurrentPreviewRecordId(coverId);

      let response;
      const cacheBuster = `?_=${new Date().getTime()}`;
      if (coverCode) {
        response = await axios.get(`/api/cover/code/${coverCode}/edit${cacheBuster}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } else if (coverId) {
        response = await axios.get(`/api/cover/${coverId}/edit${cacheBuster}`, { 
          headers: { Authorization: `Bearer ${token}` }
        });
      }

      if (response?.data?.success) {
        const coverData = response.data.data;
        setCurrentCover(coverData);
        setDataForEdit(coverData);
        
        // 优先使用编辑后的HTML内容，如果没有则使用原始HTML内容
        let htmlContent = coverData.edited_html_content || coverData.html_content;
        
        if (htmlContent) {
          // 应用与ChatSidebar.jsx相同的链接替换逻辑
          if (typeof htmlContent === 'string') {
            // 更换为更强大的正则表达式，以处理单引号、双引号和无协议的URL
            const placeholderRegex = /src=(["'])?(https?:)?\/\/via\.placeholder\.com\/[^'" >]*(?:\1)?/g;
            htmlContent = htmlContent.replace(placeholderRegex, 'src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="');
          }
          
          // 将HTML内容包装在完整的HTML文档结构中，添加必要的样式
          const wrappedHtml = `
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>
                /* 重置样式，确保不影响外部 */
                html, body { margin: 0; padding: 0; height: 100%; font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
                body { display: flex; justify-content: center; align-items: center; height: 100%; }
                
                /* 确保内容元素有合理的宽度和高度 */
                div, section, article { min-width: 100px; }
                span, p, h1, h2, h3, h4, h5, h6 { min-width: 50px; }
                img { max-width: 100%; height: auto; }
                
                /* 确保文本元素正确显示 */
                .text-element { display: inline-block; min-width: 50px; }
                
                /* 确保容器元素正确显示 */
                .container-element { display: block; min-width: 100px; }
              </style>
            </head>
            <body>
              ${htmlContent}
            </body>
            </html>
          `;
          
          // 设置处理后的HTML内容
          setPreviewHtml(wrappedHtml);
        } else {
          // 如果没有HTML内容，显示提示信息
          const imageUrl = coverData?.image_url || '';
          setPreviewHtml(`
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>
                html, body { margin: 0; padding: 0; height: 100%; }
                body { display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column; }
                .message { text-align: center; color: #666; font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
                img { max-width: 100%; max-height: 300px; margin-bottom: 20px; }
              </style>
            </head>
            <body>
              ${imageUrl ? `<img src="${imageUrl}" alt="封面图片" />` : ''}
              <div class="message">此封面没有HTML内容</div>
            </body>
            </html>
          `);
        }
      } else {
        // API请求失败
        setPreviewHtml(`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              html, body { margin: 0; padding: 0; height: 100%; }
              body { display: flex; justify-content: center; align-items: center; height: 100vh; }
              .error { color: #e53e3e; text-align: center; font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
            </style>
          </head>
          <body>
            <div class="error">加载封面内容失败</div>
          </body>
          </html>
        `);
      }
    } catch (error) {
      console.error('加载封面内容失败:', error);
      message.error('加载封面内容失败，请稍后再试');
      
      // 显示错误信息
      setPreviewHtml(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            html, body { margin: 0; padding: 0; height: 100%; }
            body { display: flex; justify-content: center; align-items: center; height: 100vh; }
            .error { color: #e53e3e; text-align: center; font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
          </style>
        </head>
        <body>
          <div class="error">加载封面内容失败: ${error.message}</div>
        </body>
        </html>
      `);
    }
  };

  // 分享封面
  const handleShareCover = async () => {
    try {
      if (!currentCover || !currentCover.id) {
        message.error('无法获取封面信息，无法分享');
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      const response = await axios.get(`/api/cover/${currentCover.id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success && response.data.data) {
        const coverCode = response.data.data.cover_code;

        if (!coverCode) {
          message.error('此封面不支持分享');
          return;
        }

        const shareUrl = `${window.location.origin}/share/${coverCode}`;
        await navigator.clipboard.writeText(shareUrl);
        message.success('分享链接已复制到剪贴板');

        setShareLinkDialogOpen(true);
        setShareUrl(shareUrl);
      } else {
        message.error(response.data.message || '获取封面信息失败');
      }
    } catch (error) {
      console.error('生成分享链接失败:', error);
      message.error('生成分享链接失败，请重试');
    }
  };

  // 编辑封面（回到编辑页面）
  const handleEditCover = () => {
    setIsPreviewOpen(false);

    if (dataForEdit) {
      try {
        localStorage.setItem('pendingCoverData', JSON.stringify(dataForEdit));
        
        // 增加一个明确的URL参数作为信号，触发二次编辑模式
        navigate('/?mode=edit');
      } catch (error) {
        console.error('存储封面数据失败:', error);
        message.error('加载编辑器失败，请稍后再试');
      }
    } else {
      message.error('没有有效的封面数据可供编辑，请重试');
    }
  };

  // 刷新数据
  const handleRefreshData = () => {
    if (contentType === 'profile') {
      fetchUserProfile(); // 个人资料没有分页，直接获取
      fetchDailyPoints(); // 刷新每日积分信息
    } else if (contentType === 'points') {
      fetchPointRecords(pointsCurrentPage, 10, true); // 传入 forceRefresh=true 强制刷新
    } else if (contentType === 'covers') {
      fetchCoverRecords(coversCurrentPage, 12, true); // 传入 forceRefresh=true 强制刷新
    } else if (contentType === 'my-creations') {
      fetchCoverRecords(coversCurrentPage, 12, true); // 传入 forceRefresh=true 强制刷新
    } else if (contentType === 'orders') {
      fetchUserOrders(ordersCurrentPage, 10, true); // 传入 forceRefresh=true 强制刷新
    }
  };
  
  // 处理退出登录
  const handleLogout = () => {
    try {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // 触发自定义事件，通知组件用户已退出登录
      window.dispatchEvent(new CustomEvent('userLogout'));
      
      message.success('退出登录成功');
      navigate('/login');
    } catch (error) {
      console.error('退出登录失败:', error);
      message.error('退出登录失败，请重试');
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    return dayjs(dateString).format('YYYY-MM-DD HH:mm');
  };

  // 查看订单详情
  const handleViewOrderDetail = async (orderNo) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) { 
        message.error('未登录或会话已过期，请先登录');
        return; 
      }
      
      // 显示加载中状态
      message.loading({ content: '正在加载订单详情...', key: 'orderDetail', duration: 0 });
      
      const response = await axios.get(`/api/payment/order/${orderNo}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      // 隐藏加载中状态
      message.destroy('orderDetail');
      
      if (response.data.success) {
        // 确保数据结构一致
        const orderData = response.data.data;
        
        // 安全地处理数据，确保即使缺少某些字段也不会导致错误
        setCurrentOrder({
          order: orderData.order || orderData || {},
          packageInfo: orderData.packageInfo || null,
          paymentParams: orderData.paymentParams || null
        });
        
        setIsOrderDetailOpen(true);
      } else {
        message.error(response.data.message || '获取订单详情失败');
      }
    } catch (error) {
      // 隐藏加载中状态
      message.destroy('orderDetail');
      
      console.error('获取订单详情失败:', error);
      
      // 根据错误类型提供更具体的错误信息
      if (error.response) {
        // 服务器响应了，但状态码不在2xx范围内
        if (error.response.status === 404) {
          message.error('订单不存在或已被删除');
        } else if (error.response.status === 403) {
          message.error('您没有权限查看此订单');
        } else {
          message.error(`获取订单详情失败: ${error.response.data?.message || '服务器错误'}`);
        }
      } else if (error.request) {
        // 请求已发出，但没有收到响应
        message.error('服务器无响应，请检查网络连接');
      } else {
        // 请求设置时出错
        message.error('请求错误，请稍后再试');
      }
    }
  };

  // 删除订单（实际是更新显示状态为"隐藏"）
  const handleDeleteOrder = async (orderId) => {
    try {
      setOrderDeleteLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { 
        message.error('未登录或会话已过期，请先登录');
        return; 
      }
      
      const response = await axios.put(`/api/payment/orders/${orderId}/status`, 
        { status: '隐藏' },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      if (response.data.success) {
        message.success('订单已成功删除');
        fetchUserOrders(ordersCurrentPage); // 重新加载订单列表
      } else {
        message.error(response.data.message || '删除订单失败');
      }
    } catch (error) {
      console.error('删除订单失败:', error);
      message.error('删除订单失败，请稍后再试');
    } finally {
      setOrderDeleteLoading(false);
      setIsOrderDeleteConfirmOpen(false);
    }
  };

  // 关闭订单
  const handleCloseOrder = async (orderId) => {
    try {
      setOrderCloseLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { 
        message.error('未登录或会话已过期，请先登录');
        return; 
      }
      
      const response = await axios.put(`/api/payment/orders/${orderId}/close`, 
        {},
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      if (response.data.success) {
        message.success('订单已成功关闭');
        fetchUserOrders(ordersCurrentPage); // 重新加载订单列表
        
        // 如果订单详情对话框打开，也关闭它
        if (isOrderDetailOpen && currentOrder?.order?.id === orderId) {
          setIsOrderDetailOpen(false);
        }
      } else {
        message.error(response.data.message || '关闭订单失败');
      }
    } catch (error) {
      console.error('关闭订单失败:', error);
      
      // 根据错误类型提供更具体的错误信息
      if (error.response) {
        message.error(error.response.data?.message || '关闭订单失败，请稍后再试');
      } else {
        message.error('关闭订单失败，请稍后再试');
      }
    } finally {
      setOrderCloseLoading(false);
      setIsOrderCloseConfirmOpen(false);
    }
  };

  // 打开订单删除确认对话框
  const openOrderDeleteConfirm = (orderId, e) => {
    if (e) e.stopPropagation();
    setDeletingOrderId(orderId);
    setIsOrderDeleteConfirmOpen(true);
  };

  // 打开订单关闭确认对话框
  const openOrderCloseConfirm = (orderId, e) => {
    if (e) e.stopPropagation();
    setClosingOrderId(orderId);
    setIsOrderCloseConfirmOpen(true);
  };

  // 渲染个人资料
  const renderProfile = () => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center py-16">
          <LoadingSpinner />
          <p className="mt-4 text-gray-500">加载个人资料中...</p>
        </div>
      );
    }

    if (!userProfile) {
      return (
        <div className="text-center py-16">
          <p className="text-gray-500">无法加载个人资料，请检查登录状态</p>
          <Button 
            onClick={() => navigate('/?view=auth')}
            className="mt-4"
          >
            去登录
          </Button>
        </div>
      );
    }

    return (
      <Card className="bg-card text-card-foreground shadow-lg mx-auto w-11/12">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-foreground">个人资料</h1>
              <p className="text-muted-foreground mt-1">管理您的账户信息</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleRefreshData}
                className="h-8 w-8 hover:bg-accent/80"
              >
                <RefreshCw className="h-4 w-4" />
                <span className="sr-only">刷新</span>
              </Button>
              {!editing && userProfile && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditing(true)}
                    className="text-muted-foreground border-border hover:bg-accent/50 hover:text-accent-foreground"
                  >
                    <Edit className="mr-2 h-4 w-4" /> 编辑资料
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPasswordDialogOpen(true)}
                    className="text-muted-foreground border-border hover:bg-accent/50 hover:text-accent-foreground ml-2"
                  >
                    <Lock className="mr-2 h-4 w-4" /> 修改密码
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="p-8 md:p-10" style={{ minHeight: '400px' }}>
          <div className="space-y-6">
            {/* 用户头像部分 */}
            <div className="flex justify-center mb-8">
              <div className="relative">
                <Avatar className={`h-24 w-24 border-4 ${userProfile.is_vip ? 'border-yellow-500/50' : 'border-white'} shadow-md`}>
                  {userProfile.avatar ? (
                    <AvatarImage src={userProfile.avatar} alt={userProfile.nickname || '用户'} />
                  ) : (
                    <AvatarFallback className="bg-gradient-to-br from-violet-600 to-indigo-600 text-white text-2xl">
                      {(userProfile.nickname || '用户').slice(0, 2).toUpperCase()}
                    </AvatarFallback>
                  )}
                </Avatar>
                {userProfile.is_vip && (
                  <div className="absolute -top-2 -right-2 bg-yellow-500 rounded-full p-1 shadow-md">
                    <Crown size={18} className="text-white" />
                  </div>
                )}
                <label 
                  htmlFor="avatar-upload" 
                  className="absolute bottom-0 right-0 bg-primary text-white w-8 h-8 rounded-full flex items-center justify-center cursor-pointer shadow-md hover:bg-primary/90 transition-colors"
                  title="上传头像"
                >
                  {avatarUploading ? (
                    <LoadingSpinner className="h-4 w-4" />
                  ) : (
                    <Upload size={16} />
                  )}
                  <input 
                    id="avatar-upload" 
                    type="file" 
                    className="hidden" 
                    accept="image/jpeg,image/png,image/gif,image/webp"
                    onChange={handleAvatarSelect}
                    disabled={avatarUploading}
                  />
                </label>
              </div>
            </div>
            
            {!editing ? (
              <div className="space-y-6">
                {[
                  { label: "昵称", value: userProfile.nickname, icon: UserCircle2 },
                  { label: "手机号", value: userProfile.phone ? maskPhoneNumber(userProfile.phone) : '未设置', icon: Smartphone },
                  { label: "邮箱", value: userProfile.email || '未设置', icon: Mail },
                  { label: "可用积分", value: <span className="font-semibold text-primary">{userProfile.points}</span>, icon: CircleDollarSign },
                  { label: "每日赠送", value: <span className="font-semibold text-green-500">{userProfile.daily_points || 0}</span>, icon: Award },
                  { label: "会员状态", value: userProfile.is_vip ? (
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-yellow-500">高级会员</span>
                      <span className="text-xs px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800">
                        到期时间: {dayjs(userProfile.vip_expire_date).format('YYYY-MM-DD')}
                      </span>
                    </div>
                  ) : '普通会员', icon: Award },
                ].map((item, index) => (
                  <div key={index} className="grid grid-cols-3 md:grid-cols-4 gap-4 items-center">
                    <div className="flex items-center space-x-3 col-span-1 md:col-span-1">
                      {item.icon && React.createElement(item.icon, {
                        className: cn(
                          "h-5 w-5 text-muted-foreground flex-shrink-0",
                          item.label === "会员状态" && userProfile.is_vip && "text-yellow-500"
                        )
                      })}
                      <Label className="text-base text-muted-foreground text-left">{item.label}</Label>
                    </div>
                    <div className="text-base text-foreground col-span-2 md:col-span-3">{item.value}</div>
                  </div>
                ))}
                
                <div className="flex justify-start pt-4">
                  <Button
                    onClick={handleLogout}
                    variant="ghost"
                    size="sm"
                    className="text-muted-foreground hover:bg-destructive/10 hover:text-destructive"
                  >
                    <LogOut className="mr-2 h-4 w-4" /> 退出登录
                  </Button>
                </div>
              </div>
            ) : (
              <form onSubmit={handleUpdateProfile} className="space-y-6">
                <div className="grid gap-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="nickname" className="text-right text-muted-foreground">
                      昵称
                    </Label>
                    <Input
                      id="nickname"
                      value={editNickname}
                      onChange={(e) => setEditNickname(e.target.value)}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="phone" className="text-right text-muted-foreground">
                      手机号
                    </Label>
                    <Input
                      id="phone"
                      value={userProfile.phone ? maskPhoneNumber(userProfile.phone) : '未设置'}
                      disabled
                      className="col-span-3 bg-muted text-muted-foreground"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="email" className="text-right text-muted-foreground">
                      邮箱
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={editEmail}
                      onChange={(e) => setEditEmail(e.target.value)}
                      className="col-span-3"
                      placeholder="请输入您的邮箱"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="dailyPoints" className="text-right text-muted-foreground">
                      每日赠送
                    </Label>
                    <div className="col-span-3 flex items-center pt-2">
                      <span className="font-semibold text-green-500">{userProfile.daily_points || 0}</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="vipStatus" className="text-right text-muted-foreground">
                      会员状态
                    </Label>
                    <div className="col-span-3 flex items-center pt-2">
                      {userProfile.is_vip ? (
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-yellow-500">高级会员</span>
                          <span className="text-xs px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800">
                            到期时间: {dayjs(userProfile.vip_expire_date).format('YYYY-MM-DD')}
                          </span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">普通会员</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={handleCancelEdit} 
                    disabled={loading}
                    type="button"
                  >
                    <X className="mr-2 h-4 w-4" /> 取消
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={loading}
                    className="bg-gradient-primary text-primary-foreground hover:opacity-90"
                  >
                    {loading ? <LoadingSpinner className="mr-2 h-4 w-4" /> : <Save className="mr-2 h-4 w-4" />}
                    保存
                  </Button>
                </div>
              </form>
            )}
          </div>
        </div>
      </Card>
    );
  };

  // 渲染积分记录
  const renderPoints = () => {
    if (loading || !userProfile) {
      return (
        <div className="text-center py-16">
          <p className="text-gray-500">无法加载积分记录，请检查登录状态</p>
          <Button 
            onClick={() => navigate('/?view=auth')}
            className="mt-4"
          >
            去登录
          </Button>
        </div>
      );
    }

    return (
      <Card className="bg-card text-card-foreground shadow-lg mx-auto w-11/12">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-foreground">积分记录</h1>
              <p className="text-muted-foreground mt-1">查看您的积分变动历史</p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => fetchPointRecords(pointsCurrentPage)}
              className="h-8 w-8 hover:bg-accent/80"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="p-6">
          {pointsLoading ? (
            <div className="flex justify-center items-center py-8">
              <LoadingSpinner />
            </div>
          ) : pointRecords.length > 0 ? (
            <>
              <div className="border rounded-md overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16 text-center">序号</TableHead>
                      <TableHead className="text-center">日期</TableHead>
                      <TableHead className="text-center">类型</TableHead>
                      <TableHead className="text-center">积分变化</TableHead>
                      <TableHead className="text-center">描述</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pointRecords.map((record, index) => (
                      <TableRow key={record.id}>
                        <TableCell className="text-center font-medium">{record.id || index + 1}</TableCell>
                        <TableCell className="text-center">{formatDate(record.operation_time || record.created_at)}</TableCell>
                        <TableCell className="text-center">{record.operation_type ? operationTypeMap[record.operation_type] || record.operation_type : '生成封面'}</TableCell>
                        <TableCell className={cn(
                          "text-center font-medium",
                          record.points_change > 0 ? "text-green-500" : "text-red-500"
                        )}>
                          {record.points_change > 0 ? `+${record.points_change}` : record.points_change}
                        </TableCell>
                        <TableCell className="text-center">{record.description ? descriptionMap[record.description] || record.description : '生成封面消费积分'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              {pointsTotalPages > 1 && (
                <div className="mt-8 flex justify-center">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious 
                          onClick={(e) => handlePageChange(Math.max(1, pointsCurrentPage - 1), 'points', e)}
                          className={cn(pointsCurrentPage <= 1 && "pointer-events-none opacity-50")}
                        />
                      </PaginationItem>
                      
                      {generatePageNumbers(pointsCurrentPage, pointsTotalPages).map((page, i) => (
                        page === 'ellipsis' ? (
                          <PaginationItem key={`ellipsis-${i}`}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        ) : (
                          <PaginationItem key={page}>
                            <PaginationLink 
                              isActive={page === pointsCurrentPage}
                              onClick={(e) => handlePageChange(page, 'points', e)}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      ))}
                      
                      <PaginationItem>
                        <PaginationNext 
                          onClick={(e) => handlePageChange(Math.min(pointsTotalPages, pointsCurrentPage + 1), 'points', e)}
                          className={cn(pointsCurrentPage >= pointsTotalPages && "pointer-events-none opacity-50")}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>暂无积分记录</p>
            </div>
          )}
        </div>
      </Card>
    );
  };

  // 渲染封面记录
  const renderCovers = () => {
    if (loading || !userProfile) {
      return (
        <div className="text-center py-16">
          <p className="text-gray-500">无法加载封面记录，请检查登录状态</p>
          <Button 
            onClick={() => navigate('/?view=auth')}
            className="mt-4"
          >
            去登录
          </Button>
        </div>
      );
    }

    return (
      <Card className="bg-card text-card-foreground shadow-lg mx-auto w-11/12">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-foreground">封面记录</h1>
              <p className="text-muted-foreground mt-1">查看您生成的封面历史</p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => fetchCoverRecords(coversCurrentPage)}
              className="h-8 w-8 hover:bg-accent/80"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="p-6">
          {coversLoading ? (
            <div className="flex justify-center items-center py-8">
              <LoadingSpinner />
            </div>
          ) : covers.length > 0 ? (
            <>
              <div className="border rounded-md overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-16 text-center">序号</TableHead>
                      <TableHead className="text-center">封面编码</TableHead>
                      <TableHead className="text-center">类型</TableHead>
                      <TableHead className="text-center">风格</TableHead>
                      <TableHead className="text-center">生成时间</TableHead>
                      <TableHead className="text-center">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {covers.map((record, index) => (
                      <TableRow key={record.id}>
                        <TableCell className="text-center font-medium">{record.id || index + 1}</TableCell>
                        <TableCell className="text-center">{record.cover_code}</TableCell>
                        <TableCell className="text-center">{record.cover_type_name || '小红书封面'}</TableCell>
                        <TableCell className="text-center">{record.style_display_name || record.cover_style_name || record.cover_style || '默认风格'}</TableCell>
                        <TableCell className="text-center">{formatDate(record.created_at)}</TableCell>
                        <TableCell className="text-center">
                          <div className="flex items-center justify-center space-x-2">
                            <Button 
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={(e) => handleLoadCover(record, e)}
                            >
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">查看</span>
                            </Button>
                            <Button 
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-100"
                              onClick={(e) => openDeleteConfirm(record.id, e)}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">删除</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              {coversTotalPages > 1 && (
                <div className="mt-8 flex justify-center">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious 
                          onClick={(e) => handlePageChange(Math.max(1, coversCurrentPage - 1), 'covers', e)}
                          className={cn(coversCurrentPage <= 1 && "pointer-events-none opacity-50")}
                        />
                      </PaginationItem>
                      
                      {generatePageNumbers(coversCurrentPage, coversTotalPages).map((page, i) => (
                        page === 'ellipsis' ? (
                          <PaginationItem key={`ellipsis-${i}`}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        ) : (
                          <PaginationItem key={page}>
                            <PaginationLink 
                              isActive={page === coversCurrentPage}
                              onClick={(e) => handlePageChange(page, 'covers', e)}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      ))}
                      
                      <PaginationItem>
                        <PaginationNext 
                          onClick={(e) => handlePageChange(Math.min(coversTotalPages, coversCurrentPage + 1), 'covers', e)}
                          className={cn(coversCurrentPage >= coversTotalPages && "pointer-events-none opacity-50")}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>暂无封面记录</p>
            </div>
          )}
        </div>
      </Card>
    );
  };

  // 渲染"我的作品集"内容
  const renderMyCreations = () => {
    if (loading || !userProfile) {
      return (
        <div className="text-center py-16">
          <p className="text-gray-500">无法加载作品集，请检查登录状态</p>
          <Button 
            onClick={() => navigate('/?view=auth')}
            className="mt-4"
          >
            去登录
          </Button>
        </div>
      );
    }

    return (
      <Card className="bg-card text-card-foreground shadow-lg h-full flex flex-col mx-auto w-11/12">
        <CardHeader className="pb-5 border-b">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl font-semibold text-card-foreground">我的作品集</CardTitle>
              <CardDescription>在这里浏览和欣赏您所有精彩的AI生成封面</CardDescription>
            </div>
            <Button 
              variant="ghost" 
              size="icon"
              onClick={handleRefreshData}
              className="h-8 w-8 hover:bg-accent/80"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="p-4 md:p-6 overflow-y-auto flex-grow">
          {coversLoading && (
            <div className="flex justify-center items-center py-20">
              <LoadingSpinner />
              <span className="ml-3 text-muted-foreground">加载作品集...</span>
            </div>
          )}

          {!coversLoading && covers.length === 0 && (
            <div className="text-center py-20 bg-card rounded-lg shadow-sm">
              <Briefcase className="h-12 w-12 mx-auto text-muted-foreground/50" />
              <h3 className="text-xl font-medium mt-4 text-foreground">您的作品集还是空的</h3>
              <p className="text-muted-foreground mt-2 mx-auto max-w-md">
                立即去创作一些精美的AI封面，它们会在这里展示。
              </p>
              <Button 
                onClick={() => navigate('/')} 
                className="mt-6 bg-primary text-primary-foreground hover:bg-primary/90"
              >
                <Wand2 className="h-4 w-4 mr-2" />
                去创建封面
              </Button>
            </div>
          )}

          {!coversLoading && covers.length > 0 && (
            <>
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
                {covers.slice(0, 12).map((record) => (
                  <WorkCard
                    key={record.id}
                    record={record}
                    onClick={(clickedRecord) => {
                      handleLoadCover(clickedRecord);
                    }}
                  />
                ))}
              </div>
              
              {coversTotalPages > 1 && (
                <div className="mt-8 flex justify-center">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious 
                          onClick={(e) => handlePageChange(Math.max(1, coversCurrentPage - 1), 'my-creations', e)}
                          className={cn(coversCurrentPage <= 1 && "pointer-events-none opacity-50")}
                        />
                      </PaginationItem>
                      
                      {generatePageNumbers(coversCurrentPage, coversTotalPages).map((page, i) => (
                        page === 'ellipsis' ? (
                          <PaginationItem key={`ellipsis-${i}`}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        ) : (
                          <PaginationItem key={page}>
                            <PaginationLink 
                              isActive={page === coversCurrentPage}
                              onClick={(e) => handlePageChange(page, 'my-creations', e)}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      ))}
                      
                      <PaginationItem>
                        <PaginationNext 
                          onClick={(e) => handlePageChange(Math.min(coversTotalPages, coversCurrentPage + 1), 'my-creations', e)}
                          className={cn(coversCurrentPage >= coversTotalPages && "pointer-events-none opacity-50")}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    );
  };

  // 渲染订单记录页面
  const renderOrders = () => {
    return (
      <Card className="bg-card text-card-foreground shadow-lg mx-auto w-11/12">
        <CardHeader className="pb-5">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl font-semibold text-card-foreground">订单记录</CardTitle>
              <CardDescription>查看您的订单历史和支付记录</CardDescription>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => fetchUserOrders(ordersCurrentPage)}
              className="h-8 w-8 hover:bg-accent/80"
            >
              <RefreshCw className="h-4 w-4" />
              <span className="sr-only">刷新</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {ordersLoading ? (
            <div className="flex justify-center items-center py-8">
              <LoadingSpinner />
            </div>
          ) : orders.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>订单号</TableHead>
                      <TableHead>商品类型</TableHead>
                      <TableHead>金额(元)</TableHead>
                      <TableHead>支付方式</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.order_no}</TableCell>
                        <TableCell>{productTypeMap[order.product_type] || order.product_type}</TableCell>
                        <TableCell>¥{parseFloat(order.amount).toFixed(2)}</TableCell>
                        <TableCell>{paymentTypeMap[order.payment_type] || order.payment_type}</TableCell>
                        <TableCell>
                          <span className={cn(
                            "px-2 py-1 rounded-full text-xs font-medium",
                            order.payment_status === 'success' && "bg-green-100 text-green-800",
                            order.payment_status === 'pending' && "bg-yellow-100 text-yellow-800",
                            order.payment_status === 'failed' && "bg-red-100 text-red-800",
                            order.payment_status === 'refunded' && "bg-gray-100 text-gray-800",
                            order.payment_status === 'closed' && "bg-gray-100 text-gray-800"
                          )}>
                            {orderStatusMap[order.payment_status] || order.payment_status}
                          </span>
                        </TableCell>
                        <TableCell>{formatDate(order.createdAt)}</TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewOrderDetail(order.order_no)}
                            >
                              <Eye className="h-4 w-4" />
                              <span className="sr-only">查看详情</span>
                            </Button>
                            {order.payment_status === 'pending' && (
                              <Button 
                                variant="ghost" 
                                size="sm"
                                className="h-8 w-8 p-0 text-amber-500 hover:text-amber-700 hover:bg-amber-100"
                                onClick={(e) => openOrderCloseConfirm(order.id, e)}
                              >
                                <Ban className="h-4 w-4" />
                                <span className="sr-only">关闭订单</span>
                              </Button>
                            )}
                            <Button 
                              variant="ghost" 
                              size="sm"
                              className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-100"
                              onClick={(e) => openOrderDeleteConfirm(order.id, e)}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">删除</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              
              {ordersTotalPages > 1 && (
                <div className="flex justify-center mt-4">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious 
                          onClick={(e) => handlePageChange(ordersCurrentPage - 1, 'orders', e)}
                          className={ordersCurrentPage === 1 ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>
                      
                      {generatePageNumbers(ordersCurrentPage, ordersTotalPages).map((pageNum, index) => (
                        pageNum === '...' ? (
                          <PaginationItem key={`ellipsis-${index}`}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        ) : (
                          <PaginationItem key={`page-${pageNum}`}>
                            <PaginationLink
                              onClick={(e) => handlePageChange(pageNum, 'orders', e)}
                              isActive={pageNum === ordersCurrentPage}
                            >
                              {pageNum}
                            </PaginationLink>
                          </PaginationItem>
                        )
                      ))}
                      
                      <PaginationItem>
                        <PaginationNext 
                          onClick={(e) => handlePageChange(ordersCurrentPage + 1, 'orders', e)}
                          className={ordersCurrentPage === ordersTotalPages ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>暂无订单记录</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // 根据contentType渲染不同内容
  const renderContent = () => {
    // 使用memo缓存渲染结果，避免不必要的重新渲染
    const content = React.useMemo(() => {
    switch (contentType) {
      case 'profile':
        return renderProfile();
      case 'points':
        return renderPoints();
      case 'covers':
        return renderCovers();
      case 'orders':
        return renderOrders();
      case 'my-creations':
        return renderMyCreations();
      default:
        return renderProfile();
    }
    }, [contentType, userProfile, pointRecords, covers, orders, 
        pointsCurrentPage, coversCurrentPage, ordersCurrentPage,
        pointsLoading, coversLoading, ordersLoading, 
        editing, editNickname, editEmail, 
        passwordDialogOpen, avatarUploading]);

    return content;
  };

  // 移除iframe加载完成后的处理逻辑，避免跨域问题

  return (
    <div className="w-full h-full overflow-hidden">
        {renderContent()}

      {/* 封面预览对话框 */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="sm:max-w-[800px] h-[80vh] max-h-[800px] p-0 gap-0 flex flex-col" style={{ backgroundColor: "#ffffff" }}>
          <DialogHeader className="p-4 border-b flex-shrink-0">
            <DialogTitle>封面预览</DialogTitle>
            <DialogDescription>
              {currentCover?.cover_text || '预览封面内容'}
            </DialogDescription>
          </DialogHeader>
          
          {/* 使用iframe替代div，提供样式隔离 */}
          <div className="flex-grow relative" style={{ backgroundColor: "#f5f5f5" }}>
            <iframe
              ref={iframeRef}
              srcDoc={previewHtml}
              title="封面预览"
              className="absolute inset-0 w-full h-full border-none"
              sandbox="allow-same-origin allow-scripts"
            />
          </div>
          
          <div className="p-4 border-t flex-shrink-0 flex justify-between items-center">
            <Button variant="outline" onClick={() => setIsPreviewOpen(false)}>
              关闭
            </Button>
            <div className="flex gap-2">
              <Button onClick={handleEditCover}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription className="text-center py-4 text-base text-muted-foreground pt-2">
              您确定要删除此封面记录吗？
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteConfirmOpen(false)}
              disabled={deleteLoading}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleDeleteCover(deletingCoverId)}
              disabled={deleteLoading}
              className="ml-2"
            >
              {deleteLoading ? <LoadingSpinner className="h-4 w-4 border-destructive-foreground" /> : '确认删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 分享链接对话框 */}
      <Dialog open={shareLinkDialogOpen} onOpenChange={setShareLinkDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>分享封面</DialogTitle>
            <DialogDescription>
              复制以下链接分享给好友
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            <Input
              value={shareUrl}
              readOnly
              className="w-full"
              onClick={(e) => e.target.select()}
            />
            <p className="mt-2 text-sm text-muted-foreground">
              链接有效期为30天，请及时保存
            </p>
          </div>
          <DialogFooter className="mt-4">
            <Button
              onClick={async () => {
                try {
                  await navigator.clipboard.writeText(shareUrl);
                  message.success('链接已复制到剪贴板');
                } catch (error) {
                  console.error('复制链接失败:', error);
                }
              }}
            >
              复制链接
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 密码设置对话框 */}
      <PasswordDialog
        open={passwordDialogOpen}
        onOpenChange={setPasswordDialogOpen}
        hasPassword={userProfile?.has_set_password}
        currentUserPhone={userProfile?.phone}
      />

      {/* 订单详情对话框 */}
      <Dialog open={isOrderDetailOpen} onOpenChange={setIsOrderDetailOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>订单详情</DialogTitle>
          </DialogHeader>
          {currentOrder?.order ? (
            <div className="space-y-5">
              <div className="grid grid-cols-2 gap-x-6 gap-y-5">
                <div>
                  <Label className="text-muted-foreground text-sm">订单号</Label>
                  <div className="font-medium mt-1 text-foreground">{currentOrder.order.order_no}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground text-sm">创建时间</Label>
                  <div className="font-medium mt-1 text-foreground">{formatDate(currentOrder.order.createdAt)}</div>
                </div>
                <div>
                  <Label className="text-muted-foreground text-sm">商品类型</Label>
                  <div className="font-medium mt-1 text-foreground">
                    {productTypeMap[currentOrder.order.product_type] || currentOrder.order.product_type}
                  </div>
                </div>
                <div>
                  <Label className="text-muted-foreground text-sm">支付金额</Label>
                  <div className="font-medium mt-1 text-primary text-lg">
                    ¥{parseFloat(currentOrder.order.amount).toFixed(2)}
                  </div>
                </div>
                <div>
                  <Label className="text-muted-foreground text-sm">支付方式</Label>
                  <div className="font-medium mt-1 text-foreground">
                    {paymentTypeMap[currentOrder.order.payment_type] || currentOrder.order.payment_type}
                  </div>
                </div>
                <div>
                  <Label className="text-muted-foreground text-sm">支付状态</Label>
                  <div className="mt-1">
                    <span className={cn(
                      "px-2.5 py-1 rounded-full text-xs font-semibold",
                      currentOrder.order.payment_status === 'success' && "bg-green-100 text-green-800",
                      currentOrder.order.payment_status === 'pending' && "bg-yellow-100 text-yellow-800",
                      currentOrder.order.payment_status === 'failed' && "bg-red-100 text-red-800",
                      currentOrder.order.payment_status === 'refunded' && "bg-gray-100 text-gray-800",
                      currentOrder.order.payment_status === 'closed' && "bg-gray-100 text-gray-800"
                    )}>
                      {orderStatusMap[currentOrder.order.payment_status] || currentOrder.order.payment_status}
                    </span>
                  </div>
                </div>
              </div>

              {/* 套餐信息 */}
              {currentOrder.packageInfo && (
                <div className="mt-6">
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">套餐信息</h3>
                  <div className="bg-accent/30 p-4 rounded-md">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-xs text-muted-foreground">名称</Label>
                        <div className="text-sm font-medium">{currentOrder.packageInfo.name}</div>
                      </div>
                      {currentOrder.order.product_type === 'vip' && (
                        <div>
                          <Label className="text-xs text-muted-foreground">有效期</Label>
                          <div className="text-sm font-medium">{currentOrder.packageInfo.duration}天</div>
                        </div>
                      )}
                      {currentOrder.order.product_type === 'points' && (
                        <div>
                          <Label className="text-xs text-muted-foreground">积分数量</Label>
                          <div className="text-sm font-medium">{currentOrder.packageInfo.points}积分</div>
                        </div>
                      )}
                      {currentOrder.packageInfo.description && (
                        <div className="col-span-2">
                          <Label className="text-xs text-muted-foreground">描述</Label>
                          <div className="text-sm">{currentOrder.packageInfo.description}</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* 待支付订单显示支付信息 */}
              {currentOrder.order.payment_status === 'pending' && currentOrder.paymentParams && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">支付信息</h3>
                  <div className="bg-accent/30 p-3 rounded-md">
                    <div className="flex flex-col items-center">
                      {currentOrder.paymentParams.qrCodeUrl && (
                        <div className="mb-3">
                          <img 
                            src={currentOrder.paymentParams.qrCodeUrl} 
                            alt="支付二维码" 
                            className="w-40 h-40 border border-border rounded-md"
                          />
                        </div>
                      )}
                      <div className="text-sm text-muted-foreground">
                        请使用{paymentTypeMap[currentOrder.order.payment_type]}扫描二维码完成支付
                      </div>
                      {currentOrder.paymentParams.paymentExpireTime && (
                        <div className="text-xs text-red-500 mt-1">
                          支付有效期至：{formatDate(currentOrder.paymentParams.paymentExpireTime)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              
              <DialogFooter className="flex justify-between items-center mt-6">
                <div>
                  {currentOrder.order.payment_status === 'pending' && (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        setIsOrderDetailOpen(false);
                        openOrderCloseConfirm(currentOrder.order.id);
                      }}
                    >
                      <X className="mr-2 h-4 w-4" />
                      关闭订单
                    </Button>
                  )}
                </div>
                <Button onClick={() => setIsOrderDetailOpen(false)}>
                  关闭
                </Button>
              </DialogFooter>
            </div>
          ) : (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">加载订单信息中...</p>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* 订单删除确认对话框 */}
      <Dialog open={isOrderDeleteConfirmOpen} onOpenChange={setIsOrderDeleteConfirmOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription className="text-center py-4 text-base text-muted-foreground pt-2">
              您确定要删除此订单记录吗？
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsOrderDeleteConfirmOpen(false)}
              disabled={orderDeleteLoading}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleDeleteOrder(deletingOrderId)}
              disabled={orderDeleteLoading}
              className="ml-2"
            >
              {orderDeleteLoading ? <LoadingSpinner className="h-4 w-4 border-destructive-foreground" /> : '确认删除'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 订单关闭确认对话框 */}
      <Dialog open={isOrderCloseConfirmOpen} onOpenChange={setIsOrderCloseConfirmOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>确认关闭</DialogTitle>
            <DialogDescription className="text-center py-4 text-base text-muted-foreground pt-2">
              您确定要关闭此订单吗？
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsOrderCloseConfirmOpen(false)}
              disabled={orderCloseLoading}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleCloseOrder(closingOrderId)}
              disabled={orderCloseLoading}
              className="ml-2"
            >
              {orderCloseLoading ? <LoadingSpinner className="h-4 w-4 border-destructive-foreground" /> : '确认关闭'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProfileContent; 
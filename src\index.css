/* 免费商用字体定义 - @import必须在所有其他CSS规则之前 */
/* 思源宋体 - Google Fonts CDN */
@import url('https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@200;300;400;500;600;700;900&display=swap');

/* Google Fonts - 免费商用非中文字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* 更多优质免费字体 */
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@200;300;400;500;600;700&display=swap');

/* 等宽字体 - 适合代码和特殊用途 */
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Code+Pro:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Ubuntu+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 阿里巴巴普惠体 - 使用本地字体，避免网络加载问题 */
@font-face {
  font-family: 'Alibaba PuHuiTi';
  src: local('Alibaba PuHuiTi Regular'), local('SimHei'), local('Microsoft YaHei');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi';
  src: local('Alibaba PuHuiTi Medium'), local('SimHei'), local('Microsoft YaHei');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi';
  src: local('Alibaba PuHuiTi Bold'), local('SimHei'), local('Microsoft YaHei');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* 站酷酷黑体 - 使用本地字体，避免网络加载问题 */
@font-face {
  font-family: 'ZCOOL KuHei';
  src: local('ZCOOL KuHei'), local('SimHei'), local('Microsoft YaHei');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* 站酷文艺体 - 使用本地字体，避免网络加载问题 */
@font-face {
  font-family: 'ZCOOL WenYi';
  src: local('ZCOOL WenYi'), local('KaiTi'), local('SimSun');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* 文泉驿微米黑 - 使用本地字体，避免网络加载问题 */
@font-face {
  font-family: 'WenQuanYi Micro Hei';
  src: local('WenQuanYi Micro Hei'), local('SimHei'), local('Microsoft YaHei');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* 文泉驿正黑 - 使用本地字体，避免网络加载问题 */
@font-face {
  font-family: 'WenQuanYi Zen Hei';
  src: local('WenQuanYi Zen Hei'), local('SimHei'), local('Microsoft YaHei');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* 江西拙楷 - 使用本地字体，避免网络加载问题 */
@font-face {
  font-family: 'JiangXi ZhuoKai';
  src: local('JiangXi ZhuoKai'), local('KaiTi'), local('SimSun');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@layer base {
  :root {
    --background: 0 0% 100%; /* White */
    --foreground: 222.2 84% 4.9%; /* almost black text */
    --card: 0 0% 100%; /* White */
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* New Stylish Purple Gradient Theme */
    --primary: 265 55% 65%;            /* Elegant Purple */
    --primary-foreground: 0 0% 100%;     /* White text on primary */
    --primary-gradient-start: 255 50% 60%; /* Vibrant Purple */
    --primary-gradient-end: 275 60% 70%;   /* Bright, slightly bluish purple */

    --secondary: 210 40% 96.1%; /* Light gray-blue for secondary elements */
    --secondary-foreground: 222.2 47.4% 11.2%; /* Dark text on secondary */
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%; /* Lighter gray text */

    /* Accent can be a variation of primary or a complementary color */
    /* For now, let's make it a slightly lighter primary */
    --accent: 265 60% 70%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%; /* Red */
    --destructive-foreground: 0 0% 100%; /* White text on destructive */

    --border: 214.3 31.8% 91.4%; /* Light gray border */
    --input: 214.3 31.8% 91.4%;
    --ring: 265 55% 65%; /* Purple for focus rings, matching new primary */

    --radius: 0.5rem;

    /* Added for DebugModeToggle and other potential uses */
    --primary-rgb: 156, 133, 199; /* Corresponds to HSL(265, 55%, 65%) - 主紫色调的RGB表示 */
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    @apply bg-background text-foreground;
  }
}
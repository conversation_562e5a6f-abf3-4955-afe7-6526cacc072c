const express = require('express');
const router = express.Router();
const paymentController = require('../controllers/paymentController');
const { auth } = require('../middlewares/authMiddleware');
const { validate, schemas } = require('../utils/validator');
const { createPaymentIPWhitelistMiddleware } = require('../middlewares/paymentIPWhitelistMiddleware');

// 获取会员套餐列表（不需要认证，前台用户可以查看）
router.get('/membership/packages', paymentController.getMemberPackages);

// 获取积分套餐列表（不需要认证，前台用户可以查看）
router.get('/points/packages', paymentController.getPointPackages);

// 创建订单（需要认证）
router.post('/create-order', auth, validate(schemas.createOrder), paymentController.createOrder);

// 查询订单（需要认证）
router.get('/order/:orderNo', auth, paymentController.queryOrder);

// 更新订单支付方式（需要认证）
router.put('/order/:orderNo/payment-method', auth, validate(schemas.updatePaymentMethod), paymentController.updatePaymentMethod);

// 获取用户订单列表（需要认证）
router.get('/orders', auth, paymentController.getUserOrders);

// 更新订单显示状态（用户删除）（需要认证）
router.put('/orders/:id/status', auth, paymentController.updateOrderStatus);

// 关闭订单（需要认证）
router.put('/orders/:id/close', auth, paymentController.closeOrder);

// 支付回调处理（不需要认证，微信/支付宝服务器调用）
// 添加IP白名单中间件，限制只接受支付服务商官方IP的回调请求
router.post('/callback/wechat', createPaymentIPWhitelistMiddleware('wechat'), paymentController.wechatPayCallback);
router.post('/callback/alipay', createPaymentIPWhitelistMiddleware('alipay'), paymentController.alipayCallback);

// 模拟支付完成（仅开发环境使用，需要认证）
router.post('/mock-pay/:orderNo', auth, paymentController.mockPayment);

module.exports = router;

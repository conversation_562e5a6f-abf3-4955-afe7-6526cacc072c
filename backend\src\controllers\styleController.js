const { StylePrompt, StyleExample } = require('../models');
const { successResponse, errorResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');

/**
 * 获取所有封面风格列表
 * @route GET /api/style
 */
const getAllStyles = async (req, res) => {
  try {
    // 添加缓存控制头，允许客户端缓存结果5分钟
    res.set('Cache-Control', 'public, max-age=300'); // 5分钟缓存

    // 添加ETag支持，允许客户端使用条件请求
    const etag = req.headers['if-none-match'];

    // 只返回状态为active的风格
    const stylePrompts = await StylePrompt.findAll({
      where: { status: 'active' },
      order: [['display_order', 'ASC']],
      include: [
        {
          model: StyleExample,
          as: 'StyleExamples',
          attributes: ['id', 'image_url']
        }
      ]
    });

    // 生成新的ETag
    const newEtag = `W/"${Buffer.from(JSON.stringify(stylePrompts)).toString('base64').substring(0, 20)}"`;

    // 如果客户端发送了ETag且匹配，返回304 Not Modified
    if (etag && etag === newEtag) {
      return res.status(304).end();
    }

    // 设置新的ETag
    res.set('ETag', newEtag);

    return successResponse(res, '获取风格列表成功', stylePrompts);
  } catch (error) {
    logger.error('获取风格列表失败:', error);
    return errorResponse(res, '获取风格列表失败', 500);
  }
};

/**
 * 获取风格详情
 * @route GET /api/style/:id
 */
const getStyleById = async (req, res) => {
  const { id } = req.params;

  try {
    // 添加缓存控制头，允许客户端缓存结果10分钟
    res.set('Cache-Control', 'public, max-age=600'); // 10分钟缓存

    // 添加ETag支持，允许客户端使用条件请求
    const etag = req.headers['if-none-match'];

    // 尝试通过id或id_code查询风格
    const whereCondition = isNaN(id) 
      ? { id_code: id, status: 'active' } // 如果id不是数字，则视为id_code
      : { id: id, status: 'active' };     // 如果id是数字，则按id查询

    const style = await StylePrompt.findOne({
      where: whereCondition,
      include: [
        {
          model: StyleExample,
          as: 'StyleExamples',
          attributes: ['id', 'image_url']
        }
      ]
    });

    if (!style) {
      return errorResponse(res, '风格不存在', 404);
    }

    // 生成新的ETag
    const newEtag = `W/"${Buffer.from(JSON.stringify(style)).toString('base64').substring(0, 20)}"`;

    // 如果客户端发送了ETag且匹配，返回304 Not Modified
    if (etag && etag === newEtag) {
      return res.status(304).end();
    }

    // 设置新的ETag
    res.set('ETag', newEtag);

    return successResponse(res, '获取风格详情成功', { stylePrompt: style });
  } catch (error) {
    logger.error('获取风格详情失败:', error);
    return errorResponse(res, '获取风格详情失败', 500);
  }
};

module.exports = {
  getAllStyles,
  getStyleById
};

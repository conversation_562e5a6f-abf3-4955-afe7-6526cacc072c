import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { ListChecks, RefreshCcw, UserCircle, Compass, LayoutDashboard, BookOpen, ChevronsLeft, ChevronsRight, Edit, X, Link as LinkIcon, Loader2, User, History, CreditCard, FileClock, Crown, Upload, FileCode } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import axios from 'axios';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { message } from 'antd';
import MembershipButton from '../membership/MembershipButton';
import UserCenterPopup from './UserCenterPopup';

// TODO: Import existing components like CoverForm, StyleSelector, SizeTypeSelector
// import CoverForm from '../../cover/CoverForm'; // Adjust path as needed
// import StyleSelector from '../../cover/StyleSelector'; // Adjust path as needed
// import SizeTypeSelector from '../../cover/SizeTypeSelector'; // Adjust path as needed

const MAX_WORKS_DISPLAYED = 10;

const ChatSidebar = (props) => {
  // Props will be passed from ChatGenerate.jsx for state and handlers
  // e.g., coverTitle, onCoverTitleChange, selectedStyle, onStyleSelect, etc.

  // const {
  //   coverData,
  //   onCoverDataChange,
  //   availableStyles,
  //   selectedStyle,
  //   onStyleChange,
  //   availableSizes,
  //   selectedSize,
  //   onSizeChange,
  //   // ... other props for existing components
  // } = props;

  const {
    onResetDesign,
    isCollapsed,
    toggleCollapse,
    userInfo = null,
    onLoadCoverData,
    onNavItemClick,
    activeView = 'chat'
  } = props;
  const navigate = useNavigate();

  // 添加iframe引用
  const iframeRef = useRef(null);
  
  // 用户历史作品数据
  const [historicalWorks, setHistoricalWorks] = useState([]);
  const [loading, setLoading] = useState(false);
  
  // 添加每日积分状态
  const [dailyPoints, setDailyPoints] = useState(0);

  // 弹出层状态
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [currentCover, setCurrentCover] = useState(null);
  const [previewHtml, setPreviewHtml] = useState('');
  const [currentPreviewRecordId, setCurrentPreviewRecordId] = useState(null);
  const [shareLinkDialogOpen, setShareLinkDialogOpen] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  // 添加个人中心弹窗状态
  const [userCenterPopupOpen, setUserCenterPopupOpen] = useState(false);

  // 从后端获取用户历史作品数据
  useEffect(() => {
    const fetchUserCovers = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');
        if (!token) {
          setHistoricalWorks([]);
          return;
        }

        const response = await axios.get('/api/user/covers?page=1&limit=10', {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (response.data.success && Array.isArray(response.data.data)) {
          const covers = response.data.data.map(cover => ({
            id: cover.id,
            title: cover.cover_text || cover.cover_type_name || cover.cover_type_display || '未命名封面',
            date: new Date(cover.created_at).toLocaleDateString('zh-CN'),
            coverCode: cover.cover_code
          }));

          setHistoricalWorks(covers);
        } else {
          setHistoricalWorks([]);
        }
      } catch (error) {
        console.error('获取用户封面记录失败:', error);
        setHistoricalWorks([]);
      } finally {
        setLoading(false);
      }
    };

    fetchUserCovers();
  }, []);
  
  // 获取每日积分信息
  useEffect(() => {
    // 只在用户已登录时获取每日积分
    if (userInfo && userInfo.id) {
      const fetchDailyPoints = async () => {
        try {
          const token = localStorage.getItem('token');
          if (!token) return;
          
          const response = await axios.get('/api/user/daily-points', {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          if (response.data.success) {
            // 直接更新组件状态
            const dailyPointsValue = response.data.data.daily_points || 0;
            setDailyPoints(dailyPointsValue);
            
            // 同时更新localStorage
            const userStr = localStorage.getItem('user');
            if (userStr) {
              try {
                const currentUser = JSON.parse(userStr);
                const updatedUser = {
                  ...currentUser,
                  daily_points: dailyPointsValue
                };
                localStorage.setItem('user', JSON.stringify(updatedUser));
              } catch (error) {
                console.error('解析用户数据失败:', error);
              }
            }
          }
        } catch (error) {
          console.error('获取每日积分信息失败:', error);
        }
      };
      
      fetchDailyPoints();
    }
  }, [userInfo]);

  // 监听用户退出登录事件
  useEffect(() => {
    const handleUserLogout = () => {
      setHistoricalWorks([]); // 清空历史作品数据
    };

    // 强制重新渲染组件的处理函数
    const handleForceUpdate = () => {
      // 从localStorage获取最新的用户数据
      const userStr = localStorage.getItem('user');
      if (userStr) {
        try {
          // 这里不需要做任何事情，只需要触发React的重新渲染
          // 由于useEffect依赖了userInfo，所以当userInfo变化时会重新渲染
          // 这里只是为了确保事件被正确处理
        } catch (error) {
          console.error('处理forceUpdate事件失败:', error);
        }
      }
    };

    window.addEventListener('userLogout', handleUserLogout);
    window.addEventListener('forceUpdate', handleForceUpdate);
    
    return () => {
      window.removeEventListener('userLogout', handleUserLogout);
      window.removeEventListener('forceUpdate', handleForceUpdate);
    };
  }, []);

  // 监听用户积分更新事件，实时更新侧边栏显示
  useEffect(() => {
    const handleUserPointsUpdated = (event) => {
      // 从localStorage获取最新的用户数据
      const userStr = localStorage.getItem('user');
      if (userStr && userInfo) {
        try {
          const updatedUser = JSON.parse(userStr);
          // 如果用户ID相同且积分有变化，则更新侧边栏显示
          if (updatedUser.id === userInfo.id && updatedUser.points !== userInfo.points) {
            // 直接更新父组件传递的userInfo
            // 这里不需要做任何事情，因为当localStorage中的用户信息更新后
            // AuthContext会自动更新userInfo，从而触发组件重新渲染
            
            // 但为了确保UI立即更新，我们可以强制触发一次重新渲染
            const forceUpdate = new Event('forceUpdate');
            window.dispatchEvent(forceUpdate);
            
            // 记录积分变化，便于调试
            console.log(`用户积分已更新: ${userInfo.points} -> ${updatedUser.points}`);
          }
        } catch (error) {
          console.error('解析用户数据失败:', error);
        }
      }
    };

    // 添加事件监听器
    window.addEventListener('userPointsUpdated', handleUserPointsUpdated);
    
    return () => {
      window.removeEventListener('userPointsUpdated', handleUserPointsUpdated);
    };
  }, [userInfo]);

  // 添加监听用户每日积分变化的事件处理
  useEffect(() => {
    // 定义处理函数
    const handleProfileUpdate = async () => {
      try {
        // 如果用户已登录，获取最新的每日积分信息
        if (userInfo && userInfo.id) {
          const token = localStorage.getItem('token');
          if (!token) return;
          
          // 获取每日积分信息
          const response = await axios.get('/api/user/daily-points', {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          if (response.data.success) {
            // 更新本地存储的用户信息，添加daily_points
            const userStr = localStorage.getItem('user');
            if (userStr) {
              const currentUser = JSON.parse(userStr);
              const updatedUser = {
                ...currentUser,
                daily_points: response.data.data.daily_points || 0
              };
              
              // 只有当每日积分发生变化时才更新
              if (currentUser.daily_points !== updatedUser.daily_points) {
                localStorage.setItem('user', JSON.stringify(updatedUser));
                
                // 触发重新渲染
                const forceUpdate = new Event('forceUpdate');
                window.dispatchEvent(forceUpdate);
              }
            }
          }
        }
      } catch (error) {
        console.error('获取每日积分信息失败:', error);
      }
    };

    // 添加事件监听器
    window.addEventListener('dailyPointsUpdated', handleProfileUpdate);
    
    // 组件挂载时立即获取一次最新数据
    handleProfileUpdate();
    
    return () => {
      window.removeEventListener('dailyPointsUpdated', handleProfileUpdate);
    };
  }, [userInfo]);

  // iframe加载完成后处理
  useEffect(() => {
    const iframe = iframeRef.current;
    if (!iframe || !isPreviewOpen) return;

    const handleIframeLoad = () => {
      try {
        const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
        if (iframeDoc && iframeDoc.body) {
          // 确保内容居中显示
          if (!iframeDoc.body.style.margin) {
            iframeDoc.body.style.margin = '0';
          }
          if (!iframeDoc.body.style.padding) {
            iframeDoc.body.style.padding = '0';
          }
          if (!iframeDoc.body.style.height) {
            iframeDoc.body.style.height = '100%';
          }
          if (!iframeDoc.body.style.display) {
            iframeDoc.body.style.display = 'flex';
            iframeDoc.body.style.justifyContent = 'center';
            iframeDoc.body.style.alignItems = 'center';
          }
        }
      } catch (error) {
        console.error('访问iframe内容失败', error);
      }
    };

    iframe.addEventListener('load', handleIframeLoad);
    return () => iframe.removeEventListener('load', handleIframeLoad);
  }, [isPreviewOpen, previewHtml]);

  const handleReset = () => {
    if (onResetDesign) {
      onResetDesign();
    }
  };

  // 修改后的加载作品函数 - 参考个人中心-我的作品页面的交互效果
  const handleLoadWork = async (workId, coverCode) => {

    if (!coverCode) {
      message.error('封面编码不存在，无法查看');
      return;
    }

    try {
      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      // 打开预览弹出层并显示加载动画
      setIsPreviewOpen(true);
      setCurrentPreviewRecordId(workId);
      
      // 使用完整的HTML文档结构显示加载动画
      setPreviewHtml(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; height: 100vh; }
            .loading { display: flex; justify-content: center; align-items: center; height: 100%; }
            .spinner { animation: spin 1s linear infinite; height: 2rem; width: 2rem; border-radius: 50%; border: 2px solid #f3f3f3; border-top-color: #3498db; }
            @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
          </style>
        </head>
        <body>
          <div class="loading">
            <div class="spinner"></div>
          </div>
        </body>
        </html>
      `);

      // 通过cover_code获取封面详情
      const response = await axios.get(`/api/cover/code/${coverCode}/edit`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        const coverData = response.data.data;
        setCurrentCover(coverData);

        // 优先使用编辑后的HTML内容，如果没有则使用原始HTML内容
        let htmlContent = coverData.edited_html_content || coverData.html_content;
        if (htmlContent) {
          // 应用与ChatPreview.jsx相同的链接替换逻辑
          if (typeof htmlContent === 'string') {
            // 更换为更强大的正则表达式，以处理单引号、双引号和无协议的URL
            // 包含三种情况: src="http://via.placeholder.com/100", src='https://via.placeholder.com/100', src=//via.placeholder.com/100
            const placeholderRegex = /src=(["'])?(https?:)?\/\/via\.placeholder\.com\/[^'" >]*(?:\1)?/g;
            htmlContent = htmlContent.replace(placeholderRegex, 'src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="');
          }
          
          // 将HTML内容包装在完整的HTML文档结构中
          if (!htmlContent.includes('<!DOCTYPE html>') && !htmlContent.includes('<html')) {
            htmlContent = `
              <!DOCTYPE html>
              <html>
              <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <style>
                  /* 重置样式，确保不影响外部 */
                  html, body { margin: 0; padding: 0; height: 100%; }
                  body { display: flex; justify-content: center; align-items: center; }
                </style>
              </head>
              <body>
                ${htmlContent}
              </body>
              </html>
            `;
          }
          
          setPreviewHtml(htmlContent);
        } else {
          // 如果没有HTML内容，显示提示信息
          const imageUrl = coverData?.image_url || '';
          setPreviewHtml(`
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>
                body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; height: 100vh; color: #666; text-align: center; }
              </style>
            </head>
            <body>
              <div>
                <p>暂无HTML内容</p>
                ${imageUrl ? `<img src="${imageUrl}" style="max-width:100%; margin-top:20px; border:1px solid #eee;" />` : ''}
              </div>
            </body>
            </html>
          `);
        }
      } else {
        // 显示错误信息
        setPreviewHtml(`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; height: 100vh; color: #666; text-align: center; }
            </style>
          </head>
          <body>
            <div>
              <p>获取封面详情失败</p>
              <p>${response.data.message || '未知错误'}</p>
            </div>
          </body>
          </html>
        `);
      }
    } catch (error) {
      console.error('加载封面数据失败');
      // 显示错误信息
      setPreviewHtml(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; height: 100vh; color: #666; text-align: center; }
          </style>
        </head>
        <body>
          <div>
            <p>获取封面详情失败</p>
            <p>${error.message || '未知错误'}</p>
          </div>
        </body>
        </html>
      `);
    }
  };

  // 编辑封面函数 - 使用回调代替URL导航，导入到chat页面下的预览区域
  const handleEditCover = () => {
    setIsPreviewOpen(false); // 关闭模态框

    // 使用localStorage存储封面数据，确保导航后能加载数据
      if (currentCover) {
        try {
        localStorage.setItem('pendingCoverData', JSON.stringify(currentCover));
        // 导航到创建封面页面，添加mode=edit参数以触发加载机制
        navigate('/?mode=edit');
        } catch (error) {
          console.error('存储封面数据失败:', error);
          message.error('加载编辑器失败，请稍后再试');
      }
    }
  };

  // 处理分享链接功能
  const handleShareCover = async () => {
    try {
      if (!currentCover || !currentCover.id) {
        message.error('无法获取封面信息，无法分享');
        return;
      }

      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      // 获取封面记录以获取cover_code
      const response = await axios.get(`/api/cover/${currentCover.id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success && response.data.data) {
        const coverCode = response.data.data.cover_code;

        if (!coverCode) {
          message.error('此封面不支持分享');
          return;
        }

        // 生成分享链接
        const shareUrl = `${window.location.origin}/share/${coverCode}`;

        // 复制链接到剪贴板
        await navigator.clipboard.writeText(shareUrl);
        message.success('分享链接已复制到剪贴板');

        // 显示分享链接对话框
        setShareLinkDialogOpen(true);
        setShareUrl(shareUrl);
      } else {
        message.error(response.data.message || '获取封面信息失败');
      }
    } catch (error) {
      console.error('生成分享链接失败:', error);
      message.error('生成分享链接失败，请重试');
    }
  };

  // 点击加载更多作品按钮，切换到封面记录页面
  const handleLoadMoreWorks = () => {
    // 使用与其他导航项相同的处理方式，确保触发离开提示
    if (onNavItemClick && typeof onNavItemClick === 'function') {
      onNavItemClick('my-creations');
    } else {
      // 使用与handleNavItemClick相同的逻辑
      const currentUrl = new URL(window.location.href);
      const searchParams = new URLSearchParams(currentUrl.search);
      
      // 设置view参数为my-creations
      searchParams.set('view', 'my-creations');
      
      // 使用history.replaceState更新URL而不导致页面跳转
      window.history.replaceState(
        {}, 
        '', 
        `${window.location.pathname}?${searchParams.toString()}`
      );
      
      // 触发自定义事件，通知其他组件视图已更改
      const viewChangeEvent = new CustomEvent('viewChange', { detail: { view: 'my-creations' } });
      window.dispatchEvent(viewChangeEvent);
    }
  };

  // 优化标题显示长度的函数
  const formatTitle = (title, maxLength = 30) => {
    if (!title) return '图片预览';
    if (title.length <= maxLength) return title;
    return title.substring(0, maxLength) + '...';
  };

  const worksToDisplay = historicalWorks.slice(0, MAX_WORKS_DISPLAYED);
  // 只有当显示的历史作品数量达到最大值（10条）时才显示"加载更多"按钮
  const showLoadMoreButton = worksToDisplay.length === MAX_WORKS_DISPLAYED;

  // 定义导航项 - 移除已合并到用户弹出菜单中的项
  const navItems = [
    { id: 'chat', icon: RefreshCcw, text: '创建封面' },
    { id: 'file-upload', icon: Upload, text: '上传文件' },
    { id: 'code-paste', icon: FileCode, text: '复制代码' },
    // { id: 'profile', icon: User, text: '个人资料' },
    // { id: 'covers', icon: BookOpen, text: '封面记录' },
    // { id: 'points', icon: CreditCard, text: '积分记录' },
    // { id: 'orders', icon: ListChecks, text: '订单记录' },
    // { id: 'my-creations', icon: History, text: '我的作品' },
    { id: 'membership', icon: Crown, text: '会员中心' },
  ];

  // 处理导航项点击 - 使用独立路由而不是查询参数
  const handleNavItemClick = (viewId) => {
    // 如果有传入的onNavItemClick函数，则优先使用它
    if (onNavItemClick && typeof onNavItemClick === 'function') {
      onNavItemClick(viewId);
    } else {
      // 使用React Router的navigate函数进行页面导航
      switch(viewId) {
        case 'chat':
          navigate('/', { replace: true });
          break;
        case 'profile':
          navigate('/profile', { replace: true });
          break;
        case 'points':
          navigate('/points', { replace: true });
          break;
        case 'orders':
          navigate('/orders', { replace: true });
          break;
        case 'covers':
          navigate('/covers', { replace: true });
          break;
        case 'my-creations':
          navigate('/my-creations', { replace: true });
          break;
        case 'membership':
          navigate('/membership', { replace: true });
          break;
        case 'auth':
          navigate('/auth', { replace: true });
          break;
        case 'register':
          navigate('/register', { replace: true });
          break;
        case 'forgot-password':
          navigate('/forgot-password', { replace: true });
          break;
        case 'payment':
          navigate('/payment', { replace: true });
          break;
        case 'payment-result':
          navigate('/payment-result', { replace: true });
          break;
        case 'file-upload':
          navigate('/file-upload', { replace: true });
          // 触发自定义事件，通知其他组件视图已更改
          window.dispatchEvent(new CustomEvent('viewChange', { detail: { view: 'file-upload' } }));
          break;
        case 'code-paste':
          navigate('/code-paste', { replace: true });
          // 触发自定义事件，通知其他组件视图已更改
          window.dispatchEvent(new CustomEvent('viewChange', { detail: { view: 'code-paste' } }));
          break;
        default:
          navigate('/', { replace: true });
      }
    }
  };

  // 统一的、更平滑的过渡效果
  const textWrapperTransition = `transition-[max-width] duration-300 ease-in-out`;
  const textOpacityTransition = `transition-opacity duration-200 ease-in`;
  const textAnimationWrapper = `overflow-hidden ${textWrapperTransition} ${isCollapsed ? 'max-w-0' : 'max-w-xs'}`;
  const textAnimationContent = `whitespace-nowrap ${textOpacityTransition} ${isCollapsed ? 'opacity-0' : 'opacity-100'}`;

  // 添加安全访问iframe的contentDocument的函数
  const safeAccessFrameDocument = (frame) => {
    if (!frame) return null;
    
    try {
      return frame.contentDocument || (frame.contentWindow && frame.contentWindow.document);
    } catch (error) {
      console.error('访问iframe文档失败:', error);
      return null;
    }
  };

  // 判断导航项是否活跃
  const isItemActive = (item) => {
    if (item.id === 'chat') {
      // 使chat按钮在auth页面也处于活跃状态
      return (activeView === 'chat') || (window.location.pathname === '/auth');
    }
    
    // 支付相关页面应该高亮会员中心导航
    if (item.id === 'membership') {
      return activeView === 'membership' || activeView === 'payment' || activeView === 'payment-result';
    }

    // 订单记录页面应该正确高亮
    if (item.id === 'orders') {
      return activeView === 'orders';
    }
    
    return activeView === item.id;
  };

  const handleLogout = () => {
    // 清除本地存储的 token 和 user 信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // 触发一个自定义事件，通知其他组件用户已退出
    const logoutEvent = new Event('userLogout');
    window.dispatchEvent(logoutEvent);
    
    // 强制刷新页面，确保所有状态都重置
    // 使用 navigate 避免整页刷新
    navigate('/auth', { replace: true });
    window.location.reload();

    message.success('您已成功退出登录');
  };

  return (
    <>
      <style>{`
        /* 隐藏滚动条但保持可滚动性 */
        .sidebar-nav-container::-webkit-scrollbar {
          display: none;
        }
        .sidebar-nav-container {
          scrollbar-width: none;
          -ms-overflow-style: none;
        }
      `}</style>
    <div className="flex flex-col h-full bg-white">
      {/* Logo Section */}
      <div className={`p-3 border-b border-slate-200 flex ${isCollapsed ? 'justify-center' : 'items-center'} h-16 shrink-0`}>
        <Link to="/" title="应用首页" className={`flex items-center ${isCollapsed ? 'justify-center' : ''} overflow-hidden`}>
          <Compass size={isCollapsed ? 24 : 22} className={`text-purple-600 shrink-0 ${isCollapsed ? '' : 'mr-2'} transition-all duration-300`}/>
            <div className={textAnimationWrapper}>
              <span className={`font-semibold text-md text-slate-700 ${textAnimationContent}`}>应用名称</span>
            </div>
        </Link>
      </div>
      
      {/* 导航列表 */}
        <div className="flex-grow overflow-y-auto flex flex-col scrollbar-thin scrollbar-thumb-slate-200 scrollbar-track-transparent px-2 py-3 sidebar-nav-container">
        
        {/* 顶部导航项 */}
        <div className="space-y-1 mb-5">
          {navItems.map((item) => {
            const isActive = isItemActive(item);

            if (item.id === 'chat') {
              return (
                <button
                  key={item.id}
                  onClick={handleReset}
                  title={item.text}
                  className={`w-full flex items-center p-2 rounded-md text-sm font-medium transition-all duration-200 ease-in-out transform
                    ${isCollapsed ? 'justify-center' : ''}
                    ${isActive 
                      ? 'text-white bg-gradient-to-r from-orange-500 to-red-500 shadow-md hover:scale-105 hover:brightness-110 active:scale-97' 
                      : 'text-slate-600 hover:bg-slate-100'
                    }
                  `}
                >
                  <item.icon size={isCollapsed ? 24 : 18} className={`shrink-0 ${isCollapsed ? '' : 'mr-3'}`} />
                    <div className={textAnimationWrapper}>
                      <span className={`font-medium ${textAnimationContent}`}>
                      {item.text}
                    </span>
                    </div>
                </button>
              );
            }
            
            return (
              <button
                key={item.id}
                onClick={() => handleNavItemClick(item.id)}
                className={`w-full flex items-center rounded-md transition-colors p-2
                  ${isActive 
                    ? 'bg-primary text-white hover:bg-primary-dark' 
                    : 'text-slate-600 hover:bg-slate-100'
                  }
                  ${isCollapsed ? 'justify-center' : ''}
                `}
              >
                <item.icon size={isCollapsed ? 24 : 18} className={`shrink-0 ${isCollapsed ? '' : 'mr-3'}`} />
                  <div className={textAnimationWrapper}>
                    <span className={`text-sm font-medium ${textAnimationContent}`}>
                    {item.text}
                  </span>
                  </div>
              </button>
            );
          })}
        </div>

        {/* 添加分隔线 */}
        <div className="border-t border-gray-200 my-3"></div>
        
        {/* 历史作品标题 */}
          <div className={`${textAnimationWrapper} px-3`}>
            <h3 className={`pt-4 pb-2 text-xs font-semibold text-slate-400 uppercase tracking-wider ${textAnimationContent}`}>
            历史作品
          </h3>
          </div>

        {/* 历史作品列表 */}
        {loading ? (
          <div className="flex justify-center py-6">
            <Loader2 className="animate-spin h-5 w-5 text-slate-400" />
          </div>
        ) : worksToDisplay.length === 0 ? (
            <div className={`${textAnimationWrapper} px-3`}>
              <div className={`py-4 text-center text-sm text-slate-500 ${textAnimationContent}`}>
              {userInfo ? "暂无历史作品" : "请登录查看历史作品"}
            </div>
            </div>
        ) : (
          <div className="space-y-1">
            {worksToDisplay.map((work) => (
              <button
                key={work.id}
                onClick={() => handleLoadWork(work.id, work.coverCode)}
                className={`w-full text-left p-2 rounded-md hover:bg-slate-100 transition-colors group ${
                  isCollapsed ? "flex justify-center" : ""
                }`}
                title={work.title}
              >
                <div className="flex items-center">
                  <FileClock 
                    size={isCollapsed ? 20 : 16} 
                    className={`shrink-0 text-slate-400 group-hover:text-slate-500 ${isCollapsed ? '' : 'mr-2'}`}
                  />
                    <div className={textAnimationWrapper}>
                      <span className={`text-sm text-slate-700 truncate ${textAnimationContent}`}>
                      {formatTitle(work.title, 19)}
                    </span>
                    </div>
                </div>
              </button>
            ))}
          </div>
        )}

        {/* 加载更多按钮 - 调整为点击后切换到封面记录视图 */}
          <div className={textAnimationWrapper}>
            {showLoadMoreButton && (
          <button
            onClick={handleLoadMoreWorks}
                className={`w-full mt-2 p-2 text-center text-sm text-primary hover:text-primary-dark hover:bg-slate-50 rounded transition-all ${textAnimationContent}`}
          >
            加载更多作品...
          </button>
        )}
          </div>
        
      </div>

      {/* 底部用户区 */}
      <div className={`border-t border-gray-200 bg-white mt-auto px-3 py-4 flex ${isCollapsed ? 'flex-col items-center space-y-2' : 'justify-between items-center'}`}>
        {userInfo ? (
          <UserCenterPopup
            userInfo={userInfo}
            dailyPoints={dailyPoints}
            onNavItemClick={handleNavItemClick}
            onLogout={handleLogout}
          >
          <div 
            className={`flex items-center cursor-pointer hover:bg-slate-100 transition-colors duration-150 ease-in-out rounded-md p-2 ${isCollapsed ? 'w-11 h-11 justify-center' : 'w-full'}`}
            title={userInfo.nickname || '个人资料'}
          >
            <Avatar
              className={`h-10 w-10 border-2 ${isCollapsed ? '' : 'mr-2'} ${userInfo.is_vip ? 'border-yellow-500' : 'border-slate-200'}`}
            >
              {userInfo.avatar ? (
                <AvatarImage 
                  src={userInfo.avatar} 
                  alt={userInfo.nickname || 'User avatar'} 
                  className="object-cover"
                />
              ) : (
                <AvatarFallback className="bg-gradient-to-br from-purple-500 to-indigo-600 text-white">
                  {(userInfo.nickname || 'U').charAt(0).toUpperCase()}
                </AvatarFallback>
              )}
              {userInfo.is_vip && (
                <div className="absolute -top-2 -right-2 bg-yellow-500 rounded-full p-0.5">
                  <Crown size={14} className="text-white" />
                </div>
              )}
            </Avatar>
              <div className={`${textAnimationWrapper} ml-2`}>
                <div className={`flex flex-col items-start truncate ${textAnimationContent}`}>
                <p className="text-[15px] font-medium text-slate-700 truncate max-w-[120px]">
                  {userInfo.nickname || '未设置昵称'}
                </p>
                <p className="text-xs text-slate-400">
                  {userInfo.is_vip 
                    ? <span className="text-yellow-500 font-medium">高级会员</span>
                    : '普通会员'} · {
                      (dailyPoints > 0) 
                      ? <><span className="text-primary">{userInfo.points || 0}</span> +<span className="text-green-500">{dailyPoints}</span> 积分</>
                      : <><span className="text-primary">{userInfo.points || 0}</span> 积分</>
                    }
                </p>
              </div>
              </div>
          </div>
          </UserCenterPopup>
        ) : (
          <Link
            to="/auth"
            title="登录/注册"
            className={`flex items-center w-full group transition-colors duration-150 ease-in-out
                        ${isCollapsed 
                            ? 'justify-center p-2 rounded-md hover:bg-slate-200' 
                            : 'p-2 rounded-md hover:bg-slate-200'}`}
          >
            <UserCircle 
                size={isCollapsed ? 26 : 22}
                className={`shrink-0 text-slate-500 group-hover:text-slate-700 ${isCollapsed ? '' : 'mr-2.5'}`}
            />
              <div className={textAnimationWrapper}>
                <span className={`text-[15px] font-medium text-slate-700 group-hover:text-slate-900 ${textAnimationContent}`}>
                登录 / 注册
              </span>
              </div>
          </Link>
        )}

        {/* Collapse/Expand Button */}
        {isCollapsed ? (
          <button
            onClick={toggleCollapse}
            title={'展开侧边栏'}
            className={`flex items-center justify-center w-10 h-10 text-sm font-medium text-slate-600 hover:bg-slate-200 hover:text-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-slate-400 transition-all duration-200 ease-in-out whitespace-nowrap overflow-hidden`}
          >
            <ChevronsRight size={18} className="shrink-0"/>
          </button>
        ) : (
          <button
            onClick={toggleCollapse}
            title={'收起侧边栏'}
            className={`flex items-center justify-center w-8 h-8 p-0 ml-auto text-sm font-medium text-slate-600 hover:bg-slate-200 hover:text-slate-800 rounded-md focus:outline-none focus:ring-1 focus:ring-slate-400 transition-all duration-200 ease-in-out`}
          >
            <ChevronsLeft size={18} className={`shrink-0`}/>
          </button>
        )}
      </div>

      {/* 封面预览弹出层 */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="sm:max-w-[800px] h-[80vh] max-h-[800px] p-0 gap-0 flex flex-col" style={{ backgroundColor: "#ffffff" }}>
          <DialogHeader className="p-4 border-b flex-shrink-0">
            <DialogTitle>封面预览</DialogTitle>
            <DialogDescription>
              {formatTitle(currentCover?.cover_text || '')}
            </DialogDescription>
          </DialogHeader>
          
          {/* 使用iframe替代div，提供样式隔离 */}
          <div className="flex-grow relative" style={{ backgroundColor: "#f5f5f5" }}>
            <iframe
              ref={iframeRef}
              srcDoc={previewHtml}
              title="封面预览"
              className="absolute inset-0 w-full h-full border-none"
              sandbox="allow-same-origin"
            />
          </div>
          
          <div className="p-4 border-t flex-shrink-0 flex justify-between items-center">
            <Button variant="outline" onClick={() => setIsPreviewOpen(false)}>
              关闭
            </Button>
            <div className="flex gap-2">
              <Button onClick={handleEditCover}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 分享链接对话框 */}
      <Dialog open={shareLinkDialogOpen} onOpenChange={setShareLinkDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>分享封面</DialogTitle>
            <DialogDescription>
              复制以下链接分享给好友（链接有效期30天）
            </DialogDescription>
          </DialogHeader>
          
          <div className="mt-4 flex items-center space-x-2">
            <input
              type="text"
              value={shareUrl}
              readOnly
              className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              onClick={(e) => e.target.select()}
            />
            <Button onClick={async () => {
              try {
                await navigator.clipboard.writeText(shareUrl);
                message.success('链接已复制到剪贴板');
              } catch (error) {
                console.error('复制链接失败:', error);
              }
            }}>
              复制
            </Button>
          </div>
          
          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setShareLinkDialogOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
    </>
  );
};

export default ChatSidebar;
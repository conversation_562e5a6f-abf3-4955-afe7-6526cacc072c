# ChatPreview.jsx 拆分计划

## 背景

ChatPreview.jsx 是一个超过2400行的大型组件，负责封面预览和编辑功能。为了提高代码可维护性和可读性，需要将其拆分为多个功能模块，遵循高内聚低耦合的开发原则。

## 拆分原则

1. 一次只拆分一个模块，确保变更可控
2. 每个模块专注于单一职责
3. 只拆分代码，不改变功能
4. 每个模块完成后进行测试，确保功能正常

## 拆分模块计划

| 模块名称 | 职责 | 状态 | 问题 |
|---------|------|------|------|
| previewScaling.js | 处理预览缩放和尺寸计算 | ✅ 已完成并修复 | ✅ 已解决：拖拽编辑框时字体大小不跟随变化 |
| previewRenderer.js | 处理HTML内容在iframe中的渲染和安全处理 | ✅ 已完成 | - |
| editorCommands.js | 处理编辑命令的执行 | ⏳ 待实现 | - |
| toolbarManager.js | 管理工具栏状态和交互 | ⏳ 待实现 | - |
| exportFunctions.js | 处理下载、分享功能 | ⏳ 待实现 | - |
| EditorToolbar.jsx | 工具栏UI组件 | ⏳ 待实现 | - |
| PermissionManager.jsx | 权限管理组件 | ⏳ 待实现 | - |

## 详细步骤记录

### 1. previewScaling.js

**完成日期**: 2023-05-30

**主要功能**:
- `calculateNaturalDimensions`: 计算自然尺寸
- `calculatePreviewContainerStyle`: 计算预览容器样式
- `initResizeObserver`: 初始化ResizeObserver监听容器变化
- `extractDimensionsFromHtml`: 从HTML中提取尺寸信息
- `applyScaleToIframeContent`: 应用缩放比例到iframe内容 (新增函数)

**遇到的问题**:
- 拖拽编辑框时字体大小不跟随变化
- 需要检查是否缺少了与拖拽相关的缩放逻辑

**解决方案**:
1. 添加了新函数 `applyScaleToIframeContent` 用于在缩放比例变化时应用到iframe内容
2. 在 ChatPreview.jsx 中添加了新的 useEffect 监听 scale 变化，并调用 applyScaleToIframeContent 函数
3. 确保拖拽句柄位置在缩放后保持正确

### 2. previewRenderer.js

**完成日期**: 2023-05-30

**主要功能**:
- `prepareHtmlContent`: 准备HTML内容，添加安全处理和必要元素
- `renderHtmlToIframe`: 将HTML内容渲染到iframe中
- `extractHtmlFromIframe`: 从iframe中提取HTML内容
- `cleanupIframeEditState`: 清理iframe中的编辑状态
- `clearIframeContent`: 清空iframe内容

**遇到的问题**:
- 暂无明显问题

**解决方案**:
- 无需调整

## 下一步工作

1. 实现 editorCommands.js 模块，处理编辑命令的执行
2. 完成后停止并等待测试反馈 
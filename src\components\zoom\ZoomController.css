.zoom-controller {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  padding: 5px 8px;
  background-color: transparent;
  box-shadow: none;
  z-index: 10;
  width: 100%;
  border-radius: 4px;
}

.zoom-controller-percentage {
  font-size: 12px;
  color: #333;
  font-weight: bold;
  min-width: 36px;
  text-align: center;
  flex-shrink: 0;
}

.zoom-controller-slider {
  -webkit-appearance: none;
  width: 100px;
  height: 6px;
  background: #f1f1f1;
  outline: none;
  border-radius: 3px;
  cursor: pointer;
  writing-mode: horizontal-tb;
  direction: ltr;
}

.zoom-controller-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: #4A90E2;
  border-radius: 50%;
  cursor: pointer;
}

.zoom-controller-slider::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: #4A90E2;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.zoom-controller-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid #ccc;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.zoom-controller-button:hover {
  background-color: #e0e0e0;
}

.zoom-controller-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
} 
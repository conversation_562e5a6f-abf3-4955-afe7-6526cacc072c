import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { Button } from "@/components/ui/button";
import { Wand2, Home } from "lucide-react";
import ZoomController from '../zoom/ZoomController'; // 引入ZoomController组件

const LoadingSpinner = ({ className }) => {
  return <div className={`animate-spin rounded-full h-8 w-8 border-b-2 border-primary ${className || ''}`}></div>;
};

const SharePage = () => {
  const { code } = useParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [htmlContent, setHtmlContent] = useState('');
  const [scale, setScale] = useState(1);
  const minScale = 0.1;
  const maxScale = 3;
  const scaleStep = 0.1;

  useEffect(() => {
    const fetchCoverData = async () => {
      try {
        setLoading(true);
        // 假设后端 /api/cover/share/:code 已经优先返回了编辑后的内容
        const response = await axios.get(`/api/cover/share/${code}`);

        if (response.data.success && typeof response.data.data === 'string') {
          // 处理HTML内容，添加缩放控制功能
          const htmlWithZoom = addZoomControlToHtml(response.data.data);
          setHtmlContent(htmlWithZoom);
        } else if (response.data.success && response.data.data != null) { // response.data.data 存在但不是字符串
          console.error('API返回的封面数据不是有效的HTML字符串:', response.data.data);
          setError('获取封面数据失败，格式不正确');
        } else if (!response.data.success) { // 请求本身失败
          setError('获取封面数据失败');
        } else { // 请求成功，但 response.data.data 为 null 或 undefined
           console.error('API未返回有效的封面数据:', response.data);
           setError('未找到封面内容');
        }
      } catch (error) {
        console.error('获取分享封面失败:', error);
        setError('获取分享封面数据失败，可能链接已失效');
      } finally {
        setLoading(false);
      }
    };

    if (code) {
      fetchCoverData();
    }
  }, [code]);

  // 处理HTML内容，添加必要的样式和脚本以支持缩放功能
  const addZoomControlToHtml = (html) => {
    // 基本包装，确保内容正确显示
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>封面分享</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: transparent;
    }
    .content-container {
      transform-origin: center center;
      transition: transform 0.2s ease;
    }
  </style>
</head>
<body>
  <div class="content-container">${html}</div>
  
  <script>
    // 与父窗口通信的脚本
    window.addEventListener('message', function(event) {
      if (event.data && event.data.type === 'set-scale') {
        const container = document.querySelector('.content-container');
        if (container) {
          container.style.transform = 'scale(' + event.data.scale + ')';
        }
      }
    });

    // 初始化后发送内容尺寸信息到父窗口
    window.addEventListener('load', function() {
      const container = document.querySelector('.content-container');
      if (container) {
        // 发送内容尺寸到父窗口
        window.parent.postMessage({
          type: 'content-size',
          width: container.scrollWidth || container.offsetWidth,
          height: container.scrollHeight || container.offsetHeight
        }, '*');
      }
    });
  </script>
</body>
</html>`;
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    const adjustedScale = Math.min(Math.max(newScale, minScale), maxScale);
    setScale(adjustedScale);
    
    // 向iframe发送缩放消息
    const iframe = document.getElementById('share-iframe');
    if (iframe && iframe.contentWindow) {
      iframe.contentWindow.postMessage({
        type: 'set-scale',
        scale: adjustedScale
      }, '*');
    }
  };

  // 重置缩放比例
  const resetScale = () => {
    handleScaleChange(1);
  };

  // 添加内联样式以确保页面不受全局样式影响
  const pageStyles = {
    width: '100vw',
    height: '100vh',
    margin: 0,
    padding: 0,
    overflow: 'hidden',
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    display: 'flex',
    flexDirection: 'column',
  };

  const headerStyles = {
    width: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 100,
    backgroundColor: 'transparent',
    borderBottom: '1px solid rgba(0,0,0,0.1)',
    boxShadow: '0 1px 2px rgba(0,0,0,0.05)'
  };

  const mainStyles = {
    width: '100%',
    height: 'calc(100% - 64px)',
    marginTop: '64px',
    overflow: 'hidden',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner className="h-12 w-12" />
        <span className="ml-3 text-lg">加载中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="text-destructive text-lg mb-4">{error}</div>
        <Button asChild variant="default">
          <Link to="/">返回首页</Link>
        </Button>
      </div>
    );
  }

  return (
    <div style={pageStyles}>
      {/* 顶部banner - 使用绝对定位确保贴紧顶部 */}
      <header style={headerStyles}>
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          {/* 左侧网站logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2">
              <Wand2 className="h-6 w-6 text-primary" />
              <span className="font-bold text-lg text-foreground">庭院 AI封面匠</span>
            </Link>
          </div>
          
          {/* 右侧返回首页按钮 */}
          <Button asChild variant="outline" size="sm" className="h-9">
            <Link to="/">
              <Home className="mr-2 h-4 w-4" />
              返回首页
            </Link>
          </Button>
        </div>
      </header>

      {/* 主要内容区域 - 使用内联样式确保正确的位置和大小 */}
      <main style={mainStyles}>
        <iframe
          id="share-iframe"
          srcDoc={htmlContent}
          title="分享封面预览"
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            margin: 0,
            padding: 0
          }}
          sandbox="allow-scripts"
          referrerPolicy="no-referrer"
        />
      </main>

      {/* 缩放控制器 - 使用垂直样式 */}
      <ZoomController 
        scale={scale}
        minScale={minScale}
        maxScale={maxScale}
        step={scaleStep}
        onScaleChange={handleScaleChange}
        onResetScale={resetScale}
      />
    </div>
  );
};

export default SharePage; 
/**
 * AI服务管理模块
 * 提供AI服务管理、测试和API密钥池查看功能
 */

window.aiServicesModule = (function() {
  // 私有变量
  let services = [];
  let currentPage = 1;
  let pageSize = 10;
  let totalPages = 1;
  let totalServices = 0;

  // 筛选条件
  let filters = {
    search: '',
    service: '',
    priority: '',
    weight: '',
    active: '',
    service_type: ''
  };

  // 初始化函数
  function init() {
    // 初始化模块
    setupEventListeners();
    loadServices();

    // 初始化筛选项
    setupFilters();
  }

  // 设置事件监听
  function setupEventListeners() {
    // 添加按钮
    document.getElementById('addAIServiceBtn').addEventListener('click', () => {
      showForm();
    });

    // 搜索按钮
    document.getElementById('aiServiceSearchBtn').addEventListener('click', () => {
      filters.search = document.getElementById('aiServiceSearchInput').value.trim();
      currentPage = 1;
      loadServices();
    });

    // 搜索输入框回车
    document.getElementById('aiServiceSearchInput').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        filters.search = e.target.value.trim();
        currentPage = 1;
        loadServices();
      }
    });

    // 筛选条件变化
    document.getElementById('serviceFilter').addEventListener('change', (e) => {
      filters.service = e.target.value;
      currentPage = 1;
      loadServices();
    });

    document.getElementById('priorityFilter').addEventListener('change', (e) => {
      filters.priority = e.target.value;
      currentPage = 1;
      loadServices();
    });

    document.getElementById('weightFilter').addEventListener('change', (e) => {
      filters.weight = e.target.value;
      currentPage = 1;
      loadServices();
    });

    document.getElementById('activeFilter').addEventListener('change', (e) => {
      filters.active = e.target.value;
      currentPage = 1;
      loadServices();
    });

    // 每页显示条数
    document.getElementById('aiServicesPageSize').addEventListener('change', (e) => {
      pageSize = parseInt(e.target.value);
      currentPage = 1;
      loadServices();
    });

    // 全选
    document.getElementById('selectAllServices').addEventListener('change', (e) => {
      const checkboxes = document.querySelectorAll('#aiServicesTableBody input[type="checkbox"]');
      checkboxes.forEach(cb => {
        cb.checked = e.target.checked;
      });
    });

    // 批量启用
    document.getElementById('batchActivateBtn').addEventListener('click', () => {
      batchUpdateStatus(true);
    });

    // 批量停用
    document.getElementById('batchDeactivateBtn').addEventListener('click', () => {
      batchUpdateStatus(false);
    });

    // 保存服务按钮
    document.getElementById('saveAIService').addEventListener('click', saveService);

    // 测试服务按钮
    document.getElementById('testAIService').addEventListener('click', testService);

    // 确认删除服务
    document.getElementById('confirmDeleteServiceBtn').addEventListener('click', () => {
      const serviceId = document.getElementById('deleteServiceId').value;
      if (serviceId) {
        deleteService(serviceId);
      }
    });
  }

  // 设置筛选器
  function setupFilters() {
    // 加载所有服务商到筛选选项
    fetch('/api/admin/ai-services/providers', {
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data && data.success) {
        // 刷新服务商筛选选项
        const serviceFilter = document.getElementById('serviceFilter');
        if (!serviceFilter) return; // 防御性检查
        
        const existingOptions = Array.from(serviceFilter.querySelectorAll('option')).map(opt => opt.value);

        // 保留第一个"所有服务商"选项
        // 添加防御性检查，确保data.providers存在
        const providers = data.providers || [];
        providers.forEach(provider => {
          if (!existingOptions.includes(provider)) {
            const option = document.createElement('option');
            option.value = provider;
            option.textContent = provider;
            serviceFilter.appendChild(option);
          }
        });
      }
    })
    .catch(error => {
      // 静默处理错误
    });

    // 设置服务类型筛选器的监听
    document.getElementById('serviceTypeFilter').addEventListener('change', (e) => {
      filters.service_type = e.target.value;
      currentPage = 1;
      loadServices();
    });
  }

  // 加载AI服务列表
  function loadServices() {
    document.getElementById('aiServicesTableBody').innerHTML = '<tr><td colspan="12" class="text-center">加载中...</td></tr>';

    const url = new URL('/api/admin/ai-services', window.location.origin);
    url.searchParams.append('page', currentPage);
    url.searchParams.append('limit', pageSize);

    // 添加筛选条件
    if (filters.search) url.searchParams.append('search', filters.search);
    if (filters.service) url.searchParams.append('service', filters.service);
    if (filters.priority) url.searchParams.append('priority', filters.priority);
    if (filters.weight) url.searchParams.append('weight', filters.weight);
    if (filters.active) url.searchParams.append('is_active', filters.active);
    if (filters.service_type) url.searchParams.append('service_type', filters.service_type);

    fetch(url, {
      method: 'GET',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        services = data.data.services || [];
        totalServices = data.data.total || 0;
        totalPages = Math.ceil(totalServices / pageSize);

        renderServicesList();
        renderPagination();
      } else {
        document.getElementById('aiServicesTableBody').innerHTML =
          `<tr><td colspan="12" class="text-center text-danger">${data.message || '加载失败'}</td></tr>`;
      }
    })
    .catch(error => {
      // 加载失败，显示错误信息
      document.getElementById('aiServicesTableBody').innerHTML =
        '<tr><td colspan="12" class="text-center text-danger">加载失败，请稍后再试</td></tr>';
    });
  }

  // 渲染服务列表
  function renderServicesList() {
    const tableBody = document.getElementById('aiServicesTableBody');
    tableBody.innerHTML = '';

    if (services.length === 0) {
      tableBody.innerHTML = '<tr><td colspan="12" class="text-center">暂无AI服务配置</td></tr>';
      return;
    }

    services.forEach((service, index) => {
      const rowNumber = (currentPage - 1) * pageSize + index + 1;
      const tr = document.createElement('tr');

      // 根据服务商获取对应图标
      let serviceIcon = '<i class="bi bi-robot"></i>';
      if (service.service) {
        switch(service.service.toLowerCase()) {
          case 'deepseek':
            serviceIcon = '<i class="bi bi-lightning-charge-fill text-primary"></i>';
            break;
          case '豆包':
            serviceIcon = '<i class="bi bi-cloud-fill text-success"></i>';
            break;
          case '阿里百炼':
            serviceIcon = '<i class="bi bi-emoji-sunglasses-fill text-warning"></i>';
            break;
          default:
            serviceIcon = '<i class="bi bi-hdd-network-fill"></i>';
        }
      }

      tr.innerHTML = `
        <td class="text-center">
          <input type="checkbox" class="form-check-input service-checkbox" value="${service.id}">
        </td>
        <td>${rowNumber}</td>
        <td>${serviceIcon} ${service.service || '-'}</td>
        <td>${service.service_name}</td>
        <td>${service.model_name}</td>
        <td>
          <span class="d-inline-block text-truncate" style="max-width: 100px;" title="${maskApiKey(service.api_key)}">
            ${maskApiKey(service.api_key)}
          </span>
        </td>
        <td>
          <span class="badge ${service.is_active ? 'bg-success' : 'bg-secondary'}">
            ${service.is_active ? '是' : '否'}
          </span>
        </td>
        <td>${service.weight || 1}</td>
        <td>${service.priority || 1}</td>
        <td>${service.max_concurrent_requests || '不限'}</td>
        <td>
          <span class="badge ${getServiceTypeBadgeClass(service.service_type)}">${service.service_type || '文本AI'}</span>
        </td>
        <td>
          <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-sm ${service.is_active ? 'btn-warning' : 'btn-success'} activate-btn" data-id="${service.id}" title="${service.is_active ? '停用' : '启用'}">
              <i class="bi ${service.is_active ? 'bi-x-circle' : 'bi-check-circle'}"></i>
            </button>
            <button type="button" class="btn btn-sm btn-primary edit-btn" data-id="${service.id}" title="编辑">
              <i class="bi bi-pencil"></i>
            </button>
            <button type="button" class="btn btn-sm btn-info view-btn" data-id="${service.id}" title="查看">
              <i class="bi bi-eye"></i>
            </button>
            <button type="button" class="btn btn-sm btn-warning config-btn" data-id="${service.id}" title="参数配置">
              <i class="bi bi-gear"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="${service.id}" data-name="${service.service_name}" title="删除">
              <i class="bi bi-trash"></i>
            </button>
            <button type="button" class="btn btn-sm btn-secondary test-btn" data-id="${service.id}" title="测试">
              <i class="bi bi-lightning"></i>
            </button>
          </div>
        </td>
      `;

      // 添加按钮事件
      tr.querySelector('.activate-btn').addEventListener('click', (e) => {
        const id = e.currentTarget.getAttribute('data-id');
        activateService(id);
      });

      tr.querySelector('.edit-btn').addEventListener('click', (e) => {
        const id = e.currentTarget.getAttribute('data-id');
        editService(id);
      });

      tr.querySelector('.view-btn').addEventListener('click', (e) => {
        const id = e.currentTarget.getAttribute('data-id');
        viewService(id);
      });

      tr.querySelector('.delete-btn').addEventListener('click', (e) => {
        const id = e.currentTarget.getAttribute('data-id');
        const name = e.currentTarget.getAttribute('data-name');
        confirmDeleteService(id, name);
      });

      tr.querySelector('.test-btn').addEventListener('click', (e) => {
        const id = e.currentTarget.getAttribute('data-id');
        testService(id);
      });

      tr.querySelector('.config-btn').addEventListener('click', (e) => {
        const id = e.currentTarget.getAttribute('data-id');
        configureParameters(id);
      });

      tableBody.appendChild(tr);
    });
  }

  // 渲染分页
  function renderPagination() {
    const pagination = document.getElementById('aiServicesPagination');
    pagination.innerHTML = '';

    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    const prevLink = document.createElement('a');
    prevLink.className = 'page-link';
    prevLink.href = '#';
    prevLink.textContent = '上一页';
    prevLink.addEventListener('click', (e) => {
      e.preventDefault();
      if (currentPage > 1) {
        currentPage--;
        loadServices();
      }
    });
    prevLi.appendChild(prevLink);
    pagination.appendChild(prevLi);

    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);

    for (let i = startPage; i <= endPage; i++) {
      const pageLi = document.createElement('li');
      pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
      const pageLink = document.createElement('a');
      pageLink.className = 'page-link';
      pageLink.href = '#';
      pageLink.textContent = i;
      pageLink.addEventListener('click', (e) => {
        e.preventDefault();
        currentPage = i;
        loadServices();
      });
      pageLi.appendChild(pageLink);
      pagination.appendChild(pageLi);
    }

    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    const nextLink = document.createElement('a');
    nextLink.className = 'page-link';
    nextLink.href = '#';
    nextLink.textContent = '下一页';
    nextLink.addEventListener('click', (e) => {
      e.preventDefault();
      if (currentPage < totalPages) {
        currentPage++;
        loadServices();
      }
    });
    nextLi.appendChild(nextLink);
    pagination.appendChild(nextLi);
  }

  // API密钥掩码处理
  function maskApiKey(apiKey) {
    if (!apiKey) return '******';
    if (apiKey.length <= 8) return '******';

    return apiKey.substring(0, 4) + '****' + apiKey.substring(apiKey.length - 4);
  }

  // 显示表单
  function showForm(serviceId = null) {
    const form = document.getElementById('aiServiceForm');
    form.reset();
    document.getElementById('serviceId').value = serviceId || '';

    const modalTitle = document.getElementById('aiServiceModalTitle');
    const testButton = document.getElementById('testAIService');

    if (serviceId) {
      modalTitle.textContent = '编辑AI服务';
      testButton.style.display = 'block';
      document.getElementById('apiKeyNote').textContent = '如果不修改密钥，请留空';

      // 获取服务详情
      fetch(`/api/admin/ai-services/${serviceId}`, {
        headers: getAuthHeaders()
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const service = data.data.service;

          document.getElementById('service').value = service.service || '';
          document.getElementById('serviceName').value = service.service_name || '';
          document.getElementById('baseUrl').value = service.base_url || '';
          document.getElementById('modelName').value = service.model_name || '';
          document.getElementById('weight').value = service.weight || 1;
          document.getElementById('priority').value = service.priority || 1;
          document.getElementById('maxConcurrentRequests').value = service.max_concurrent_requests || 0;
          document.getElementById('serviceType').value = service.service_type || '文本AI';
          document.getElementById('requestFormat').value = service.request_format || '';
          document.getElementById('responseFormat').value = service.response_format || '';

          const apiKeyField = document.getElementById('apiKey');
          apiKeyField.required = false;
          apiKeyField.value = '';
        } else {
          alert('获取AI服务详情失败: ' + (data.message || '未知错误'));
        }
      })
      .catch(error => {
        logger.error('获取AI服务详情失败:', error);
        alert('获取AI服务详情失败，请稍后再试');
      });
    } else {
      modalTitle.textContent = '添加AI服务';
      testButton.style.display = 'none';
      document.getElementById('apiKeyNote').textContent = 'API密钥是必填项';

      // 设置默认值
      document.getElementById('weight').value = 1;
      document.getElementById('priority').value = 1;
      document.getElementById('maxConcurrentRequests').value = 5;
      document.getElementById('serviceType').value = '文本AI';

      const apiKeyField = document.getElementById('apiKey');
      apiKeyField.required = true;
      apiKeyField.value = '';
    }

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('aiServiceModal'));
    modal.show();
  }

  // 查看服务
  function viewService(serviceId) {
    showForm(serviceId);

    // 禁用所有输入
    const inputs = document.querySelectorAll('#aiServiceForm input, #aiServiceForm select, #aiServiceForm textarea');
    inputs.forEach(input => {
      input.disabled = true;
    });

    document.getElementById('saveAIService').style.display = 'none';
    document.getElementById('testAIService').style.display = 'none';

    // 添加"关闭"按钮
    const modalFooter = document.querySelector('#aiServiceModal .modal-footer');
    const closeBtn = document.querySelector('#aiServiceModal .modal-footer .btn-secondary');
    closeBtn.textContent = '关闭';

    // 修改标题
    document.getElementById('aiServiceModalTitle').textContent = '查看AI服务';
  }

  // 编辑服务
  function editService(serviceId) {
    showForm(serviceId);
  }

  // 确认删除服务
  function confirmDeleteService(serviceId, serviceName) {
    document.getElementById('deleteServiceId').value = serviceId;
    document.getElementById('deleteServiceName').textContent = serviceName;

    const modal = new bootstrap.Modal(document.getElementById('deleteServiceModal'));
    modal.show();
  }

  // 删除服务
  function deleteService(serviceId) {
    fetch(`/api/admin/ai-services/${serviceId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('AI服务删除成功');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteServiceModal'));
        modal.hide();

        // 重新加载列表
        loadServices();
      } else {
        alert('删除AI服务失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      logger.error('删除AI服务失败:', error);
      alert('删除AI服务失败，请稍后再试');
    });
  }

  // 启用/停用服务
  function activateService(serviceId) {
    fetch(`/api/admin/ai-services/${serviceId}/activate`, {
      method: 'PUT',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(`AI服务${data.data.service.is_active ? '启用' : '停用'}成功`);
        loadServices();
      } else {
        alert('操作失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('操作失败:', error);
      alert('操作失败，请稍后再试');
    });
  }

  // 批量更新状态
  function batchUpdateStatus(isActive) {
    const checkboxes = document.querySelectorAll('#aiServicesTableBody input[type="checkbox"]:checked');
    const serviceIds = Array.from(checkboxes).map(cb => cb.value);

    if (serviceIds.length === 0) {
      alert('请先选择要操作的服务');
      return;
    }

    if (!confirm(`确定要批量${isActive ? '启用' : '停用'}选中的 ${serviceIds.length} 个服务吗？`)) {
      return;
    }

    fetch('/api/admin/ai-services/batch-update', {
      method: 'PUT',
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        serviceIds,
        isActive
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(`批量${isActive ? '启用' : '停用'}成功，已更新 ${data.data.updatedCount} 个服务`);
        loadServices();
      } else {
        alert('操作失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('批量操作失败:', error);
      alert('批量操作失败，请稍后再试');
    });
  }

  // 保存服务
  function saveService() {
    const serviceId = document.getElementById('serviceId').value;
    const service = document.getElementById('service').value;
    const serviceName = document.getElementById('serviceName').value;
    const baseUrl = document.getElementById('baseUrl').value;
    const apiKey = document.getElementById('apiKey').value;
    const modelName = document.getElementById('modelName').value;
    const weight = document.getElementById('weight').value;
    const priority = document.getElementById('priority').value;
    const maxConcurrentRequests = document.getElementById('maxConcurrentRequests').value;
    const serviceType = document.getElementById('serviceType').value;
    const requestFormat = document.getElementById('requestFormat').value;
    const responseFormat = document.getElementById('responseFormat').value;

    // 验证
    if (!serviceName || !baseUrl || !modelName) {
      alert('请填写服务名称、基础URL和模型名称');
      return;
    }

    if (!serviceId && !apiKey) {
      alert('新增服务时API密钥为必填项');
      return;
    }

    if (!service) {
      alert('请选择AI服务商');
      return;
    }

    // 构建请求数据
    const data = {
      service,
      service_name: serviceName,
      base_url: baseUrl,
      model_name: modelName,
      weight: parseInt(weight) || 1,
      priority: parseInt(priority) || 1,
      max_concurrent_requests: parseInt(maxConcurrentRequests) || 0,
      service_type: serviceType,
      request_format: requestFormat,
      response_format: responseFormat
    };

    // 仅当API密钥填写时才包含在请求中
    if (apiKey) {
      data.api_key = apiKey;
    }

    const method = serviceId ? 'PUT' : 'POST';
    const url = serviceId ? `/api/admin/ai-services/${serviceId}` : '/api/admin/ai-services';

    fetch(url, {
      method,
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(`AI服务${serviceId ? '更新' : '添加'}成功`);

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('aiServiceModal'));
        modal.hide();

        // 重新加载列表
        loadServices();
      } else {
        alert(`${serviceId ? '更新' : '添加'}AI服务失败: ` + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error(`${serviceId ? '更新' : '添加'}AI服务失败:`, error);
      alert(`${serviceId ? '更新' : '添加'}AI服务失败，请稍后再试`);
    });
  }

  // 测试服务
  function testService(serviceId) {
    serviceId = serviceId || document.getElementById('serviceId').value;

    if (!serviceId) {
      alert('请先保存服务');
      return;
    }

    // 获取服务信息
    const service = services.find(s => s.id == serviceId);
    if (!service) {
      alert('未找到服务信息');
      return;
    }

    // 打开聊天测试界面
    openChatTestModal(service);
  }

  // 打开聊天测试界面
  function openChatTestModal(service) {
    // 设置服务名称
    document.getElementById('testServiceName').textContent = service.service_name || service.service;

    // 清空聊天记录
    clearChatMessages();

    // 清空API详情
    clearApiDetails();

    // 重置文件上传
    resetFileUpload();

    // 设置当前测试的服务ID
    window.currentTestServiceId = service.id;

    // 显示聊天界面
    const chatContainer = document.getElementById('aiChatTestModal');
    chatContainer.classList.remove('d-none');

    // 聚焦到输入框
    setTimeout(() => {
      document.getElementById('chatInput').focus();
    }, 100);

    // 设置事件监听器
    setupChatEventListeners();
  }

  // 设置聊天事件监听器
  function setupChatEventListeners() {
    // 避免重复绑定事件
    if (window.chatEventListenersSetup) {
      return;
    }
    window.chatEventListenersSetup = true;

    // 发送消息按钮
    document.getElementById('sendMessageBtn').addEventListener('click', sendChatMessage);

    // 输入框回车发送和自动调整高度
    const chatInput = document.getElementById('chatInput');
    chatInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendChatMessage();
      }
    });

    // 输入框自动调整高度
    chatInput.addEventListener('input', (e) => {
      e.target.style.height = 'auto';
      e.target.style.height = Math.min(e.target.scrollHeight, 120) + 'px';
    });

    // 文件上传按钮
    document.getElementById('attachFileBtn').addEventListener('click', () => {
      document.getElementById('fileInput').click();
    });

    // 图片上传按钮
    document.getElementById('attachImageBtn').addEventListener('click', () => {
      document.getElementById('imageInput').click();
    });

    // 文件选择事件
    document.getElementById('fileInput').addEventListener('change', handleFileSelect);
    document.getElementById('imageInput').addEventListener('change', handleImageSelect);

    // 清空对话按钮
    document.getElementById('clearChatBtn').addEventListener('click', () => {
      if (confirm('确定要清空对话记录吗？')) {
        clearChatMessages();
        clearApiDetails();
        resetFileUpload();
      }
    });

    // 关闭聊天界面按钮
    document.getElementById('closeChatBtn').addEventListener('click', () => {
      const chatContainer = document.getElementById('aiChatTestModal');
      chatContainer.classList.add('d-none');
    });
  }

  // 发送聊天消息
  function sendChatMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();
    const files = getSelectedFiles();

    if (!message && files.length === 0) {
      return;
    }

    // 添加用户消息到聊天区域
    addChatMessage('user', message, files);

    // 清空输入框和文件
    input.value = '';
    resetFileUpload();

    // 显示AI正在回复的状态
    const loadingMessageId = addLoadingMessage();

    // 调用AI API
    callAIService(message, files, loadingMessageId);
  }

  // 添加聊天消息 - 简化版本，直接显示完整内容
  function addChatMessage(type, message, files = [], timestamp = null) {
    console.log(`添加${type}消息，内容长度:`, message ? message.length : 0);

    const chatMessages = document.getElementById('chatMessages');

    // 移除欢迎消息（如果存在）
    const welcomeMsg = chatMessages.querySelector('.ai-chat-welcome');
    if (welcomeMsg) {
      welcomeMsg.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `ai-message ${type}`;

    const time = timestamp || new Date().toLocaleTimeString();

    // 创建消息头像
    const messageAvatar = document.createElement('div');
    messageAvatar.className = 'ai-message-avatar';
    messageAvatar.innerHTML = type === 'user' ? '<i class="bi bi-person"></i>' : '<i class="bi bi-robot"></i>';

    // 创建消息内容容器
    const messageContent = document.createElement('div');
    messageContent.className = 'ai-message-content';

    // 添加文本消息（优化显示格式，参考主流AI聊天界面）
    if (message) {
      const messageText = document.createElement('div');
      messageText.className = 'ai-message-text';

      // 设置样式，参考主流AI聊天界面
      messageText.style.margin = '0';
      messageText.style.lineHeight = '1.6';
      messageText.style.fontSize = '14px';
      messageText.style.color = type === 'user' ? 'inherit' : '#333';
      messageText.style.whiteSpace = 'pre-wrap';
      messageText.style.wordWrap = 'break-word';
      messageText.style.overflow = 'visible';
      messageText.style.fontFamily = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';

      // 设置内容，保持自然换行
      messageText.textContent = message;

      console.log('设置后的内容长度:', messageText.textContent.length);
      console.log('原始内容长度:', message.length);

      messageContent.appendChild(messageText);
    }

    // 添加文件附件
    if (files && files.length > 0) {
      files.forEach(file => {
        if (file.type && file.type.startsWith('image/')) {
          const img = document.createElement('img');
          img.src = file.url || URL.createObjectURL(file);
          img.alt = file.name;
          img.style.maxWidth = '200px';
          img.style.maxHeight = '200px';
          img.style.borderRadius = '8px';
          img.style.marginTop = '8px';
          messageContent.appendChild(img);
        } else {
          const fileDiv = document.createElement('div');
          fileDiv.className = 'file-attachment';
          fileDiv.innerHTML = `
            <i class="bi bi-file-earmark"></i>
            <div>
              <div class="file-name">${file.name}</div>
              <div class="file-size">${formatFileSize(file.size)}</div>
            </div>
          `;
          messageContent.appendChild(fileDiv);
        }
      });
    }

    // 添加时间戳
    const messageTime = document.createElement('div');
    messageTime.className = 'ai-message-time';
    messageTime.textContent = time;
    messageContent.appendChild(messageTime);

    messageDiv.appendChild(messageAvatar);
    messageDiv.appendChild(messageContent);
    chatMessages.appendChild(messageDiv);

    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageDiv;
  }

  // 添加加载消息
  function addLoadingMessage() {
    const chatMessages = document.getElementById('chatMessages');

    // 移除欢迎消息（如果存在）
    const welcomeMsg = chatMessages.querySelector('.ai-chat-welcome');
    if (welcomeMsg) {
      welcomeMsg.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = 'ai-message ai';
    messageDiv.id = 'loading-message-' + Date.now();

    messageDiv.innerHTML = `
      <div class="ai-message-avatar">
        <i class="bi bi-robot"></i>
      </div>
      <div class="ai-message-content">
        <div class="ai-loading-message">
          <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
          AI正在思考中...
        </div>
      </div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageDiv.id;
  }

  // 移除加载消息
  function removeLoadingMessage(messageId) {
    const loadingMessage = document.getElementById(messageId);
    if (loadingMessage) {
      loadingMessage.remove();
    }
  }

  // 提取AI回复文本内容 - 重写版本，确保完整内容显示
  function extractAIResponse(responseData) {
    console.log('原始响应数据:', responseData);

    if (!responseData) {
      return '收到回复，但内容为空';
    }

    // 如果是字符串，直接返回完整内容
    if (typeof responseData === 'string') {
      console.log('字符串响应，长度:', responseData.length);
      return responseData;
    }

    // 如果是对象，尝试提取文本内容
    if (typeof responseData === 'object') {
      // 常见的AI回复字段名，按优先级排序
      const possibleFields = ['content', 'text', 'message', 'response', 'answer', 'reply', 'output'];

      // 首先检查直接字段
      for (const field of possibleFields) {
        if (responseData[field] && typeof responseData[field] === 'string') {
          console.log(`找到字段 ${field}，内容长度:`, responseData[field].length);
          return responseData[field];
        }
      }

      // 检查choices数组格式（OpenAI格式）
      if (responseData.choices && Array.isArray(responseData.choices) && responseData.choices.length > 0) {
        const choice = responseData.choices[0];
        if (choice.message && choice.message.content) {
          console.log('OpenAI格式响应，内容长度:', choice.message.content.length);
          return choice.message.content;
        }
        if (choice.text) {
          console.log('OpenAI文本格式响应，内容长度:', choice.text.length);
          return choice.text;
        }
      }

      // 检查嵌套的data字段
      if (responseData.data && typeof responseData.data === 'object') {
        for (const field of possibleFields) {
          if (responseData.data[field] && typeof responseData.data[field] === 'string') {
            console.log(`在data中找到字段 ${field}，内容长度:`, responseData.data[field].length);
            return responseData.data[field];
          }
        }
      }

      // 如果都没找到，返回完整的JSON字符串
      try {
        const jsonString = JSON.stringify(responseData, null, 2);
        console.log('返回完整JSON，长度:', jsonString.length);
        return jsonString;
      } catch (e) {
        console.error('JSON序列化失败:', e);
        return '无法解析AI回复内容';
      }
    }

    const stringResult = String(responseData);
    console.log('转换为字符串，长度:', stringResult.length);
    return stringResult;
  }

  // 调用AI服务
  function callAIService(message, files, loadingMessageId) {
    const serviceId = window.currentTestServiceId;

    // 显示API请求详情
    showApiRequest(message, files);

    // 构建请求数据
    const requestData = {
      message: message,
      files: files.map(file => ({
        name: file.name,
        type: file.type,
        size: file.size
      }))
    };

    fetch(`/api/admin/ai-services/${serviceId}/chat-test`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
      // 移除加载消息
      removeLoadingMessage(loadingMessageId);

      // 显示API响应详情
      showApiResponse(data);

      if (data.success) {
        // 提取AI回复的实际内容
        console.log('完整API响应数据:', data);
        console.log('data.data.response:', data.data.response);

        // 使用extractAIResponse函数提取AI的实际回复内容
        const aiResponse = extractAIResponse(data.data.response);
        console.log('提取的AI回复内容:', aiResponse);
        console.log('AI回复内容长度:', aiResponse ? aiResponse.length : 0);

        // 添加AI回复到聊天消息
        addChatMessage('ai', aiResponse);
      } else {
        // 添加错误消息
        addChatMessage('ai', `错误: ${data.message || '未知错误'}`);
      }
    })
    .catch(error => {
      console.error('AI服务调用失败:', error);

      // 移除加载消息
      removeLoadingMessage(loadingMessageId);

      // 显示错误响应
      showApiResponse({
        success: false,
        error: error.message || '网络请求失败'
      });

      // 添加错误消息
      addChatMessage('ai', `网络错误: ${error.message || '请求失败，请稍后再试'}`);
    });
  }

  // 显示API请求详情
  function showApiRequest(message, files) {
    const apiDetails = document.getElementById('apiDetails');

    // 移除占位符（如果存在）
    const placeholder = apiDetails.querySelector('.ai-sidebar-placeholder');
    if (placeholder) {
      placeholder.remove();
    }

    const requestSection = document.createElement('div');
    requestSection.className = 'ai-api-section';

    // 创建标题
    const title = document.createElement('div');
    title.className = 'ai-api-header';
    title.innerHTML = `<i class="bi bi-arrow-up-circle text-primary"></i> 请求 (${new Date().toLocaleTimeString()})`;

    // 创建内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'ai-api-content';

    // 创建pre元素并使用textContent确保纯文本显示
    const pre = document.createElement('pre');
    pre.textContent = JSON.stringify({
      message: message,
      files: files.map(f => ({ name: f.name, type: f.type, size: f.size })),
      timestamp: new Date().toISOString()
    }, null, 2);

    contentDiv.appendChild(pre);
    requestSection.appendChild(title);
    requestSection.appendChild(contentDiv);
    apiDetails.appendChild(requestSection);
    apiDetails.scrollTop = apiDetails.scrollHeight;
  }

  // 显示API响应详情
  function showApiResponse(response) {
    const apiDetails = document.getElementById('apiDetails');

    const responseSection = document.createElement('div');
    responseSection.className = 'ai-api-section';

    // 创建标题
    const title = document.createElement('div');
    title.className = 'ai-api-header';
    title.innerHTML = `<i class="bi bi-arrow-down-circle ${response.success ? 'text-success' : 'text-danger'}"></i> 响应 (${new Date().toLocaleTimeString()})`;

    // 创建内容容器
    const contentDiv = document.createElement('div');
    contentDiv.className = 'ai-api-content';

    // 创建pre元素并使用textContent确保纯文本显示
    const pre = document.createElement('pre');
    pre.textContent = JSON.stringify(response, null, 2);

    contentDiv.appendChild(pre);
    responseSection.appendChild(title);
    responseSection.appendChild(contentDiv);
    apiDetails.appendChild(responseSection);
    apiDetails.scrollTop = apiDetails.scrollHeight;
  }

  // 获取认证头信息
  function getAuthHeaders() {
    // 优先使用utils.js中的全局函数
    if (typeof window.getAuthHeaders === 'function') {
      return window.getAuthHeaders();
    }
    
    // 如果全局函数不可用，使用本地实现
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    };
  }

  // 文件处理相关函数
  let selectedFiles = [];

  // 处理文件选择
  function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    files.forEach(file => {
      selectedFiles.push(file);
    });
    updateFilePreview();
    event.target.value = ''; // 清空input，允许重复选择同一文件
  }

  // 处理图片选择
  function handleImageSelect(event) {
    const files = Array.from(event.target.files);
    files.forEach(file => {
      selectedFiles.push(file);
    });
    updateFilePreview();
    event.target.value = ''; // 清空input，允许重复选择同一文件
  }

  // 更新文件预览
  function updateFilePreview() {
    const previewArea = document.getElementById('filePreviewArea');
    const previewList = document.getElementById('filePreviewList');

    if (selectedFiles.length === 0) {
      previewArea.classList.add('d-none');
      return;
    }

    previewArea.classList.remove('d-none');
    previewList.innerHTML = '';

    selectedFiles.forEach((file, index) => {
      const previewItem = document.createElement('div');
      previewItem.className = 'ai-file-item';

      if (file.type.startsWith('image/')) {
        const img = document.createElement('img');
        img.className = 'ai-file-preview';
        img.src = URL.createObjectURL(file);
        img.alt = file.name;
        previewItem.appendChild(img);
      } else {
        const fileInfo = document.createElement('div');
        fileInfo.className = 'ai-file-info';
        fileInfo.innerHTML = `
          <i class="bi bi-file-earmark me-2"></i>
          <div>
            <div class="file-name">${file.name}</div>
            <div class="file-size text-muted">${formatFileSize(file.size)}</div>
          </div>
        `;
        previewItem.appendChild(fileInfo);
      }

      const removeBtn = document.createElement('button');
      removeBtn.className = 'ai-file-remove';
      removeBtn.innerHTML = '×';
      removeBtn.onclick = () => removeFile(index);
      previewItem.appendChild(removeBtn);

      previewList.appendChild(previewItem);
    });
  }

  // 移除文件
  function removeFile(index) {
    selectedFiles.splice(index, 1);
    updateFilePreview();
  }

  // 获取选中的文件
  function getSelectedFiles() {
    return selectedFiles;
  }

  // 重置文件上传
  function resetFileUpload() {
    selectedFiles = [];
    updateFilePreview();
    document.getElementById('fileInput').value = '';
    document.getElementById('imageInput').value = '';
  }

  // 格式化文件大小
  function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 清空聊天消息
  function clearChatMessages() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.innerHTML = `
      <div class="ai-chat-welcome">
        <div class="ai-chat-welcome-icon">
          <i class="bi bi-chat-dots"></i>
        </div>
        <h6>开始与AI对话测试</h6>
        <p class="text-muted">发送消息来测试AI服务的响应</p>
      </div>
    `;
  }

  // 清空API详情
  function clearApiDetails() {
    const apiDetails = document.getElementById('apiDetails');
    apiDetails.innerHTML = `
      <div class="ai-sidebar-placeholder">
        <i class="bi bi-info-circle"></i>
        <p>API调用详情将在这里显示</p>
      </div>
    `;
  }

  // 获取服务类型对应的徽章样式
  function getServiceTypeBadgeClass(serviceType) {
    if (!serviceType) return 'bg-secondary';

    switch(serviceType) {
      case '文本AI':
        return 'bg-primary';
      case '图像AI':
        return 'bg-success';
      case '音频AI':
        return 'bg-info';
      default:
        return 'bg-secondary';
    }
  }

  // 参数配置功能
  function configureParameters(serviceId) {
    // 获取参数配置
    fetch(`/api/admin/ai-services/${serviceId}/parameters`, {
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showParameterConfigModal(data.data);
      } else {
        alert('获取参数配置失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      // 获取参数配置失败
      alert('获取参数配置失败，请稍后再试');
    });
  }

  // 显示参数配置模态框
  function showParameterConfigModal(serviceData) {
    const { service_id, service_name, parameters } = serviceData;

    // 判断服务商类型，决定显示哪些参数
    const serviceName = service_name.toLowerCase();
    const isDeepSeek = serviceName.includes('deepseek');
    const isQwen = serviceName.includes('qwen') || serviceName.includes('阿里') || serviceName.includes('百炼');
    const isDoubao = serviceName.includes('豆包') || serviceName.includes('doubao');

    // 生成参数表单HTML
    let parametersHtml = `
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Temperature (创意度) <span class="text-success">✓通用</span></label>
            <input type="number" class="form-control" name="temperature"
                   value="${parameters.temperature || 0.7}" step="0.1" min="0" max="2">
            <div class="form-text">控制生成文本的多样性。值越高越有创意，值越低越确定。推荐0.7</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Max Tokens (最大长度) <span class="text-success">✓通用</span></label>
            <input type="number" class="form-control" name="max_tokens"
                   value="${parameters.max_tokens || 2000}" min="100" max="32000">
            <div class="form-text">控制输出长度和成本。封面生成推荐2000-8192</div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Top P (核采样) <span class="text-success">✓通用</span></label>
            <input type="number" class="form-control" name="top_p"
                   value="${parameters.top_p || 0.8}" step="0.1" min="0" max="1">
            <div class="form-text">控制生成质量。值越高越多样，值越低越确定。推荐0.8</div>
          </div>
        </div>`;

    // 根据服务商类型添加特有参数
    if (isQwen) {
      // 阿里百炼特有参数
      parametersHtml += `
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Top K (采样候选集) <span class="text-primary">阿里百炼专用</span></label>
            <input type="number" class="form-control" name="top_k"
                   value="${parameters.top_k || 20}" min="0" max="100">
            <div class="form-text">采样候选集大小。值越大随机性越高，推荐20</div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Repetition Penalty (重复惩罚) <span class="text-primary">阿里百炼专用</span></label>
            <input type="number" class="form-control" name="repetition_penalty"
                   value="${parameters.repetition_penalty || 1.05}" step="0.01" min="0.01" max="3">
            <div class="form-text">控制连续序列重复度。值越高重复度越低，推荐1.05</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Presence Penalty (内容重复控制) <span class="text-primary">阿里百炼专用</span></label>
            <input type="number" class="form-control" name="presence_penalty"
                   value="${parameters.presence_penalty || 0}" step="0.1" min="-2" max="2">
            <div class="form-text">正数减少内容重复，负数增加重复。推荐0-1.5</div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Seed (随机种子) <span class="text-primary">阿里百炼专用</span></label>
            <div class="d-flex align-items-center gap-2 mb-2">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="enableSeed" 
                       ${parameters.seed !== undefined && parameters.seed !== null ? 'checked' : ''}>
                <label class="form-check-label" for="enableSeed">启用随机种子</label>
              </div>
            </div>
            <input type="number" class="form-control" name="seed" id="seedInput"
                   value="${parameters.seed !== undefined && parameters.seed !== null ? parameters.seed : 1234}" 
                   min="0" max="2147483647" 
                   ${parameters.seed !== undefined && parameters.seed !== null ? '' : 'disabled'}>
            <div class="form-text">设置随机种子使结果可复现。相同seed产生相同结果。关闭时不使用seed参数</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Enable Search (联网搜索) <span class="text-primary">阿里百炼专用</span></label>
            <select class="form-select" name="enable_search">
              <option value="false" ${(parameters.enable_search === false || !parameters.enable_search) ? 'selected' : ''}>关闭</option>
              <option value="true" ${parameters.enable_search === true ? 'selected' : ''}>开启</option>
            </select>
            <div class="form-text">是否启用联网搜索功能增强生成效果</div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Response Format (返回格式) <span class="text-primary">阿里百炼专用</span></label>
            <select class="form-select" name="response_format">
              <option value="text" ${(parameters.response_format === 'text' || !parameters.response_format) ? 'selected' : ''}>文本格式</option>
              <option value="json_object" ${parameters.response_format === 'json_object' ? 'selected' : ''}>JSON格式</option>
            </select>
            <div class="form-text">控制返回内容格式，JSON格式适合结构化输出</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Result Format (数据格式) <span class="text-primary">阿里百炼专用</span></label>
            <select class="form-select" name="result_format">
              <option value="text" ${parameters.result_format === 'text' ? 'selected' : ''}>文本格式</option>
              <option value="message" ${(parameters.result_format === 'message' || !parameters.result_format) ? 'selected' : ''}>消息格式</option>
            </select>
            <div class="form-text">推荐使用消息格式，便于多轮对话</div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Incremental Output (增量输出) <span class="text-primary">阿里百炼专用</span></label>
            <select class="form-select" name="incremental_output">
              <option value="false" ${(parameters.incremental_output === false || !parameters.incremental_output) ? 'selected' : ''}>关闭</option>
              <option value="true" ${parameters.incremental_output === true ? 'selected' : ''}>开启</option>
            </select>
            <div class="form-text">是否启用增量输出模式</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Stop Words (停止词) <span class="text-primary">阿里百炼专用</span></label>
            <input type="text" class="form-control" name="stop"
                   value="${Array.isArray(parameters.stop) ? parameters.stop.join(',') : (parameters.stop || '')}"
                   placeholder="多个停止词用逗号分隔">
            <div class="form-text">设置停止词控制生成结束，如：结束,完毕</div>
          </div>
        </div>`;
    } else if (isDoubao) {
      // 豆包特有参数
      parametersHtml += `
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Stream (流式输出) <span class="text-info">豆包专用</span></label>
            <select class="form-select" name="stream">
              <option value="false" ${(parameters.stream === false || !parameters.stream) ? 'selected' : ''}>关闭</option>
              <option value="true" ${parameters.stream === true ? 'selected' : ''}>开启</option>
            </select>
            <div class="form-text">是否启用流式输出，实时返回生成内容</div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Max Completion Tokens (最大完成长度) <span class="text-info">豆包专用</span></label>
            <input type="number" class="form-control" name="max_completion_tokens"
                   value="${parameters.max_completion_tokens || 2000}" min="100" max="32000">
            <div class="form-text">控制完成部分的最大token数，与max_tokens配合使用</div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Stop Words (停止词) <span class="text-info">豆包专用</span></label>
            <input type="text" class="form-control" name="stop"
                   value="${Array.isArray(parameters.stop) ? parameters.stop.join(',') : (parameters.stop || '')}"
                   placeholder="多个停止词用逗号分隔">
            <div class="form-text">设置停止词控制生成结束，如：结束,完毕</div>
          </div>
        </div>`;
    } else if (isDeepSeek) {
      // DeepSeek特有参数
      parametersHtml += `
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Presence Penalty (重复度控制) <span class="text-warning">DeepSeek专用</span></label>
            <input type="number" class="form-control" name="presence_penalty"
                   value="${parameters.presence_penalty || 0}" step="0.1" min="-2" max="2">
            <div class="form-text">正数减少重复，负数增加重复。封面生成建议0-1.5</div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Frequency Penalty (频率惩罚) <span class="text-warning">DeepSeek专用</span></label>
            <input type="number" class="form-control" name="frequency_penalty"
                   value="${parameters.frequency_penalty || 0}" step="0.1" min="-2" max="2">
            <div class="form-text">控制词汇重复频率。正数减少重复词汇</div>
          </div>
        </div>`;
    } else {
      // 其他服务商
      parametersHtml += `
        <div class="col-md-6">
          <div class="mb-3">
            <div class="alert alert-secondary">
              <small><i class="bi bi-info-circle"></i>
              当前服务支持通用参数配置，如需更多参数请联系服务商。</small>
            </div>
          </div>
        </div>`;
    }

    parametersHtml += `</div>`;

    // 创建模态框HTML
    const modalHtml = `
      <div class="modal fade" id="parameterConfigModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">${service_name} - 参数配置</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                <strong>参数说明：</strong>这些参数将影响AI模型的生成行为，请根据封面生成业务需求谨慎调整。
                <br><small class="text-muted">
                  服务商类型：${isQwen ? '阿里百炼 (支持12个参数)' : isDoubao ? '豆包 (支持6个参数)' : isDeepSeek ? 'DeepSeek (支持5个参数)' : '其他 (支持3个通用参数)'}
                </small>
              </div>
              <form id="parameterForm">
                ${parametersHtml}
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
              <button type="button" class="btn btn-primary" onclick="saveParameters(${service_id})">保存配置</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('parameterConfigModal');
    if (existingModal) {
      existingModal.remove();
    }

    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('parameterConfigModal'));
    modal.show();

    // 模态框关闭时清理
    document.getElementById('parameterConfigModal').addEventListener('hidden.bs.modal', function() {
      this.remove();
    });

    // 设置随机种子开关事件监听
    const enableSeedCheckbox = document.getElementById('enableSeed');
    if (enableSeedCheckbox) {
      enableSeedCheckbox.addEventListener('change', function() {
        const seedInput = document.getElementById('seedInput');
        if (seedInput) {
          seedInput.disabled = !this.checked;
          if (!this.checked) {
            // 在表单提交时可以识别这是一个需要删除的参数
            seedInput.setAttribute('data-remove-param', 'true');
          } else {
            seedInput.removeAttribute('data-remove-param');
          }
        }
      });
    }
  }

  // 保存参数配置
  window.saveParameters = function(serviceId) {
    const form = document.getElementById('parameterForm');
    const formData = new FormData(form);

    const parameters = {};
    for (let [key, value] of formData.entries()) {
      // 检查是否是需要跳过的种子参数
      if (key === 'seed') {
        const seedInput = document.getElementById('seedInput');
        const enableSeedCheckbox = document.getElementById('enableSeed');
        if (seedInput && (!enableSeedCheckbox || !enableSeedCheckbox.checked)) {
          // 如果seed开关关闭，发送null值表示要删除此参数
          parameters[key] = null;
          continue;
        }
      }
      
      // 根据参数类型进行转换
      if (['temperature', 'top_p', 'presence_penalty', 'frequency_penalty', 'repetition_penalty'].includes(key)) {
        parameters[key] = parseFloat(value);
      } else if (['max_tokens', 'top_k', 'seed', 'max_completion_tokens'].includes(key)) {
        parameters[key] = parseInt(value);
      } else if (['enable_search', 'stream', 'incremental_output'].includes(key)) {
        parameters[key] = value === 'true';
      } else if (key === 'stop') {
        // 处理停止词：将逗号分隔的字符串转换为数组
        if (value && value.trim()) {
          parameters[key] = value.split(',').map(s => s.trim()).filter(s => s);
        } else {
          parameters[key] = [];
        }
      } else if (['response_format', 'result_format'].includes(key)) {
        // 字符串类型参数
        parameters[key] = value;
      } else {
        parameters[key] = value;
      }
    }

    fetch(`/api/admin/ai-services/${serviceId}/parameters`, {
      method: 'PUT',
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ parameters })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('参数配置保存成功');
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('parameterConfigModal'));
        modal.hide();
      } else {
        alert('保存失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('保存参数配置失败:', error);
      alert('保存失败，请稍后再试');
    });
  };

  // 暴露公共方法
  return {
    init
  };
})();

// 模块注册
if (typeof moduleRegistry !== 'undefined') {
  moduleRegistry.register('aiServices', window.aiServicesModule);
}

/**
 * 功能激活与可视化管理器
 * 第十九阶段：让用户能够感受到系统改进
 */

import logger from '../../../services/logs/frontendLogger';
import { getSystemHealthReport } from './systemMonitor';
import { getLoadingStateInfo } from './loadingStateManager';
import { recommendEditableElements } from './advancedElementDetector';

/**
 * 功能状态枚举
 */
export const FEATURE_STATUS = {
  DISABLED: 'disabled',
  ENABLED: 'enabled',
  TESTING: 'testing',
  ERROR: 'error'
};

/**
 * 增强功能列表
 */
export const ENHANCED_FEATURES = {
  ADVANCED_HTML_LOADER: {
    id: 'advanced_html_loader',
    name: '增强型HTML加载器',
    description: '支持复杂静态页面的安全加载',
    benefits: ['提升加载成功率', '更好的安全隔离', '支持复杂页面结构'],
    status: FEATURE_STATUS.DISABLED
  },
  INTELLIGENT_ELEMENT_DETECTION: {
    id: 'intelligent_element_detection',
    name: '智能元素检测系统',
    description: '自动识别8种元素类型，提供编辑建议',
    benefits: ['智能元素推荐', '编辑策略优化', '支持复杂页面编辑'],
    status: FEATURE_STATUS.DISABLED
  },
  DUAL_MODE_PREVIEW: {
    id: 'dual_mode_preview',
    name: '双模式预览控制器',
    description: '标准模式和高级模式智能切换',
    benefits: ['灵活的预览选项', '优化的安全策略', '更好的兼容性'],
    status: FEATURE_STATUS.DISABLED
  },
  RESOURCE_MANAGEMENT: {
    id: 'resource_management',
    name: '资源管理系统',
    description: '智能处理外部资源，解决CORS问题',
    benefits: ['消除CORS错误', '提升图片加载成功率', '智能代理选择'],
    status: FEATURE_STATUS.ENABLED // 这个功能已经默认启用
  },
  SYSTEM_MONITORING: {
    id: 'system_monitoring',
    name: '系统监控与优化',
    description: '实时监控系统性能，提供优化建议',
    benefits: ['性能实时监控', '健康状态评估', '优化建议生成'],
    status: FEATURE_STATUS.ENABLED // 这个功能已经默认启用
  }
};

/**
 * 功能激活管理器类
 */
class FeatureActivationManager {
  constructor() {
    this.features = { ...ENHANCED_FEATURES };
    this.performanceBaseline = null;
    this.activationHistory = [];
    this.listeners = new Set();
  }

  /**
   * 添加状态监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.add(listener);
  }

  /**
   * 移除状态监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    this.listeners.delete(listener);
  }

  /**
   * 通知所有监听器
   */
  notifyListeners() {
    const status = this.getActivationStatus();
    this.listeners.forEach(listener => {
      try {
        listener(status);
      } catch (error) {
        logger.error('功能激活监听器错误', { error: error.message });
      }
    });
  }

  /**
   * 激活指定功能
   * @param {string} featureId - 功能ID
   * @returns {Promise<boolean>} 激活是否成功
   */
  async activateFeature(featureId) {
    const feature = this.features[featureId];
    if (!feature) {
      logger.error('未知功能ID', { featureId });
      return false;
    }

    try {
      logger.info('开始激活功能', { featureId, name: feature.name });
      
      // 设置测试状态
      feature.status = FEATURE_STATUS.TESTING;
      this.notifyListeners();

      // 记录激活前的性能基线
      if (!this.performanceBaseline) {
        this.performanceBaseline = await this.capturePerformanceBaseline();
      }

      // 执行功能激活
      const success = await this.executeFeatureActivation(featureId);

      if (success) {
        feature.status = FEATURE_STATUS.ENABLED;
        this.activationHistory.push({
          featureId,
          timestamp: Date.now(),
          action: 'activate',
          success: true
        });
        logger.info('功能激活成功', { featureId, name: feature.name });
      } else {
        feature.status = FEATURE_STATUS.ERROR;
        logger.error('功能激活失败', { featureId, name: feature.name });
      }

      this.notifyListeners();
      return success;

    } catch (error) {
      feature.status = FEATURE_STATUS.ERROR;
      logger.error('功能激活异常', { featureId, error: error.message });
      this.notifyListeners();
      return false;
    }
  }

  /**
   * 停用指定功能
   * @param {string} featureId - 功能ID
   * @returns {Promise<boolean>} 停用是否成功
   */
  async deactivateFeature(featureId) {
    const feature = this.features[featureId];
    if (!feature) {
      return false;
    }

    try {
      const success = await this.executeFeatureDeactivation(featureId);
      
      if (success) {
        feature.status = FEATURE_STATUS.DISABLED;
        this.activationHistory.push({
          featureId,
          timestamp: Date.now(),
          action: 'deactivate',
          success: true
        });
        logger.info('功能停用成功', { featureId, name: feature.name });
      }

      this.notifyListeners();
      return success;

    } catch (error) {
      logger.error('功能停用异常', { featureId, error: error.message });
      return false;
    }
  }

  /**
   * 执行功能激活
   * @param {string} featureId - 功能ID
   * @returns {Promise<boolean>} 激活是否成功
   */
  async executeFeatureActivation(featureId) {
    switch (featureId) {
      case 'advanced_html_loader':
        // 激活增强型HTML加载器
        return this.activateAdvancedHtmlLoader();
      
      case 'intelligent_element_detection':
        // 激活智能元素检测
        return this.activateElementDetection();
      
      case 'dual_mode_preview':
        // 激活双模式预览
        return this.activateDualModePreview();
      
      default:
        return false;
    }
  }

  /**
   * 执行功能停用
   * @param {string} featureId - 功能ID
   * @returns {Promise<boolean>} 停用是否成功
   */
  async executeFeatureDeactivation(featureId) {
    // 实现功能停用逻辑
    return true;
  }

  /**
   * 激活增强型HTML加载器
   * @returns {Promise<boolean>} 激活是否成功
   */
  async activateAdvancedHtmlLoader() {
    try {
      // 这里可以动态修改featureConfig或直接调用相关函数
      logger.info('增强型HTML加载器已激活');
      return true;
    } catch (error) {
      logger.error('激活增强型HTML加载器失败', { error: error.message });
      return false;
    }
  }

  /**
   * 激活智能元素检测
   * @returns {Promise<boolean>} 激活是否成功
   */
  async activateElementDetection() {
    try {
      logger.info('智能元素检测系统已激活');
      return true;
    } catch (error) {
      logger.error('激活智能元素检测失败', { error: error.message });
      return false;
    }
  }

  /**
   * 激活双模式预览
   * @returns {Promise<boolean>} 激活是否成功
   */
  async activateDualModePreview() {
    try {
      logger.info('双模式预览控制器已激活');
      return true;
    } catch (error) {
      logger.error('激活双模式预览失败', { error: error.message });
      return false;
    }
  }

  /**
   * 捕获性能基线
   * @returns {Promise<Object>} 性能基线数据
   */
  async capturePerformanceBaseline() {
    try {
      const healthReport = getSystemHealthReport();
      return {
        timestamp: Date.now(),
        healthScore: healthReport.score,
        loadTime: performance.now(),
        memoryUsage: performance.memory ? performance.memory.usedJSHeapSize : 0
      };
    } catch (error) {
      logger.error('捕获性能基线失败', { error: error.message });
      return null;
    }
  }

  /**
   * 获取激活状态
   * @returns {Object} 激活状态信息
   */
  getActivationStatus() {
    const enabledFeatures = Object.values(this.features).filter(
      f => f.status === FEATURE_STATUS.ENABLED
    ).length;

    const totalFeatures = Object.keys(this.features).length;

    return {
      features: this.features,
      summary: {
        total: totalFeatures,
        enabled: enabledFeatures,
        disabled: totalFeatures - enabledFeatures,
        activationRate: (enabledFeatures / totalFeatures * 100).toFixed(1)
      },
      performanceBaseline: this.performanceBaseline,
      activationHistory: this.activationHistory.slice(-10) // 最近10条记录
    };
  }

  /**
   * 获取功能价值报告
   * @returns {Object} 功能价值报告
   */
  getFeatureValueReport() {
    const healthReport = getSystemHealthReport();
    const loadingState = getLoadingStateInfo();

    return {
      systemHealth: {
        score: healthReport.score,
        status: healthReport.status,
        suggestions: healthReport.suggestions
      },
      loadingPerformance: {
        state: loadingState.state,
        progress: loadingState.progress,
        duration: loadingState.duration
      },
      featureImpact: this.calculateFeatureImpact(),
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * 计算功能影响
   * @returns {Object} 功能影响数据
   */
  calculateFeatureImpact() {
    const enabledFeatures = Object.values(this.features).filter(
      f => f.status === FEATURE_STATUS.ENABLED
    );

    return {
      enabledCount: enabledFeatures.length,
      benefits: enabledFeatures.flatMap(f => f.benefits),
      estimatedImprovement: enabledFeatures.length * 15 // 每个功能预估15%改进
    };
  }

  /**
   * 生成推荐
   * @returns {Array} 推荐列表
   */
  generateRecommendations() {
    const recommendations = [];
    
    Object.values(this.features).forEach(feature => {
      if (feature.status === FEATURE_STATUS.DISABLED) {
        recommendations.push({
          type: 'activation',
          priority: 'medium',
          message: `建议激活${feature.name}`,
          benefits: feature.benefits,
          featureId: feature.id
        });
      }
    });

    return recommendations;
  }
}

// 创建全局功能激活管理器实例
const featureActivationManager = new FeatureActivationManager();

export default featureActivationManager;

/**
 * 便捷函数：激活功能
 */
export const activateFeature = (featureId) => {
  return featureActivationManager.activateFeature(featureId);
};

/**
 * 便捷函数：停用功能
 */
export const deactivateFeature = (featureId) => {
  return featureActivationManager.deactivateFeature(featureId);
};

/**
 * 便捷函数：获取激活状态
 */
export const getActivationStatus = () => {
  return featureActivationManager.getActivationStatus();
};

/**
 * 便捷函数：获取功能价值报告
 */
export const getFeatureValueReport = () => {
  return featureActivationManager.getFeatureValueReport();
};

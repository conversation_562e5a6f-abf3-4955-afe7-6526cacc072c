const LogService = require('../services/logService');
const { getActionFromMethod, getModuleFromPath, sanitizeRequestBody, sanitizeResponseData, shouldSkipLogging } = require('../utils/logUtils'); // 添加 shouldSkipLogging 导入

/**
 * 操作日志中间件 (修改后)
 * 主要用于提供手动记录日志的辅助功能，不再自动记录所有请求。
 */
const loggerMiddleware = (options = {}) => {
  // 导入了统一工具，不再需要重复定义这些函数

  return async (req, res, next) => {
    // 记录请求开始时间，可能用于手动日志记录时的耗时计算
    req._startTime = Date.now();

    // 将 LogService 附加到 req 对象上，方便 Controller 中调用
    // 注意：这种方式有利有弊，也可以选择在 Controller 中直接导入 LogService
    req.LogService = LogService;
    // 移除 req.logUtils 传递，让 Controller 直接导入 logUtils
    // req.logUtils = { getActionFromMethod, getModuleFromPath, sanitizeRequestBody, sanitizeResponseData };

    // 不再重写 res.json，移除自动日志记录

    next();
  };
};

module.exports = loggerMiddleware;

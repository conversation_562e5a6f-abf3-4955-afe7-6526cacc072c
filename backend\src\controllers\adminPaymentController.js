const { 
  PaymentRecord, 
  MemberPackage, 
  PointPackage, 
  User, 
  RefundRecord,
  PaymentConfig,
  sequelize 
} = require('../models');
const { successResponse, errorResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');
const dayjs = require('dayjs');
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const { encryptData, decryptData } = require('../utils/encryption');

/**
 * 获取订单列表
 * @route GET /api/admin/payment/orders
 */
const getOrdersList = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status, 
      product_type, 
      payment_type,
      start_date,
      end_date,
      search
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // 添加过滤条件
    if (status) where.payment_status = status;
    if (product_type) where.product_type = product_type;
    if (payment_type) where.payment_type = payment_type;

    // 添加日期范围过滤
    if (start_date || end_date) {
      where.created_at = {};
      if (start_date) {
        where.created_at[Op.gte] = new Date(start_date);
      }
      if (end_date) {
        where.created_at[Op.lte] = new Date(end_date);
      }
    }

    // 添加搜索条件
    if (search) {
      where[Op.or] = [
        { order_no: { [Op.like]: `%${search}%` } },
        { product_detail: { [Op.like]: `%${search}%` } }
      ];
    }

    // 查询订单
    const { count, rows } = await PaymentRecord.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'phone', 'nickname', 'role']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // 准备分页信息
    const pagination = {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      total_pages: Math.ceil(count / limit)
    };

    return successResponse(res, '获取订单列表成功', {
      orders: rows,
      pagination
    });
  } catch (error) {
    logger.error('获取订单列表失败:', error);
    return errorResponse(res, '获取订单列表失败', 500);
  }
};

/**
 * 获取订单详情
 * @route GET /api/admin/payment/orders/:id
 */
const getOrderDetail = async (req, res) => {
  try {
    const { id } = req.params;

    const order = await PaymentRecord.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'phone', 'nickname', 'role', 'points']
        }
      ]
    });

    if (!order) {
      return errorResponse(res, '订单不存在', 404);
    }

    // 根据产品类型获取套餐信息
    let packageInfo = null;
    if (order.product_type === 'vip' && order.package_id) {
      packageInfo = await MemberPackage.findByPk(order.package_id);
    } else if (order.product_type === 'points' && order.package_id) {
      packageInfo = await PointPackage.findByPk(order.package_id);
    }

    return successResponse(res, '获取订单详情成功', {
      order,
      packageInfo
    });
  } catch (error) {
    logger.error('获取订单详情失败:', error);
    return errorResponse(res, '获取订单详情失败', 500);
  }
};

/**
 * 更新订单状态
 * @route PUT /api/admin/payment/orders/:id/status
 */
const updateOrderStatus = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!['pending', 'success', 'failed', 'refunded'].includes(status)) {
      await transaction.rollback();
      return errorResponse(res, '无效的订单状态', 400);
    }

    const order = await PaymentRecord.findByPk(id, { transaction });

    if (!order) {
      await transaction.rollback();
      return errorResponse(res, '订单不存在', 404);
    }

    // 如果状态没有变化，直接返回成功
    if (order.payment_status === status) {
      await transaction.commit();
      return successResponse(res, '订单状态更新成功', { order });
    }

    // 更新订单状态
    await order.update({ payment_status: status }, { transaction });

    // 如果状态变更为成功，需要处理会员权益或积分充值
    if (status === 'success' && order.payment_status !== 'success') {
      const userId = order.user_id;
      const user = await User.findByPk(userId, { transaction });

      if (!user) {
        await transaction.rollback();
        return errorResponse(res, '用户不存在', 404);
      }

      if (order.product_type === 'vip') {
        // 处理会员购买
        const memberPackage = await MemberPackage.findByPk(order.package_id, { transaction });
        if (!memberPackage) {
          await transaction.rollback();
          return errorResponse(res, '会员套餐不存在', 404);
        }

        // 计算新的会员到期时间
        let newExpireDate;
        if (user.role === 'vip' && user.vip_expire_date && new Date(user.vip_expire_date) > new Date()) {
          // 已是会员，续费
          newExpireDate = dayjs(user.vip_expire_date).add(memberPackage.duration, 'day').toDate();
        } else {
          // 新开通会员
          newExpireDate = dayjs().add(memberPackage.duration, 'day').toDate();
        }

        // 更新用户会员状态
        await user.update({
          role: 'vip',
          vip_expire_date: newExpireDate
        }, { transaction });

      } else if (order.product_type === 'points') {
        // 处理积分充值
        const pointPackage = await PointPackage.findByPk(order.package_id, { transaction });
        if (!pointPackage) {
          await transaction.rollback();
          return errorResponse(res, '积分套餐不存在', 404);
        }

        // 计算总积分（包括赠送积分）
        const totalPoints = pointPackage.points + (pointPackage.bonus_points || 0);

        // 更新用户积分
        const newPoints = (user.points || 0) + totalPoints;
        await user.update({
          points: newPoints
        }, { transaction });

        // 创建积分记录
        await sequelize.models.point_record.create({
          user_id: userId,
          points_change: totalPoints,
          points_after: newPoints,
          operation_type: 'recharge',
          description: `管理员手动处理：购买${pointPackage.name}，充值${pointPackage.points}积分，赠送${pointPackage.bonus_points || 0}积分`,
          operation_id: order.order_no
        }, { transaction });
      }
    }

    await transaction.commit();
    return successResponse(res, '订单状态更新成功', { order });
  } catch (error) {
    await transaction.rollback();
    logger.error('更新订单状态失败:', error);
    return errorResponse(res, '更新订单状态失败', 500);
  }
};

/**
 * 获取会员套餐列表
 * @route GET /api/admin/payment/member-packages
 */
const getMemberPackages = async (req, res) => {
  try {
    const packages = await MemberPackage.findAll({
      order: [['duration', 'ASC']]
    });

    return successResponse(res, '获取会员套餐列表成功', { packages });
  } catch (error) {
    logger.error('获取会员套餐列表失败:', error);
    return errorResponse(res, '获取会员套餐列表失败', 500);
  }
};

/**
 * 创建会员套餐
 * @route POST /api/admin/payment/member-packages
 */
const createMemberPackage = async (req, res) => {
  try {
    const { name, duration, price, discount_price, description, is_active } = req.body;

    // 验证必要字段
    if (!name || !duration || !price) {
      return errorResponse(res, '缺少必要参数', 400);
    }

    // 创建会员套餐
    const memberPackage = await MemberPackage.create({
      name,
      duration,
      price,
      discount_price,
      description,
      is_active: is_active !== undefined ? is_active : true
    });

    return successResponse(res, '创建会员套餐成功', { package: memberPackage });
  } catch (error) {
    logger.error('创建会员套餐失败:', error);
    return errorResponse(res, '创建会员套餐失败', 500);
  }
};

/**
 * 获取会员套餐详情
 * @route GET /api/admin/payment/member-packages/:id
 */
const getMemberPackageById = async (req, res) => {
  try {
    const { id } = req.params;

    const memberPackage = await MemberPackage.findByPk(id);

    if (!memberPackage) {
      return errorResponse(res, '会员套餐不存在', 404);
    }

    return successResponse(res, '获取会员套餐详情成功', { package: memberPackage });
  } catch (error) {
    logger.error('获取会员套餐详情失败:', error);
    return errorResponse(res, '获取会员套餐详情失败', 500);
  }
};

/**
 * 更新会员套餐
 * @route PUT /api/admin/payment/member-packages/:id
 */
const updateMemberPackage = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, duration, price, discount_price, description, is_active } = req.body;

    const memberPackage = await MemberPackage.findByPk(id);

    if (!memberPackage) {
      return errorResponse(res, '会员套餐不存在', 404);
    }

    // 更新会员套餐
    await memberPackage.update({
      name: name !== undefined ? name : memberPackage.name,
      duration: duration !== undefined ? duration : memberPackage.duration,
      price: price !== undefined ? price : memberPackage.price,
      discount_price: discount_price !== undefined ? discount_price : memberPackage.discount_price,
      description: description !== undefined ? description : memberPackage.description,
      is_active: is_active !== undefined ? is_active : memberPackage.is_active
    });

    return successResponse(res, '更新会员套餐成功', { package: memberPackage });
  } catch (error) {
    logger.error('更新会员套餐失败:', error);
    return errorResponse(res, '更新会员套餐失败', 500);
  }
};

/**
 * 删除会员套餐
 * @route DELETE /api/admin/payment/member-packages/:id
 */
const deleteMemberPackage = async (req, res) => {
  try {
    const { id } = req.params;

    const memberPackage = await MemberPackage.findByPk(id);

    if (!memberPackage) {
      return errorResponse(res, '会员套餐不存在', 404);
    }

    // 检查是否有订单使用了该套餐
    const orderCount = await PaymentRecord.count({
      where: {
        product_type: 'vip',
        package_id: id
      }
    });

    if (orderCount > 0) {
      // 如果有订单使用了该套餐，只将其标记为非活跃
      await memberPackage.update({ is_active: false });
      return successResponse(res, '会员套餐已停用（存在关联订单，无法删除）');
    }

    // 如果没有订单使用该套餐，可以直接删除
    await memberPackage.destroy();

    return successResponse(res, '删除会员套餐成功');
  } catch (error) {
    logger.error('删除会员套餐失败:', error);
    return errorResponse(res, '删除会员套餐失败', 500);
  }
};

/**
 * 获取积分套餐列表
 * @route GET /api/admin/payment/point-packages
 */
const getPointPackages = async (req, res) => {
  try {
    const packages = await PointPackage.findAll({
      order: [['points', 'ASC']]
    });

    return successResponse(res, '获取积分套餐列表成功', { packages });
  } catch (error) {
    logger.error('获取积分套餐列表失败:', error);
    return errorResponse(res, '获取积分套餐列表失败', 500);
  }
};

/**
 * 创建积分套餐
 * @route POST /api/admin/payment/point-packages
 */
const createPointPackage = async (req, res) => {
  try {
    const { name, points, price, bonus_points, description, is_active } = req.body;

    // 验证必要字段
    if (!name || !points || !price) {
      return errorResponse(res, '缺少必要参数', 400);
    }

    // 创建积分套餐
    const pointPackage = await PointPackage.create({
      name,
      points,
      price,
      bonus_points: bonus_points || 0,
      description,
      is_active: is_active !== undefined ? is_active : true
    });

    return successResponse(res, '创建积分套餐成功', { package: pointPackage });
  } catch (error) {
    logger.error('创建积分套餐失败:', error);
    return errorResponse(res, '创建积分套餐失败', 500);
  }
};

/**
 * 获取积分套餐详情
 * @route GET /api/admin/payment/point-packages/:id
 */
const getPointPackageById = async (req, res) => {
  try {
    const { id } = req.params;

    const pointPackage = await PointPackage.findByPk(id);

    if (!pointPackage) {
      return errorResponse(res, '积分套餐不存在', 404);
    }

    return successResponse(res, '获取积分套餐详情成功', { package: pointPackage });
  } catch (error) {
    logger.error('获取积分套餐详情失败:', error);
    return errorResponse(res, '获取积分套餐详情失败', 500);
  }
};

/**
 * 更新积分套餐
 * @route PUT /api/admin/payment/point-packages/:id
 */
const updatePointPackage = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, points, price, bonus_points, description, is_active } = req.body;

    const pointPackage = await PointPackage.findByPk(id);

    if (!pointPackage) {
      return errorResponse(res, '积分套餐不存在', 404);
    }

    // 更新积分套餐
    await pointPackage.update({
      name: name !== undefined ? name : pointPackage.name,
      points: points !== undefined ? points : pointPackage.points,
      price: price !== undefined ? price : pointPackage.price,
      bonus_points: bonus_points !== undefined ? bonus_points : pointPackage.bonus_points,
      description: description !== undefined ? description : pointPackage.description,
      is_active: is_active !== undefined ? is_active : pointPackage.is_active
    });

    return successResponse(res, '更新积分套餐成功', { package: pointPackage });
  } catch (error) {
    logger.error('更新积分套餐失败:', error);
    return errorResponse(res, '更新积分套餐失败', 500);
  }
};

/**
 * 删除积分套餐
 * @route DELETE /api/admin/payment/point-packages/:id
 */
const deletePointPackage = async (req, res) => {
  try {
    const { id } = req.params;

    const pointPackage = await PointPackage.findByPk(id);

    if (!pointPackage) {
      return errorResponse(res, '积分套餐不存在', 404);
    }

    // 检查是否有订单使用了该套餐
    const orderCount = await PaymentRecord.count({
      where: {
        product_type: 'points',
        package_id: id
      }
    });

    if (orderCount > 0) {
      // 如果有订单使用了该套餐，只将其标记为非活跃
      await pointPackage.update({ is_active: false });
      return successResponse(res, '积分套餐已停用（存在关联订单，无法删除）');
    }

    // 如果没有订单使用该套餐，可以直接删除
    await pointPackage.destroy();

    return successResponse(res, '删除积分套餐成功');
  } catch (error) {
    logger.error('删除积分套餐失败:', error);
    return errorResponse(res, '删除积分套餐失败', 500);
  }
};

/**
 * 获取退款列表
 * @route GET /api/admin/payment/refunds
 */
const getRefundsList = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status,
      start_date,
      end_date,
      search
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // 添加过滤条件
    if (status) where.refund_status = status;

    // 添加日期范围过滤
    if (start_date || end_date) {
      where.created_at = {};
      if (start_date) {
        where.created_at[Op.gte] = new Date(start_date);
      }
      if (end_date) {
        where.created_at[Op.lte] = new Date(end_date);
      }
    }

    // 添加搜索条件
    if (search) {
      where[Op.or] = [
        { refund_no: { [Op.like]: `%${search}%` } },
        { refund_reason: { [Op.like]: `%${search}%` } }
      ];
    }

    // 查询退款记录
    const { count, rows } = await RefundRecord.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'phone', 'role']
        },
        {
          model: PaymentRecord,
          as: 'payment',
          attributes: ['id', 'order_no', 'amount', 'product_type', 'payment_type']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // 准备分页信息
    const pagination = {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      total_pages: Math.ceil(count / limit)
    };

    return successResponse(res, '获取退款列表成功', {
      refunds: rows,
      pagination
    });
  } catch (error) {
    logger.error('获取退款列表失败:', error);
    return errorResponse(res, '获取退款列表失败', 500);
  }
};

/**
 * 创建退款
 * @route POST /api/admin/payment/refunds
 */
const createRefund = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { payment_id, refund_amount, refund_reason } = req.body;

    // 验证参数
    if (!payment_id || !refund_amount) {
      await transaction.rollback();
      return errorResponse(res, '支付ID和退款金额不能为空', 400);
    }

    // 查找支付记录
    const payment = await PaymentRecord.findByPk(payment_id, { transaction });
    if (!payment) {
      await transaction.rollback();
      return errorResponse(res, '支付记录不存在', 404);
    }

    // 检查支付状态
    if (payment.payment_status !== 'success') {
      await transaction.rollback();
      return errorResponse(res, '只能对支付成功的订单申请退款', 400);
    }

    // 检查退款金额
    if (parseFloat(refund_amount) <= 0 || parseFloat(refund_amount) > parseFloat(payment.amount)) {
      await transaction.rollback();
      return errorResponse(res, '退款金额必须大于0且不能超过支付金额', 400);
    }

    // 生成退款单号
    const refund_no = `RF${dayjs().format('YYYYMMDDHHmmss')}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

    // 创建退款记录
    const refund = await RefundRecord.create({
      payment_id,
      user_id: payment.user_id,
      refund_no,
      refund_amount,
      refund_reason,
      refund_status: 'pending'
    }, { transaction });

    // 更新支付记录的退款状态
    const newRefundStatus = parseFloat(refund_amount) === parseFloat(payment.amount) ? 'full' : 'partial';
    await payment.update({ refund_status: newRefundStatus }, { transaction });

    await transaction.commit();

    return successResponse(res, '创建退款申请成功', { refund });
  } catch (error) {
    await transaction.rollback();
    logger.error('创建退款申请失败:', error);
    return errorResponse(res, '创建退款申请失败', 500);
  }
};

/**
 * 获取退款详情
 * @route GET /api/admin/payment/refunds/:id
 */
const getRefundDetail = async (req, res) => {
  try {
    const { id } = req.params;

    const refund = await RefundRecord.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'phone', 'role']
        },
        {
          model: PaymentRecord,
          as: 'payment',
          attributes: ['id', 'order_no', 'amount', 'product_type', 'payment_type', 'payment_status', 'refund_status']
        }
      ]
    });

    if (!refund) {
      return errorResponse(res, '退款记录不存在', 404);
    }

    return successResponse(res, '获取退款详情成功', { refund });
  } catch (error) {
    logger.error('获取退款详情失败:', error);
    return errorResponse(res, '获取退款详情失败', 500);
  }
};

/**
 * 更新退款状态
 * @route PUT /api/admin/payment/refunds/:id/status
 */
const updateRefundStatus = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { status, transaction_id } = req.body;

    if (!['pending', 'success', 'failed'].includes(status)) {
      await transaction.rollback();
      return errorResponse(res, '无效的退款状态', 400);
    }

    const refund = await RefundRecord.findByPk(id, { transaction });

    if (!refund) {
      await transaction.rollback();
      return errorResponse(res, '退款记录不存在', 404);
    }

    // 如果状态没有变化，直接返回成功
    if (refund.refund_status === status) {
      await transaction.commit();
      return successResponse(res, '退款状态更新成功', { refund });
    }

    // 更新退款状态
    const updateData = { refund_status: status };
    
    // 如果状态为成功，记录退款时间和交易ID
    if (status === 'success') {
      updateData.refund_time = new Date();
      if (transaction_id) {
        updateData.transaction_id = transaction_id;
      }
    }

    await refund.update(updateData, { transaction });

    // 如果退款状态变更为成功或失败，可能需要更新支付记录的退款状态
    if (status === 'success' || status === 'failed') {
      const payment = await PaymentRecord.findByPk(refund.payment_id, { transaction });
      
      if (payment) {
        // 如果退款成功，检查是全额退款还是部分退款
        if (status === 'success') {
          // 如果退款金额等于支付金额，则为全额退款
          if (parseFloat(refund.refund_amount) === parseFloat(payment.amount)) {
            await payment.update({ refund_status: 'full' }, { transaction });
          } else {
            await payment.update({ refund_status: 'partial' }, { transaction });
          }
        }
      }
    }

    await transaction.commit();

    return successResponse(res, '退款状态更新成功', { refund });
  } catch (error) {
    await transaction.rollback();
    logger.error('更新退款状态失败:', error);
    return errorResponse(res, '更新退款状态失败', 500);
  }
};

/**
 * 获取支付配置列表
 * @route GET /api/admin/payment/payment-configs
 */
const getPaymentConfigs = async (req, res) => {
  try {
    const { payment_type } = req.query;
    const where = {};

    if (payment_type) {
      where.payment_type = payment_type;
    }

    const configs = await PaymentConfig.findAll({
      where,
      order: [['payment_type', 'ASC'], ['config_name', 'ASC']]
    });

    // 处理加密的配置值
    const processedConfigs = configs.map(config => {
      const configObj = config.toJSON();
      if (configObj.is_encrypted && configObj.config_value) {
        // 对于加密的配置值，只返回掩码
        configObj.config_value = '********';
      }
      return configObj;
    });

    return successResponse(res, '获取支付配置列表成功', { configs: processedConfigs });
  } catch (error) {
    logger.error('获取支付配置列表失败:', error);
    return errorResponse(res, '获取支付配置列表失败', 500);
  }
};

/**
 * 创建支付配置
 * @route POST /api/admin/payment/payment-configs
 */
const createPaymentConfig = async (req, res) => {
  try {
    const { payment_type, config_name, config_key, config_value, is_encrypted, description } = req.body;

    // 验证参数
    if (!payment_type || !config_name || !config_key || config_value === undefined) {
      return errorResponse(res, '支付类型、配置名称、配置键和配置值不能为空', 400);
    }

    // 检查配置键是否已存在
    const existingConfig = await PaymentConfig.findOne({
      where: {
        payment_type,
        config_key
      }
    });

    if (existingConfig) {
      return errorResponse(res, '该配置键已存在', 400);
    }

    // 处理配置值加密
    let processedValue = config_value;
    if (is_encrypted && config_value) {
      processedValue = encryptData(config_value);
    }

    // 创建支付配置
    const config = await PaymentConfig.create({
      payment_type,
      config_name,
      config_key,
      config_value: processedValue,
      is_encrypted: !!is_encrypted,
      description
    });

    // 如果是加密的配置，不返回真实值
    const responseConfig = config.toJSON();
    if (responseConfig.is_encrypted) {
      responseConfig.config_value = '********';
    }

    return successResponse(res, '创建支付配置成功', { config: responseConfig });
  } catch (error) {
    logger.error('创建支付配置失败:', error);
    return errorResponse(res, '创建支付配置失败', 500);
  }
};

/**
 * 更新支付配置
 * @route PUT /api/admin/payment/payment-configs/:id
 */
const updatePaymentConfig = async (req, res) => {
  try {
    const { id } = req.params;
    const { config_name, config_value, is_encrypted, description, is_active } = req.body;

    const config = await PaymentConfig.findByPk(id);
    if (!config) {
      return errorResponse(res, '支付配置不存在', 404);
    }

    // 准备更新数据
    const updateData = {};
    
    if (config_name !== undefined) updateData.config_name = config_name;
    if (description !== undefined) updateData.description = description;
    if (is_active !== undefined) updateData.is_active = is_active;

    // 处理配置值和加密状态
    if (config_value !== undefined) {
      // 如果更新了加密状态
      if (is_encrypted !== undefined) {
        updateData.is_encrypted = is_encrypted;
        
        // 如果从非加密变为加密，需要加密值
        if (is_encrypted && !config.is_encrypted) {
          updateData.config_value = encryptData(config_value);
        }
        // 如果从加密变为非加密，需要使用明文
        else if (!is_encrypted && config.is_encrypted) {
          updateData.config_value = config_value;
        }
        // 如果加密状态不变
        else if (is_encrypted && config.is_encrypted) {
          // 如果提供的值不是掩码，则认为是新值需要加密
          if (config_value !== '********') {
            updateData.config_value = encryptData(config_value);
          }
        }
        else {
          updateData.config_value = config_value;
        }
      }
      // 如果没有更新加密状态，保持原有加密状态
      else {
        if (config.is_encrypted) {
          // 如果提供的值不是掩码，则认为是新值需要加密
          if (config_value !== '********') {
            updateData.config_value = encryptData(config_value);
          }
        } else {
          updateData.config_value = config_value;
        }
      }
    }

    // 更新配置
    await config.update(updateData);

    // 如果是加密的配置，不返回真实值
    const responseConfig = config.toJSON();
    if (responseConfig.is_encrypted) {
      responseConfig.config_value = '********';
    }

    return successResponse(res, '更新支付配置成功', { config: responseConfig });
  } catch (error) {
    logger.error('更新支付配置失败:', error);
    return errorResponse(res, '更新支付配置失败', 500);
  }
};

/**
 * 删除支付配置
 * @route DELETE /api/admin/payment/payment-configs/:id
 */
const deletePaymentConfig = async (req, res) => {
  try {
    const { id } = req.params;

    const config = await PaymentConfig.findByPk(id);
    if (!config) {
      return errorResponse(res, '支付配置不存在', 404);
    }

    await config.destroy();

    return successResponse(res, '删除支付配置成功');
  } catch (error) {
    logger.error('删除支付配置失败:', error);
    return errorResponse(res, '删除支付配置失败', 500);
  }
};

/**
 * 获取支付统计
 * @route GET /api/admin/payment/statistics
 */
const getPaymentStatistics = async (req, res) => {
  try {
    // 获取时间范围参数
    const { start_date, end_date } = req.query;
    const startDate = start_date ? new Date(start_date) : new Date(new Date().setMonth(new Date().getMonth() - 1));
    const endDate = end_date ? new Date(end_date) : new Date();

    // 构建时间范围条件
    const dateWhere = {
      created_at: {
        [Op.between]: [startDate, endDate]
      }
    };

    // 获取总订单数
    const totalOrders = await PaymentRecord.count({
      where: dateWhere
    });

    // 获取成功订单数
    const successOrders = await PaymentRecord.count({
      where: {
        ...dateWhere,
        payment_status: 'success'
      }
    });

    // 获取总收入
    const totalIncome = await PaymentRecord.sum('amount', {
      where: {
        ...dateWhere,
        payment_status: 'success'
      }
    }) || 0;

    // 获取会员订单收入
    const vipIncome = await PaymentRecord.sum('amount', {
      where: {
        ...dateWhere,
        payment_status: 'success',
        product_type: 'vip'
      }
    }) || 0;

    // 获取积分订单收入
    const pointsIncome = await PaymentRecord.sum('amount', {
      where: {
        ...dateWhere,
        payment_status: 'success',
        product_type: 'points'
      }
    }) || 0;

    // 获取支付方式分布
    const paymentTypeDistribution = await PaymentRecord.findAll({
      attributes: [
        'payment_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'amount']
      ],
      where: {
        ...dateWhere,
        payment_status: 'success'
      },
      group: ['payment_type']
    });

    // 获取产品类型分布
    const productTypeDistribution = await PaymentRecord.findAll({
      attributes: [
        'product_type',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'amount']
      ],
      where: {
        ...dateWhere,
        payment_status: 'success'
      },
      group: ['product_type']
    });

    // 获取每日收入趋势
    const dailyIncomeTrend = await PaymentRecord.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'amount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        ...dateWhere,
        payment_status: 'success'
      },
      group: [sequelize.fn('DATE', sequelize.col('created_at'))],
      order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']]
    });

    return successResponse(res, '获取支付统计成功', {
      overview: {
        totalOrders,
        successOrders,
        successRate: totalOrders > 0 ? (successOrders / totalOrders * 100).toFixed(2) : 0,
        totalIncome,
        vipIncome,
        pointsIncome
      },
      paymentTypeDistribution,
      productTypeDistribution,
      dailyIncomeTrend
    });
  } catch (error) {
    logger.error('获取支付统计失败:', error);
    return errorResponse(res, '获取支付统计失败', 500);
  }
};

/**
 * 删除订单
 * @route DELETE /api/admin/payment/orders/:id
 */
const deleteOrder = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    
    // 查找订单
    const order = await PaymentRecord.findByPk(id, { transaction });
    
    if (!order) {
      await transaction.rollback();
      return errorResponse(res, '订单不存在', 404);
    }
    
    // 获取订单号，用于删除关联记录
    const orderNo = order.order_no;
    
    // 1. 删除订单状态日志
    await sequelize.query(
      `DELETE FROM order_status_logs WHERE order_no = ?`,
      {
        replacements: [orderNo],
        type: sequelize.QueryTypes.DELETE,
        transaction
      }
    );
    
    // 2. 检查是否有关联的退款记录
    const refundRecords = await RefundRecord.findAll({
      where: { payment_id: id },
      transaction
    });
    
    if (refundRecords.length > 0) {
      // 有退款记录，不删除退款记录，但更新其关联订单号为备注
      for (const refund of refundRecords) {
        // 将原始订单号记录到备注中
        const remark = `原关联订单号: ${orderNo}，订单已删除`;
        await refund.update({ 
          remark: refund.remark ? `${refund.remark}; ${remark}` : remark 
        }, { transaction });
      }
    }
    
    // 3. 删除订单记录
    await order.destroy({ transaction });
    
    // 4. 记录操作日志
    logger.info(`管理员${req.user.id}删除订单成功，订单ID: ${id}, 订单号: ${orderNo}`);
    
    // 提交事务
    await transaction.commit();
    
    return successResponse(res, '订单删除成功');
  } catch (error) {
    // 回滚事务
    await transaction.rollback();
    
    logger.error('删除订单失败:', error);
    return errorResponse(res, '删除订单失败', 500);
  }
};

/**
 * 关闭订单
 * @route PUT /api/admin/payment/orders/:id/close
 */
const closeOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const adminId = req.user.id;
    
    const order = await PaymentRecord.findByPk(id);
    
    if (!order) {
      return errorResponse(res, '订单不存在', 404);
    }
    
    // 只有待支付的订单可以关闭
    if (order.payment_status !== 'pending') {
      return errorResponse(res, '只有待支付的订单可以关闭', 400);
    }
    
    // 更新状态为已关闭
    await order.update({ payment_status: 'closed' });
    
    logger.info(`管理员${adminId}关闭订单${id}成功`);
    
    return successResponse(res, '订单已关闭', { id: id, status: 'closed' });
  } catch (error) {
    logger.error('关闭订单失败:', error);
    return errorResponse(res, '关闭订单失败', 500);
  }
};

module.exports = {
  getOrdersList,
  getOrderDetail,
  updateOrderStatus,
  getMemberPackages,
  createMemberPackage,
  getMemberPackageById,
  updateMemberPackage,
  deleteMemberPackage,
  getPointPackages,
  createPointPackage,
  getPointPackageById,
  updatePointPackage,
  deletePointPackage,
  getRefundsList,
  createRefund,
  getRefundDetail,
  updateRefundStatus,
  getPaymentConfigs,
  createPaymentConfig,
  updatePaymentConfig,
  deletePaymentConfig,
  getPaymentStatistics,
  deleteOrder,
  closeOrder
}; 
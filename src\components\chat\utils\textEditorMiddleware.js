/**
 * 文本编辑器中间件模块 - 处理模式切换
 * 完全重写版本，确保编辑模式和拖拽模式的清晰分离
 */

import { createGlobalMouseMoveHandler } from './globalMouseMoveHandler';

/**
 * 检查是否点击了特殊元素
 * @param {Event} e - 事件对象
 * @return {boolean} 是否点击了特殊元素
 */
export const isClickOnSpecialElement = (e) => {
  // 获取点击的元素
  const clickedElement = e.target;
  
  // 检查常见的UI控件
  const isToolbarClick = clickedElement.closest('.text-editor-toolbar-container, .fixed-text-editor-toolbar');
  const isColorPickerClick = clickedElement.closest('.color-picker-popup');
  const isResizeHandleClick = clickedElement.classList && (clickedElement.classList.contains('resize-handle') || clickedElement.closest('.resize-handle'));
  const isEditorControlClick = clickedElement.closest('.editor-control');
  const isSelectElement = clickedElement.tagName === 'SELECT' || clickedElement.tagName === 'OPTION';
  const isOptionClick = clickedElement.closest('select') || clickedElement.closest('option');

  // 检查按钮和表单元素
  const isButtonClick = clickedElement.tagName === 'BUTTON' || clickedElement.closest('button');
  const isInputClick = clickedElement.tagName === 'INPUT' || clickedElement.closest('input');
  
  // 检查SVG控制元素
  const isSvgControlClick = (clickedElement.namespaceURI && clickedElement.namespaceURI.includes('svg')) && 
                          (clickedElement.tagName === 'circle' || clickedElement.tagName === 'rect' || 
                           clickedElement.tagName === 'path' || clickedElement.tagName === 'g' || 
                           clickedElement.closest('svg') && clickedElement.closest('svg').classList.contains('control'));

  // 检查图片和媒体元素
  const isImageClick = clickedElement.tagName === 'IMG' || clickedElement.closest('img');
  const isMediaClick = clickedElement.tagName === 'VIDEO' || clickedElement.tagName === 'AUDIO' || 
                      clickedElement.closest('video') || clickedElement.closest('audio');

  return isToolbarClick || isColorPickerClick || isResizeHandleClick ||
         isEditorControlClick || isSelectElement || isOptionClick ||
         isButtonClick || isInputClick || isSvgControlClick ||
         isImageClick || isMediaClick;
};

// 当前编辑器全局状态
let editorState = {
  activeMode: null, // 当前激活的模式: 'dragMode', 'editMode' 等
  enabled: true, // 编辑器是否启用
  selectedElement: null, // 当前选中元素
  container: null, // 编辑器容器
  options: {} // 编辑器配置选项
};

// 可用的编辑器模式
const availableModes = ['dragMode', 'editMode'];

/**
 * 初始化编辑器中间件
 * @param {Object} options - 中间件配置选项
 */
export const initMiddleware = (options = {}) => {
  editorState.options = {
    ...editorState.options,
    ...options
  };
  
  console.log('文本编辑器中间件初始化完成', editorState.options);
};

/**
 * 设置编辑器容器
 * @param {HTMLElement} container - 编辑器容器元素
 */
export const setContainer = (container) => {
  if (container && container instanceof HTMLElement) {
    editorState.container = container;
  }
};

/**
 * 获取编辑器容器
 * @return {HTMLElement} 编辑器容器元素
 */
export const getContainer = () => {
  return editorState.container;
};

/**
 * 启用或禁用编辑器
 * @param {boolean} value - 是否启用
 */
export const setEnabled = (value) => {
  editorState.enabled = Boolean(value);
};

/**
 * 检查编辑器是否启用
 * @param {string} mode - 要检查的模式（可选）
 * @return {boolean} 编辑器是否启用
 */
export const isEnabled = (mode = null) => {
  if (mode) {
    return editorState.enabled && editorState.activeMode === mode;
  }
  return editorState.enabled;
};

/**
 * 设置激活的编辑器模式
 * @param {string} mode - 要激活的模式
 * @return {boolean} 是否成功设置
 */
export const setActiveMode = (mode) => {
  if (!mode) {
    editorState.activeMode = null;
    return true;
  }
  
  if (availableModes.includes(mode)) {
    editorState.activeMode = mode;
    return true;
  }
  
  console.error(`未知的编辑器模式: ${mode}`);
  return false;
};

/**
 * 获取当前激活的编辑器模式
 * @return {string|null} 当前激活的模式
 */
export const getActiveMode = () => {
  return editorState.activeMode;
};

/**
 * 选择元素
 * @param {HTMLElement} element - 要选择的元素
 */
export const selectElement = (element) => {
  editorState.selectedElement = element;
};

/**
 * 获取当前选中的元素
 * @return {HTMLElement|null} 当前选中的元素
 */
export const getSelectedElement = () => {
  return editorState.selectedElement;
};

/**
 * 清除当前选中的元素
 */
export const clearSelection = () => {
  editorState.selectedElement = null;
};

export const createTextEditorMiddleware = (context, dragMode, editMode) => {
  const { doc, win, containerEl, getActiveElement, setActiveElement, debugMode, isDivMode, editableElementsTracker, onElementClick } = context;

  // 添加拖拽状态跟踪变量
  let isDragging = false;

  // 同步容器到全局状态
  setContainer(containerEl);
  
  // 创建全局鼠标移动处理器
  const globalMouseMoveHandler = createGlobalMouseMoveHandler(
    doc, 
    editableElementsTracker, 
    isClickOnSpecialElement, // 使用导出的函数
    debugMode
  );

  // 强制退出所有编辑模式的函数
  const forceExitAllEditModes = () => {
    // 查找所有处于编辑模式的元素
    const editingElements = doc.querySelectorAll('[data-editing="true"]');
    editingElements.forEach(el => {
      // 移除编辑标记
      el.removeAttribute('data-editing');
      el.classList.remove('editing-active-outline');
      // 失去焦点
      if (document.activeElement === el) {
        el.blur();
      }
    });

    // 调用editMode的退出函数
    editMode.exitEditingMode();

    // 重置活动元素
    setActiveElement(null);
    
    // 清除全局状态的选中元素
    clearSelection();
    
    // 如果当前模式是editMode，则清除激活模式
    if (getActiveMode() === 'editMode') {
      setActiveMode(null);
    }
  };

  // 强制进入编辑模式的函数
  const forceEnterEditMode = (element) => {
    // 先强制退出所有编辑模式
    forceExitAllEditModes();

    // 移除拖拽模式
    element.classList.remove('drag-mode');
    dragMode.clearAllDragModes();

    // 进入编辑模式（这会设置所有必要的属性和状态）
    editMode.enterEditMode(element);
    setActiveElement(element);
    
    // 更新全局状态
    selectElement(element);
    setActiveMode('editMode');
  };

  // 添加一个新的组合鼠标移动处理函数
  const handleCombinedMouseMove = (e) => {
    // 优先处理拖拽，提高响应性
    if (isDragging) {
      dragMode.handleDragMove(e);
      // 更新全局鼠标移动处理器的拖拽状态
      globalMouseMoveHandler.setDragging(true);
      return; // 如果正在拖拽，直接返回，不执行其他处理
    }
    
    // 更新全局鼠标移动处理器的拖拽状态
    globalMouseMoveHandler.setDragging(false);
    
    // 非拖拽状态下调用全局处理函数
    globalMouseMoveHandler.handler(e);
  };

  // 修改添加全局事件监听器函数
  const addGlobalEventListeners = () => {
    if (doc) {
      doc.addEventListener('click', documentClickHandler, true);
      doc.addEventListener('dblclick', documentDblClickHandler, false);
      doc.addEventListener('dragstart', preventDefaultAction, true);
      doc.addEventListener('mousedown', globalMouseDownHandler, true);
      // 替换两个单独的mousemove监听器为一个组合监听器
      doc.addEventListener('mousemove', handleCombinedMouseMove, true);
      doc.addEventListener('mouseup', globalMouseUpHandler, true);
      doc.addEventListener('mouseup', dragMode.handleDragEnd, true);
      doc.addEventListener('selectionchange', handleSelectionChange);
    }

    if (win) {
      win.addEventListener('resize', handleResize);
      win.addEventListener('scroll', handleScroll, true);
    }
  };

  // 为元素添加事件监听器
  const attachElementEventListeners = (el) => {
    if (!el || typeof el.addEventListener !== 'function') return;

    // 添加事件监听器，确保顺序正确
    // 先添加mousedown事件，确保它在click事件之前处理
    el.addEventListener('mousedown', handleMouseDown, true);

    // 再添加dblclick事件，确保双击时不会触发单击的拖拽模式
    el.addEventListener('dblclick', handleElementDblClick, true);

    // 最后添加click事件
    el.addEventListener('click', handleElementClick, true);

    // 其他事件监听器
    el.addEventListener('focus', editMode.handleFocus, true);
    el.addEventListener('blur', handleElementBlur, true);
    el.addEventListener('paste', editMode.handlePaste, true);
    el.addEventListener('keydown', editMode.handleKeyDown, true);
  };

  // 修改清理监听器函数
  const cleanupListeners = (fullCleanup = true) => {
    // 移除元素事件监听器
    editableElementsTracker.forEach(el => {
      if (!el) return; // 跳过null元素

      el.removeEventListener('mousedown', handleMouseDown, true);
      el.removeEventListener('dblclick', handleElementDblClick, true);
      el.removeEventListener('click', handleElementClick, true);
      el.removeEventListener('focus', editMode.handleFocus, true);
      el.removeEventListener('blur', handleElementBlur, true);
      el.removeEventListener('paste', editMode.handlePaste, true);
      el.removeEventListener('keydown', editMode.handleKeyDown, true);

      // 移除样式类
      el.classList.remove('element-hover-outline', 'selected-for-drag', 'editing-active-outline');
    });

    if (fullCleanup && doc) {
      doc.removeEventListener('click', documentClickHandler, true);
      doc.removeEventListener('dblclick', documentDblClickHandler, false);
      doc.removeEventListener('dragstart', preventDefaultAction, true);
      doc.removeEventListener('mousedown', globalMouseDownHandler, true);
      // 替换两个单独的mousemove监听器清理为一个组合监听器清理
      doc.removeEventListener('mousemove', handleCombinedMouseMove, true);
      doc.removeEventListener('mouseup', globalMouseUpHandler, true);
      doc.removeEventListener('mouseup', dragMode.handleDragEnd, true);
      doc.removeEventListener('selectionchange', handleSelectionChange);

      // 移除调整大小相关的事件监听器
      doc.removeEventListener('mousemove', handleResizeMove);
      doc.removeEventListener('mouseup', handleResizeEnd);

      if (win) {
        win.removeEventListener('resize', handleResize);
        win.removeEventListener('scroll', handleScroll, true);
      }
      
      // 清除全局状态
      setActiveMode(null);
      setEnabled(false);
      clearSelection();
    }
  };

  // 修改handleMouseDown函数，设置isDragging状态
  const handleMouseDown = (e) => {
    // 关键修复：明确分离鼠标左键和右键的行为
    if (e.button === 0) { // 鼠标左键
      // 获取当前元素
      const target = e.currentTarget;
       
      // 检查是否为SVG元素或其子元素
      const isSvgElement = target.namespaceURI === 'http://www.w3.org/2000/svg' || 
                           target.tagName.toLowerCase() === 'svg' || 
                           target.closest('svg');
      
      // 设置拖拽状态
      isDragging = true;
      // 更新全局鼠标移动处理器的拖拽状态
      globalMouseMoveHandler.setDragging(true);
    
      // 如果元素已在编辑模式且不是SVG元素，则不启动拖拽
      // SVG元素始终可以拖拽，无论是否处于编辑模式
      if (target.hasAttribute('data-editing') && !isSvgElement) {
        return;
      }

      // 如果点击的是特殊控件（如调整大小的手柄），则不启动拖拽
      const isSpecialControlClick = e.target.closest('.resize-handle');
      if (isSpecialControlClick) {
        return;
      }

      // 阻止默认的文本选择等行为
      e.preventDefault();

      // 如果不处于编辑模式，允许dragMode处理拖拽
      dragMode.handleDragStart(e);
      
      // 设置全局状态
      setActiveMode('dragMode');
      selectElement(target);

    } else if (e.button === 2) { // 鼠标右键
      // 阻止默认的上下文菜单，并停止事件传播，防止触发后续的click事件
      // 这是解决右键点击出现"幽灵输入框"的关键
      e.preventDefault();
      e.stopPropagation();
    }
  };

  // 修改globalMouseUpHandler函数，重置isDragging状态
  const globalMouseUpHandler = (e) => {
    // 重置拖拽状态
    isDragging = false;
    // 更新全局鼠标移动处理器的拖拽状态
    globalMouseMoveHandler.setDragging(false);
    
    // 实现全局鼠标释放逻辑...
  };

  // 文档点击事件处理
  const documentClickHandler = (e) => {
    if (!containerEl || !doc) return;

    // 检查是否点击了特殊元素
    const isSpecialElementClick = isClickOnSpecialElement(e);

    // 检查是否点击了可编辑元素
    const clickedEditableElement = e.target.closest('[contenteditable="true"], [data-editable-fengmian]');

    // 如果点击了特殊元素，保持当前状态
    if (isSpecialElementClick) {
      return;
    }

    // 如果点击了可编辑元素，由元素自己的点击事件处理
    if (clickedEditableElement) {
      return;
    }

    // 获取当前选中的元素
    const currentSelectedElement = getSelectedElement();

    // 单击非文本区域退出所有编辑模式并清除拖拽状态
    forceExitAllEditModes();
    dragMode.clearAllDragModes();
    
    // 清除全局状态
    setActiveMode(null);
    
    // 如果之前有选中元素，确保正确清除其状态
    if (currentSelectedElement && currentSelectedElement.isConnected) {
      // 移除编辑标记
      currentSelectedElement.removeAttribute('data-editing');
      currentSelectedElement.classList.remove('editing-active-outline');
      // 如果当前元素是活动元素，则置为null
      if (currentSelectedElement === getActiveElement()) {
        setActiveElement(null);
      }
    }
    
    // 触发自定义事件，通知工具栏重置状态
    const event = new CustomEvent('editor-element-blur', {
      bubbles: true,
      detail: { target: null }
    });
    doc.dispatchEvent(event);
    
    // 如果有onElementClick回调，通知已退出编辑状态
    if (typeof onElementClick === 'function') {
      onElementClick(null, 'document-click');
    }
  };

  // 文档双击事件处理
  const documentDblClickHandler = (e) => {
    if (!containerEl || !doc) return;

    // 检查是否双击了特殊元素
    const isSpecialElementClick = isClickOnSpecialElement(e);

    // 检查是否双击了可编辑元素
    const clickedEditableElement = e.target.closest('[contenteditable="true"]');

    // 如果双击了特殊元素，保持当前状态
    if (isSpecialElementClick) {
      return;
    }

    // 如果双击了可编辑元素，由元素自己的双击事件处理
    if (clickedEditableElement) {
      return;
    }

    // 如果双击了其他区域，退出所有编辑模式并清除拖拽状态
    forceExitAllEditModes();
    dragMode.clearAllDragModes();
    
    // 清除全局状态
    setActiveMode(null);
  };

  // 元素点击事件处理 (单击)
  const handleElementClick = (e) => {
    // 单击（左键）的唯一职责是：如果元素不在编辑模式，则进入拖拽模式。
    if (e.button !== 0) {
      return; // 只响应左键
    }

    const target = e.currentTarget;
    if (!target || typeof target.hasAttribute !== 'function') return;

    // 检查是否为SVG元素或其子元素
    const isSvgElement = target.namespaceURI === 'http://www.w3.org/2000/svg' || 
                         target.tagName.toLowerCase() === 'svg' || 
                         target.closest('svg');

    // 如果点击的是特殊UI控件，则不处理
    if (isClickOnSpecialElement(e)) {
      if (onElementClick) {
        onElementClick(target, 'click-special-control');
      }
      return;
    }
    
    // 如果元素当前已经在编辑模式且不是SVG元素，则单击不执行任何操作，保持编辑状态
    // SVG元素始终响应点击进入拖拽模式
    if (target.hasAttribute('data-editing') && !isSvgElement) {
      return;
    }

    // 如果元素不在编辑状态，则单击进入拖拽模式
    // 清理之前的拖拽状态并为当前元素设置
    dragMode.clearAllDragModes();
    dragMode.setElementToDragMode(target);
    setActiveElement(target);
    
    // 更新全局状态
    setActiveMode('dragMode');
    selectElement(target);

    // 通知外部监听器
    if (onElementClick) {
      onElementClick(target, 'click-to-drag');
    }
  };

  // 元素双击事件处理
  const handleElementDblClick = (e) => {
    if (e.button !== 0) {
      return; // 只响应左键
    }

    // 立即阻止事件传播，防止触发 handleElementClick
    e.preventDefault();
    e.stopPropagation();

    const target = e.currentTarget;
    if (!target || typeof target.hasAttribute !== 'function') return;

    // 如果双击的是特殊UI控件，则不处理
    if (isClickOnSpecialElement(e)) {
      if (onElementClick) {
        onElementClick(target, 'dblclick-special-control');
      }
      return;
    }

    // 任何情况下的双击都应强制进入编辑模式
    forceEnterEditMode(target);

    if (onElementClick) {
      onElementClick(target, 'dblclick-to-edit');
    }
  };

  // 处理元素失去焦点事件
  const handleElementBlur = (e) => {
    const target = e.currentTarget;
    if (!target) return;

    // 调用editMode的handleBlur函数
    editMode.handleBlur(e);
    
    // 如果当前模式是editMode，且blur的元素是当前选中的元素，则清除模式
    if (getActiveMode() === 'editMode' && target === getSelectedElement()) {
      setActiveMode(null);
      clearSelection();
      setActiveElement(null); // 清除活动元素
      
      // 确保元素的编辑状态标记被清除
      target.removeAttribute('data-editing');
      target.classList.remove('editing-active-outline');
      
      // 触发自定义事件，通知工具栏重置状态
      const event = new CustomEvent('editor-element-blur', {
        bubbles: true,
        detail: { target }
      });
      doc.dispatchEvent(event);
    
      // 如果有onElementClick回调，通知已退出编辑状态
      if (typeof onElementClick === 'function') {
        onElementClick(null, 'element-blur');
      }
    }
  };

  // 使用前面已经定义的isClickOnSpecialElement函数

  // 处理全局鼠标按下事件
  const globalMouseDownHandler = (e) => {
    // 如果是右键点击，阻止默认行为和事件传播
    if (e.button === 2) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    
    // 实现全局鼠标按下逻辑...
  };

  // 处理选择改变事件
  const handleSelectionChange = (e) => {
    // 如果正在拖拽，不处理选择变化
    if (isDragging) {
      if (debugMode) console.log('正在拖拽，不处理选择变化');
      return;
    }

    // 获取当前选区
    const selection = doc.getSelection();
    if (!selection || selection.rangeCount === 0) {
      if (debugMode) console.log('没有有效选区，不处理选择变化');
      return;
    }

    try {
      // 获取选区的公共祖先节点
      let parentElement = selection.getRangeAt(0).commonAncestorContainer;
      if (parentElement.nodeType === Node.TEXT_NODE) {
        parentElement = parentElement.parentNode;
      }

      if (debugMode) console.log('选择变更: 找到元素', parentElement.tagName, parentElement.className);

      // 如果找到有效元素，更新activeElement
      if (parentElement && parentElement instanceof HTMLElement && parentElement.isConnected) {
        // 获取当前活动元素进行比较
        const currentActiveElement = getActiveElement();
        
        // 检查元素是否可编辑的更全面方法
        const isEditableElement = 
          // 1. 检查元素是否在可编辑元素跟踪器中
          (editableElementsTracker && editableElementsTracker.has(parentElement)) || 
          // 2. 检查元素是否有contenteditable属性
          parentElement.getAttribute('contenteditable') === 'true' || 
          // 3. 检查元素是否有data-editable-fengmian属性
          parentElement.hasAttribute('data-editable-fengmian') ||
          // 4. 检查元素是否在contenteditable元素内
          parentElement.closest('[contenteditable="true"]') ||
          // 5. 检查元素是否在data-editable-fengmian元素内
          parentElement.closest('[data-editable-fengmian]');
        
        if (isEditableElement) {
          if (debugMode) console.log('选择变更: 找到可编辑元素', parentElement.tagName, parentElement.className);
          
          // 如果当前活动元素与新选中元素不同，则先处理上一个元素的失焦
          if (currentActiveElement && currentActiveElement !== parentElement && currentActiveElement.isConnected) {
            if (debugMode) console.log('选择变更: 切换到新元素，清除旧元素状态');
            
            // 移除旧元素的编辑标记
            currentActiveElement.removeAttribute('data-editing');
            currentActiveElement.classList.remove('editing-active-outline');
            
            // 触发自定义事件，通知工具栏有元素失焦
            const blurEvent = new CustomEvent('editor-element-blur', {
              bubbles: true,
              detail: { target: currentActiveElement }
            });
            doc.dispatchEvent(blurEvent);
          }
          
          // 设置活动元素
          setActiveElement(parentElement);
          
          // 如果当前不在编辑模式，则设置为编辑模式
          if (getActiveMode() !== 'editMode') {
            if (debugMode) console.log('选择变更: 设置编辑模式');
            setActiveMode('editMode');
            selectElement(parentElement);
          }
          
          // 确保元素有正确的样式和状态
          if (!parentElement.classList.contains('editing-active-outline')) {
            if (debugMode) console.log('选择变更: 添加编辑样式');
            parentElement.classList.add('editing-active-outline');
          }
          
          if (!parentElement.hasAttribute('data-editing')) {
            if (debugMode) console.log('选择变更: 添加编辑状态标记');
            parentElement.setAttribute('data-editing', 'true');
          }
          
          // 移除可能的拖拽模式样式
          parentElement.classList.remove('drag-mode', 'selected-for-drag');
          
          // 如果有onSelectionChange回调，通知外部
          if (typeof onElementClick === 'function') {
            if (debugMode) console.log('选择变更: 调用onElementClick回调');
            onElementClick(parentElement, 'selection-change');
          }
        } else {
          if (debugMode) console.log('选择变更: 元素不可编辑', parentElement.tagName, parentElement.className);
          
          // 如果当前有活动编辑元素，且不在点击事件的处理中（避免冲突）
          // 由于selectionchange可能在click之前触发，这里不清除活动元素，让click事件处理
        }
      } else {
        if (debugMode) console.log('选择变更: 无有效元素');
        // 同样，这里不清除活动元素，让click事件处理
      }
    } catch (error) {
      console.error('选择变更处理错误:', error);
    }
  };

  // 处理窗口大小变化
  const handleResize = (e) => {
    // 实现窗口大小变化逻辑...
  };

  // 处理滚动事件
  const handleScroll = (e) => {
    // 实现滚动逻辑...
  };

  // 阻止默认动作
  const preventDefaultAction = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // 处理调整大小移动事件
  const handleResizeMove = (e) => {
    // 实现调整大小移动逻辑...
  };

  // 处理调整大小结束事件
  const handleResizeEnd = (e) => {
    // 实现调整大小结束逻辑...
  };

  // 返回公共API
  return {
    addGlobalEventListeners,
    attachElementEventListeners,
    cleanupListeners,
    forceExitAllEditModes,
    forceEnterEditMode,
    isEditing: () => context.getIsEditing(),
    setIsEditing: (status) => context.setIsEditing(status),
    selectElement: (element) => selectElement(element),
    getSelectedElement: () => getSelectedElement(),
    clearSelection: () => clearSelection(),
    getActiveMode: () => getActiveMode(),
    setActiveMode: (mode) => setActiveMode(mode),
    isEnabled: (mode) => isEnabled(mode),
    setEnabled: (value) => setEnabled(value),
    getContainer: () => getContainer()
  };
};

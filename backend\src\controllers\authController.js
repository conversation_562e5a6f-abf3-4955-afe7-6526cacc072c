const { User, PointRecord, LoginLog, FeatureControl } = require('../models');
const { generateToken } = require('../utils/tokenUtils');
const { successResponse, errorResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');
const { SystemConfig } = require('../models');
const { verifySmsCode } = require('../utils/smsCodeManager');
const crypto = require('crypto');
const jwt = require('jsonwebtoken');

/**
 * 获取用户角色对应的每日积分配置值
 * @param {string} role 用户角色
 * @returns {Promise<number>} 每日积分值
 */
const getDailyPointsForRole = async (role) => {
  try {
    // 根据角色选择对应的功能名称
    const featureName = role === 'vip' ? '每日积分（高级）' : '每日积分（普通）';
    
    // 查询功能控制记录
    const feature = await FeatureControl.findOne({
      where: {
        feature_name: featureName,
        is_active: true
      }
    });
    
    // 如果找到功能配置且积分值有效，则返回配置的积分值
    if (feature && typeof feature.points_cost === 'number') {
      return feature.points_cost;
    }
    
    // 默认返回0
    return 0;
  } catch (error) {
    logger.error(`获取${role}角色每日积分配置失败:`, error);
    return 0; // 出错时返回默认值
  }
};

/**
 * 用户注册
 * @route POST /api/auth/register
 */
const register = async (req, res) => {
  const { phone, password, nickname } = req.body;

  try {
    // 检查手机号是否已注册
    const existingUser = await User.findOne({ where: { phone } });

    if (existingUser) {
      return errorResponse(res, '该手机号已注册', 400);
    }

    // 获取系统配置中的新用户积分奖励
    let newUserPoints = 50; // 默认值为50

    try {
      const pointsConfig = await SystemConfig.findOne({
        where: { config_key: 'points_new_user' }
      });

      if (pointsConfig && pointsConfig.config_value) {
        newUserPoints = parseInt(pointsConfig.config_value);
        if (isNaN(newUserPoints)) {
          newUserPoints = 50;
          logger.warn('新用户积分配置值无效，使用默认值50');
        }
      } else {
        logger.info('未找到新用户积分配置，使用默认值50');
      }
    } catch (error) {
      logger.error('获取新用户积分配置失败:', error);
      // 使用默认值继续
    }

    // 获取用户角色对应的每日积分默认值
    const role = 'user'; // 新注册用户默认为普通用户
    const dailyPoints = await getDailyPointsForRole(role);

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 创建新用户
      const user = await User.create({
        phone,
        password, // 密码将通过钩子自动加密
        nickname: nickname || `用户${phone.substring(7)}`, // 默认昵称
        role: 'user',
        points: newUserPoints, // 使用系统配置的新用户积分
        login_count: 1,
        last_login_time: new Date(),
        daily_points: dailyPoints, // 设置每日积分默认值
        last_daily_points_update: new Date() // 设置每日积分更新时间
      }, { transaction });

      // 创建积分记录
      await PointRecord.create({
        user_id: user.id,
        points_change: newUserPoints,
        points_after: newUserPoints,
        operation_type: 'register',
        description: '新用户注册奖励'
      }, { transaction });

      // 提交事务
      await transaction.commit();

      // 生成token
      const token = generateToken(user);

      logger.info(`新用户注册成功: ${phone}，获得${newUserPoints}积分，每日积分${dailyPoints}`);

      return successResponse(res, '注册成功', {
        token,
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          role: user.role,
          points: user.points,
          daily_points: user.daily_points
        }
      }, 201);
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    logger.error('用户注册失败:', error);
    return errorResponse(res, '注册失败，请稍后再试', 500);
  }
};

/**
 * 用户验证码注册
 * @route POST /api/auth/register/verify-code
 */
const registerByVerifyCode = async (req, res) => {
  const { phone, verifyCode, nickname, password } = req.body;

  try {
    // 检查验证码
    const isCodeValid = verifySmsCode(phone, verifyCode, 'register');

    if (!isCodeValid) {
      return errorResponse(res, '验证码无效或已过期', 400);
    }

    // 检查手机号是否已注册
    const existingUser = await User.findOne({ where: { phone } });

    if (existingUser) {
      return errorResponse(res, '该手机号已注册', 400);
    }

    // 获取系统配置中的新用户积分奖励
    let newUserPoints = 50; // 默认值为50

    try {
      const pointsConfig = await SystemConfig.findOne({
        where: { config_key: 'points_new_user' }
      });

      if (pointsConfig && pointsConfig.config_value) {
        newUserPoints = parseInt(pointsConfig.config_value);
        if (isNaN(newUserPoints)) {
          newUserPoints = 50;
          logger.warn('新用户积分配置值无效，使用默认值50');
        }
      } else {
        logger.info('未找到新用户积分配置，使用默认值50');
      }
    } catch (error) {
      logger.error('获取新用户积分配置失败:', error);
      // 使用默认值继续
    }

    // 如果没有提供密码，则使用空字符串
    const userPassword = password || '';

    // 根据用户是否提供有效密码来设置has_set_password
    // 如果密码为空或长度小于8，认为用户未设置密码
    const hasSetPassword = userPassword.length >= 8;

    // 获取用户角色对应的每日积分默认值
    const role = 'user'; // 新注册用户默认为普通用户
    const dailyPoints = await getDailyPointsForRole(role);

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 创建新用户
      const user = await User.create({
        phone,
        password: userPassword, // 密码将通过钩子自动加密
        nickname: nickname || `用户${phone.substring(7)}`, // 默认昵称
        role: 'user',
        points: newUserPoints, // 使用系统配置的新用户积分
        login_count: 1,
        last_login_time: new Date(),
        has_set_password: hasSetPassword, // 设置是否已设置密码状态
        status: 'active', // 确保用户状态为激活
        daily_points: dailyPoints, // 设置每日积分默认值
        last_daily_points_update: new Date() // 设置每日积分更新时间
      }, { transaction });

      // 创建积分记录
      await PointRecord.create({
        user_id: user.id,
        points_change: newUserPoints,
        points_after: newUserPoints,
        operation_type: 'register',
        description: '新用户注册奖励'
      }, { transaction });

      // 提交事务
      await transaction.commit();

      // 生成token
      const token = generateToken(user);

      logger.info(`新用户通过验证码注册成功: ${phone}，获得${newUserPoints}积分, 密码设置状态: ${hasSetPassword}，每日积分${dailyPoints}`);

      return successResponse(res, '注册成功', {
        token,
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          role: user.role,
          points: user.points,
          has_set_password: hasSetPassword,
          is_new_user: !hasSetPassword, // 标记是否为新用户
          daily_points: user.daily_points
        }
      }, 201);
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    logger.error('用户验证码注册失败:', error);
    return errorResponse(res, '注册失败，请稍后再试', 500);
  }
};

/**
 * 用户登录
 * @route POST /api/auth/login
 */
const login = async (req, res) => {
  const { phone, password } = req.body;

  try {
    // 参数验证
    if (!phone || !password) {
      return errorResponse(res, '手机号和密码不能为空', 400);
    }

    // 查找用户
    const user = await User.findOne({ where: { phone } });
    if (!user) {
      logger.warn(`登录失败: 手机号 ${phone} 不存在`);
      return errorResponse(res, '手机号或密码错误', 401);
    }

    // 检查用户是否被锁定
    if (user.isLocked()) {
      logger.warn(`登录失败: 用户 ${user.id} 账号已被锁定`);
      // 计算剩余锁定时间
      const remainingTime = user.lock_expires_at ? Math.ceil((new Date(user.lock_expires_at) - new Date()) / (60 * 60 * 1000)) : 24;
      return errorResponse(res, `账号已被锁定，请${remainingTime}小时后再试或联系客服`, 403);
    }

    // 验证密码
    const isPasswordValid = await user.validatePassword(password);
    if (!isPasswordValid) {
      logger.warn(`登录失败: 用户 ${user.id} 密码错误`);

      // 增加失败登录尝试次数
      const isLocked = await user.incrementFailedLoginAttempts();
      if (isLocked) {
        logger.warn(`用户 ${user.id} 登录失败次数过多，账号已被锁定24小时`);
        return errorResponse(res, '密码错误次数过多，账号已被锁定24小时', 403);
      }

      // 计算剩余尝试次数
      const remainingAttempts = 5 - user.failed_login_attempts;
      return errorResponse(res, `手机号或密码错误，还有${remainingAttempts}次尝试机会`, 401);
    }

    // 重置失败登录尝试次数
    await user.resetFailedLoginAttempts();

    // 检查并更新VIP状态
    if (user.role === 'vip') {
      const vipStatusUpdated = await user.checkAndUpdateVipStatus();
      if (vipStatusUpdated) {
        logger.info(`用户${user.id}(${user.phone})的VIP已过期，已自动转为普通用户`);
      }
    }

    // 检查并更新每日积分
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    // 如果没有上次每日积分更新时间或上次更新时间不是今天
    if (!user.last_daily_points_update || new Date(user.last_daily_points_update) < today) {
      // 获取用户角色对应的每日积分默认值
      const dailyPoints = await getDailyPointsForRole(user.role);
      
      // 只在积分值发生变化时记录日志
      if (user.daily_points !== dailyPoints) {
        logger.info(`用户 ${user.id} 登录时更新每日积分为 ${dailyPoints}`);
      }
      
      // 更新用户的每日积分
      user.daily_points = dailyPoints;
      user.last_daily_points_update = now;
    }

    // 更新登录信息
    user.last_login_time = new Date();
    user.login_count += 1;
    user.last_login_type = 'password';
    user.last_login_ip = req.ip;
    await user.save();

    // 创建JWT令牌
    const token = generateToken(user);

    // 创建登录日志
    try {
      await LoginLog.create({
        user_id: user.id,
        login_type: 'password',
        login_time: new Date(),
        login_ip: req.ip || '未知',
        login_device: req.headers['user-agent'] || '未知',
        login_status: 'success'
      });
    } catch (logError) {
      logger.error(`记录登录日志失败: ${logError.message}`, logError);
      // 继续执行，不影响主流程
    }

    logger.info(`用户 ${user.id} (${user.phone}) 登录成功`);

    return successResponse(res, '登录成功', {
      token,
      user: {
        id: user.id,
        phone: user.phone,
        nickname: user.nickname,
        avatar: user.avatar,
        role: user.role,
        points: user.points,
        daily_points: user.daily_points,
        vip_expire_date: user.vip_expire_date,
        last_login_time: user.last_login_time,
        is_vip: user.isVip(),
        created_at: user.createdAt
      }
    });
  } catch (error) {
    logger.error('用户登录出错:', error);
    return errorResponse(res, '登录失败，请稍后再试', 500);
  }
};

/**
 * 用户验证码登录
 * @route POST /api/auth/login/verify-code
 */
const loginByVerifyCode = async (req, res) => {
  const { phone, verifyCode } = req.body;

  try {
    // 检查验证码
    const isCodeValid = verifySmsCode(phone, verifyCode, 'login');

    if (!isCodeValid) {
      return errorResponse(res, '验证码无效或已过期', 400);
    }

    // 查找用户
    let user = await User.findOne({ where: { phone } });

    // 如果用户存在且被锁定，验证码登录也不允许
    if (user && user.isLocked()) {
      logger.warn(`验证码登录失败: 用户 ${user.id} 账号已被锁定`);
      // 计算剩余锁定时间
      const remainingTime = user.lock_expires_at ? Math.ceil((new Date(user.lock_expires_at) - new Date()) / (60 * 60 * 1000)) : 24;
      return errorResponse(res, `账号已被锁定，请${remainingTime}小时后再试或联系客服`, 403);
    }

    // 如果用户不存在，自动注册
    if (!user) {
      // 获取系统配置中的新用户积分奖励
      let newUserPoints = 50; // 默认值
      try {
        const pointsConfig = await SystemConfig.findOne({
          where: { config_key: 'points_new_user' }
        });

        if (pointsConfig && pointsConfig.config_value) {
          newUserPoints = parseInt(pointsConfig.config_value);
          if (isNaN(newUserPoints)) {
            newUserPoints = 50;
            logger.warn('新用户积分配置值无效，使用默认值50');
          }
        }
      } catch (error) {
        logger.error('获取新用户积分配置失败:', error);
        // 使用默认值继续
      }

      // 获取用户角色对应的每日积分默认值
      const role = 'user'; // 新注册用户默认为普通用户
      const dailyPoints = await getDailyPointsForRole(role);

      // 不再生成随机密码，使用空字符串
      const randomPassword = '';

      // 开启事务
      const transaction = await sequelize.transaction();

      try {
        // 创建新用户
        user = await User.create({
          phone,
          password: randomPassword, // 密码将通过钩子自动加密
          nickname: `用户${phone.substring(7)}`, // 默认昵称
          role: 'user',
          status: 'active', // 设置状态为正常
          points: newUserPoints,
          has_set_password: false, // 标记用户未设置密码
          login_count: 1,
          last_login_time: new Date(),
          last_login_ip: req.ip,
          last_login_type: 'phone',
          daily_points: dailyPoints, // 设置每日积分默认值
          last_daily_points_update: new Date() // 设置每日积分更新时间
        }, { transaction });

        // 创建积分记录
        await PointRecord.create({
          user_id: user.id,
          points_change: newUserPoints,
          points_after: newUserPoints,
          operation_type: 'register',
          description: '新用户注册奖励'
        }, { transaction });

        // 创建登录日志
        await LoginLog.create({
          user_id: user.id,
          login_type: 'phone',
          login_time: new Date(),
          login_ip: req.ip,
          login_device: req.headers['user-agent'] || '',
          login_status: 'success'
        }, { transaction });

        // 提交事务
        await transaction.commit();

        logger.info(`用户通过验证码登录自动注册成功: ${phone}，获得${newUserPoints}积分，每日积分${dailyPoints}`);
      } catch (error) {
        // 回滚事务
        await transaction.rollback();
        throw error;
      }
    } else {
      // 如果用户存在，重置失败登录尝试次数
      await user.resetFailedLoginAttempts();
      
      // 检查并更新每日积分
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      // 如果没有上次每日积分更新时间或上次更新时间不是今天
      if (!user.last_daily_points_update || new Date(user.last_daily_points_update) < today) {
        // 获取用户角色对应的每日积分默认值
        const dailyPoints = await getDailyPointsForRole(user.role);
        
        // 只在积分值发生变化时记录日志
        if (user.daily_points !== dailyPoints) {
          logger.info(`用户 ${user.id} 验证码登录时更新每日积分为 ${dailyPoints}`);
        }
        
        // 更新用户的每日积分
        user.daily_points = dailyPoints;
        user.last_daily_points_update = now;
      }
    }

    // 更新登录信息
    user.last_login_time = new Date();
    user.last_login_ip = req.ip;
    user.last_login_type = 'phone';
    user.login_count += 1;
    await user.save();

    // 检查VIP状态 - 如果是VIP且今天没有获得每日积分，则添加每日积分
    if (user.isVip()) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      // 如果没有上次积分重置时间或上次重置时间不是今天
      if (!user.last_points_reset || new Date(user.last_points_reset) < today) {
        // 开启事务
        const transaction = await sequelize.transaction();

        try {
          // 增加积分
          user.points += 100;
          user.last_points_reset = now;
          await user.save({ transaction });

          // 创建积分记录
          await PointRecord.create({
            user_id: user.id,
            points_change: 100,
            points_after: user.points,
            operation_type: 'daily_reward',
            description: 'VIP用户每日积分奖励'
          }, { transaction });

          // 提交事务
          await transaction.commit();

          logger.info(`VIP用户${user.phone}获得每日积分奖励`);
        } catch (error) {
          // 回滚事务
          await transaction.rollback();
          throw error;
        }
      }
    }

    // 生成token
    const token = generateToken(user);

    // 创建登录日志（如果不是新注册用户）
    if (user.login_count > 1) {
      try {
        await LoginLog.create({
          user_id: user.id,
          login_type: 'phone',
          login_time: new Date(),
          login_ip: req.ip || '未知',
          login_device: req.headers['user-agent'] || '未知',
          login_status: 'success'
        });
      } catch (logError) {
        logger.error(`记录登录日志失败: ${logError.message}`, logError);
        // 继续执行，不影响主流程
      }
    }

    logger.info(`用户通过验证码登录成功: ${phone}`);

    return successResponse(res, '登录成功', {
      token,
      user: {
        id: user.id,
        phone: user.phone,
        nickname: user.nickname,
        avatar: user.avatar,
        role: user.role,
        points: user.points,
        daily_points: user.daily_points,
        vip_expire_date: user.vip_expire_date,
        has_set_password: user.has_set_password,
        is_new_user: !user.has_set_password // 标记是否为新用户
      }
    });
  } catch (error) {
    logger.error('用户验证码登录失败:', error);
    return errorResponse(res, '登录失败，请稍后再试', 500);
  }
};

/**
 * 重置密码
 * @route POST /api/auth/reset-password
 */
const resetPassword = async (req, res) => {
  const { phone, newPassword, verifyCode } = req.body;

  try {
    logger.info(`尝试重置密码，手机号: ${phone}`);

    // 查找用户
    const user = await User.findOne({ where: { phone } });

    if (!user) {
      logger.warn(`用户不存在，手机号: ${phone}`);
      return errorResponse(res, '用户不存在', 404);
    }

    try {
      // 更新密码
      user.password = newPassword; // 密码将通过钩子自动加密
      user.has_set_password = true; // 标记用户已设置密码
      await user.save();

      logger.info(`密码更新成功，用户ID: ${user.id}, 手机号: ${phone}`);
    } catch (saveError) {
      logger.error(`保存密码失败: ${saveError.message}`, saveError);
      return errorResponse(res, '密码更新失败，请稍后再试', 500);
    }

    // 记录登录日志
    try {
      await LoginLog.create({
        user_id: user.id,
        login_type: 'reset_password',
        login_time: new Date(),
        login_ip: req.ip || '未知',
        login_device: req.headers['user-agent'] || '未知',
        login_status: 'success'
      });
      logger.info(`记录密码重置日志成功，用户ID: ${user.id}`);
    } catch (logError) {
      logger.error(`记录密码重置日志失败: ${logError.message}`, logError);
      // 继续执行，不影响主流程
    }

    logger.info(`用户${phone}重置密码成功`);

    return successResponse(res, '密码重置成功');
  } catch (error) {
    logger.error(`重置密码失败: ${error.message}`, error);
    return errorResponse(res, '重置密码失败，请稍后再试', 500);
  }
};

/**
 * 获取当前用户信息
 * @route GET /api/auth/me
 */
const getMe = async (req, res) => {
  try {
    // 用户信息已在auth中间件中添加到req.user
    const user = req.user;

    return successResponse(res, '获取用户信息成功', {
      id: user.id,
      phone: user.phone,
      nickname: user.nickname,
      avatar: user.avatar,
      role: user.role,
      points: user.points,
      vip_expire_date: user.vip_expire_date,
      last_login_time: user.last_login_time,
      login_count: user.login_count,
      created_at: user.createdAt
    });
  } catch (error) {
    logger.error('获取用户信息失败:', error);
    return errorResponse(res, '获取用户信息失败', 500);
  }
};

module.exports = {
  register,
  registerByVerifyCode,
  login,
  loginByVerifyCode,
  resetPassword,
  getMe
};

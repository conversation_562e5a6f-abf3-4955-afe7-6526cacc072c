import React, { useState, useEffect } from 'react';
import { Button, Result } from 'antd';

// 由于React的Error Boundary必须是类组件，我们只能做一个简单的错误处理组件
function ErrorFallback({ error, resetErrorBoundary }) {
  return (
    <Result
      status="error"
      title="页面出现了错误"
      subTitle={`错误信息: ${error?.message || '未知错误'}`}
      extra={
        <Button type="primary" onClick={() => {
          // 清除可能导致问题的全局变量
          if (window.__originalHTML) {
            delete window.__originalHTML;
          }
          // 刷新页面
          window.location.reload();
        }}>
          刷新页面
        </Button>
      }
    />
  );
}

// 由于无法使用类组件的ErrorBoundary，我们提供一个简单函数，将来集成真正的错误边界
export default function ErrorBoundary({ children }) {
  const [error, setError] = useState(null);

  useEffect(() => {
    // 监听全局错误
    const handleError = (event) => {
      console.error('捕获到全局错误:', event);
      setError(event.error || new Error('未知错误'));
    };

    window.addEventListener('error', handleError);
    return () => {
      window.removeEventListener('error', handleError);
    };
  }, []);

  if (error) {
    return <ErrorFallback error={error} resetErrorBoundary={() => setError(null)} />;
  }

  return children;
}

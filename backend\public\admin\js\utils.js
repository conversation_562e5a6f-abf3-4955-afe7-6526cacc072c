/**
 * 管理后台通用工具函数
 */

/**
 * 获取带有授权标头的请求头对象
 * @returns {Object} 包含授权标头的请求头对象
 */
function getAuthHeaders() {
  const token = localStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'Cache-Control': 'no-cache, no-store, must-revalidate'
  };
}

/**
 * 处理API响应
 * @param {Response} response - fetch API的响应对象
 * @returns {Promise} 解析后的JSON数据
 * @throws {Error} 如果响应状态码不是200，则抛出错误
 */
function handleResponse(response) {
  if (!response.ok) {
    return response.json().then(data => {
      throw new Error(data.message || `HTTP错误! 状态: ${response.status}`);
    });
  }
  return response.json();
}

/**
 * 显示加载中提示
 * @param {string} message - 可选的加载提示消息
 */
function showLoading(message = '加载中...') {
  // 检查是否已存在loading元素
  let loadingElement = document.getElementById('globalLoadingIndicator');
  
  if (!loadingElement) {
    loadingElement = document.createElement('div');
    loadingElement.id = 'globalLoadingIndicator';
    loadingElement.className = 'loading-overlay';
    
    const spinner = document.createElement('div');
    spinner.className = 'spinner-border text-primary';
    spinner.setAttribute('role', 'status');
    
    const loadingText = document.createElement('span');
    loadingText.className = 'loading-text';
    loadingText.textContent = message;
    
    loadingElement.appendChild(spinner);
    loadingElement.appendChild(loadingText);
    
    document.body.appendChild(loadingElement);
  } else {
    // 更新现有loading元素的消息
    const loadingText = loadingElement.querySelector('.loading-text');
    if (loadingText) {
      loadingText.textContent = message;
    }
    
    // 确保它是可见的
    loadingElement.style.display = 'flex';
  }
}

/**
 * 隐藏加载中提示
 */
function hideLoading() {
  const loadingElement = document.getElementById('globalLoadingIndicator');
  if (loadingElement) {
    loadingElement.style.display = 'none';
  }
}

/**
 * 显示错误提示
 * @param {string} message - 错误消息
 * @param {number} duration - 显示持续时间，单位为毫秒，默认为3000
 */
function showError(message, duration = 3000) {
  showToast(message, 'danger', duration);
}

/**
 * 显示成功提示
 * @param {string} message - 成功消息
 * @param {number} duration - 显示持续时间，单位为毫秒，默认为3000
 */
function showSuccess(message, duration = 3000) {
  showToast(message, 'success', duration);
}

/**
 * 显示通用提示
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型，可选值：'success', 'danger', 'warning', 'info'
 * @param {number} duration - 显示持续时间，单位为毫秒，默认为3000
 */
function showToast(message, type = 'info', duration = 3000) {
  // 检查是否已存在toast容器
  let toastContainer = document.getElementById('toast-container');
  
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(toastContainer);
  }
  
  // 创建新的toast元素
  const toastId = 'toast-' + Date.now();
  const toastElement = document.createElement('div');
  toastElement.id = toastId;
  toastElement.className = `toast align-items-center bg-${type} text-white`;
  toastElement.setAttribute('role', 'alert');
  toastElement.setAttribute('aria-live', 'assertive');
  toastElement.setAttribute('aria-atomic', 'true');
  
  const toastContent = document.createElement('div');
  toastContent.className = 'd-flex';
  
  const toastBody = document.createElement('div');
  toastBody.className = 'toast-body';
  toastBody.textContent = message;
  
  const closeButton = document.createElement('button');
  closeButton.type = 'button';
  closeButton.className = 'btn-close btn-close-white me-2 m-auto';
  closeButton.setAttribute('data-bs-dismiss', 'toast');
  closeButton.setAttribute('aria-label', '关闭');
  
  toastContent.appendChild(toastBody);
  toastContent.appendChild(closeButton);
  toastElement.appendChild(toastContent);
  
  toastContainer.appendChild(toastElement);
  
  // 实例化toast
  const toast = new bootstrap.Toast(toastElement, {
    autohide: true,
    delay: duration
  });
  
  // 显示toast
  toast.show();
  
  // 自动移除toast元素
  setTimeout(() => {
    if (toastElement && toastElement.parentNode) {
      toastElement.parentNode.removeChild(toastElement);
    }
  }, duration + 500);
}

/**
 * 格式化日期时间
 * @param {string|Date} dateString - 日期字符串或Date对象
 * @param {boolean} includeTime - 是否包含时间部分，默认为true
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(dateString, includeTime = true) {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) return '-';
    
    if (includeTime) {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    }
  } catch (error) {
    // 日期格式化错误，返回默认值
    return '-';
  }
}

/**
 * 防抖函数，用于限制函数的调用频率
 * @param {Function} func - 要执行的函数
 * @param {number} wait - 等待时间，单位为毫秒，默认为300
 * @returns {Function} 防抖处理后的函数
 */
function debounce(func, wait = 300) {
  let timeout;
  
  return function(...args) {
    const context = this;
    
    clearTimeout(timeout);
    
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
} 

/**
 * 安全的日志工具，在生产环境中不输出日志
 * 默认禁用所有控制台输出，以提高安全性
 */
const logger = (function() {
  // 设置为false可以禁用所有日志输出
  const isLoggingEnabled = false;
  
  return {
    log: function(...args) {
      if (isLoggingEnabled) {
        console.log(...args);
      }
    },
    
    error: function(...args) {
      if (isLoggingEnabled) {
        console.error(...args);
      }
    },
    
    warn: function(...args) {
      if (isLoggingEnabled) {
        console.warn(...args);
      }
    },
    
    info: function(...args) {
      if (isLoggingEnabled) {
        console.info(...args);
      }
    },
    
    debug: function(...args) {
      if (isLoggingEnabled) {
        console.debug(...args);
      }
    }
  };
})(); 
import axios from 'axios';
import { message } from 'antd';
import logger from '../../../services/logs/frontendLogger';

/**
 * 计算HTML内容的哈希值，用于比较内容是否变化
 * @param {string} str HTML内容
 * @returns {number} 哈希值
 */
const getContentHash = (str) => {
  let hash = 0;
  if (!str || str.length === 0) return hash;
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return hash;
};

/**
 * 清理HTML内容，移除编辑相关属性和元素
 * @param {HTMLElement} element 要清理的元素
 * @returns {HTMLElement} 清理后的元素克隆
 */
const cleanHtmlForSave = (element) => {
  if (!element) return null;

  // 创建元素的克隆
  const cleanElement = element.cloneNode(true);

  // 移除所有contenteditable属性和相关编辑属性
  const editableElements = cleanElement.querySelectorAll('[contenteditable], [data-field], .drag-mode, [data-editing], [data-editable-fengmian]');
  editableElements.forEach(el => {
    // 检查是否是图片或图片容器或文本框
    const isImg = el.tagName.toLowerCase() === 'img';
    const hasImg = el.querySelector('img') !== null;
    // 识别文本框元素 - 绝对定位且不是图片元素
    const isTextBox = el.style && 
                     el.style.position === 'absolute' && 
                     !isImg && !hasImg && 
                     !el.hasAttribute('data-image-container') &&
                     el.textContent && el.textContent.trim() !== '';
    const isImageContainer = isImg || hasImg || el.hasAttribute('data-image-container') || isTextBox;
    
    // 检查是否有垂直对齐设置
    const hasVerticalAlign = el.hasAttribute('data-vertical-align');
    const verticalAlignValue = hasVerticalAlign ? el.getAttribute('data-vertical-align') : null;
    
    if (!isImageContainer) {
      // 普通元素移除所有编辑属性
      el.removeAttribute('contenteditable');
      el.removeAttribute('data-editable-fengmian');
      el.removeAttribute('data-field');
      el.removeAttribute('data-editing');
      el.removeAttribute('title');
      el.removeAttribute('tabindex');
    } else {
      // 图片容器或文本框保留关键属性
      if (isTextBox) {
        // 文本框特殊处理 - 保留关键属性
        el.setAttribute('data-editable-fengmian', 'true');
        el.setAttribute('contenteditable', 'true');
      } else {
        // 图片容器处理
        el.setAttribute('data-image-container', 'true');
        el.setAttribute('data-editable-fengmian', 'true');
        el.setAttribute('contenteditable', 'true');
      }
      
      // 确保图片元素可以接收鼠标事件
      el.style.pointerEvents = 'auto';
      
      // 确保图片容器内的图片也可以接收鼠标事件
      if (hasImg) {
        const imgElement = el.querySelector('img');
        if (imgElement) {
          imgElement.style.pointerEvents = 'auto';
        }
      }
    }
    
    // 保留垂直对齐状态和相关样式
    if (hasVerticalAlign) {
      // 保留data-vertical-align属性
      el.setAttribute('data-vertical-align', verticalAlignValue);
      
      // 保留flex布局相关样式，确保垂直对齐效果在页面加载后能正确恢复
      if (el.style) {
        if (verticalAlignValue === 'top' || verticalAlignValue === 'middle' || verticalAlignValue === 'bottom') {
          // 保留flex布局
          el.style.display = 'flex';
          el.style.flexDirection = 'column';
          el.style.alignItems = 'stretch';
          
          // 根据垂直对齐值设置justify-content
          if (verticalAlignValue === 'top') {
            el.style.justifyContent = 'flex-start';
          } else if (verticalAlignValue === 'middle') {
            el.style.justifyContent = 'center';
          } else if (verticalAlignValue === 'bottom') {
            el.style.justifyContent = 'flex-end';
          }
        }
      }
    }
    
    // 移除所有元素的拖拽相关类
    el.classList.remove('drag-mode');
    el.classList.remove('dragging');
    el.classList.remove('resize-mode');
    el.classList.remove('editing-active-outline');
    el.classList.remove('selected-for-drag');

    // 清理可能影响显示的内联样式，但保留图片容器和文本框的必要样式
    if (el.style) {
      el.style.cursor = isImageContainer ? 'move' : 'default';
      el.style.outline = 'none';
      
      // 对于非图片容器和非文本框，移除所有可能的拖拽相关内联样式
      if (!isImageContainer && !hasVerticalAlign) {
        el.style.removeProperty('z-index');
        el.style.removeProperty('opacity');
        el.style.removeProperty('user-select');
        el.style.removeProperty('-webkit-user-select');
        el.style.removeProperty('-moz-user-select');
        el.style.removeProperty('-ms-user-select');
      }
    }
  });

  // 移除所有编辑器UI相关元素
  const elementsToRemove = cleanElement.querySelectorAll(
    '.resize-handle, .drag-handle, .text-editor-toolbar, .text-editor-toolbar-container, ' +
    '.color-picker, .color-picker-wrapper, .color-picker-popup, .editor-control, ' +
    '.text-editor-controls, .text-editor-panel, [data-editor-ui="true"]'
  );
  elementsToRemove.forEach(el => el.remove());

  return cleanElement;
};

/**
 * 获取当前iframe中的HTML内容
 * @param {React.RefObject} iframeRef iframe的React引用
 * @returns {Object|null} 包含HTML内容和内容容器的对象，如果获取失败则返回null
 */
const getCurrentHtmlContent = (iframeRef) => {
  if (!iframeRef.current) {
    return null;
  }

  try {
    const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow?.document;
    if (!iframeDoc) {
      return null;
    }

    // 获取内容容器
    const contentContainer = iframeDoc.querySelector('.content-container');
    if (!contentContainer) {
      return null;
    }

    // 清除文本选中状态
    if (iframeDoc.getSelection) {
      iframeDoc.getSelection().removeAllRanges();
    }

    // 清除交互边框和编辑器相关元素
    const cleanedContainer = cleanHtmlForSave(contentContainer);
    const htmlContent = cleanedContainer.innerHTML;

    return {
      htmlContent,
      contentContainer,
      hash: getContentHash(htmlContent)
    };
  } catch (error) {
    console.error('获取HTML内容失败:', error);
    return null;
  }
};

/**
 * 保存封面内容到服务器
 * @param {string} coverId 封面ID
 * @param {string} htmlContent HTML内容
 * @param {string} saveType 保存类型 ('manual'|'auto'|'beforeunload'|'router')
 * @returns {Promise<Object>} 保存结果
 */
const saveCoverToServer = async (coverId, htmlContent, saveType = 'manual') => {
  if (!coverId || !htmlContent) {
    return { success: false, message: '缺少必要参数' };
  }

  try {
    const token = localStorage.getItem('token');
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    const response = await axios.post(`/api/cover/${coverId}/save`, {
      edited_html_content: htmlContent,
      save_type: saveType
    }, { headers });

    return response.data || { success: false, message: '未收到服务器响应' };
  } catch (error) {
    console.error('保存封面失败:', error);
    return { 
      success: false, 
      message: error.response?.data?.message || error.message || '保存失败，请重试' 
    };
  }
};

/**
 * 使用sendBeacon API发送保存请求，适用于页面关闭/刷新时
 * @param {string} coverId 封面ID
 * @param {string} htmlContent HTML内容
 * @returns {boolean} 是否成功发送请求
 */
const saveCoverWithBeacon = (coverId, htmlContent) => {
  if (!coverId || !htmlContent || !navigator.sendBeacon) {
    return false;
  }

  try {
    const token = localStorage.getItem('token');
    const data = new FormData();
    data.append('edited_html_content', htmlContent);
    data.append('save_type', 'beforeunload');

    const url = `/api/cover/${coverId}/save`;
    const options = {
      type: 'application/x-www-form-urlencoded',
      ...(token ? { headers: { 'Authorization': `Bearer ${token}` } } : {})
    };

    return navigator.sendBeacon(url, data, options);
  } catch (error) {
    console.error('使用Beacon保存失败:', error);
    return false;
  }
};

/**
 * 自动保存控制器类
 */
class AutoSaveController {
  constructor(options) {
    this.iframeRef = options.iframeRef;
    this.coverId = options.coverId;
    this.debugMode = options.debugMode || false;
    this.interval = options.interval || 10000;
    this.onSaveStatusChange = options.onSaveStatusChange || (() => {});
    this.lastSavedContentHash = null;
    this.isRunning = false;
    this.timer = null;
    this.onSaveComplete = options.onSaveComplete || (() => {});
    
    // 绑定方法到实例
    this.start = this.start.bind(this);
    this.stop = this.stop.bind(this);
    this.save = this.save.bind(this);
    this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    this.handleRouterNavigation = this.handleRouterNavigation.bind(this);
    
    // 添加事件监听器
    window.addEventListener('beforeunload', this.handleBeforeUnload);
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    window.addEventListener('router-navigation', this.handleRouterNavigation);
  }

  /**
   * 开始自动保存循环
   */
  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.debugLog('自动保存已启动');
    
    // 立即执行一次保存以确保内容已保存
    this.save();
    
    // 设置定时保存
    this.timer = setInterval(this.save, this.interval);
  }

  /**
   * 停止自动保存
   */
  stop() {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    this.debugLog('自动保存已停止');
    
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    
    // 移除事件监听器
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('router-navigation', this.handleRouterNavigation);
  }

  /**
   * 执行保存操作
   * @param {string} saveType 保存类型
   * @returns {Promise<boolean>} 是否成功保存
   */
  async save(saveType = 'auto') {
    if (!this.isRunning || !this.coverId || !this.iframeRef.current) {
      return false;
    }

    try {
      // 获取当前HTML内容
      const content = getCurrentHtmlContent(this.iframeRef);
      if (!content) {
        this.debugLog('无法获取HTML内容，跳过保存');
        return false;
      }

      // 检查内容是否有变化，如果没有变化则跳过保存
      if (this.lastSavedContentHash === content.hash && saveType === 'auto') {
        this.debugLog('内容未变化，跳过保存');
        return false;
      }

      this.debugLog(`开始${saveType}保存`);
      this.onSaveStatusChange(true);

      // 保存到服务器
      const result = await saveCoverToServer(this.coverId, content.htmlContent, saveType);
      
      if (result.success) {
        this.lastSavedContentHash = content.hash;
        this.debugLog(`${saveType}保存成功`);
        
        // 如果是手动保存，显示成功消息
        if (saveType === 'manual') {
          message.success('保存成功');
        }
        
        // 触发保存完成回调
        this.onSaveComplete(true);
        return true;
      } else {
        this.debugLog(`${saveType}保存失败: ${result.message}`);
        
        // 只有手动保存时才显示错误消息
        if (saveType === 'manual') {
          message.error(`保存失败: ${result.message}`);
        }
        
        // 触发保存完成回调
        this.onSaveComplete(false, result.message);
        return false;
      }
    } catch (error) {
      this.debugLog(`${saveType}保存出错: ${error.message}`);
      
      // 只有手动保存时才显示错误消息
      if (saveType === 'manual') {
        message.error(`保存失败: ${error.message}`);
      }
      
      // 触发保存完成回调
      this.onSaveComplete(false, error.message);
      return false;
    } finally {
      this.onSaveStatusChange(false);
    }
  }

  /**
   * 处理页面关闭/刷新事件
   * @param {Event} event beforeunload事件对象
   */
  handleBeforeUnload(event) {
    if (!this.isRunning || !this.coverId) return;
    
    this.debugLog('页面关闭前保存');
    
    // 获取当前HTML内容
    const content = getCurrentHtmlContent(this.iframeRef);
    if (!content) return;
    
    // 使用sendBeacon API发送保存请求
    const success = saveCoverWithBeacon(this.coverId, content.htmlContent);
    
    if (!success) {
      // 如果sendBeacon失败，尝试同步XHR请求
      this.debugLog('Beacon保存失败，尝试同步XHR');
      
      try {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', `/api/cover/${this.coverId}/save`, false); // 同步请求
        xhr.setRequestHeader('Content-Type', 'application/json');
        
        const token = localStorage.getItem('token');
        if (token) {
          xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        }
        
        xhr.send(JSON.stringify({
          edited_html_content: content.htmlContent,
          save_type: 'beforeunload'
        }));
      } catch (error) {
        this.debugLog('同步XHR保存失败');
      }
    }
  }

  /**
   * 处理页面可见性变化事件
   */
  handleVisibilityChange() {
    if (!this.isRunning) return;
    
    if (document.visibilityState === 'hidden') {
      // 页面变为不可见时保存
      this.debugLog('页面变为不可见，触发保存');
      this.save('visibility');
    } else if (document.visibilityState === 'visible') {
      // 页面变为可见时，立即保存一次
      this.debugLog('页面变为可见，重新检查保存');
      setTimeout(() => this.save('visibility'), 1000);
    }
  }

  /**
   * 处理路由导航事件
   * @param {CustomEvent} event 自定义路由导航事件
   */
  handleRouterNavigation(event) {
    if (!this.isRunning) return;
    
    this.debugLog('检测到路由导航，触发保存');
    this.save('router');
  }

  /**
   * 输出调试日志
   * @param {string} message 日志消息
   */
  debugLog(message) {
    if (this.debugMode) {
      console.log(`[AutoSave] ${message}`);
      logger.debug(`[AutoSave] ${message}`, { coverId: this.coverId });
    }
  }

  /**
   * 更新自动保存控制器的配置
   * @param {Object} options 新的配置选项
   */
  updateOptions(options) {
    // 更新配置前先停止自动保存
    const wasRunning = this.isRunning;
    if (wasRunning) {
      this.stop();
    }
    
    // 更新配置
    if (options.iframeRef) this.iframeRef = options.iframeRef;
    if (options.coverId) this.coverId = options.coverId;
    if (options.debugMode !== undefined) this.debugMode = options.debugMode;
    if (options.interval) this.interval = options.interval;
    if (options.onSaveStatusChange) this.onSaveStatusChange = options.onSaveStatusChange;
    if (options.onSaveComplete) this.onSaveComplete = options.onSaveComplete;
    
    // 如果之前在运行，则重新启动
    if (wasRunning) {
      this.start();
    }
  }
}

/**
 * 创建自动保存管理器
 * @param {Object} options 配置选项
 * @returns {Object} 自动保存管理器对象
 */
export const createAutoSaveManager = (options) => {
  let controller = null;
  let monitorTimer = null;
  
  // 创建自动保存控制器
  const create = (opts) => {
    if (controller) {
      controller.updateOptions(opts);
      return controller;
    }
    
    controller = new AutoSaveController(opts);
    return controller;
  };
  
  // 启动自动保存
  const start = () => {
    if (controller) {
      controller.start();
      
      // 启动监控
      if (!monitorTimer) {
        monitorTimer = setInterval(() => {
          if (controller && !controller.isRunning) {
            console.log('[AutoSaveManager] 检测到自动保存已停止，尝试重新启动');
            controller.start();
          }
        }, 30000); // 每30秒检查一次
      }
    }
  };
  
  // 停止自动保存
  const stop = () => {
    if (controller) {
      controller.stop();
    }
    
    // 停止监控
    if (monitorTimer) {
      clearInterval(monitorTimer);
      monitorTimer = null;
    }
  };
  
  // 执行保存操作
  const save = async (saveType = 'manual') => {
    if (controller) {
      return controller.save(saveType);
    }
    return false;
  };
  
  // 检查是否可以安全地保存
  const canSave = () => {
    return !!(controller && controller.coverId && controller.iframeRef.current);
  };
  
  // 清理资源
  const cleanup = () => {
    if (controller) {
      controller.stop();
      controller = null;
    }
    
    if (monitorTimer) {
      clearInterval(monitorTimer);
      monitorTimer = null;
    }
  };
  
  // 创建初始控制器
  if (options) {
    create(options);
  }
  
  return {
    create,
    start,
    stop,
    save,
    canSave,
    cleanup
  };
};

/**
 * 手动保存
 * @param {React.RefObject} iframeRef iframe的React引用
 * @param {string} coverId 封面ID
 * @param {function} onSaveStatusChange 保存状态变化回调
 * @returns {Promise<boolean>} 是否成功保存
 */
export const manualSave = async (iframeRef, coverId, onSaveStatusChange = () => {}) => {
  // 提供更具体的错误消息
  if (!coverId) {
    message.error('无法保存：未找到封面ID');
    return false;
  }
  
  if (!iframeRef.current) {
    message.error('无法保存：预览内容尚未加载完成');
    return false;
  }
  
  try {
    onSaveStatusChange(true);
    
    // 获取当前HTML内容
    const content = getCurrentHtmlContent(iframeRef);
    if (!content) {
      message.error('无法获取预览内容');
      return false;
    }
    
    // 保存到服务器
    const result = await saveCoverToServer(coverId, content.htmlContent, 'manual');
    
    if (result.success) {
      message.success('保存成功');
      return true;
    } else {
      message.error(`保存失败: ${result.message}`);
      return false;
    }
  } catch (error) {
    console.error('手动保存失败:', error);
    message.error(`保存失败: ${error.message}`);
    return false;
  } finally {
    onSaveStatusChange(false);
  }
};

export default {
  createAutoSaveManager,
  manualSave,
  getCurrentHtmlContent,
  cleanHtmlForSave
}; 
/**
 * 权限受控组件包装器
 * 用于根据权限控制组件的显示
 */
import React from 'react';
import useFeaturePermissions from '../hooks/useFeaturePermissions';

/**
 * 权限受控组件包装器
 * @param {Object} props - 组件属性
 * @param {string} props.featureName - 功能名称
 * @param {React.ReactNode} props.children - 子组件
 * @param {React.ReactNode} props.fallback - 无权限时显示的内容
 * @param {boolean} props.defaultPermission - 默认权限状态
 * @returns {JSX.Element} 权限受控的组件
 */
const PermissionControl = ({ 
  featureName, 
  children, 
  fallback = null, 
  defaultPermission = false 
}) => {
  // 使用通用权限检查Hook
  const { permissions, loading } = useFeaturePermissions(
    [featureName], 
    { [featureName]: defaultPermission }
  );

  // 如果正在加载，显示加载状态或null
  if (loading) {
    return null;
  }

  // 如果有权限，显示子组件
  if (permissions[featureName]) {
    return children;
  }

  // 如果没有权限，显示fallback或null
  return fallback;
};

export default PermissionControl;

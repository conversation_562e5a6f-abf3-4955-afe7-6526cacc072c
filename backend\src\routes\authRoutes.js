const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { validate, schemas } = require('../utils/validator');
const { auth } = require('../middlewares/authMiddleware');

// 用户注册
router.post('/register', validate(schemas.register), authController.register);

// 验证码注册
router.post('/register/verify-code', validate(schemas.verifyCodeRegister), authController.registerByVerifyCode);

// 用户登录
router.post('/login', validate(schemas.login), authController.login);

// 验证码登录
router.post('/login/verify-code', validate(schemas.verifyCodeLogin), authController.loginByVerifyCode);

// 重置密码
router.post('/reset-password', validate(schemas.resetPassword), authController.resetPassword);

// 获取当前用户信息（需要认证）
router.get('/me', auth, authController.getMe);

module.exports = router;

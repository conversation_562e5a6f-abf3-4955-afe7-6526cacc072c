/**
 * 封面AI - 支付服务
 * 提供支付相关功能，包括创建订单、处理支付回调、查询支付状态等
 */

const { sequelize } = require('../models');
const { PaymentRecord, User, PointRecord, MemberPackage, PointPackage, RefundRecord } = require('../models');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const dayjs = require('dayjs');
const logger = require('../utils/logger');

/**
 * 计算会员到期时间（当日计时模式）
 * 例如：3月6日任何时间开通一个月会员，则在4月6日23:59:59到期
 * 
 * @param {Date|null} currentDate 当前日期，如果为null则使用当前时间
 * @param {number} durationDays 会员持续时间（天数）
 * @returns {Date} 计算后的到期时间
 */
exports.calculateMemberExpireDate = function(currentDate, durationDays) {
  const baseDate = currentDate || new Date();
  
  // 计算对应月份的日期
  const expiryDate = new Date(baseDate);
  expiryDate.setDate(expiryDate.getDate() + durationDays);
  
  // 设置为当天的23:59:59
  expiryDate.setHours(23, 59, 59, 999);
  
  return expiryDate;
}

/**
 * 创建支付订单
 * @param {number} userId 用户ID
 * @param {string} productType 产品类型 'vip'|'points'
 * @param {number} packageId 套餐ID
 * @param {string} paymentType 支付方式 'wechat'|'alipay'
 * @param {string} clientIp 客户端IP
 * @param {string} deviceType 设备类型 'PC'|'Mobile'
 * @returns {Object} 订单信息和支付参数
 */
exports.createPaymentOrder = async (userId, productType, packageId, paymentType, clientIp, deviceType) => {
  try {
    // 验证套餐是否存在
    let packageInfo;
    let amount = 0;
    let productDetail = '';
    
    if (productType === 'vip') {
      packageInfo = await MemberPackage.findByPk(packageId);
      if (!packageInfo) throw new Error('会员套餐不存在');
      
      amount = packageInfo.discount_price || packageInfo.price;
      // 存储更详细的套餐信息
      productDetail = JSON.stringify({
        name: packageInfo.name,
        duration: packageInfo.duration,
        description: packageInfo.description || `${packageInfo.name}(${packageInfo.duration}天)`
      });
    } else if (productType === 'points') {
      packageInfo = await PointPackage.findByPk(packageId);
      if (!packageInfo) throw new Error('积分套餐不存在');
      
      amount = packageInfo.price;
      // 存储更详细的套餐信息
      productDetail = JSON.stringify({
        name: packageInfo.name,
        points: packageInfo.points,
        bonus_points: packageInfo.bonus_points || 0,
        description: packageInfo.description || `${packageInfo.points}积分`
      });
    } else {
      throw new Error('不支持的产品类型');
    }
    
    // 生成唯一订单号
    const orderNo = `FM${dayjs().format('YYYYMMDDHHmmss')}${Math.floor(Math.random() * 1000)}`;
    
    // 创建支付记录
    const paymentRecord = await PaymentRecord.create({
      user_id: userId,
      order_no: orderNo,
      payment_type: paymentType,
      payment_type_detail: deviceType === 'Mobile' ? 'H5' : 'Native',
      amount,
      payment_status: 'pending',
      refund_status: 'none',
      product_type: productType,
      product_detail: productDetail,
      package_id: packageId,
      client_ip: clientIp,
      device_type: deviceType
    });
    
    // TODO: 调用实际的支付接口获取支付参数
    // 这里先返回模拟的支付参数
    const paymentParams = {
      orderNo,
      amount,
      productDetail,
      // 模拟支付参数，实际项目中替换为真实支付接口返回的参数
      qrCodeUrl: `https://example.com/pay/${orderNo}`,
      paymentExpireTime: dayjs().add(30, 'minute').format('YYYY-MM-DD HH:mm:ss')
    };
    
    return {
      paymentRecord,
      paymentParams
    };
  } catch (error) {
    logger.error('创建支付订单失败', error);
    throw error;
  }
};

/**
 * 处理支付成功回调
 * @param {string} orderNo 订单号
 * @param {string} transactionId 支付平台交易ID
 * @param {Object} notifyData 回调原始数据
 * @returns {boolean} 处理结果
 */
exports.handlePaymentCallback = async (orderNo, transactionId, notifyData) => {
  const transaction = await sequelize.transaction();
  
  try {
    // 查询订单
    const paymentRecord = await PaymentRecord.findOne({ 
      where: { order_no: orderNo },
      transaction
    });
    
    if (!paymentRecord) {
      throw new Error('订单不存在');
    }
    
    // 检查订单状态，避免重复处理
    if (paymentRecord.payment_status === 'success') {
      await transaction.commit();
      return true; // 订单已处理，直接返回成功
    }
    
    // 更新支付记录
    await paymentRecord.update({
      payment_status: 'success',
      transaction_id: transactionId,
      payment_time: new Date(),
      notify_data: JSON.stringify(notifyData)
    }, { transaction });
    
    // 根据产品类型处理不同业务逻辑
    const userId = paymentRecord.user_id;
    const user = await User.findByPk(userId, { transaction });
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    if (paymentRecord.product_type === 'vip') {
      // 处理会员购买
      const memberPackage = await MemberPackage.findByPk(paymentRecord.package_id, { transaction });
      if (!memberPackage) {
        throw new Error('会员套餐不存在');
      }
      
      // 计算新的会员到期时间
      let newExpireDate;
      if (user.role === 'vip' && user.vip_expire_date && new Date(user.vip_expire_date) > new Date()) {
        // 已是会员，续费
        newExpireDate = exports.calculateMemberExpireDate(new Date(user.vip_expire_date), memberPackage.duration);
      } else {
        // 新开通会员
        newExpireDate = exports.calculateMemberExpireDate(null, memberPackage.duration);
      }
      
      // 更新用户会员状态
      await user.update({
        role: 'vip',
        vip_expire_date: newExpireDate
      }, { transaction });
      
    } else if (paymentRecord.product_type === 'points') {
      // 处理积分充值
      const pointPackage = await PointPackage.findByPk(paymentRecord.package_id, { transaction });
      if (!pointPackage) {
        throw new Error('积分套餐不存在');
      }
      
      // 计算总积分（包括赠送积分）
      const totalPoints = pointPackage.points + (pointPackage.bonus_points || 0);
      
      // 更新用户积分
      const newPoints = (user.points || 0) + totalPoints;
      await user.update({
        points: newPoints
      }, { transaction });
      
      // 创建积分记录
      await PointRecord.create({
        user_id: userId,
        points_change: totalPoints,
        points_after: newPoints,
        operation_type: 'recharge',
        description: `购买${pointPackage.name}，充值${pointPackage.points}积分，赠送${pointPackage.bonus_points || 0}积分`,
        operation_id: orderNo
      }, { transaction });
    }
    
    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    logger.error('处理支付回调失败', error);
    throw error;
  }
};

/**
 * 查询支付状态
 * @param {string} orderNo 订单号
 * @returns {Object} 支付状态信息
 */
exports.queryPaymentStatus = async (orderNo) => {
  try {
    const paymentRecord = await PaymentRecord.findOne({
      where: { order_no: orderNo }
    });
    
    if (!paymentRecord) {
      throw new Error('订单不存在');
    }
    
    return {
      orderNo: paymentRecord.order_no,
      status: paymentRecord.payment_status,
      amount: paymentRecord.amount,
      paymentTime: paymentRecord.payment_time,
      productType: paymentRecord.product_type,
      productDetail: paymentRecord.product_detail
    };
  } catch (error) {
    logger.error('查询支付状态失败', error);
    throw error;
  }
};

/**
 * 申请退款
 * @param {number} userId 用户ID
 * @param {number} paymentId 支付记录ID
 * @param {string} reason 退款原因
 * @returns {Object} 退款申请结果
 */
exports.applyRefund = async (userId, paymentId, reason) => {
  const transaction = await sequelize.transaction();
  
  try {
    // 查询支付记录
    const paymentRecord = await PaymentRecord.findOne({
      where: {
        id: paymentId,
        user_id: userId,
        payment_status: 'success'
      },
      transaction
    });
    
    if (!paymentRecord) {
      throw new Error('订单不存在或不可退款');
    }
    
    // 检查是否已申请退款
    if (paymentRecord.refund_status !== 'none') {
      throw new Error('该订单已申请退款');
    }
    
    // 生成退款单号
    const refundNo = `RF${dayjs().format('YYYYMMDDHHmmss')}${Math.floor(Math.random() * 1000)}`;
    
    // 创建退款记录
    const refundRecord = await RefundRecord.create({
      payment_id: paymentId,
      user_id: userId,
      refund_no: refundNo,
      refund_amount: paymentRecord.amount,
      refund_reason: reason,
      refund_status: 'pending'
    }, { transaction });
    
    // 更新支付记录退款状态
    await paymentRecord.update({
      refund_status: 'partial'
    }, { transaction });
    
    await transaction.commit();
    
    return {
      refundNo,
      refundRecord
    };
  } catch (error) {
    await transaction.rollback();
    logger.error('申请退款失败', error);
    throw error;
  }
};

/**
 * 处理退款结果
 * @param {string} refundNo 退款单号
 * @param {string} status 退款状态 'success'|'failed'
 * @param {string} transactionId 支付平台退款交易ID
 * @returns {boolean} 处理结果
 */
exports.handleRefundResult = async (refundNo, status, transactionId) => {
  const transaction = await sequelize.transaction();
  
  try {
    // 查询退款记录
    const refundRecord = await RefundRecord.findOne({
      where: { refund_no: refundNo },
      transaction
    });
    
    if (!refundRecord) {
      throw new Error('退款记录不存在');
    }
    
    // 更新退款记录
    await refundRecord.update({
      refund_status: status,
      transaction_id: transactionId,
      refund_time: status === 'success' ? new Date() : null
    }, { transaction });
    
    // 如果退款成功，更新支付记录和用户状态
    if (status === 'success') {
      const paymentRecord = await PaymentRecord.findByPk(refundRecord.payment_id, { transaction });
      
      if (!paymentRecord) {
        throw new Error('支付记录不存在');
      }
      
      await paymentRecord.update({
        refund_status: 'full'
      }, { transaction });
      
      // 根据产品类型处理不同业务逻辑
      if (paymentRecord.product_type === 'vip') {
        // 处理会员退款，取消会员资格
        const user = await User.findByPk(refundRecord.user_id, { transaction });
        
        if (user && user.role === 'vip') {
          await user.update({
            role: 'user',
            vip_expire_date: null
          }, { transaction });
        }
      } else if (paymentRecord.product_type === 'points') {
        // 处理积分退款，扣减积分
        // 这里可能需要更复杂的逻辑，如检查用户当前积分是否足够扣减
        // 简单起见，这里只做积分记录
        const user = await User.findByPk(refundRecord.user_id, { transaction });
        
        if (user) {
          const deductPoints = parseInt(paymentRecord.product_detail);
          const newPoints = Math.max(0, user.points - deductPoints);
          
          await user.update({
            points: newPoints
          }, { transaction });
          
          // 创建积分记录
          await PointRecord.create({
            user_id: refundRecord.user_id,
            points_change: -deductPoints,
            points_after: newPoints,
            operation_type: 'admin_adjust',
            description: `退款扣除积分，订单号：${paymentRecord.order_no}`,
            operation_id: refundNo
          }, { transaction });
        }
      }
    }
    
    await transaction.commit();
    return true;
  } catch (error) {
    await transaction.rollback();
    logger.error('处理退款结果失败', error);
    throw error;
  }
}; 
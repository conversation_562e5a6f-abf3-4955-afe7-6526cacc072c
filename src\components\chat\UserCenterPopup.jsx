import React, { forwardRef } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuPortal,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  User, BookOpen, CircleDollarSign, ListChecks, History, Crown, LogOut
} from 'lucide-react';
import { Button } from "@/components/ui/button";

const UserMenuItem = ({ icon: Icon, text, onClick }) => (
  <DropdownMenuItem 
    onClick={onClick} 
    className="flex items-center p-2 text-sm text-slate-700 hover:bg-slate-100 cursor-pointer focus:bg-slate-100 focus:text-slate-900"
  >
    <Icon className="h-4 w-4 mr-3 text-slate-500" />
    <span>{text}</span>
  </DropdownMenuItem>
);

/**
 * 个人中心弹窗组件
 * 显示用户基本信息和导航按钮
 */
const UserCenterPopup = forwardRef(({ 
  children,
  userInfo,
  onNavItemClick,
  dailyPoints,
  onLogout
}, ref) => {
  
  const navItems = [
    { id: 'profile', icon: User, text: '个人资料' },
    { id: 'covers', icon: BookOpen, text: '封面记录' },
    { id: 'points', icon: CircleDollarSign, text: '积分记录' },
    { id: 'orders', icon: ListChecks, text: '订单记录' },
    { id: 'my-creations', icon: History, text: '我的作品' },
  ];

  const handleLogout = (e) => {
    e.stopPropagation();
    if (onLogout) {
      onLogout();
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {children}
      </DropdownMenuTrigger>
      <DropdownMenuPortal>
        <DropdownMenuContent 
          side="top" 
          align="start"
          sideOffset={10}
          className="w-64 rounded-xl shadow-lg border-slate-200 bg-white p-2"
        >
          {/* User Info Header */}
          <div className="p-3 border-b border-slate-100 mb-2">
            <div className="flex items-center">
              <Avatar className="h-12 w-12 border-2 border-slate-100 mr-3">
                {userInfo?.avatar ? (
                  <AvatarImage 
                    src={userInfo.avatar} 
                    alt={userInfo.nickname || 'User avatar'} 
                    className="object-cover"
                  />
                ) : (
                  <AvatarFallback className="bg-gradient-to-br from-purple-500 to-indigo-600 text-white">
                    {(userInfo?.nickname || 'U').charAt(0).toUpperCase()}
                  </AvatarFallback>
                )}
                {userInfo?.is_vip && (
                  <div className="absolute -bottom-1 -right-1 bg-yellow-400 rounded-full p-0.5 shadow-md">
                    <Crown size={12} className="text-white" />
                  </div>
                )}
              </Avatar>
              <div className="overflow-hidden">
                <p className="text-md font-semibold text-slate-800 truncate">
                  {userInfo?.nickname || '未设置昵称'}
                </p>
                <p className="text-xs text-slate-500 mt-0.5">
                  {userInfo?.is_vip 
                    ? <span className="text-yellow-500 font-medium">高级会员</span>
                    : '普通会员'}
                </p>
              </div>
            </div>
          </div>
          
          {/* Navigation Items */}
          {navItems.map((item) => (
            <UserMenuItem
              key={item.id}
              icon={item.icon}
              text={item.text}
              onClick={() => onNavItemClick(item.id)}
            />
          ))}
          
          <DropdownMenuSeparator className="my-2 bg-slate-100" />
          
          {/* Logout Button */}
          <UserMenuItem
            icon={LogOut}
            text="退出登录"
            onClick={handleLogout}
          />
        </DropdownMenuContent>
      </DropdownMenuPortal>
    </DropdownMenu>
  );
});

export default UserCenterPopup; 
const { SystemConfig } = require('../models');
const logger = require('../utils/logger');
const crypto = require('crypto');
const { sequelize, Sequelize } = require('../config/database');
const { Op } = Sequelize;

// AI服务配置前缀和激活服务键名
const AI_SERVICE_PREFIX = 'ai_service_';
const ACTIVE_SERVICE_KEY = 'active_ai_service';

// 加密API密钥
const encryptApiKey = (apiKey) => {
  // 在实际生产环境中，应该使用更安全的加密方式和环境变量存储密钥
  const algorithm = 'aes-256-ctr';
  // 确保密钥长度为32字节（256位）
  const secretKey = process.env.API_KEY_SECRET || 'your-secret-key-should-be-in-env-file';
  const key = crypto.createHash('sha256').update(String(secretKey)).digest('base64').substring(0, 32);
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  const encrypted = Buffer.concat([cipher.update(apiKey), cipher.final()]);
  
  return {
    iv: iv.toString('hex'),
    content: encrypted.toString('hex')
  };
};

// 初始AI服务配置
const initialAIServices = [
  {
    service_name: 'DeepSeek',
    base_url: 'https://api.deepseek.com',
    api_key: '***********************************',
    model_name: 'deepseek-chat',
    request_format: null,
    response_format: null
  },
  {
    service_name: 'xAI',
    base_url: 'https://api.xai.com',
    api_key: '************************************************************************************',
    model_name: 'grok-1',
    request_format: null,
    response_format: null
  }
];

// 执行初始化
async function initAIServices() {
  try {
    logger.info('开始初始化AI服务配置...');
    
    // 确保数据库连接
    await sequelize.authenticate();
    logger.info('数据库连接成功');
    
    // 检查是否已有AI服务配置
    const existingServices = await SystemConfig.findAll({
      where: {
        config_key: {
          [Op.like]: `${AI_SERVICE_PREFIX}%`
        }
      }
    });
    
    if (existingServices.length > 0) {
      logger.info(`已存在${existingServices.length}个AI服务配置，跳过初始化`);
      return;
    }
    
    // 开始事务
    const transaction = await sequelize.transaction();
    
    try {
      let activeServiceId = null;
      
      // 创建初始AI服务配置
      for (let i = 0; i < initialAIServices.length; i++) {
        const service = initialAIServices[i];
        
        // 加密API密钥
        const encryptedKey = encryptApiKey(service.api_key);
        
        // 创建服务配置数据
        const serviceData = {
          service_name: service.service_name,
          base_url: service.base_url,
          api_key: JSON.stringify(encryptedKey),
          model_name: service.model_name,
          request_format: service.request_format,
          response_format: service.response_format
        };
        
        // 创建新的服务配置
        const newService = await SystemConfig.create({
          config_key: `${AI_SERVICE_PREFIX}${Date.now() + i}`,
          config_value: JSON.stringify(serviceData),
          description: `AI服务配置: ${service.service_name}`
        }, { transaction });
        
        logger.info(`创建AI服务配置: ${service.service_name}`);
        
        // 第一个服务设为激活状态
        if (i === 0) {
          activeServiceId = newService.id;
        }
      }
      
      // 设置激活的服务
      if (activeServiceId) {
        await SystemConfig.create({
          config_key: ACTIVE_SERVICE_KEY,
          config_value: activeServiceId.toString(),
          description: '当前激活的AI服务ID'
        }, { transaction });
        
        logger.info(`设置ID为${activeServiceId}的服务为激活状态`);
      }
      
      // 提交事务
      await transaction.commit();
      logger.info('AI服务配置初始化完成');
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      logger.error('AI服务配置初始化失败:', error);
      throw error;
    }
  } catch (error) {
    logger.error('执行AI服务配置初始化时出错:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行初始化
initAIServices();

/* App.css - Custom styles for App layout and components */

/* Define some root variables for easier theming */
:root {
  --primary-color: #3B82F6; /* A modern blue */
  --primary-color-hover: #2563EB;
  --primary-color-active: #1D4ED8;
  --border-color-base: #D1D5DB; /* Lighter gray for borders */
  --input-bg: #F9FAFB; /* Very light gray for input background */
  --component-background-light: #ffffff;
  --component-background-dark: #1f1f1f; /* Slightly adjusted dark background */
  --text-color-light: #1F2937;
  --text-color-secondary-light: #6B7280;
  --text-color-dark: rgba(255, 255, 255, 0.85);
  --text-color-secondary-dark: rgba(255, 255, 255, 0.45);
  --heading-color-light: #111827;
  --heading-color-dark: rgba(255, 255, 255, 0.9);
  --content-bg-light: #F3F4F6; /* Slightly different light page background */
  --content-bg-dark: #111111; /* Darker page background */
  --header-bg-light: #ffffff;
  --header-bg-dark: #1a1a1a;
  --menu-item-color-light: #4B5563;
  --menu-item-color-dark: rgba(255, 255, 255, 0.65);
  --menu-item-active-color-light: var(--primary-color);
  --menu-item-active-color-dark: #ffffff;
  --box-shadow-light: 0 4px 12px rgba(0, 0, 0, 0.08);
  --box-shadow-dark: 0 4px 15px rgba(0, 0, 0, 0.5);
  --border-radius-base: 8px;
  --input-height: 42px; /* Standard height for inputs/buttons */
}

/* Apply base text color */
body {
  color: var(--text-color-light);
}

body.dark-theme {
  color: var(--text-color-dark);
}

.app-layout {
  min-height: 100vh;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  background: var(--header-bg-light);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  height: 64px;
  line-height: 64px;
  position: sticky;
  top: 0;
  z-index: 10;
  transition: background 0.3s ease;
}

/* Dark theme specific header background */
body.dark-theme .app-header {
  background: var(--header-bg-dark);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.logo-title {
  color: var(--primary-color); /* Use primary color for logo */
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  transition: color 0.3s ease;
} /* Default color applied via body */

/* Dark theme specific logo color */
body.dark-theme .logo-title {
  color: var(--heading-color-dark);
}

.header-right-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.app-menu {
  background: transparent !important;
  border-bottom: none !important;
  line-height: 62px;
}

/* Style menu items */
.app-menu .ant-menu-item,
.app-menu .ant-menu-submenu-title {
  color: var(--menu-item-color-light);
  transition: color 0.3s ease;
  border-bottom: 2px solid transparent !important;
  top: 1px;
}

/* Style selected/active menu items */
.app-menu .ant-menu-item-selected,
.app-menu .ant-menu-item-active {
  color: var(--menu-item-active-color-light) !important;
  border-bottom: 2px solid var(--menu-item-active-color-light) !important;
  background-color: transparent !important;
}

/* Dark theme menu item colors */
body.dark-theme .app-menu .ant-menu-item,
body.dark-theme .app-menu .ant-menu-submenu-title {
  color: var(--menu-item-color-dark);
}

body.dark-theme .app-menu .ant-menu-item-selected,
body.dark-theme .app-menu .ant-menu-item-active {
  color: var(--menu-item-active-color-dark);
  border-bottom-color: var(--menu-item-active-color-dark) !important;
}

/* User profile dropdown */
.user-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px; /* Default text color applied via body */
  transition: color 0.3s ease;
}

body.dark-theme .user-dropdown-link {
  color: var(--text-color-dark);
}

.user-dropdown-link:hover {
  color: var(--primary-color);
}

body.dark-theme .user-dropdown-link:hover {
  color: var(--primary-color-hover);
}

/* Login/Register button */
/* Removed empty rule */

/* Page Content */
.page-content {
  padding: 24px;
  margin: 0 !important;
  background-color: var(--content-bg-light);
  transition: background-color 0.3s ease;
}

body.dark-theme .page-content {
  background-color: var(--content-bg-dark);
}

/* Footer */
.app-footer {
  text-align: center;
  padding: 15px 50px;
  background: transparent;
  color: var(--text-color-secondary-light);
  font-size: 12px;
  transition: color 0.3s ease;
}

body.dark-theme .app-footer {
  color: var(--text-color-secondary-dark);
}

.footer-content {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

/* =========================== */
/* Auth Page Styles - Base */
/* =========================== */

.auth-page-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 64px - 50px);
  padding: 40px 20px;
  background: var(--content-bg-light);
}

body.dark-theme .auth-page-container {
  background: var(--content-bg-dark);
}

.auth-form-wrapper {
  width: 100%;
  max-width: 400px;
  background: var(--component-background-light);
  padding: 40px 40px 30px 40px; /* Adjust bottom padding */
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-light);
  transition: background 0.3s ease, box-shadow 0.3s ease;
}

body.dark-theme .auth-form-wrapper {
  background: var(--component-background-dark);
  box-shadow: var(--box-shadow-dark);
}

.auth-title {
  text-align: center;
  font-size: 26px; /* Slightly larger title */
  font-weight: 600;
  margin-bottom: 35px; /* More space below title */
  color: var(--heading-color-light);
  transition: color 0.3s ease;
}

body.dark-theme .auth-title {
  color: var(--heading-color-dark);
}

.auth-switch-link {
  text-align: center;
  margin-top: 25px; /* More space above switch link */
}

.auth-switch-link span {
  color: var(--text-color-secondary-light);
  transition: color 0.3s ease;
}

body.dark-theme .auth-switch-link span {
  color: var(--text-color-secondary-dark);
}

.auth-switch-link a {
  color: var(--primary-color);
  margin-left: 5px;
  transition: color 0.3s ease;
  font-weight: 500; /* Make link slightly bolder */
}

.auth-switch-link a:hover {
  color: var(--primary-color-hover);
}

body.dark-theme .auth-switch-link a {
  color: #69b1ff; /* Lighter blue for dark theme link */
}

/* Keep remaining old styles for now */
.auth-form-wrapper .ant-form-item {
  margin-bottom: 24px; /* Consistent spacing */
}

.auth-form-wrapper .ant-form-item-label > label {
  font-weight: 500; /* Slightly bolder labels */
}

/* --- Form Element Styling --- */

/* Input field styling */
.auth-form-wrapper .ant-input-affix-wrapper,
.auth-form-wrapper .ant-input {
  height: var(--input-height);
  border-radius: 6px; /* Slightly less rounded than wrapper */
  border: 1px solid var(--border-color-base);
  background-color: var(--input-bg);
  transition: border-color 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
}

.auth-form-wrapper .ant-input::placeholder {
  color: #9CA3AF; /* Lighter placeholder text */
}

.auth-form-wrapper .ant-input-affix-wrapper:hover,
.auth-form-wrapper .ant-input:hover {
  border-color: var(--primary-color);
}

.auth-form-wrapper .ant-input-affix-wrapper:focus,
.auth-form-wrapper .ant-input-affix-wrapper-focused,
.auth-form-wrapper .ant-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2); /* Focus ring matching primary */
  background-color: var(--component-background-light); /* White background on focus */
}

.auth-form-wrapper .ant-input-prefix .anticon {
  color: #9CA3AF; /* Gray icon color */
}

/* Dark theme inputs */
body.dark-theme .auth-form-wrapper .ant-input-affix-wrapper,
body.dark-theme .auth-form-wrapper .ant-input {
  background-color: #2d2d2d;
  border-color: #4a4a4a;
  color: var(--text-color-dark);
}

body.dark-theme .auth-form-wrapper .ant-input::placeholder {
  color: rgba(255, 255, 255, 0.45);
}

body.dark-theme .auth-form-wrapper .ant-input-affix-wrapper:hover,
body.dark-theme .auth-form-wrapper .ant-input:hover {
  border-color: #69b1ff;
}

body.dark-theme .auth-form-wrapper .ant-input-affix-wrapper:focus,
body.dark-theme .auth-form-wrapper .ant-input-affix-wrapper-focused,
body.dark-theme .auth-form-wrapper .ant-input:focus {
  background-color: var(--component-background-dark);
  border-color: #69b1ff;
  box-shadow: 0 0 0 2px rgba(105, 177, 255, 0.3);
}

body.dark-theme .auth-form-wrapper .ant-input-prefix .anticon {
  color: rgba(255, 255, 255, 0.45);
}

/* Button Styling */
.auth-form-wrapper .ant-btn-primary {
  width: 100%; /* Make submit button full width */
  height: var(--input-height); /* Match input height */
  font-size: 16px;
  border-radius: 6px;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.auth-form-wrapper .ant-btn-primary:hover {
  background-color: var(--primary-color-hover);
  border-color: var(--primary-color-hover);
}

.auth-form-wrapper .ant-btn-primary:active {
  background-color: var(--primary-color-active);
  border-color: var(--primary-color-active);
}

/* Dark theme button */
body.dark-theme .auth-form-wrapper .ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff; /* Ensure text is white on dark primary */
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

body.dark-theme .auth-form-wrapper .ant-btn-primary:hover {
  background-color: var(--primary-color-hover);
  border-color: var(--primary-color-hover);
}

body.dark-theme .auth-form-wrapper .ant-btn-primary:active {
  background-color: var(--primary-color-active);
  border-color: var(--primary-color-active);
}

/* --- Specific Form Element Styling --- */

/* Radio Button Group (e.g., Login Type) */
.auth-form-wrapper .ant-radio-group {
  width: 100%; /* Make group take full width */
}

.auth-form-wrapper .ant-radio-button-wrapper {
  height: var(--input-height);
  line-height: calc(var(--input-height) - 2px); /* Adjust line-height for border */
  width: 50%; /* Assuming 2 options */
  text-align: center;
  border-color: var(--border-color-base);
  color: var(--text-color-secondary-light);
  transition: all 0.3s ease;
}

.auth-form-wrapper .ant-radio-button-wrapper:not(:first-child)::before {
  background-color: var(--border-color-base); /* Divider color */
}

.auth-form-wrapper .ant-radio-button-wrapper:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
  z-index: 1; /* Ensure hover border is on top */
}

.auth-form-wrapper .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: #EBF4FF; /* Light blue background for selected */
}

.auth-form-wrapper .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
  background-color: var(--primary-color) !important; /* Divider when selected */
}

/* Dark theme radio buttons */
body.dark-theme .auth-form-wrapper .ant-radio-button-wrapper {
  background-color: #2d2d2d;
  border-color: #4a4a4a;
  color: var(--text-color-secondary-dark);
}

body.dark-theme .auth-form-wrapper .ant-radio-button-wrapper:not(:first-child)::before {
  background-color: #4a4a4a;
}

body.dark-theme .auth-form-wrapper .ant-radio-button-wrapper:hover {
  color: #69b1ff;
  border-color: #69b1ff;
}

body.dark-theme .auth-form-wrapper .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  color: #ffffff;
  border-color: var(--primary-color);
  background-color: var(--primary-color);
}

/* Send Code Button (assuming it's default type) */
.auth-form-wrapper .ant-btn-default {
  height: var(--input-height);
  border-color: var(--border-color-base);
  color: var(--text-color-secondary-light);
}

.auth-form-wrapper .ant-btn-default:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

body.dark-theme .auth-form-wrapper .ant-btn-default {
  background-color: #2d2d2d;
  border-color: #4a4a4a;
  color: var(--text-color-secondary-dark);
}

body.dark-theme .auth-form-wrapper .ant-btn-default:hover {
  color: #69b1ff;
  border-color: #69b1ff;
}

/* Form Links (e.g., Forgot Password) */
.auth-form-wrapper .ant-form-item-control-input a {
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.auth-form-wrapper .ant-form-item-control-input a:hover {
  color: var(--primary-color-hover);
}

body.dark-theme .auth-form-wrapper .ant-form-item-control-input a {
  color: #69b1ff;
}

/* Agreement Checkbox */
.auth-form-wrapper .ant-checkbox-wrapper {
  color: var(--text-color-secondary-light);
}

.auth-form-wrapper .ant-checkbox-wrapper a {
  color: var(--primary-color);
}

.auth-form-wrapper .ant-checkbox-wrapper a:hover {
  color: var(--primary-color-hover);
}

body.dark-theme .auth-form-wrapper .ant-checkbox-wrapper {
  color: var(--text-color-secondary-dark);
}

body.dark-theme .auth-form-wrapper .ant-checkbox-wrapper a {
  color: #69b1ff;
}

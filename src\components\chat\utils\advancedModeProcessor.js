/**
 * 高级模式处理器 - 统一的高级模式架构
 * 在保持原始布局的前提下提供增强功能
 */

import logger from '../../../services/logs/frontendLogger';

/**
 * 高级模式的核心价值功能
 */
export const ADVANCED_FEATURES = {
  // 资源优化
  RESOURCE_OPTIMIZATION: {
    name: '资源优化',
    description: '自动优化图片加载、处理CORS问题、资源预加载',
    enabled: true
  },
  
  // 性能增强
  PERFORMANCE_ENHANCEMENT: {
    name: '性能增强',
    description: '字体渲染优化、图像质量优化、加载性能提升',
    enabled: true
  },
  
  // 兼容性保障
  COMPATIBILITY_ASSURANCE: {
    name: '兼容性保障',
    description: '跨浏览器兼容性增强、CSS前缀自动添加',
    enabled: true
  },
  
  // 安全增强
  SECURITY_ENHANCEMENT: {
    name: '安全增强',
    description: '更严格的CSP策略、XSS防护、安全资源加载',
    enabled: true
  },
  
  // 调试支持
  DEBUG_SUPPORT: {
    name: '调试支持',
    description: '详细的错误信息、性能监控、资源加载状态',
    enabled: true
  }
};

/**
 * 检测HTML内容类型和特征
 * @param {string} htmlContent - HTML内容
 * @returns {Object} 内容分析结果
 */
export const analyzeHtmlContent = (htmlContent) => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return { type: 'empty', features: [], complexity: 0 };
  }

  const features = [];
  let complexity = 0;

  try {
    // 检测HTML结构完整性
    const hasHtmlTag = /<html[^>]*>/i.test(htmlContent);
    const hasHeadTag = /<head[^>]*>/i.test(htmlContent);
    const hasBodyTag = /<body[^>]*>/i.test(htmlContent);
    const hasDoctype = /<!DOCTYPE/i.test(htmlContent);
    
    const structureType = hasHtmlTag && hasHeadTag && hasBodyTag ? 'complete' : 'fragment';
    
    // 检测资源类型
    const images = htmlContent.match(/<img[^>]+>/gi) || [];
    const backgrounds = htmlContent.match(/background-image\s*:\s*url\([^)]+\)/gi) || [];
    const scripts = htmlContent.match(/<script[^>]*>[\s\S]*?<\/script>/gi) || [];
    const styles = htmlContent.match(/<style[^>]*>[\s\S]*?<\/style>/gi) || [];
    const links = htmlContent.match(/<link[^>]+>/gi) || [];
    
    // 计算复杂度
    complexity += images.length * 2;
    complexity += backgrounds.length * 2;
    complexity += scripts.length * 3;
    complexity += styles.length * 2;
    complexity += links.length;
    
    // 记录特征
    if (images.length > 0) features.push(`${images.length}个图片`);
    if (backgrounds.length > 0) features.push(`${backgrounds.length}个背景图`);
    if (scripts.length > 0) features.push(`${scripts.length}个脚本`);
    if (styles.length > 0) features.push(`${styles.length}个样式块`);
    if (links.length > 0) features.push(`${links.length}个外部资源`);
    
    return {
      type: structureType,
      features,
      complexity,
      hasDoctype,
      resourceCounts: {
        images: images.length,
        backgrounds: backgrounds.length,
        scripts: scripts.length,
        styles: styles.length,
        links: links.length
      }
    };

  } catch (error) {
    logger.error('HTML内容分析失败', { error: error.message });
    return { type: 'unknown', features: [], complexity: 0 };
  }
};

/**
 * 应用高级模式增强功能
 * @param {string} htmlContent - 原始HTML内容
 * @param {Object} analysis - 内容分析结果
 * @returns {string} 增强后的HTML内容
 */
export const applyAdvancedEnhancements = (htmlContent, analysis) => {
  try {
    let enhancedContent = htmlContent;
    
    // 1. 性能增强 - 添加优化CSS
    const performanceCSS = `
      <style data-advanced-mode="performance">
        /* 高级模式性能优化 */
        * {
          /* 字体渲染优化 */
          text-rendering: optimizeLegibility;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        
        img {
          /* 图像渲染优化 */
          image-rendering: optimizeQuality;
          /* 图像加载优化 */
          loading: lazy;
        }
        
        /* 资源加载错误处理 */
        .img-error {
          display: inline-block;
          padding: 10px;
          background: #f5f5f5;
          border: 1px dashed #ccc;
          color: #666;
          font-size: 12px;
          text-align: center;
          min-width: 100px;
          min-height: 50px;
        }
      </style>
    `;
    
    // 2. 兼容性增强 - 添加CSS前缀和兼容性样式
    const compatibilityCSS = `
      <style data-advanced-mode="compatibility">
        /* 高级模式兼容性增强 */
        * {
          -webkit-box-sizing: border-box;
          -moz-box-sizing: border-box;
          box-sizing: border-box;
        }
        
        /* Flexbox兼容性 */
        .flex {
          display: -webkit-box;
          display: -webkit-flex;
          display: -ms-flexbox;
          display: flex;
        }
      </style>
    `;
    
    // 3. 安全增强 - 资源加载监控脚本
    const securityScript = `
      <script data-advanced-mode="security">
        (function() {
          'use strict';
          
          // 高级模式安全监控
          window.AdvancedModeMonitor = {
            resourceErrors: [],
            
            // 监控资源加载错误
            monitorResourceErrors: function() {
              const images = document.querySelectorAll('img');
              images.forEach(img => {
                img.addEventListener('error', (e) => {
                  this.resourceErrors.push({
                    type: 'image',
                    src: e.target.src,
                    timestamp: Date.now()
                  });
                  
                  // 创建错误占位符
                  const errorDiv = document.createElement('div');
                  errorDiv.className = 'img-error';
                  errorDiv.textContent = '图片加载失败';
                  e.target.parentNode.replaceChild(errorDiv, e.target);
                });
              });
            },
            
            // 初始化监控
            init: function() {
              if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                  this.monitorResourceErrors();
                });
              } else {
                this.monitorResourceErrors();
              }
            }
          };
          
          // 启动监控
          window.AdvancedModeMonitor.init();
        })();
      </script>
    `;
    
    // 根据HTML结构类型应用增强
    if (analysis.type === 'complete') {
      // 完整HTML结构 - 在head中添加样式，在body结束前添加脚本
      if (enhancedContent.includes('</head>')) {
        enhancedContent = enhancedContent.replace('</head>', 
          performanceCSS + '\n' + compatibilityCSS + '\n</head>');
      }
      
      if (enhancedContent.includes('</body>')) {
        enhancedContent = enhancedContent.replace('</body>', 
          securityScript + '\n</body>');
      }
    } else {
      // HTML片段 - 直接添加增强内容
      enhancedContent = performanceCSS + '\n' + compatibilityCSS + '\n' + 
                       enhancedContent + '\n' + securityScript;
    }
    
    logger.info('高级模式增强应用成功', {
      type: analysis.type,
      complexity: analysis.complexity,
      features: analysis.features
    });
    
    return enhancedContent;

  } catch (error) {
    logger.error('应用高级模式增强失败', { error: error.message });
    return htmlContent; // 返回原始内容作为降级
  }
};

/**
 * 高级模式处理主函数
 * @param {string} htmlContent - 原始HTML内容
 * @param {Object} options - 处理选项
 * @returns {Object} 处理结果
 */
export const processAdvancedMode = (htmlContent, options = {}) => {
  try {
    // 1. 分析HTML内容
    const analysis = analyzeHtmlContent(htmlContent);
    
    // 2. 应用高级模式增强
    const enhancedContent = applyAdvancedEnhancements(htmlContent, analysis);
    
    // 3. 返回处理结果
    return {
      success: true,
      originalContent: htmlContent,
      enhancedContent,
      analysis,
      appliedFeatures: Object.keys(ADVANCED_FEATURES).filter(key => 
        ADVANCED_FEATURES[key].enabled
      ),
      metadata: {
        processingTime: Date.now(),
        contentLength: enhancedContent.length,
        enhancementRatio: enhancedContent.length / htmlContent.length
      }
    };

  } catch (error) {
    logger.error('高级模式处理失败', { error: error.message });
    
    return {
      success: false,
      originalContent: htmlContent,
      enhancedContent: htmlContent,
      error: error.message,
      analysis: { type: 'error', features: [], complexity: 0 }
    };
  }
};

export default {
  ADVANCED_FEATURES,
  analyzeHtmlContent,
  applyAdvancedEnhancements,
  processAdvancedMode
};

const { SystemLog, User, sequelize } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * 系统日志服务
 * 用于记录系统操作日志
 */
class LogService {
  /**
   * 记录系统操作日志
   * @param {Object} logData 日志数据
   * @param {number} logData.user_id 用户ID（可选）
   * @param {string} logData.username 用户名（可选）
   * @param {string} logData.action 操作类型
   * @param {string} logData.module 操作模块
   * @param {string} logData.description 操作描述
   * @param {string} logData.ip_address IP地址（可选）
   * @param {string} logData.user_agent 用户代理（可选）
   * @param {string} logData.status 操作状态（success/failure）
   * @param {string} logData.level 日志级别（info/warning/error）
   * @param {Object} logData.details 详细信息（可选）
   * @returns {Promise<Object>} 创建的日志记录
   */
  static async createLog(logData) {
    try {
      // 只在开发环境输出详细日志
      if (process.env.NODE_ENV === 'development') {
        console.log('准备创建日志记录:', {
          username: logData.username,
          module: logData.module,
          action: logData.action,
          description: logData.description,
          status: logData.status,
          level: logData.level || 'info'
        });
      }

      // 如果details是对象，转换为JSON字符串
      if (logData.details && typeof logData.details === 'object') {
        logData.details = JSON.stringify(logData.details);
      }

      // 设置默认的日志级别（如果未提供）
      if (!logData.level) {
        logData.level = logData.status === 'failure' ? 'error' : 'info';
      }

      // 确保source字段值正确
      let dataToCreate = { ...logData };

      // 确保source字段值为'frontend'或'backend'
      if (dataToCreate.source) {
        if (String(dataToCreate.source).trim() === 'frontend') {
          dataToCreate.source = 'frontend';
          console.log(`创建前台用户日志，source=${dataToCreate.source}`);
        } else {
          dataToCreate.source = 'backend';
        }
      } else {
        // 默认为后台日志
        dataToCreate.source = 'backend';
      }

      // 创建日志记录
      const log = await SystemLog.create(dataToCreate);

      // 只在开发环境输出详细日志
      if (process.env.NODE_ENV === 'development') {
        console.log('日志记录创建成功, ID:', log.id);
      }

      // 使用Winston记录到文件，根据日志级别记录
      const winstonLogLevel = logData.level || (logData.status === 'failure' ? 'error' : 'info');
      logger[winstonLogLevel](`[${logData.module || 'unknown'}] ${logData.action || 'action'}: ${logData.description || ''}`, {
        log_id: log.id,
        user_id: logData.user_id,
        username: logData.username,
        module: logData.module,
        level: winstonLogLevel
      });

      return log;
    } catch (error) {
      console.error('创建日志记录失败:', error);
      // 出错时依然尝试用Winston记录
      logger.error(`日志创建失败: ${error.message}`, {
        error: error.message,
        user_id: logData.user_id,
        module: logData.module
      });
      return null;
    }
  }

  /**
   * 查询系统日志
   * @param {Object} query 查询条件
   * @param {number} query.page 页码
   * @param {number} query.limit 每页数量
   * @param {string} query.module 模块筛选
   * @param {string} query.action 操作类型筛选
   * @param {string} query.username 用户名筛选
   * @param {string} query.status 状态筛选
   * @param {string} query.startDate 开始日期
   * @param {string} query.endDate 结束日期
   * @returns {Promise<Object>} 查询结果，包含日志列表和分页信息
   */
  static async getLogs(query) {
    const {
      page = 1,
      limit = 20,
      module,
      action,
      username,
      status,
      startDate,
      endDate
    } = query;

    const where = {};

    // 添加筛选条件
    if (module) where.module = module;
    if (action) where.action = action;
    if (username) where.username = { [Op.like]: `%${username}%` };
    if (status) where.status = status;

    // 日期范围筛选
    if (startDate || endDate) {
      where.created_at = {};
      if (startDate) where.created_at[Op.gte] = new Date(startDate);
      if (endDate) {
        // 设置结束日期为当天的23:59:59
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.created_at[Op.lte] = endDateTime;
      }
    }

    try {
      // 查询日志记录
      const { count, rows } = await SystemLog.findAndCountAll({
        where,
        order: [['created_at', 'DESC']],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname', 'phone', 'role'],
            required: false
          }
        ]
      });

      return {
        logs: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          total_pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('查询系统日志失败:', error);
      throw error;
    }
  }

  /**
   * 获取模块统计信息
   * @param {string} startDate 开始日期
   * @param {string} endDate 结束日期
   * @returns {Promise<Array>} 模块统计信息
   */
  static async getModuleStats(startDate, endDate) {
    const where = {};

    // 日期范围筛选
    if (startDate || endDate) {
      where.created_at = {};
      if (startDate) where.created_at[Op.gte] = new Date(startDate);
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.created_at[Op.lte] = endDateTime;
      }
    }

    try {
      const stats = await SystemLog.findAll({
        attributes: [
          'module',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where,
        group: ['module'],
        order: [[sequelize.literal('count'), 'DESC']]
      });

      return stats.map(item => ({
        module: item.module,
        count: parseInt(item.getDataValue('count'))
      }));
    } catch (error) {
      logger.error('获取模块统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取操作类型统计信息
   * @param {string} startDate 开始日期
   * @param {string} endDate 结束日期
   * @returns {Promise<Array>} 操作类型统计信息
   */
  static async getActionStats(startDate, endDate) {
    const where = {};

    // 日期范围筛选
    if (startDate || endDate) {
      where.created_at = {};
      if (startDate) where.created_at[Op.gte] = new Date(startDate);
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.created_at[Op.lte] = endDateTime;
      }
    }

    try {
      const stats = await SystemLog.findAll({
        attributes: [
          'action',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where,
        group: ['action'],
        order: [[sequelize.literal('count'), 'DESC']]
      });

      return stats.map(item => ({
        action: item.action,
        count: parseInt(item.getDataValue('count'))
      }));
    } catch (error) {
      logger.error('获取操作类型统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取状态统计信息
   * @param {string} startDate 开始日期
   * @param {string} endDate 结束日期
   * @returns {Promise<Array>} 状态统计信息
   */
  static async getStatusStats(startDate, endDate) {
    const where = {};

    // 日期范围筛选
    if (startDate || endDate) {
      where.created_at = {};
      if (startDate) where.created_at[Op.gte] = new Date(startDate);
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        where.created_at[Op.lte] = endDateTime;
      }
    }

    try {
      const stats = await SystemLog.findAll({
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where,
        group: ['status']
      });

      return stats.map(item => ({
        status: item.status,
        count: parseInt(item.getDataValue('count'))
      }));
    } catch (error) {
      logger.error('获取状态统计失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期日志
   * @param {Object} options 清理选项
   * @param {number} options.infoKeepDays 信息级别日志保留天数，默认15天
   * @param {number} options.warningKeepDays 警告级别日志保留天数，默认30天
   * @param {number} options.errorKeepDays 错误级别日志保留天数，默认60天
   * @param {number} options.maxLogs 日志表最大记录数，超过时会清理更多旧日志，默认10000条
   * @returns {Promise<Object>} 清理结果
   */
  static async cleanupLogs(options = {}) {
    try {
      // 首先检查数据库连接
      await sequelize.authenticate();

      // 默认配置和输入配置合并
      const config = {
        infoKeepDays: 15,
        warningKeepDays: 30,
        errorKeepDays: 60,
        maxLogs: 10000,
        ...options
      };

      // 获取当前日志总数
      const totalCount = await SystemLog.count();
      logger.info(`日志表当前有${totalCount}条记录`);

      // 按日志级别清理
      let infoResult = 0, warningResult = 0, errorResult = 0;

      // 清理info级别日志
      const infoCleanupDate = new Date();
      infoCleanupDate.setDate(infoCleanupDate.getDate() - config.infoKeepDays);
      infoResult = await SystemLog.destroy({
        where: {
          level: 'info',
          created_at: {
            [Op.lt]: infoCleanupDate
          }
        }
      });
      logger.info(`清理了${infoResult}条info级别日志（保留${config.infoKeepDays}天）`);

      // 清理warning级别日志
      const warningCleanupDate = new Date();
      warningCleanupDate.setDate(warningCleanupDate.getDate() - config.warningKeepDays);
      warningResult = await SystemLog.destroy({
        where: {
          level: 'warning',
          created_at: {
            [Op.lt]: warningCleanupDate
          }
        }
      });
      logger.info(`清理了${warningResult}条warning级别日志（保留${config.warningKeepDays}天）`);

      // 清理error级别日志
      const errorCleanupDate = new Date();
      errorCleanupDate.setDate(errorCleanupDate.getDate() - config.errorKeepDays);
      errorResult = await SystemLog.destroy({
        where: {
          level: 'error',
          created_at: {
            [Op.lt]: errorCleanupDate
          }
        }
      });
      logger.info(`清理了${errorResult}条error级别日志（保留${config.errorKeepDays}天）`);

      // 检查清理后的总数，如果仍超过最大记录数，则清理额外的记录
      const remainingCount = await SystemLog.count();
      let extraCleanupCount = 0;

      if (remainingCount > config.maxLogs) {
        const excessCount = remainingCount - config.maxLogs;
        // 获取需要清理的最旧记录的ID
        const oldestLogs = await SystemLog.findAll({
          attributes: ['id'],
          order: [['created_at', 'ASC']],
          limit: excessCount
        });

        const oldestIds = oldestLogs.map(log => log.id);

        // 清理这些记录
        extraCleanupCount = await SystemLog.destroy({
          where: {
            id: {
              [Op.in]: oldestIds
            }
          }
        });
        logger.info(`因超出最大限制，额外清理了${extraCleanupCount}条旧日志记录`);
      }

      // 尝试优化表
      try {
        await sequelize.query('OPTIMIZE TABLE system_logs');
        logger.info('日志表优化完成');
      } catch (optimizeError) {
        // 某些数据库可能不支持OPTIMIZE TABLE
        logger.warn('日志表优化失败，但不影响清理操作:', optimizeError.message);
      }

      // 返回清理结果
      return {
        infoRemoved: infoResult,
        warningRemoved: warningResult,
        errorRemoved: errorResult,
        extraRemoved: extraCleanupCount,
        totalRemoved: infoResult + warningResult + errorResult + extraCleanupCount,
        remainingCount: remainingCount - extraCleanupCount
      };
    } catch (error) {
      logger.error('清理过期日志失败:', error);
      throw error;
    }
  }
  /**
   * 归档日志
   * @param {Object} options 归档选项
   * @param {number} options.olderThan 归档多少天前的日志，默认30天
   * @param {string} options.status 归档特定状态的日志
   * @param {string} options.level 归档特定级别的日志
   * @returns {Promise<Object>} 归档结果
   */
  static async archiveLogs(options = {}) {
    const { olderThan = 30, status, level } = options;

    // 构建查询条件
    const where = {};
    if (status) where.status = status;
    if (level) where.level = level;

    // 日期条件
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThan);
    where.created_at = { [Op.lt]: cutoffDate };

    try {
      // 查询要归档的日志
      const logs = await SystemLog.findAll({ where });

      if (logs.length === 0) {
        return { archived: 0 };
      }

      // 确保归档目录存在
      const fs = require('fs');
      const path = require('path');
      const archiveDir = path.join('logs', 'archives');
      if (!fs.existsSync(archiveDir)) {
        fs.mkdirSync(archiveDir, { recursive: true });
      }

      // 生成归档文件名
      const archiveDate = new Date().toISOString().split('T')[0];
      const archiveFileName = `logs_archive_${archiveDate}.json`;

      // 将日志写入文件
      fs.writeFileSync(
        path.join(archiveDir, archiveFileName),
        JSON.stringify(logs, null, 2)
      );

      // 删除已归档的日志
      await SystemLog.destroy({ where });

      return { archived: logs.length, fileName: archiveFileName };
    } catch (error) {
      logger.error('归档日志失败:', error);
      throw error;
    }
  }
}

module.exports = LogService;

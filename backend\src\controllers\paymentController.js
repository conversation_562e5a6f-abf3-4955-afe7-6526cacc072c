const { PaymentRecord, User, PointRecord } = require('../models');
const { successResponse, errorResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');
const { sequelize } = require('../config/database');
const { v4: uuidv4 } = require('uuid');
const dayjs = require('dayjs');
const { 
  verifyWechatPaySignature, 
  verifyAlipaySignature, 
  logPaymentSecurity,
  checkIPWhitelist,
  decryptWechatPayResource,
  checkReplayAttack,
  checkAlipayReplayAttack
} = require('../utils/paymentSecurityUtils');
const { createPaymentOrder, processPaymentCallback, queryPaymentOrder, updateOrderPaymentMethod, closePaymentOrder, calculateMemberExpireDate } = require('../services/paymentService');
const MemberPackage = require('../models/MemberPackage');
const PointPackage = require('../models/PointPackage');
const { Op } = require('sequelize');

/**
 * 获取会员套餐列表（前台用户使用）
 * @route GET /api/payment/membership/packages
 */
const getMemberPackages = async (req, res) => {
  try {
    const packages = await MemberPackage.findAll({
      where: { is_active: true },
      order: [['duration', 'ASC']],
      attributes: ['id', 'name', 'duration', 'price', 'discount_price', 'description']
    });

    return successResponse(res, '获取会员套餐列表成功', { packages });
  } catch (error) {
    logger.error('获取会员套餐列表失败:', error);
    return errorResponse(res, '获取会员套餐列表失败', 500);
  }
};

/**
 * 获取积分套餐列表（前台用户使用）
 * @route GET /api/payment/points/packages
 */
const getPointPackages = async (req, res) => {
  try {
    const packages = await PointPackage.findAll({
      where: { is_active: true },
      order: [['points', 'ASC']],
      attributes: ['id', 'name', 'points', 'price', 'bonus_points', 'description']
    });

    return successResponse(res, '获取积分套餐列表成功', { packages });
  } catch (error) {
    logger.error('获取积分套餐列表失败:', error);
    return errorResponse(res, '获取积分套餐列表失败', 500);
  }
};

/**
 * 创建订单
 * @route POST /api/payment/create-order
 */
const createOrder = async (req, res) => {
  const { product_type, payment_type, package_id } = req.body;
  const userId = req.user.id;
  const clientIp = req.ip || req.connection.remoteAddress;
  const deviceType = req.headers['user-agent'].includes('Mobile') ? 'Mobile' : 'PC';

  try {
    // 创建订单
    const result = await createPaymentOrder(
      userId,
      product_type,
      package_id,
      payment_type,
      clientIp,
      deviceType
    );

    // 记录支付安全日志
    await logPaymentSecurity(
      userId,
      result.paymentRecord.order_no,
      clientIp,
      'create_order',
      'success',
      JSON.stringify(req.body),
      JSON.stringify({
        orderNo: result.paymentRecord.order_no,
        amount: result.paymentRecord.amount
      }),
      'low',
      `用户创建${product_type === 'vip' ? '会员' : '积分'}订单`
    );

    return successResponse(res, '创建订单成功', {
      order_no: result.paymentRecord.order_no,
      amount: result.paymentRecord.amount,
      payment_type: result.paymentRecord.payment_type,
      qr_code_url: result.paymentParams.qrCodeUrl,
      expire_time: result.paymentParams.paymentExpireTime
    });
  } catch (error) {
    logger.error('创建订单失败:', error);
    
    // 记录失败日志
    try {
      await logPaymentSecurity(
        userId,
        null,
        clientIp,
        'create_order',
        'failed',
        JSON.stringify(req.body),
        JSON.stringify({ error: error.message }),
        'medium',
        `用户创建订单失败: ${error.message}`
      );
    } catch (logError) {
      logger.error('记录支付安全日志失败:', logError);
    }
    
    return errorResponse(res, error.message || '创建订单失败', 500);
  }
};

/**
 * 订单查询
 * @route GET /api/payment/order/:orderNo
 */
const queryOrder = async (req, res) => {
  const { orderNo } = req.params;
  const userId = req.user.id;

  try {
    const order = await PaymentRecord.findOne({
      where: { order_no: orderNo, user_id: userId },
      include: [
        { model: User, as: 'user', attributes: ['id', 'nickname', 'phone', 'role'] }
      ]
    });

    if (!order) {
      return errorResponse(res, '订单不存在', 404);
    }

    // 根据产品类型获取套餐信息
    let packageInfo = null;
    
    if (order.product_type === 'vip') {
      // 尝试解析product_detail
      let packageDetail;
      try {
        packageDetail = JSON.parse(order.product_detail);
      } catch (err) {
        // 如果解析失败，说明product_detail不是JSON格式，可能是老数据
        packageDetail = { name: order.product_detail || 'VIP会员套餐', duration: 30 };
      }

      // 获取VIP套餐信息
      packageInfo = {
        id: order.package_id,
        name: packageDetail.name,
        duration: packageDetail.duration || 30,
        price: order.amount,
        discount_price: order.amount,
        description: packageDetail.description || 'VIP会员服务'
      };
    } else if (order.product_type === 'points') {
      // 尝试解析product_detail
      let packageDetail;
      try {
        packageDetail = JSON.parse(order.product_detail);
      } catch (err) {
        // 如果解析失败，说明product_detail不是JSON格式，可能是老数据
      const points = Math.floor(order.amount * 10); // 简单换算：10元=100积分
        packageDetail = { 
        name: order.product_detail || '积分充值',
        points: points,
          bonus_points: Math.floor(points * 0.1) // 赠送10%
        };
      }

      // 获取积分套餐信息
      packageInfo = {
        id: order.package_id,
        name: packageDetail.name,
        points: packageDetail.points,
        price: order.amount,
        bonus_points: packageDetail.bonus_points || 0,
        description: packageDetail.description || `充值${packageDetail.points}积分`
      };
    }

    // 根据支付状态准备支付参数
    let paymentParams = null;
    if (order.payment_status === 'pending') {
      try {
        // 检查created_at是否为有效的日期时间值
        const createdAt = order.created_at ? new Date(order.created_at) : new Date();
        if (isNaN(createdAt.getTime())) {
          // 如果不是有效的日期时间，使用当前时间
          logger.warn(`订单 ${orderNo} 的创建时间无效，使用当前时间代替`);
          paymentParams = {
            qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderNo}`,
            paymentExpireTime: new Date(Date.now() + 15 * 60000).toISOString() // 15分钟过期
          };
        } else {
          // 正常情况
          paymentParams = {
            qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderNo}`,
            paymentExpireTime: new Date(createdAt.getTime() + 15 * 60000).toISOString() // 15分钟过期
          };
        }
      } catch (err) {
        // 出现异常，使用当前时间
        logger.error(`计算订单 ${orderNo} 的过期时间时出错:`, err);
        paymentParams = {
          qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderNo}`,
          paymentExpireTime: new Date(Date.now() + 15 * 60000).toISOString() // 15分钟过期
        };
      }
    }

    return successResponse(res, '查询订单成功', {
      order,
      packageInfo,
      paymentParams
    });
  } catch (error) {
    logger.error('查询订单失败:', error);
    return errorResponse(res, '查询订单失败', 500);
  }
};

/**
 * 获取用户订单列表
 * @route GET /api/payment/orders
 */
const getUserOrders = async (req, res) => {
  const { page = 1, limit = 10, status } = req.query;
  const userId = req.user.id;

  try {
    const offset = (page - 1) * limit;

    // 构建查询条件
    const where = { 
      user_id: userId,
      display_status: '显示' // 只查询显示状态为"显示"的订单
    };
    if (status) {
      where.payment_status = status;
    }

    // 查询订单
    const { count, rows } = await PaymentRecord.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // 准备分页信息
    const pagination = {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      total_pages: Math.ceil(count / limit)
    };

    return successResponse(res, '获取订单列表成功', {
      orders: rows,
      pagination
    });
  } catch (error) {
    logger.error('获取订单列表失败:', error);
    return errorResponse(res, '获取订单列表失败', 500);
  }
};

/**
 * 支付回调处理（微信支付）
 * @route POST /api/payment/callback/wechat
 */
const wechatPayCallback = async (req, res) => {
  // 获取客户端IP
  const clientIP = req.ip || 
                  req.connection.remoteAddress || 
                  req.socket.remoteAddress || 
                  req.connection.socket.remoteAddress;
  
  try {
    // 1. 检查IP白名单
    const isIPAllowed = await checkIPWhitelist(clientIP, 'wechat');
    if (!isIPAllowed) {
      logger.warn(`微信支付回调IP不在白名单中: ${clientIP}`);
      await logPaymentSecurity(
        null,
        '未知订单号',
        clientIP,
        'wechat_callback',
        'failed',
        {},
        { error: 'IP不在白名单' },
        'high',
        `微信支付回调IP不在白名单: ${clientIP}`
      );
      return res.status(403).json({ code: 'FAIL', message: '拒绝访问' });
    }
    
    // 2. 获取微信支付回调信息
    const timestamp = req.headers['wechatpay-timestamp'];
    const nonce = req.headers['wechatpay-nonce'];
    const signature = req.headers['wechatpay-signature'];
    const serial = req.headers['wechatpay-serial'];
    const requestBody = req.body;
    const rawBody = req.rawBody || JSON.stringify(requestBody);
    
    logger.info('收到微信支付回调，headers:', {
      timestamp,
      nonce,
      serial,
      signature: signature ? signature.substring(0, 10) + '...' : 'missing'
    });
    
    // 3. 检查必要的头信息
    if (!timestamp || !nonce || !signature || !serial) {
      logger.error('微信支付回调缺少必要的请求头');
      return res.status(400).json({ code: 'FAIL', message: '缺少必要参数' });
    }
    
    // 4. 检查重放攻击
    const isReplay = await checkReplayAttack(nonce, timestamp);
    if (isReplay) {
      logger.warn(`检测到疑似微信支付回调重放攻击`);
    await logPaymentSecurity(
        null,
        '未知订单号',
      clientIP,
      'wechat_callback',
        'failed',
        { timestamp, nonce },
        { error: '疑似重放攻击' },
        'high',
        `检测到疑似微信支付回调重放攻击`
      );
      return res.status(401).json({ code: 'FAIL', message: '签名验证失败' });
    }
    
    // 5. 验证签名
    const isSignatureValid = await verifyWechatPaySignature({
      timestamp,
      nonce,
      signature,
      serial,
      body: rawBody
    });
    
    if (!isSignatureValid && process.env.NODE_ENV === 'production') {
      logger.error(`微信支付回调签名验证失败`);
      await logPaymentSecurity(
        null,
        '未知订单号',
        clientIP,
        'wechat_callback',
        'failed',
        { timestamp, nonce, serial },
        { error: '签名验证失败' },
        'high',
        `微信支付回调签名验证失败`
      );
      return res.status(401).json({ code: 'FAIL', message: '签名验证失败' });
    }
    
    // 6. 解析回调数据
    // 微信支付V3的回调数据格式为: { id: 'xxx', create_time: 'xxx', resource_type: 'encrypt-resource', event_type: 'xxx', resource: { ciphertext: 'xxx', associated_data: 'xxx', nonce: 'xxx' } }
    logger.info('开始解析微信支付回调数据');
    if (!requestBody || !requestBody.resource) {
      logger.error('微信支付回调数据格式异常，缺少resource字段');
      return res.status(400).json({ code: 'FAIL', message: '回调数据格式异常' });
    }
    
    // 7. 解密数据
    let decryptedData;
    try {
      decryptedData = await decryptWechatPayResource(requestBody.resource);
      logger.info('微信支付回调数据解密成功');
    } catch (err) {
      logger.error('微信支付回调数据解密失败:', err);
      await logPaymentSecurity(
        null,
        '未知订单号',
        clientIP,
        'wechat_callback',
        'failed',
        requestBody,
        { error: '数据解密失败' },
        'high',
        `微信支付回调数据解密失败: ${err.message}`
      );
      return res.status(400).json({ code: 'FAIL', message: '数据解密失败' });
    }
    
    // 8. 从解密后的数据中获取订单信息
    const { out_trade_no, transaction_id, trade_state } = decryptedData;
    if (!out_trade_no) {
      logger.error('微信支付回调解密数据缺少订单号');
      return res.status(400).json({ code: 'FAIL', message: '缺少订单号' });
    }

    // 记录安全日志
    await logPaymentSecurity(
      null,
      out_trade_no,
      clientIP,
      'wechat_callback',
      'success',
      { event_type: requestBody.event_type },
      { trade_state, transaction_id },
      'low',
      `微信支付回调数据接收与解密成功，订单号: ${out_trade_no}`
    );

    // 9. 查找订单
    const order = await PaymentRecord.findOne({
      where: { order_no: out_trade_no }
    });

    if (!order) {
      logger.error(`微信支付回调订单不存在，订单号: ${out_trade_no}`);
      await logPaymentSecurity(
        null,
        out_trade_no,
        clientIP,
        'wechat_callback',
        'failed',
        { trade_state, transaction_id },
        { error: '订单不存在' },
        'high',
        '微信支付回调订单不存在'
      );
      return res.status(200).json({ code: 'SUCCESS', message: '成功' }); // 返回成功避免微信重复通知
    }

    // 10. 实现幂等性处理 - 检查订单状态
    if (order.payment_status !== 'pending') {
      logger.info(`微信支付回调订单已处理，订单号: ${out_trade_no}, 当前状态: ${order.payment_status}`);
      return res.status(200).json({ code: 'SUCCESS', message: '成功' }); // 已处理过的订单直接返回成功
    }

    // 11. 开启事务处理
    const transaction = await sequelize.transaction();

    try {
      // 12. 根据支付结果更新订单状态
      const paymentSuccess = trade_state === 'SUCCESS';
      
      // 使用update方法替代直接赋值属性
      await order.update({
        payment_status: paymentSuccess ? 'success' : 'failed',
        transaction_id: transaction_id,
        payment_time: new Date(),
        notify_data: JSON.stringify({
        ...decryptedData,
        event_type: requestBody.event_type,
        resource_type: requestBody.resource_type
        })
      }, { transaction });

      // 13. 如果支付成功，处理后续业务逻辑（会员/积分）
      if (paymentSuccess) {
        const user = await User.findByPk(order.user_id);
        if (!user) {
          throw new Error(`用户不存在，用户ID: ${order.user_id}`);
        }

        if (order.product_type === 'vip') {
          // 处理VIP会员购买
          // 从product_detail解析套餐信息
          let packageInfo;
          try {
            packageInfo = JSON.parse(order.product_detail);
          } catch (err) {
            // 如果解析失败，说明product_detail不是JSON格式，可能是老数据
            throw new Error('订单中缺少有效的套餐信息，无法完成支付');
          }

          if (!packageInfo || !packageInfo.duration) {
            throw new Error('订单中缺少套餐时长信息，无法完成支付');
          }

          let expireDate;
          if (user.vip_expire_date && new Date(user.vip_expire_date) > new Date()) {
            // 如果用户已经是VIP，延长VIP到期时间
            expireDate = calculateMemberExpireDate(new Date(user.vip_expire_date), packageInfo.duration);
          } else {
            // 如果用户不是VIP，设置新的到期时间
            expireDate = calculateMemberExpireDate(null, packageInfo.duration);
          }

          // 使用update方法更新用户信息
          await user.update({
            role: 'vip',
            vip_expire_date: expireDate
          }, { transaction });

          logger.info(`用户${user.id}成功购买VIP会员，到期时间：${expireDate.toISOString()}`);
        } else if (order.product_type === 'points') {
          // 处理积分购买
          // 这里简化处理，按照 10元 = 100积分 计算
          const pointsToAdd = Math.floor(order.amount * 10);
          const currentPoints = user.points || 0;
          
          // 使用update方法更新用户积分
          await user.update({
            points: currentPoints + pointsToAdd
          }, { transaction });
          
          // 创建积分记录
          await PointRecord.create({
            user_id: user.id,
            points_change: pointsToAdd,
            points_after: currentPoints + pointsToAdd,
            operation_type: 'recharge',
            description: `充值获得${pointsToAdd}积分`,
            operation_id: `recharge_${order.order_no}`
          }, { transaction });

          logger.info(`用户${user.id}成功购买${pointsToAdd}积分`);
        }
      }

      // 提交事务
      await transaction.commit();
      
      // 记录安全日志
      await logPaymentSecurity(
        order.user_id,
        out_trade_no,
        clientIP,
        'wechat_payment_processed',
        'success',
        { trade_state, transaction_id },
        { order_status: order.payment_status },
        'low',
        `微信支付回调处理成功，订单状态: ${order.payment_status}`
      );

      return res.status(200).json({ code: 'SUCCESS', message: '成功' });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      
      // 记录安全日志
      await logPaymentSecurity(
        order.user_id,
        out_trade_no,
        clientIP,
        'wechat_payment_processed',
        'failed',
        { trade_state, transaction_id },
        { error: error.message },
        'high',
        '微信支付回调处理失败'
      );
      
      throw error;
    }
  } catch (error) {
    logger.error('处理微信支付回调失败:', error);
    return res.status(500).json({ code: 'FAIL', message: '内部服务器错误' });
  }
};

/**
 * 支付回调处理（支付宝）
 * @route POST /api/payment/callback/alipay
 */
const alipayCallback = async (req, res) => {
  // 获取客户端IP
  const clientIP = req.ip || 
                  req.connection.remoteAddress || 
                  req.socket.remoteAddress || 
                  req.connection.socket.remoteAddress;
  
  try {
    // 解析支付宝回调数据
    const callbackData = req.body;
    const { out_trade_no, trade_no, trade_status, notify_id, notify_time } = callbackData;
    
    // 记录原始回调数据
    logger.info(`收到支付宝回调数据: ${JSON.stringify(callbackData)}`);
    
    // 验证签名
    const isSignatureValid = await verifyAlipaySignature(callbackData);
    
    // 记录安全日志
    await logPaymentSecurity(
      null, // 用户ID，此时还未获取
      out_trade_no,
      clientIP,
      'alipay_callback',
      isSignatureValid ? 'success' : 'failed',
      callbackData,
      { signature_valid: isSignatureValid },
      isSignatureValid ? 'low' : 'high',
      isSignatureValid ? '支付宝回调签名验证通过' : '支付宝回调签名验证失败'
    );
    
    if (!isSignatureValid && process.env.NODE_ENV === 'production') {
      logger.error(`支付宝回调签名验证失败，订单号: ${out_trade_no}`);
      return res.status(200).send('failure');
    }
    
    // 检测重放攻击
    const isReplayAttack = await checkAlipayReplayAttack(notify_id, notify_time);
    if (isReplayAttack && process.env.NODE_ENV === 'production') {
      logger.error(`检测到支付宝回调重放攻击，订单号: ${out_trade_no}`);
      
      // 记录安全日志
      await logPaymentSecurity(
        null,
        out_trade_no,
        clientIP,
        'alipay_callback',
        'failed',
        callbackData,
        { error: '检测到重放攻击' },
        'high',
        '支付宝回调检测到重放攻击'
      );
      
      return res.status(200).send('failure');
    }

    // 查找订单
    const order = await PaymentRecord.findOne({
      where: { order_no: out_trade_no }
    });

    if (!order) {
      logger.error(`支付宝回调订单不存在，订单号: ${out_trade_no}`);
      await logPaymentSecurity(
        null,
        out_trade_no,
        clientIP,
        'alipay_callback',
        'failed',
        callbackData,
        { error: '订单不存在' },
        'high',
        '支付宝回调订单不存在'
      );
      return res.status(200).send('failure');
    }

    // 检查订单状态
    if (order.payment_status !== 'pending') {
      logger.info(`支付宝回调订单已处理，订单号: ${out_trade_no}, 状态: ${order.payment_status}`);
      return res.status(200).send('success'); // 已处理过的订单直接返回成功
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 更新订单状态
      await order.update({
        payment_status: trade_status === 'TRADE_SUCCESS' ? 'success' : 'failed',
        transaction_id: trade_no,
        payment_time: new Date(), // 记录支付时间
        notify_data: JSON.stringify(callbackData) // 记录回调原始数据
      }, { transaction });

      // 如果支付成功，处理后续业务
      if (trade_status === 'TRADE_SUCCESS') {
        const user = await User.findByPk(order.user_id);

        if (order.product_type === 'vip') {
          // 处理VIP会员购买
          // 从product_detail解析套餐信息
          let packageInfo;
          try {
            packageInfo = JSON.parse(order.product_detail);
          } catch (err) {
            // 如果解析失败，说明product_detail不是JSON格式，可能是老数据
            throw new Error('订单中缺少有效的套餐信息，无法完成支付');
          }

          if (!packageInfo || !packageInfo.duration) {
            throw new Error('订单中缺少套餐时长信息，无法完成支付');
          }

          let expireDate;
          if (user.vip_expire_date && new Date(user.vip_expire_date) > new Date()) {
            // 如果用户已经是VIP，延长VIP到期时间
            expireDate = calculateMemberExpireDate(new Date(user.vip_expire_date), packageInfo.duration);
          } else {
            // 如果用户不是VIP，设置新的到期时间
            expireDate = calculateMemberExpireDate(null, packageInfo.duration);
          }

          // 使用update方法更新用户信息
          await user.update({
            role: 'vip',
            vip_expire_date: expireDate
          }, { transaction });

          logger.info(`用户${user.id}成功购买VIP会员，到期时间：${expireDate.toISOString()}`);
        } else if (order.product_type === 'points') {
          // 处理积分购买
          // 这里简化处理，按照 10元 = 100积分 计算
          const pointsToAdd = Math.floor(order.amount * 10);
          const currentPoints = user.points || 0;
          
          // 使用update方法更新用户积分
          await user.update({
            points: currentPoints + pointsToAdd
          }, { transaction });
          
          // 创建积分记录
          await PointRecord.create({
            user_id: user.id,
            points_change: pointsToAdd,
            points_after: currentPoints + pointsToAdd,
            operation_type: 'recharge',
            description: `充值获得${pointsToAdd}积分`,
            operation_id: `recharge_${order.order_no}`
          }, { transaction });

          logger.info(`用户${user.id}成功购买${pointsToAdd}积分`);
        }
      }

      // 提交事务
      await transaction.commit();
      
      // 记录安全日志
      await logPaymentSecurity(
        order.user_id,
        out_trade_no,
        clientIP,
        'alipay_payment_processed',
        'success',
        callbackData,
        { order_status: order.payment_status },
        'low',
        `支付宝回调处理成功，订单状态: ${order.payment_status}`
      );

      return res.status(200).send('success');
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      
      // 记录安全日志
      await logPaymentSecurity(
        order.user_id,
        out_trade_no,
        clientIP,
        'alipay_payment_processed',
        'failed',
        callbackData,
        { error: error.message },
        'high',
        '支付宝回调处理失败'
      );
      
      throw error;
    }
  } catch (error) {
    logger.error('处理支付宝回调失败:', error);
    return res.status(200).send('failure');
  }
};

/**
 * 模拟支付完成
 * @route POST /api/payment/mock-pay/:orderNo
 */
const mockPayment = async (req, res) => {
  const { orderNo } = req.params;
  const userId = req.user.id;
  const clientIP = req.ip || 
                req.connection.remoteAddress || 
                req.socket.remoteAddress || 
                req.connection.socket.remoteAddress;

  try {
    // 记录安全日志
    await logPaymentSecurity(
      userId,
      orderNo,
      clientIP,
      'mock_payment',
      'suspicious',
      { orderNo },
      {},
      'low',
      `模拟支付请求，订单号: ${orderNo}`
    );

    // 查找订单
    const order = await PaymentRecord.findOne({
      where: { order_no: orderNo, user_id: userId }
    });

    if (!order) {
      await logPaymentSecurity(
        userId,
        orderNo,
        clientIP,
        'mock_payment',
        'failed',
        { orderNo },
        { error: '订单不存在' },
        'medium',
        `模拟支付失败，订单不存在: ${orderNo}`
      );
      return errorResponse(res, '订单不存在', 404);
    }

    // 检查订单状态
    if (order.payment_status !== 'pending') {
      await logPaymentSecurity(
        userId,
        orderNo,
        clientIP,
        'mock_payment',
        'failed',
        { orderNo },
        { error: '订单已处理', status: order.payment_status },
        'medium',
        `模拟支付失败，订单已处理: ${orderNo}, 状态: ${order.payment_status}`
      );
      return errorResponse(res, '订单已处理，不能重复支付', 400);
    }

    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 更新订单状态 - 修改这里，使用update方法而不是直接赋值属性
      const paymentTime = new Date();
      await order.update({
        payment_status: 'success',
        transaction_id: `MOCK_${Date.now()}`,
        payment_time: paymentTime,
        notify_data: JSON.stringify({ mock: true, time: paymentTime })
      }, { transaction });

      // 处理后续业务
      const user = await User.findByPk(userId);

      if (order.product_type === 'vip') {
        // 处理VIP会员购买
        // 从product_detail解析套餐信息
        let packageInfo;
        try {
          packageInfo = JSON.parse(order.product_detail);
        } catch (err) {
          // 如果解析失败，说明product_detail不是JSON格式，可能是老数据
          throw new Error('订单中缺少有效的套餐信息，无法完成支付');
        }

        if (!packageInfo || !packageInfo.duration) {
          throw new Error('订单中缺少套餐时长信息，无法完成支付');
        }

        let expireDate;
        if (user.vip_expire_date && new Date(user.vip_expire_date) > new Date()) {
          // 如果用户已经是VIP，延长VIP到期时间
          expireDate = calculateMemberExpireDate(new Date(user.vip_expire_date), packageInfo.duration);
        } else {
          // 如果用户不是VIP，设置新的到期时间
          expireDate = calculateMemberExpireDate(null, packageInfo.duration);
        }

        await user.update({
          role: 'vip',
          vip_expire_date: expireDate
        }, { transaction });

        logger.info(`用户${userId}成功购买VIP会员，到期时间：${expireDate.toISOString()}`);
      } else if (order.product_type === 'points') {
        // 处理积分购买
        // 按照 10元 = 100积分 计算
        const pointsToAdd = Math.floor(order.amount * 10);
        const currentPoints = user.points; // 保存当前积分
        
        await user.update({
          points: currentPoints + pointsToAdd
        }, { transaction });

        // 创建积分记录
        await PointRecord.create({
          user_id: userId,
          points_change: pointsToAdd,
          points_after: currentPoints + pointsToAdd, // 使用更新后的积分
          operation_type: 'recharge',
          description: `通过快捷支付获得${pointsToAdd}积分`,
          operation_id: `recharge_${order.order_no}`
        }, { transaction });

        logger.info(`用户${userId}成功购买${pointsToAdd}积分`);
      }

      // 提交事务
      await transaction.commit();

      // 记录安全日志
      await logPaymentSecurity(
        userId,
        orderNo,
        clientIP,
        'mock_payment',
        'success',
        { orderNo },
        { order_status: 'success', transaction_id: order.transaction_id },
        'low',
        `模拟支付成功，订单号: ${orderNo}`
      );

      // 重新获取更新后的订单信息，确保返回最新数据
      const updatedOrder = await PaymentRecord.findOne({
        where: { order_no: orderNo }
      });

      return successResponse(res, '模拟支付成功', { order: updatedOrder });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      
      // 记录安全日志
      await logPaymentSecurity(
        userId,
        orderNo,
        clientIP,
        'mock_payment',
        'failed',
        { orderNo },
        { error: error.message },
        'high',
        `模拟支付失败，内部错误: ${error.message}`
      );
      
      throw error;
    }
  } catch (error) {
    logger.error('模拟支付失败:', error);
    return errorResponse(res, '模拟支付失败: ' + error.message, 500);
  }
};

/**
 * 更新订单支付方式
 * @route PUT /api/payment/order/:orderNo/payment-method
 */
const updatePaymentMethod = async (req, res) => {
  const { orderNo } = req.params;
  const { payment_type } = req.body;
  const userId = req.user.id;

  try {
    // 查找并验证订单
    const order = await PaymentRecord.findOne({
      where: { order_no: orderNo, user_id: userId, payment_status: 'pending' }
    });

    if (!order) {
      return errorResponse(res, '订单不存在或不可修改', 404);
    }

    // 更新支付方式
    order.payment_type = payment_type;
    await order.save();

    // 记录客户端IP
    const clientIP = req.ip || 
                    req.connection.remoteAddress || 
                    req.socket.remoteAddress || 
                    req.connection.socket.remoteAddress;

    // 记录安全日志
    await logPaymentSecurity(
      userId,
      orderNo,
      clientIP,
      'update_payment_method',
      'success',
      req.body,
      { payment_type },
      'low',
      `用户更新订单${orderNo}支付方式为${payment_type}`
    );

    // 获取支付参数（实际项目需对接真实支付网关）
    const paymentParams = {
      qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderNo}_${payment_type}`,
      paymentExpireTime: new Date(new Date().getTime() + 15 * 60000).toISOString() // 15分钟过期
    };

    logger.info(`用户${userId}更新订单${orderNo}支付方式为${payment_type}`);

    return successResponse(res, '更新支付方式成功', {
      order,
      payment_params: paymentParams
    });
  } catch (error) {
    logger.error('更新支付方式失败:', error);
    return errorResponse(res, '更新支付方式失败', 500);
  }
};

/**
 * 更新订单显示状态（用户删除）
 * @route PUT /api/payment/orders/:id/status
 */
const updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { status } = req.body;

    if (!id) {
      return errorResponse(res, '缺少订单ID', 400);
    }

    if (!status || !['显示', '隐藏'].includes(status)) {
      return errorResponse(res, '状态参数无效，必须是"显示"或"隐藏"', 400);
    }

    // 查找用户的订单记录
    const order = await PaymentRecord.findOne({
      where: {
        id: id,
        user_id: userId
      }
    });

    if (!order) {
      return errorResponse(res, '订单不存在或不属于当前用户', 404);
    }

    // 更新状态
    await order.update({ display_status: status });

    logger.info(`用户${userId}更新订单${id}状态为${status}成功`);

    return successResponse(res, `订单状态已更新为"${status}"`, { id: id, status: status });
  } catch (error) {
    logger.error('更新订单状态失败:', error);
    return errorResponse(res, '更新订单状态失败', 500);
  }
};

/**
 * 关闭订单
 * @route PUT /api/payment/orders/:id/close
 */
const closeOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    if (!id) {
      return errorResponse(res, '缺少订单ID', 400);
    }

    // 查找用户的订单记录
    const order = await PaymentRecord.findOne({
      where: {
        id: id,
        user_id: userId
      }
    });

    if (!order) {
      return errorResponse(res, '订单不存在或不属于当前用户', 404);
    }

    // 只有待支付的订单可以关闭
    if (order.payment_status !== 'pending') {
      return errorResponse(res, '只有待支付的订单可以关闭', 400);
    }

    // 更新状态为已关闭
    await order.update({ payment_status: 'closed' });

    logger.info(`用户${userId}关闭订单${id}成功`);

    return successResponse(res, '订单已关闭', { id: id, status: 'closed' });
  } catch (error) {
    logger.error('关闭订单失败:', error);
    return errorResponse(res, '关闭订单失败', 500);
  }
};

module.exports = {
  getMemberPackages,
  getPointPackages,
  createOrder,
  queryOrder,
  getUserOrders,
  wechatPayCallback,
  alipayCallback,
  mockPayment,
  updatePaymentMethod,
  updateOrderStatus,
  closeOrder
};

/**
 * 任务清理调度器
 * 启动定期任务清理服务
 */
const { startCleanupSchedule } = require('../utils/taskCleanupService');
const logger = require('../utils/logger');

/**
 * 启动任务清理调度器
 * @param {number} intervalMs - 清理间隔（毫秒）
 * @returns {Object} 定时器对象
 */
const startTaskCleanupScheduler = (intervalMs = 60000) => {
  logger.info(`正在启动任务清理调度器，间隔: ${intervalMs}ms`);
  return startCleanupSchedule(intervalMs);
};

module.exports = startTaskCleanupScheduler; 
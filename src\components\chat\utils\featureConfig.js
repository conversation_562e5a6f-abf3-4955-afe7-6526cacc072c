/**
 * 功能配置管理器
 * 使用Feature Flag模式安全管理增强功能的启用/禁用
 * 避免环境变量依赖，提供配置驱动的功能控制
 */

import logger from '../../../services/logs/frontendLogger';

/**
 * 功能开关配置
 * 所有增强功能默认禁用，确保系统稳定性
 */
const FEATURE_FLAGS = {
  // 增强型HTML加载器
  ADVANCED_HTML_LOADER: {
    enabled: true, // 默认启用 - 支持复杂静态页面加载
    name: 'advancedHtmlLoader',
    description: '增强型HTML加载器，支持复杂静态页面',
    version: '1.0.0',
    dependencies: [],
    riskLevel: 'medium'
  },

  // 智能元素检测
  ADVANCED_ELEMENT_DETECTION: {
    enabled: true, // 默认启用 - 提供智能编辑建议
    name: 'advancedElementDetection',
    description: '智能元素检测系统，识别复杂页面元素',
    version: '1.0.0',
    dependencies: ['ADVANCED_HTML_LOADER'],
    riskLevel: 'low'
  },

  // 双模式预览控制
  DUAL_MODE_PREVIEW: {
    enabled: true, // 默认启用 - 支持标准/高级模式切换
    name: 'dualModePreview',
    description: '双模式预览控制器，支持标准/高级模式切换',
    version: '1.0.0',
    dependencies: ['ADVANCED_HTML_LOADER'],
    riskLevel: 'medium'
  },
  
  // 调试模式
  DEBUG_MODE: {
    enabled: false,
    name: 'debugMode',
    description: '调试模式，显示详细日志和调试信息',
    version: '1.0.0',
    dependencies: [],
    riskLevel: 'low'
  }
};

/**
 * 功能配置管理器类
 */
class FeatureConfigManager {
  constructor() {
    this.flags = { ...FEATURE_FLAGS };
    this.listeners = new Map();
    this.initialized = false;
  }

  /**
   * 初始化配置管理器
   */
  init() {
    if (this.initialized) return;
    
    try {
      // 从localStorage读取用户配置（如果存在）
      const savedConfig = localStorage.getItem('chatPreview_featureFlags');
      if (savedConfig) {
        const userConfig = JSON.parse(savedConfig);
        this.mergeUserConfig(userConfig);
      }
      
      this.initialized = true;
      logger.info('功能配置管理器初始化完成', { flags: this.getEnabledFeatures() });
    } catch (error) {
      logger.error('功能配置管理器初始化失败', { error: error.message });
      // 初始化失败时使用默认配置
      this.flags = { ...FEATURE_FLAGS };
      this.initialized = true;
    }
  }

  /**
   * 合并用户配置
   * @param {Object} userConfig - 用户配置
   */
  mergeUserConfig(userConfig) {
    Object.keys(userConfig).forEach(key => {
      if (this.flags[key] && typeof userConfig[key].enabled === 'boolean') {
        this.flags[key].enabled = userConfig[key].enabled;
      }
    });
  }

  /**
   * 检查功能是否启用
   * @param {string} featureName - 功能名称
   * @returns {boolean} - 是否启用
   */
  isEnabled(featureName) {
    if (!this.initialized) {
      this.init();
    }
    
    const flag = this.flags[featureName];
    if (!flag) {
      logger.warn('未知的功能标志', { featureName });
      return false;
    }
    
    // 检查依赖项
    if (flag.dependencies && flag.dependencies.length > 0) {
      const dependenciesMet = flag.dependencies.every(dep => this.isEnabled(dep));
      if (!dependenciesMet) {
        return false;
      }
    }
    
    return flag.enabled;
  }

  /**
   * 启用功能
   * @param {string} featureName - 功能名称
   * @param {boolean} persist - 是否持久化到localStorage
   */
  enable(featureName, persist = false) {
    if (!this.flags[featureName]) {
      logger.error('尝试启用未知功能', { featureName });
      return false;
    }
    
    this.flags[featureName].enabled = true;
    
    if (persist) {
      this.saveToStorage();
    }
    
    this.notifyListeners(featureName, true);
    logger.info('功能已启用', { featureName });
    return true;
  }

  /**
   * 禁用功能
   * @param {string} featureName - 功能名称
   * @param {boolean} persist - 是否持久化到localStorage
   */
  disable(featureName, persist = false) {
    if (!this.flags[featureName]) {
      logger.error('尝试禁用未知功能', { featureName });
      return false;
    }
    
    this.flags[featureName].enabled = false;
    
    if (persist) {
      this.saveToStorage();
    }
    
    this.notifyListeners(featureName, false);
    logger.info('功能已禁用', { featureName });
    return true;
  }

  /**
   * 获取所有启用的功能
   * @returns {Array} - 启用的功能列表
   */
  getEnabledFeatures() {
    return Object.keys(this.flags).filter(key => this.flags[key].enabled);
  }

  /**
   * 获取功能配置信息
   * @param {string} featureName - 功能名称
   * @returns {Object|null} - 功能配置信息
   */
  getFeatureInfo(featureName) {
    return this.flags[featureName] || null;
  }

  /**
   * 获取所有功能配置
   * @returns {Object} - 所有功能配置
   */
  getAllFeatures() {
    return { ...this.flags };
  }

  /**
   * 保存配置到localStorage
   */
  saveToStorage() {
    try {
      const config = {};
      Object.keys(this.flags).forEach(key => {
        config[key] = { enabled: this.flags[key].enabled };
      });
      localStorage.setItem('chatPreview_featureFlags', JSON.stringify(config));
    } catch (error) {
      logger.error('保存功能配置失败', { error: error.message });
    }
  }

  /**
   * 添加功能状态变化监听器
   * @param {string} featureName - 功能名称
   * @param {Function} callback - 回调函数
   */
  addListener(featureName, callback) {
    if (!this.listeners.has(featureName)) {
      this.listeners.set(featureName, []);
    }
    this.listeners.get(featureName).push(callback);
  }

  /**
   * 移除功能状态变化监听器
   * @param {string} featureName - 功能名称
   * @param {Function} callback - 回调函数
   */
  removeListener(featureName, callback) {
    const callbacks = this.listeners.get(featureName);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  /**
   * 通知监听器
   * @param {string} featureName - 功能名称
   * @param {boolean} enabled - 是否启用
   */
  notifyListeners(featureName, enabled) {
    const callbacks = this.listeners.get(featureName);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(enabled);
        } catch (error) {
          logger.error('功能监听器执行失败', { featureName, error: error.message });
        }
      });
    }
  }

  /**
   * 重置所有配置到默认状态
   */
  reset() {
    this.flags = { ...FEATURE_FLAGS };
    localStorage.removeItem('chatPreview_featureFlags');
    logger.info('功能配置已重置');
  }

  /**
   * 获取配置调试信息
   * @returns {Object} - 调试信息
   */
  getDebugInfo() {
    return {
      initialized: this.initialized,
      enabledFeatures: this.getEnabledFeatures(),
      allFeatures: this.getAllFeatures(),
      listenersCount: Array.from(this.listeners.entries()).map(([key, callbacks]) => ({
        feature: key,
        listenerCount: callbacks.length
      }))
    };
  }
}

// 创建全局单例实例
const featureConfig = new FeatureConfigManager();

// 导出实例和常量
export default featureConfig;
export { FEATURE_FLAGS };

// 便捷方法导出
export const isFeatureEnabled = (featureName) => featureConfig.isEnabled(featureName);
export const enableFeature = (featureName, persist = false) => featureConfig.enable(featureName, persist);
export const disableFeature = (featureName, persist = false) => featureConfig.disable(featureName, persist);
export const getFeatureInfo = (featureName) => featureConfig.getFeatureInfo(featureName);
export const getDebugInfo = () => featureConfig.getDebugInfo();

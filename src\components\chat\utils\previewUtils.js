import { message } from 'antd';

/**
 * 下载当前预览的HTML内容
 * 支持两种调用方式:
 * 1. downloadHtml(htmlContent, filename) - 直接下载HTML内容
 * 2. downloadHtml(previewAreaRef, selectedStyle, generatedHTML) - 从预览区域获取内容
 *
 * @param {Object|String} previewAreaRefOrHtml - 预览区域的引用或HTML内容字符串
 * @param {String} selectedStyleOrFilename - 当前选择的样式或文件名
 * @param {String} generatedHTML - 生成的HTML内容
 */
export function downloadHtml(previewAreaRefOrHtml, selectedStyleOrFilename, generatedHTML) {
  try {
    let htmlToProcess;
    const filename = (typeof previewAreaRefOrHtml === 'string' && selectedStyleOrFilename) ?
      selectedStyleOrFilename :
      `封面_${Date.now()}.html`;

    if (typeof previewAreaRefOrHtml === 'string') {
      htmlToProcess = generatedHTML || previewAreaRefOrHtml;
    } else {
      const previewAreaRef = previewAreaRefOrHtml;
      if (!previewAreaRef || !previewAreaRef.current) throw new Error('预览区域无效');
      
      let iframe;
      if (typeof previewAreaRef.current.getIframe === 'function') iframe = previewAreaRef.current.getIframe();
      else if (previewAreaRef.current.tagName === 'IFRAME') iframe = previewAreaRef.current;
      if (!iframe || !iframe.contentDocument) throw new Error('无法访问预览内容');
      
      htmlToProcess = `<!DOCTYPE html>${iframe.contentDocument.documentElement.outerHTML}`;
    }

    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlToProcess, 'text/html');

    // 提取完整的head内容，包括脚本和meta标签
    const headElements = Array.from(doc.head.children)
      .filter(el => {
        const tagName = el.tagName.toUpperCase();
        const isEditorRelated = el.classList.contains('editor-style') ||
                               el.id?.includes('editor') ||
                               el.className?.includes('editor');

        return ['STYLE', 'LINK', 'SCRIPT', 'META', 'TITLE', 'BASE'].includes(tagName) && !isEditorRelated;
      })
      .map(el => {
        // 清理style标签中的编辑器相关样式
        if (el.tagName === 'STYLE') {
          const cleanedCSS = el.innerHTML.replace(/\.text-editor-[^{]*\{[^}]*\}/g, '')
                                        .replace(/\.resize-handle[^{]*\{[^}]*\}/g, '')
                                        .replace(/\.drag-handle[^{]*\{[^}]*\}/g, '');
          return `<style>${cleanedCSS}</style>`;
        }
        return el.outerHTML;
      })
      .join('\n');

    // 清理body内容，移除编辑器相关元素
    const bodyContent = doc.body.innerHTML
      .replace(/<[^>]*class="[^"]*(?:text-editor-|resize-handle|drag-handle)[^"]*"[^>]*>.*?<\/[^>]*>/g, '')
      .replace(/\s*contenteditable="[^"]*"/g, '')
      .replace(/\s*data-editable="[^"]*"/g, '');

    const finalHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>${filename.replace('.html', '')}</title>
  ${headElements}
  <style>
    /* 允许内容滚动并保持居中的样式 */
    html {
      background-color: #f0f2f5; /* 给滚动区域一个背景色 */
    }
    body {
      min-height: 100vh; /* 至少占满视口高度 */
      margin: 0;
      padding: 20px; /* 添加一些内边距，避免内容紧贴边缘 */
      box-sizing: border-box;
      display: flex; /* 使用Flexbox布局 */
      justify-content: center; /* 水平居中 */
      align-items: center; /* 垂直居中 */
      overflow: auto; /* 允许滚动 */
    }
    #content-wrapper {
      transform-origin: center center; /* 保持变换原点在中心 */
    }
    /* 隐藏编辑器UI元素 */
    .text-editor-toolbar, .resize-handle, .drag-handle, [data-role="text-editor-toolbar"], [data-role="resize-handle"], [data-role="drag-handle"] { display: none !important; }
    [contenteditable="true"], [data-editable="true"] { outline: none !important; cursor: default !important; }
  </style>
  <style>
    /* 确保内容可以滚动的样式，使用!important覆盖其他样式 */
    html, body {
      overflow: auto !important;
    }
  </style>
</head>
<body>
  <div id="content-wrapper">
    ${bodyContent}
  </div>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      document.querySelectorAll('[contenteditable]').forEach(el => el.removeAttribute('contenteditable'));
    });
  </script>
</body>
</html>`;

    const blob = new Blob([finalHtml], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
  } catch (error) {
    console.error('HTML下载失败:', error);
    message.error('HTML下载失败: ' + error.message);
  }
}

/**
 * 找到对应的原始元素
 * @param {HTMLElement} originalRoot - 原始DOM树的根元素
 * @param {HTMLElement} clonedElement - 克隆DOM树中的元素
 * @returns {HTMLElement|null} 找到的对应元素或null
 */
function findCorrespondingElement(originalRoot, clonedElement) {
  // 获取元素的标签名和类名
  const tagName = clonedElement.tagName;
  const className = clonedElement.className;

  // 构建选择器
  const selector = className ? `${tagName}.${className.replace(/ /g, '.')}` : tagName;

  // 在原始DOM树中查找匹配的元素
  return originalRoot.querySelector(selector);
}

/**
 * 复制计算样式
 * @param {HTMLElement} sourceElement - 源元素
 * @param {HTMLElement} targetElement - 目标元素
 */
function copyComputedStyles(sourceElement, targetElement) {
  const styles = window.getComputedStyle(sourceElement);
  for (let i = 0; i < styles.length; i++) {
    const property = styles[i];
    targetElement.style[property] = styles.getPropertyValue(property);
  }
}

/**
 * 处理伪元素
 * @param {HTMLElement} originalElement - 原始元素
 * @param {HTMLElement} clonedElement - 克隆元素
 * @param {string} pseudo - 伪元素名称 ('before' 或 'after')
 */
function handlePseudoElements(originalElement, clonedElement, pseudo) {
  try {
    const style = window.getComputedStyle(originalElement, `::${pseudo}`);
    const content = style.content;

    if (content && content !== 'none' && content !== '""' && content !== "''") {
      const pseudoElement = document.createElement('div');
      pseudoElement.style.width = 'auto';
      pseudoElement.style.height = 'auto';
      pseudoElement.style.content = content;
      pseudoElement.style.display = 'inline-block';

      for (let i = 0; i < style.length; i++) {
        const property = style[i];
        pseudoElement.style[property] = style.getPropertyValue(property);
      }

      // 根据伪元素类型设置位置
      if (pseudo === 'after') {
        clonedElement.appendChild(pseudoElement);
      } else {
        clonedElement.insertBefore(pseudoElement, clonedElement.firstChild);
      }
    }
  } catch (error) {
    console.warn(`处理伪元素 ::${pseudo} 失败:`, error);
  }
}

/**
 * 清理HTML内容中的编辑相关属性和元素
 * 用于确保预览和下载的HTML内容不包含任何编辑状态标记
 * @param {string} htmlContent - HTML内容字符串
 * @returns {string} 清理后的HTML内容
 */
export const cleanupEditingAttributes = (htmlContent) => {
  try {
    // 创建一个临时的DOM文档
    const tempDoc = document.implementation.createHTMLDocument('');
    tempDoc.body.innerHTML = htmlContent;
    
    // 清理编辑相关的属性
    const attributesToRemove = ['contenteditable', 'data-editing', 'data-editable-fengmian', 'data-field', 'tabindex', 'title'];
    attributesToRemove.forEach(attr => {
      tempDoc.querySelectorAll(`[${attr}]`).forEach(el => {
        el.removeAttribute(attr);
      });
    });
    
    // 清理编辑相关的类
    const editingClasses = ['drag-mode', 'dragging', 'selected-for-drag', 'resize-mode', 'editing-active-outline', 'mode-switching'];
    editingClasses.forEach(className => {
      tempDoc.querySelectorAll(`.${className}`).forEach(el => {
        el.classList.remove(className);
      });
    });
    
    // 清理可能影响外观的样式属性
    tempDoc.querySelectorAll('[style]').forEach(el => {
      const style = el.style;
      const propertiesToClear = ['outline', 'outlineStyle', 'outlineWidth', 'outlineColor', 'outlineOffset'];
      propertiesToClear.forEach(prop => {
        if (style[prop]) style[prop] = '';
      });
      
      // 清理特定的border和cursor样式
      if (style.border && (style.border.includes('dashed') || style.border.includes('solid'))) style.border = '';
      if (style.cursor && (style.cursor === 'text' || style.cursor === 'move' || style.cursor === 'default')) style.cursor = '';
      
      // 移除pointer-events和user-select（模式切换期间可能添加的）
      style.pointerEvents = '';
      style.userSelect = '';
    });
    
    // 移除编辑器UI元素
    const uiSelectors = [
      '.text-editor-toolbar',
      '.resize-handle',
      '.drag-handle',
      '[data-role="text-editor-toolbar"]',
      '[data-role="resize-handle"]',
      '[data-role="drag-handle"]'
    ];
    uiSelectors.forEach(selector => {
      tempDoc.querySelectorAll(selector).forEach(el => el.remove());
    });
    
    // 移除所有mode-switching类
    if (tempDoc.body.classList.contains('mode-switching')) {
      tempDoc.body.classList.remove('mode-switching');
    }
    if (tempDoc.documentElement.classList.contains('mode-switching')) {
      tempDoc.documentElement.classList.remove('mode-switching');
    }
    
    return tempDoc.body.innerHTML;
  } catch (error) {
    console.warn('清理HTML内容中的编辑属性失败:', error);
    return htmlContent;
  }
};

/**
 * 生成并打开HTML预览窗口
 * @param {HTMLElement} iframe - 预览区域iframe元素
 * @param {Number} initialScale - 初始缩放比例，默认为1
 * @returns {Promise<string>} 预览窗口的URL
 */
export const openHtmlPreview = async (iframe, initialScale = 1) => {
  try {
    if (!iframe || !iframe.contentWindow || !iframe.contentDocument) {
      throw new Error('无法访问预览框架内容');
    }

    const iframeDoc = iframe.contentDocument;
    
    // 1. Extract styles and links from iframe's head
    const headElements = Array.from(iframeDoc.head.children)
      .filter(el => ['STYLE', 'LINK'].includes(el.tagName))
      .map(el => el.outerHTML)
      .join('\n');

    // 2. Extract content from iframe's body and clean up editing attributes
    const bodyContent = cleanupEditingAttributes(iframeDoc.body.innerHTML);

    // 3. Define the HTML for the zoom controller
    const zoomControllerToAdd = `
    <div class="zoom-controller">
      <div class="zoom-controller-percentage" id="zoom-percentage">${Math.round(initialScale * 100)}%</div>
      <button class="zoom-controller-button zoom-out" id="zoom-out" title="缩小"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg></button>
      <input type="range" min="0.05" max="3.0" step="0.05" value="${initialScale}" id="zoom-slider" class="zoom-controller-slider" orient="vertical"/>
      <button class="zoom-controller-button zoom-in" id="zoom-in" title="放大"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line><line x1="11" y1="8" x2="11" y2="14"></line><line x1="8" y1="11" x2="14" y2="11"></line></svg></button>
      <button class="zoom-controller-button zoom-reset" id="zoom-reset" title="重置缩放"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"></path></svg></button>
    </div>`;

    // 4. Assemble the final HTML for the new page
    const finalHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>封面预览</title>
  ${headElements}
  <style>
    /* Base styles for robust centering */
    html, body {
      height: 100%;
      width: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
      background-color: #f0f2f5;
    }
    body {
      position: relative; /* Positioning context for the wrapper */
    }
    #content-wrapper {
      position: absolute;
      top: 50%;
      left: 50%;
      transform-origin: center center;
      /* transform property will be set by JavaScript */
    }
    /* Hide editor-specific elements */
    .text-editor-toolbar, .resize-handle, .drag-handle, [data-role="text-editor-toolbar"], [data-role="resize-handle"], [data-role="drag-handle"] {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      pointer-events: none !important;
    }
    /* 确保所有元素不显示编辑相关的样式 */
    * {
      outline: none !important;
      user-select: text !important; /* 允许文本选择，但不显示编辑框 */
    }
    /* 确保contenteditable元素不显示编辑框 */
    [contenteditable], [contenteditable="true"], [data-editable="true"] {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
      cursor: default !important;
    }
    /* 恢复某些元素的默认光标 */
    a, button, input, select, textarea {
      cursor: auto !important;
    }
    /* Zoom controller styles */
    .zoom-controller{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:10px 5px;background-color:#fff;border-radius:4px;box-shadow:0 2px 5px rgba(0,0,0,.1);z-index:10;position:fixed;right:15px;top:50%;transform:translateY(-50%)}.zoom-controller-percentage{font-size:12px;margin-bottom:5px;color:#333;font-weight:700}.zoom-controller-slider{-webkit-appearance:none;width:8px;height:150px;margin:10px 0;background:#f1f1f1;outline:none;border-radius:4px;cursor:pointer;writing-mode:vertical-lr;direction:rtl}.zoom-controller-slider::-webkit-slider-thumb{-webkit-appearance:none;appearance:none;width:16px;height:16px;background:#4caf50;border-radius:50%;cursor:pointer}.zoom-controller-slider::-moz-range-thumb{width:16px;height:16px;background:#4caf50;border-radius:50%;cursor:pointer;border:none}.zoom-controller-button{width:24px;height:24px;border-radius:50%;border:none;background-color:#f5f5f5;display:flex;align-items:center;justify-content:center;cursor:pointer;margin:5px 0;color:#333;transition:background-color .2s}.zoom-controller-button:hover{background-color:#e0e0e0}.zoom-controller-button:disabled{opacity:.5;cursor:not-allowed}
  </style>
</head>
<body>
  <div id="content-wrapper">
    ${bodyContent}
  </div>
  ${zoomControllerToAdd}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 清理所有编辑相关的属性
      document.querySelectorAll('[contenteditable], [data-editing], [data-editable-fengmian], [data-field], [tabindex]').forEach(function(el) {
        el.removeAttribute('contenteditable');
        el.removeAttribute('data-editing');
        el.removeAttribute('data-editable-fengmian');
        el.removeAttribute('data-field');
        el.removeAttribute('tabindex');
        el.removeAttribute('title');
      });
      
      // 移除所有编辑相关的类
      const editingClasses = ['drag-mode', 'dragging', 'selected-for-drag', 'resize-mode', 'editing-active-outline'];
      editingClasses.forEach(function(className) {
        document.querySelectorAll('.' + className).forEach(function(el) {
          el.classList.remove(className);
        });
      });
      
      // 清理可能影响外观的样式属性
      document.querySelectorAll('[style]').forEach(function(el) {
        if (el.style.outline) el.style.outline = '';
        if (el.style.outlineStyle) el.style.outlineStyle = '';
        if (el.style.outlineWidth) el.style.outlineWidth = '';
        if (el.style.outlineColor) el.style.outlineColor = '';
        if (el.style.outlineOffset) el.style.outlineOffset = '';
        if (el.style.border && (el.style.border.includes('dashed') || el.style.border.includes('solid'))) el.style.border = '';
        if (el.style.cursor && (el.style.cursor === 'text' || el.style.cursor === 'move')) el.style.cursor = '';
      });
      
      // 移除所有编辑器UI元素
      document.querySelectorAll('.text-editor-toolbar, .resize-handle, .drag-handle, [data-role="text-editor-toolbar"], [data-role="resize-handle"], [data-role="drag-handle"]').forEach(function(el) {
        el.remove();
      });
      
      // 确保所有选中状态被清除
      if (window.getSelection) {
        window.getSelection().removeAllRanges();
      }
      
      const contentContainer = document.getElementById('content-wrapper');
      const zoomOutBtn = document.getElementById('zoom-out');
      const zoomInBtn = document.getElementById('zoom-in');
      const zoomResetBtn = document.getElementById('zoom-reset');
      const zoomSlider = document.getElementById('zoom-slider');
      const zoomPercentage = document.getElementById('zoom-percentage');
      
      let scale = ${initialScale};
      const minScale = 0.05;
      const maxScale = 3.0;
      const scaleStep = 0.05;

      function updateZoomValue() {
        zoomPercentage.textContent = Math.round(scale * 100) + '%';
        zoomSlider.value = scale;
      }
      
      function applyScale() {
        if (contentContainer) {
          contentContainer.style.transform = 'translate(-50%, -50%) scale(' + scale + ')';
          }
          updateZoomValue();
        }
        
        function calculateInitialScale() {
          if (!contentContainer) return;
          try {
            const contentWidth = contentContainer.scrollWidth || contentContainer.offsetWidth;
            const contentHeight = contentContainer.scrollHeight || contentContainer.offsetHeight;
          const windowWidth = window.innerWidth - 80; // Add some padding
            const windowHeight = window.innerHeight - 40;
            const widthScale = windowWidth / contentWidth;
            const heightScale = windowHeight / contentHeight;
          scale = Math.max(Math.min(widthScale, heightScale, 1), minScale);
            applyScale();
          } catch (e) {
            console.error('计算初始缩放比例失败:', e);
            scale = ${initialScale};
            applyScale();
          }
        }
        
      zoomOutBtn.addEventListener('click', () => { scale = Math.max(scale - scaleStep, minScale); applyScale(); });
      zoomInBtn.addEventListener('click', () => { scale = Math.min(scale + scaleStep, maxScale); applyScale(); });
      zoomResetBtn.addEventListener('click', calculateInitialScale);
      zoomSlider.addEventListener('input', (e) => { scale = parseFloat(e.target.value); applyScale(); });
      
        window.addEventListener('resize', calculateInitialScale);
        
      // Initial setup
          calculateInitialScale();
      });
    </script>
</body>
</html>`;

    // 5. Create a Blob and open it in a new window
    const blob = new Blob([finalHtml], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const newWindow = window.open(url, '_blank');

    if (!newWindow) {
      message.error('弹出窗口被阻止，无法打开预览。');
      URL.revokeObjectURL(url);
    } else {
      message.info('预览窗口已打开');
      // Revoke the object URL when the new window is closed to free up memory
      newWindow.addEventListener('pagehide', () => URL.revokeObjectURL(url), { once: true });
    }
    return url;
  } catch (error) {
    console.error('预览生成失败:', error);
    message.error('预览生成失败: ' + error.message);
    throw error;
  }
};

/**
 * 打开HTML预览窗口
 * @param {Object} previewAreaRef - 预览区域的引用对象
 * @param {Function} setIsPreviewLoading - 设置预览加载状态的函数
 */
export const handleOpenPreview = async (previewAreaRef, setIsPreviewLoading) => {
  try {
    setIsPreviewLoading && setIsPreviewLoading(true);
    message.loading('正在生成预览...', 0.5);

    if (!previewAreaRef.current) {
      throw new Error('找不到预览区域，请刷新页面后重试');
    }

    let iframe;
    if (typeof previewAreaRef.current.getIframe === 'function') {
      iframe = previewAreaRef.current.getIframe();
    } else if (previewAreaRef.current.tagName === 'IFRAME') {
      iframe = previewAreaRef.current;
    } else {
      throw new Error('无法访问预览内容 iframe');
    }

    let currentScale = 1;
    if (previewAreaRef.current.getScale && typeof previewAreaRef.current.getScale === 'function') {
      currentScale = previewAreaRef.current.getScale();
    }
    
    await openHtmlPreview(iframe, currentScale);

  } catch (error) {
    console.error('打开预览失败:', error);
    message.error('打开预览失败: ' + error.message);
  } finally {
    setIsPreviewLoading && setIsPreviewLoading(false);
  }
};

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 验证码模型
 * 对应数据库中的verify_codes表
 */
const VerifyCode = sequelize.define('VerifyCode', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '验证码ID，唯一标识'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '手机号'
  },
  code: {
    type: DataTypes.STRING(10),
    allowNull: false,
    comment: '验证码'
  },
  purpose: {
    type: DataTypes.ENUM('login', 'register', 'reset_password'),
    allowNull: false,
    comment: '验证码用途'
  },
  expire_time: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: '过期时间'
  },
  is_used: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否已使用'
  }
}, {
  tableName: 'verify_codes',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

module.exports = VerifyCode;

/**
 * 支付安全工具，提供签名验证、IP白名单检查等功能
 */
const crypto = require('crypto');
const { sequelize } = require('../models');
const { PaymentConfig, PaymentSecurityLog } = require('../models');
const { decryptPaymentKey } = require('./encryption');
const logger = require('./logger');
const paymentUtils = require('./paymentUtils');

/**
 * 获取支付配置
 * @param {string} paymentType - 支付类型 'wechat'|'alipay'
 * @returns {Object} 支付配置信息
 */
const getPaymentConfig = async (paymentType) => {
  try {
    const configs = await PaymentConfig.findAll({
      where: {
        payment_type: paymentType,
        is_active: true
      }
    });

    if (!configs || configs.length === 0) {
      throw new Error(`未找到${paymentType}支付配置`);
    }

    // 转换为键值对格式
    const configMap = {};
    for (const config of configs) {
      let value = config.config_value;
      // 如果是加密配置，需要解密
      if (config.is_encrypted) {
        value = decryptPaymentKey(value);
      }
      configMap[config.config_key] = value;
    }

    return configMap;
  } catch (error) {
    logger.error(`获取${paymentType}支付配置失败:`, error);
    throw error;
  }
};

/**
 * 验证微信支付回调签名
 * @param {Object} params - 回调参数
 * @param {string} params.timestamp - 时间戳
 * @param {string} params.nonce - 随机字符串
 * @param {string} params.signature - 签名
 * @param {string} params.serial - 证书序列号
 * @param {string|Object} params.body - 消息体
 * @returns {Promise<boolean>} 签名是否有效
 */
const verifyWechatPaySignature = async (params) => {
  try {
    // 验证必要参数
    const { timestamp, nonce, signature, serial, body } = params;
    
    if (!timestamp || !nonce || !signature || !serial || !body) {
      logger.warn('微信支付回调缺少必要参数');
      return false;
    }
    
    // 构建验签名串：时间戳\n随机串\n报文主体\n
    let message;
    if (typeof body === 'string') {
      message = `${timestamp}\n${nonce}\n${body}\n`;
    } else {
      message = `${timestamp}\n${nonce}\n${JSON.stringify(body)}\n`;
    }
    
    // 获取微信支付平台证书
    const certificates = await paymentUtils.getWechatPayCertificates();
    
    // 根据序列号获取对应的证书
    const matchedCert = certificates.find(cert => cert.serial_no === serial);
    
    if (!matchedCert) {
      logger.warn(`未找到序列号为 ${serial} 的微信支付平台证书`);
      return false;
    }
    
    // 使用平台证书验证签名
    const publicKey = matchedCert.certificate;
    
    // 验证签名
    const verify = crypto.createVerify('RSA-SHA256');
    verify.update(message);
    
    const result = verify.verify(publicKey, signature, 'base64');
    
    if (!result) {
      logger.warn('微信支付签名验证失败');
    } else {
      logger.info('微信支付签名验证成功');
    }
    
    return result;
  } catch (error) {
    logger.error('验证微信支付签名失败:', error);
    // 开发环境下可以忽略签名验证错误
    return process.env.NODE_ENV === 'development';
  }
};

/**
 * 验证支付宝回调签名
 * @param {Object} params - 回调参数
 * @returns {boolean} 签名是否有效
 */
const verifyAlipaySignature = async (params) => {
  try {
    const config = await getPaymentConfig('alipay');
    
    // 获取签名和待签名内容
    const signature = params.sign;
    delete params.sign;
    delete params.sign_type;
    
    // 按照字母顺序排序参数
    const sortedParams = Object.keys(params).sort().reduce((result, key) => {
      result[key] = params[key];
      return result;
    }, {});
    
    // 构建待签名字符串
    const signContent = Object.keys(sortedParams)
      .map(key => `${key}=${sortedParams[key]}`)
      .join('&');
    
    // 使用支付宝公钥验证签名
    const verifier = crypto.createVerify('RSA-SHA256');
    verifier.update(signContent, 'utf8');
    
    // 注意：开发环境可能没有正确的证书，因此做兼容处理
    if (process.env.NODE_ENV === 'development') {
      logger.warn('开发环境跳过支付宝签名验证');
      return true;
    }
    
    return verifier.verify(
      config.alipay_public_key.replace(/\\n/g, '\n'),
      signature,
      'base64'
    );
  } catch (error) {
    logger.error('验证支付宝签名失败:', error);
    // 开发环境下直接返回true，方便测试
    return process.env.NODE_ENV === 'development';
  }
};

/**
 * 检查IP是否在白名单中
 * @param {string} ip - 客户端IP
 * @param {string} paymentType - 支付类型 'wechat'|'alipay'
 * @returns {boolean} 是否在白名单中
 */
const checkIPWhitelist = async (ip, paymentType) => {
  try {
    // 从数据库获取IP白名单配置
    const config = await PaymentConfig.findOne({
      where: {
        payment_type: paymentType,
        config_key: 'ip_whitelist',
        is_active: true
      }
    });
    
    if (!config) {
      // 未配置白名单，开发环境默认放行，生产环境禁止
      return process.env.NODE_ENV !== 'production';
    }
    
    // 解析IP白名单
    const whitelist = config.config_value.split(',').map(ip => ip.trim());
    
    // 检查IP是否在白名单中
    return whitelist.includes(ip) || whitelist.includes('*');
  } catch (error) {
    logger.error('检查IP白名单失败:', error);
    // 开发环境下直接返回true，方便测试
    return process.env.NODE_ENV !== 'production';
  }
};

/**
 * 记录支付安全日志
 * @param {number} userId - 用户ID
 * @param {string} orderNo - 订单号
 * @param {string} ipAddress - IP地址
 * @param {string} action - 操作类型
 * @param {string} status - 状态 'success'|'failed'|'suspicious'
 * @param {Object} requestData - 请求数据
 * @param {Object} responseData - 响应数据
 * @param {string} riskLevel - 风险等级 'low'|'medium'|'high'
 * @param {string} description - 描述
 */
const logPaymentSecurity = async (
  userId,
  orderNo,
  ipAddress,
  action,
  status,
  requestData,
  responseData,
  riskLevel = 'low',
  description = ''
) => {
  try {
    // 确保状态值有效
    const validStatus = ['success', 'failed', 'suspicious'];
    if (!validStatus.includes(status)) {
      status = 'suspicious'; // 如果状态值无效，默认使用'suspicious'
      logger.warn(`支付安全日志状态值无效: ${status}，使用默认值'suspicious'`);
    }

    // 写入支付安全日志表
    await PaymentSecurityLog.create({
      user_id: userId,
      order_no: orderNo,
      ip_address: ipAddress,
      action,
      status,
      request_data: JSON.stringify(requestData || {}),
      response_data: JSON.stringify(responseData || {}),
      risk_level: riskLevel,
      description
    });

    // 同时记录系统日志
    logger.info(`支付安全日志: ${description}`, {
      user_id: userId,
      order_no: orderNo,
      ip_address: ipAddress,
      action,
      status,
      risk_level: riskLevel
    });
  } catch (error) {
    logger.error('记录支付安全日志失败:', error);
    // 继续执行，不中断主流程
  }
};

/**
 * 使用AES-256-GCM解密微信支付回调中的敏感数据
 * @param {Object} params 解密参数
 * @param {string} params.ciphertext 密文
 * @param {string} params.associated_data 附加数据
 * @param {string} params.nonce 随机串
 * @returns {Promise<Object>} 解密后的数据对象
 */
const decryptWechatPayResource = async (params) => {
  try {
    // 获取APIv3密钥
    const config = await PaymentConfig.findOne({
      where: {
        payment_type: 'wechat',
        config_key: 'apiv3_key',
        is_active: true
      }
    });

    if (!config) {
      throw new Error('未找到APIv3密钥配置');
    }

    const apiV3Key = config.is_encrypted 
      ? decryptPaymentKey(config.config_value) 
      : config.config_value;

    // 解密过程
    const { ciphertext, associated_data, nonce } = params;
    
    // Base64解码密文
    const ciphertextBuffer = Buffer.from(ciphertext, 'base64');
    
    // 分离密文和认证标签
    const AUTH_TAG_LENGTH = 16;  // GCM认证标签长度固定为16字节
    const authTag = ciphertextBuffer.slice(ciphertextBuffer.length - AUTH_TAG_LENGTH);
    const encryptedData = ciphertextBuffer.slice(0, ciphertextBuffer.length - AUTH_TAG_LENGTH);
    
    // 创建解密器
    const decipher = crypto.createDecipheriv(
      'aes-256-gcm', 
      Buffer.from(apiV3Key, 'utf8'), 
      Buffer.from(nonce, 'utf8')
    );
    
    // 设置认证标签和附加数据
    decipher.setAuthTag(authTag);
    if (associated_data) {
      decipher.setAAD(Buffer.from(associated_data, 'utf8'));
    }
    
    // 解密
    let decrypted = decipher.update(encryptedData);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    // 解析JSON数据
    const result = JSON.parse(decrypted.toString('utf8'));
    
    logger.info('微信支付回调数据解密成功');
    return result;
  } catch (error) {
    logger.error('解密微信支付回调数据失败:', error);
    throw error;
  }
};

/**
 * 检测支付回调中的重放攻击
 * @param {string} nonceStr 随机字符串
 * @param {string} timestamp 时间戳
 * @returns {Promise<boolean>} 是否是重放攻击
 */
const checkReplayAttack = async (nonceStr, timestamp) => {
  try {
    // 检查时间戳是否在合理范围内（默认5分钟）
    const now = Math.floor(Date.now() / 1000);
    const timeDiff = now - parseInt(timestamp, 10);
    const MAX_TIME_DIFF = 5 * 60; // 5分钟
    
    if (timeDiff > MAX_TIME_DIFF || timeDiff < -MAX_TIME_DIFF) {
      logger.warn(`支付回调时间戳异常，时间差为${timeDiff}秒`);
      return true; // 时间戳异常，可能是重放攻击
    }
    
    // TODO: 可以在数据库中存储已处理的随机字符串，防止重放攻击
    // 由于需要创建新表，这里简化处理，只检查时间戳
    
    return false;
  } catch (error) {
    logger.error('检测重放攻击失败:', error);
    return true; // 出错时认为是重放攻击，确保安全
  }
};

/**
 * 检测支付宝回调中的重放攻击
 * @param {string} notifyId 通知ID
 * @param {string} notifyTime 通知时间
 * @returns {Promise<boolean>} 是否是重放攻击
 */
const checkAlipayReplayAttack = async (notifyId, notifyTime) => {
  try {
    // 检查通知时间是否在合理范围内（默认30分钟）
    if (notifyTime) {
      const notifyTimeDate = new Date(notifyTime);
      const now = new Date();
      const timeDiff = (now - notifyTimeDate) / 1000; // 转换为秒
      const MAX_TIME_DIFF = 30 * 60; // 30分钟
      
      if (timeDiff > MAX_TIME_DIFF || timeDiff < -MAX_TIME_DIFF) {
        logger.warn(`支付宝回调时间异常，时间差为${Math.floor(timeDiff)}秒`);
        return true; // 时间戳异常，可能是重放攻击
      }
    }
    
    // 检查通知ID是否已处理过
    // TODO: 可以在数据库中存储已处理的通知ID，防止重放攻击
    // 由于需要创建新表，这里简化处理，只检查时间
    
    return false;
  } catch (error) {
    logger.error('检测支付宝重放攻击失败:', error);
    return true; // 出错时认为是重放攻击，确保安全
  }
};

module.exports = {
  verifyWechatPaySignature,
  verifyAlipaySignature,
  checkIPWhitelist,
  logPaymentSecurity,
  getPaymentConfig,
  decryptWechatPayResource,
  checkReplayAttack,
  checkAlipayReplayAttack
}; 
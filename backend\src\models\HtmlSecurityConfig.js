const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * HTML安全配置模型
 * 对应数据库中的html_security_configs表
 */
const HtmlSecurityConfig = sequelize.define('HtmlSecurityConfig', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '配置ID，唯一标识'
  },
  config_key: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    comment: '配置键名'
  },
  config_value: {
    type: DataTypes.JSON,
    allowNull: false,
    comment: '配置值'
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '配置描述'
  }
}, {
  tableName: 'html_security_configs',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

module.exports = HtmlSecurityConfig;

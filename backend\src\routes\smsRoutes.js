const express = require('express');
const router = express.Router();
const smsController = require('../controllers/smsController');
const { validate, schemas } = require('../utils/validator');

// 发送验证码
router.post('/send', validate(schemas.smsVerification), smsController.sendVerificationCode);

// 验证验证码
router.post('/verify', validate(schemas.smsVerification), smsController.verifyCode);

module.exports = router;

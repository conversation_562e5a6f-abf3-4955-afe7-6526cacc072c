const { User, PointRecord, CoverRecord, StylePrompt, BasePrompt, FeatureControl } = require('../models');
const { successResponse, errorResponse, paginationResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');
const { sequelize } = require('../config/database');
const { Op } = require('sequelize');

/**
 * 获取用户个人资料
 * @route GET /api/user/profile
 */
const getUserProfile = async (req, res) => {
  try {
    const user = req.user;
    
    // 检查上次更新时间是否是今天
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const lastUpdate = user.last_daily_points_update ? new Date(user.last_daily_points_update) : null;
    const isToday = lastUpdate && lastUpdate >= today;

    // 获取每日积分值
    let dailyPoints = 0;
    if (isToday) {
      // 如果今天已经更新过，使用数据库中的值
      dailyPoints = user.daily_points;
    } else {
      // 如果今天没有更新过或者从未更新过，从feature_controls表获取配置的值
      try {
        // 根据用户角色获取对应的每日积分配置
        const featureName = user.role === 'vip' ? '每日积分（高级）' : '每日积分（普通）';
        const featureControl = await FeatureControl.findOne({
          where: {
            feature_name: featureName,
            is_active: true
          }
        });
        
        // 如果找到配置，使用配置的积分值；否则使用0
        dailyPoints = featureControl ? featureControl.points_cost : 0;
      } catch (error) {
        logger.error('获取每日积分配置失败:', error);
        dailyPoints = 0;
      }
    }

    return successResponse(res, '获取个人资料成功', {
      id: user.id,
      phone: user.phone,
      nickname: user.nickname,
      avatar: user.avatar,
      role: user.role,
      points: user.points,
      daily_points: dailyPoints,
      vip_expire_date: user.vip_expire_date,
      is_vip: user.isVip(),
      has_set_password: user.has_set_password,
      created_at: user.createdAt,
      email: user.email
    });
  } catch (error) {
    logger.error('获取用户个人资料失败:', error);
    return errorResponse(res, '获取个人资料失败', 500);
  }
};

/**
 * 更新用户个人资料
 * @route PUT /api/user/profile
 */
const updateUserProfile = async (req, res) => {
  const { nickname, avatar, email } = req.body;

  try {
    const user = req.user;

    // 更新用户信息
    if (nickname !== undefined) user.nickname = nickname;
    if (avatar !== undefined) user.avatar = avatar;
    if (email !== undefined) user.email = email;

    await user.save();

    logger.info(`用户${user.id}更新个人资料成功`);

    return successResponse(res, '更新个人资料成功', {
      id: user.id,
      nickname: user.nickname,
      avatar: user.avatar,
      email: user.email
    });
  } catch (error) {
    logger.error('更新用户个人资料失败:', error);
    return errorResponse(res, '更新个人资料失败', 500);
  }
};

/**
 * 获取用户积分
 * @route GET /api/user/points
 */
const getUserPoints = async (req, res) => {
  try {
    const user = req.user;

    return successResponse(res, '获取积分成功', {
      points: user.points,
      is_vip: user.isVip(),
      vip_expire_date: user.vip_expire_date
    });
  } catch (error) {
    logger.error('获取用户积分失败:', error);
    return errorResponse(res, '获取积分失败', 500);
  }
};

/**
 * 获取用户积分记录
 * @route GET /api/user/point-records
 */
const getPointRecords = async (req, res) => {
  const { page = 1, limit = 10 } = req.query;
  const userId = req.user.id;

  try {
    const offset = (page - 1) * limit;

    // 查询积分记录
    const { count, rows } = await PointRecord.findAndCountAll({
      where: { user_id: userId },
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // 准备分页信息
    const pagination = {
      total: count,
      page: parseInt(page),
      limit: parseInt(limit),
      total_pages: Math.ceil(count / limit)
    };

    return paginationResponse(res, rows, pagination, '获取积分记录成功');
  } catch (error) {
    logger.error('获取用户积分记录失败:', error);
    return errorResponse(res, '获取积分记录失败', 500);
  }
};

/**
 * 消费积分
 * @route POST /api/user/consume-points
 */
const consumePoints = async (req, res) => {
  const { amount, reason, operation_id } = req.body;
  const userId = req.user.id;

  try {
    // 开启事务
    const transaction = await sequelize.transaction();

    try {
      // 使用行锁查询用户，防止并发问题
      const user = await User.findByPk(userId, {
        lock: true,
        transaction
      });

      if (!user) {
        await transaction.rollback();
        return errorResponse(res, '用户不存在', 404);
      }

      // 如果提供了operation_id，检查是否已经处理过
      if (operation_id) {
        const existingRecord = await PointRecord.findOne({
          where: {
            operation_id,
            user_id: userId
          },
          transaction
        });

        // 如果已经存在记录，直接返回成功
        if (existingRecord) {
          await transaction.commit();
          logger.info(`操作ID ${operation_id} 已存在记录，跳过重复处理`);
          return successResponse(res, '积分消费成功(已处理)', {
            points: user.points,
            daily_points: user.daily_points,
            consumed: amount,
            duplicate: true
          });
        }
      }

      // 检查总积分是否足够（常规积分 + 每日积分）
      const totalAvailablePoints = user.points + (user.daily_points || 0);
      if (totalAvailablePoints < amount) {
        await transaction.rollback();
        return errorResponse(res, '积分不足', 400);
      }

      // 计算需要从每日积分和常规积分中扣除的数量
      let dailyPointsToDeduct = 0;
      let regularPointsToDeduct = 0;

      // 优先使用每日积分
      if (user.daily_points > 0) {
        dailyPointsToDeduct = Math.min(user.daily_points, amount);
        regularPointsToDeduct = amount - dailyPointsToDeduct;
      } else {
        regularPointsToDeduct = amount;
      }

      // 更新用户积分
      user.daily_points -= dailyPointsToDeduct;
      user.points -= regularPointsToDeduct;
      await user.save({ transaction });

      // 创建积分记录
      await PointRecord.create({
        user_id: userId,
        points_change: -amount,
        points_after: user.points,
        operation_type: 'generate',
        description: reason || '生成封面消费',
        operation_time: new Date(),
        operation_id: operation_id || null
      }, { transaction });

      // 提交事务
      await transaction.commit();

      logger.info(`用户${userId}消费积分成功，总计${amount}积分（每日积分：${dailyPointsToDeduct}，常规积分：${regularPointsToDeduct}），原因：${reason}${operation_id ? '，操作ID：' + operation_id : ''}`);

      return successResponse(res, '积分消费成功', {
        points: user.points,
        daily_points: user.daily_points,
        consumed: amount,
        daily_points_consumed: dailyPointsToDeduct,
        regular_points_consumed: regularPointsToDeduct
      });
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    logger.error(`消费积分失败: ${error.message}`, error);
    return errorResponse(res, '消费积分失败', 500);
  }
};

/**
 * 获取用户封面生成记录
 * @route GET /api/user/covers
 */
const getUserCovers = async (req, res) => {
  const { page = 1, limit = 10, cover_type } = req.query;
  const userId = req.user.id;

  try {
    const offset = (page - 1) * limit;

    // 基本的where条件，只获取当前用户的记录
    const where = {
      user_id: userId,
      status: '显示' // <-- Add this line to filter by status
    };

    // 构建查询条件
    if (cover_type) {
      where.cover_type = cover_type;
    }

    // 使用原生SQL查询确保一致的日期格式和最新记录
    try {
      // 添加更多错误捕获和处理
      try {
        // 使用QueryTypes.SELECT时，sequelize.query直接返回查询结果数组
        const records = await sequelize.query(`
          SELECT cr.*,
                 u.phone,
                 u.nickname
          FROM cover_records cr
          LEFT JOIN users u ON cr.user_id = u.id
          WHERE cr.user_id = :userId
          ${cover_type ? "AND cr.cover_type = :cover_type" : ""}
          AND cr.status = '显示'
          ORDER BY cr.id DESC, cr.created_at DESC
          LIMIT :limit OFFSET :offset
        `, {
          replacements: {
            userId,
            cover_type: cover_type || null,
            limit: parseInt(limit),
            offset: parseInt(offset)
          },
          type: sequelize.QueryTypes.SELECT
        });

        // 从数据库获取风格和封面类型信息
        const stylePrompts = await StylePrompt.findAll({
          // 明确指定所有需要的属性
          attributes: ['id', 'id_code', 'style_name', 'display_name']
        });

        // 从数据库获取封面类型信息
        const basePrompts = await BasePrompt.findAll({
          // 明确指定所有需要的属性，注意base_prompts表没有cover_type_name字段
          attributes: ['id', 'cover_type', 'id_code']
        });

        // 创建风格和封面类型的映射
        const styleMap = {};
        const typeMap = {};
        const typeCodeMap = {};

        // 填充风格映射（使用id和id_code两种键）
        stylePrompts.forEach(style => {
          // 始终使用style_name作为显示名称，确保反映最新的修改
          // 这与封面类型的处理逻辑保持一致，直接使用cover_type字段
          const displayName = style.style_name;

          // 数字ID作为键
          if (style.id) {
            styleMap[style.id] = displayName;
            styleMap[String(style.id)] = displayName; // 同时添加字符串形式的ID
          }

          // id_code作为键
          if (style.id_code) {
            styleMap[style.id_code] = displayName;
          }
        });

        // 填充封面类型映射 - 修正逻辑以适应base_prompts表的实际结构
        basePrompts.forEach(type => {
          const typeName = type.cover_type; // 直接使用cover_type作为显示名称

          // 数字ID作为键
          if (type.id) {
            typeMap[type.id] = typeName;
            typeMap[String(type.id)] = typeName; // 同时添加字符串形式的ID
          }

          // id_code作为键
          if (type.id_code) {
            typeMap[type.id_code] = typeName;
            typeCodeMap[type.id_code] = typeName; // 专门存储id_code到显示名称的映射
          }

          // cover_type作为键 (用于直接匹配)
          typeMap[typeName] = typeName;
        });

        // 处理记录，添加最新的风格和类型名称
        const processedRecords = records.map(record => {
          // 获取最新的风格显示名称
          let styleDisplayName = null;

          // 优先级：1.已存储的cover_style_name 2.通过styleMap查找 3.原始cover_style值
          if (record.cover_style_name) {
            styleDisplayName = record.cover_style_name;
          } else if (record.cover_style && styleMap[record.cover_style]) {
            styleDisplayName = styleMap[record.cover_style];
          } else if (!isNaN(parseInt(record.cover_style, 10)) && styleMap[parseInt(record.cover_style, 10)]) {
            // 尝试将cover_style解析为数字再查找
            styleDisplayName = styleMap[parseInt(record.cover_style, 10)];
          } else {
            // 如果找不到映射，使用原始值
            styleDisplayName = record.cover_style || '未知风格';
          }

          // 获取最新的封面类型显示名称
          let coverTypeName = null;

          // 优先级：1.通过typeMap查找 2.通过常见类型名称判断 3.原始cover_type值
          if (record.cover_type && typeMap[record.cover_type]) {
            // 直接匹配
            coverTypeName = typeMap[record.cover_type];
          } else if (record.cover_type && typeCodeMap[record.cover_type]) {
            // 通过id_code匹配
            coverTypeName = typeCodeMap[record.cover_type];
          } else if (!isNaN(parseInt(record.cover_type, 10)) && typeMap[parseInt(record.cover_type, 10)]) {
            // 尝试将cover_type解析为数字再查找
            coverTypeName = typeMap[parseInt(record.cover_type, 10)];
          } else {
            // 如果找不到映射，使用常见类型名称或原始值
            if (record.cover_type === 'wechat') {
              coverTypeName = '微信公众号封面';
            } else if (record.cover_type === 'xiaohongshu') {
              coverTypeName = '小红书封面';
            } else if (record.cover_type === 'test' || record.cover_type === 'test1') {
              coverTypeName = '测试封面';
            } else {
              coverTypeName = record.cover_type || '未知类型';
            }
          }

          return {
            ...record,
            cover_type_display: coverTypeName,
            style_display_name: styleDisplayName,
            // 保留原始值，以便前端需要时使用
            original_cover_style: record.cover_style,
            original_cover_type: record.cover_type
          };
        });

        // 获取总数
        const countResult = await sequelize.query(`
          SELECT COUNT(*) as total
          FROM cover_records
          WHERE user_id = :userId
          ${cover_type ? "AND cover_type = :cover_type" : ""}
          AND status = '显示'
        `, {
          replacements: {
            userId,
            cover_type: cover_type || null
          },
          type: sequelize.QueryTypes.SELECT
        });

        const count = countResult && countResult[0] && countResult[0].total ? parseInt(countResult[0].total) : 0;

        // 准备分页信息
        const pagination = {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          total_pages: Math.ceil(count / limit)
        };

        return paginationResponse(res, processedRecords, pagination, '获取封面记录成功');
      } catch (sqlError) {
        logger.error('原生SQL查询封面记录失败:', sqlError);

        // 备用方法：使用Sequelize查询
        const { count, rows } = await CoverRecord.findAndCountAll({
          where, // Use the updated where clause
          order: [['created_at', 'DESC']], // Keep order for ROW_NUMBER() consistency
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'phone', 'nickname', 'role']
            }
          ],
          limit: parseInt(limit),
          offset: parseInt(offset)
        });

        // 从数据库获取风格显示名称
        const stylePrompts = await StylePrompt.findAll({
          attributes: ['id', 'id_code', 'style_name', 'display_name']
        });

        // 创建风格ID到显示名称的映射
        const styleNameMapping = {};
        stylePrompts.forEach(style => {
          // 始终使用style_name作为显示名称，确保反映最新的修改
          const displayName = style.style_name;
          styleNameMapping[style.id_code] = displayName;
          styleNameMapping[style.id] = displayName; // 同时添加数字ID映射
        });

        // 处理日期格式、用户信息和风格显示名称
        const formattedRecords = rows.map((record, index) => {
          const recordJson = record.toJSON();
          if (recordJson.user) {
            recordJson.phone = recordJson.user.phone;
            recordJson.nickname = recordJson.user.nickname;
            recordJson.role = recordJson.user.role;
          }
          // 确保日期格式是标准字符串
          if (recordJson.createdAt) {
            const createdDate = new Date(recordJson.createdAt);
            recordJson.created_at = createdDate.toISOString().replace('T', ' ').substring(0, 19);
            // 添加时间戳便于前端处理
            recordJson.created_at_timestamp = createdDate.getTime();
          }

          // **重要**: 修正 row_num 的计算，使其基于分页结果
          // Sequelize的ROW_NUMBER()是基于整个结果集的，我们需要基于当前页调整
          recordJson.row_num = offset + index + 1;

          // 添加风格显示名称，简单优先使用数据库中的字段
          recordJson.style_display_name = recordJson.cover_style_name ||
                                         styleNameMapping[recordJson.cover_style] ||
                                         recordJson.cover_style;

          // 确保封面类型名称正确存在
          recordJson.cover_type_name = recordJson.cover_type_name ||
                                      (recordJson.cover_type === 'wechat' ? '微信公众号' :
                                       recordJson.cover_type === 'xiaohongshu' ? '小红书' :
                                       recordJson.cover_type);

          return recordJson;
        });

        // 准备分页信息
        const pagination = {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          total_pages: Math.ceil(count / limit)
        };

        return paginationResponse(res, formattedRecords, pagination, '获取封面记录成功');
      }
    } catch (error) {
      logger.error('获取用户封面记录失败:', error);
      return errorResponse(res, '获取封面记录失败', 500);
    }
  } catch (error) {
    logger.error('获取用户封面记录失败:', error);
    return errorResponse(res, '获取封面记录失败', 500);
  }
};

/**
 * 上传用户头像
 * @route POST /api/user/avatar
 */
const uploadAvatar = async (req, res) => {
  try {
    if (!req.file) {
      return errorResponse(res, '请选择要上传的头像', 400);
    }

    const user = req.user;

    // 构建相对路径，便于前端访问
    const avatarUrl = `/uploads/avatars/${req.file.filename}`;

    // 更新用户头像
    user.avatar = avatarUrl;
    await user.save();

    logger.info(`用户${user.id}上传头像成功`);

    return successResponse(res, '头像上传成功', {
      avatar: avatarUrl
    });
  } catch (error) {
    logger.error('上传头像失败:', error);
    return errorResponse(res, '上传头像失败', 500);
  }
};

/**
 * 设置或修改密码
 * @route POST /api/user/set-password
 */
const setPassword = async (req, res) => {
  const { password, oldPassword } = req.body;
  const user = req.user;

  try {
    // 如果用户已设置密码，需要验证旧密码
    if (user.has_set_password && oldPassword) {
      const isPasswordValid = await user.validatePassword(oldPassword);

      if (!isPasswordValid) {
        return errorResponse(res, '旧密码不正确', 400);
      }
    }

    // 更新密码
    user.password = password; // 密码将通过钩子自动加密
    user.has_set_password = true; // 标记用户已设置密码
    await user.save();

    // 记录日志
    logger.info(`用户${user.id}设置/修改密码成功`);

    return successResponse(res, '密码设置成功');
  } catch (error) {
    logger.error('设置密码失败:', error);
    return errorResponse(res, '设置密码失败，请稍后再试', 500);
  }
};

/**
 * 获取用户每日积分
 * @route GET /api/user/daily-points
 */
const getUserDailyPoints = async (req, res) => {
  try {
    const user = req.user;
    
    // 检查上次更新时间是否是今天
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const lastUpdate = user.last_daily_points_update ? new Date(user.last_daily_points_update) : null;
    const isToday = lastUpdate && lastUpdate >= today;
    
    if (isToday) {
      // 如果今天已经更新过，返回数据库中的值
      return successResponse(res, '获取每日积分成功', {
        daily_points: user.daily_points,
        last_update: user.last_daily_points_update
      });
    } else {
      // 如果今天没有更新过或者从未更新过，从feature_controls表获取配置的值
      try {
        // 根据用户角色获取对应的每日积分配置
        const featureName = user.role === 'vip' ? '每日积分（高级）' : '每日积分（普通）';
        const featureControl = await FeatureControl.findOne({
          where: {
            feature_name: featureName,
            is_active: true
          }
        });
        
        // 如果找到配置，返回配置的积分值；否则返回0
        const dailyPoints = featureControl ? featureControl.points_cost : 0;
        
        // 更新用户的每日积分和最后更新时间
        user.daily_points = dailyPoints;
        user.last_daily_points_update = new Date();
        await user.save();
        
        // 只在首次获取或积分值发生变化时记录日志
        if (!lastUpdate || user.daily_points !== dailyPoints) {
          logger.info(`用户 ${user.id} 的每日积分已更新为 ${dailyPoints}`);
        }
        
        return successResponse(res, '获取每日积分成功', {
          daily_points: dailyPoints,
          last_update: user.last_daily_points_update
        });
      } catch (error) {
        logger.error('获取每日积分配置失败:', error);
        return successResponse(res, '获取每日积分成功', {
          daily_points: user.daily_points || 0,
          last_update: user.last_daily_points_update
        });
      }
    }
  } catch (error) {
    logger.error('获取用户每日积分失败:', error);
    return errorResponse(res, '获取每日积分失败', 500);
  }
};

module.exports = {
  getUserProfile,
  updateUserProfile,
  getUserPoints,
  getPointRecords,
  consumePoints,
  getUserCovers,
  uploadAvatar,
  setPassword,
  getUserDailyPoints
};

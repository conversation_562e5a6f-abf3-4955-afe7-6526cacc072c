/**
 * 成功响应工具函数
 * @param {Object} res - Express响应对象
 * @param {string} message - 响应消息
 * @param {Object} data - 响应数据
 * @param {number} statusCode - HTTP状态码
 */
const successResponse = (res, message = '操作成功', data = {}, statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  });
};

/**
 * 错误响应工具函数
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 * @param {number} statusCode - HTTP状态码
 * @param {Object} errors - 详细错误信息
 */
const errorResponse = (res, message = '操作失败', statusCode = 400, errors = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  if (errors) {
    response.errors = errors;
  }

  return res.status(statusCode).json(response);
};

/**
 * 分页响应工具函数
 * @param {Object} res - Express响应对象
 * @param {Array} data - 分页数据
 * @param {Object} pagination - 分页信息
 * @param {string} message - 响应消息
 */
const paginationResponse = (res, data, pagination, message = '获取数据成功') => {
  return res.status(200).json({
    success: true,
    message,
    data,
    pagination,
    timestamp: new Date().toISOString()
  });
};

module.exports = {
  successResponse,
  errorResponse,
  paginationResponse
};

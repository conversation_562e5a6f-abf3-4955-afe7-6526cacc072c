import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Login from '../auth/Login';
import Register from '../auth/Register';
import ForgotPassword from '../auth/ForgotPassword';

const AuthContent = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('login');
  
  // 检查URL路径，确定显示哪个组件
  useEffect(() => {
    const pathname = location.pathname;
    
    if (pathname === '/register') {
      setActiveTab('register');
    } else if (pathname === '/forgot-password') {
      setActiveTab('forgot-password');
    } else {
      setActiveTab('login');
    }
  }, [location.pathname]);

  // 根据activeTab渲染不同的组件
  const renderAuthComponent = () => {
    switch (activeTab) {
      case 'register':
        return <Register onSwitchToLogin={() => navigate('/auth', { replace: true })} />;
      case 'forgot-password':
        return <ForgotPassword onBackToLogin={() => navigate('/auth', { replace: true })} />;
      case 'login':
      default:
        return <Login 
          onSwitchToRegister={() => navigate('/register', { replace: true })} 
          onForgotPassword={() => navigate('/forgot-password', { replace: true })} 
        />;
    }
  };

  return (
    <div className="w-full h-full flex flex-col items-center justify-center p-4 md:p-6">
      <div className="max-w-md w-full">
        {renderAuthComponent()}
      </div>
    </div>
  );
};

export default AuthContent; 
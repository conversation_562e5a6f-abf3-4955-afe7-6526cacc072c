/**
 * 层级控制工具 - 用于调整元素的层级顺序
 */

/**
 * 获取元素的z-index
 * @param {HTMLElement} element - 目标元素
 * @returns {number} z-index值
 */
const getZIndex = (element) => {
  if (!element) return 0;
  try {
    // 首先检查内联样式，这通常是最新设置的值
    if (element.style && element.style.zIndex && element.style.zIndex !== 'auto') {
      const inlineZIndex = parseInt(element.style.zIndex, 10);
      if (!isNaN(inlineZIndex)) {
        return inlineZIndex;
      }
    }
    
    // 然后检查计算样式
    const style = window.getComputedStyle(element);
    const zIndex = style.zIndex;
    return zIndex === 'auto' ? 0 : parseInt(zIndex, 10);
  } catch (error) {
    console.error('获取z-index时出错:', error);
    return 0;
  }
};

/**
 * 设置元素的z-index并确保它可以正确显示
 * @param {HTMLElement} element - 目标元素
 * @param {number} zIndex - 要设置的z-index值
 */
const setZIndex = (element, zIndex) => {
  if (!element) return;
  try {
    // 确保元素可以设置z-index
    if (element.style.position === 'static' || !element.style.position) {
      element.style.position = 'relative';
    }
    
    // 设置z-index
    element.style.zIndex = String(zIndex);
    
    // 标记元素已被层级控制功能修改
    element.setAttribute('data-layer-modified', 'true');
    
    // 触发重绘
    void element.offsetHeight;
    
    // 确保元素在DOM中可见
    if (element.style.display === 'none') {
      element.style.display = '';
    }
    
    // 确保元素不透明
    if (element.style.opacity === '0') {
      element.style.opacity = '1';
    }
    
    // 确保元素不被隐藏
    if (element.style.visibility === 'hidden') {
      element.style.visibility = 'visible';
    }
  } catch (error) {
    console.error('设置z-index时出错:', error);
  }
};

/**
 * 获取iframe文档中当前选中或激活的元素
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @returns {HTMLElement|null} 当前选中或激活的元素
 */
const getActiveElement = (iframe) => {
  if (!iframe) return null;
  
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) return null;
    
    // 尝试多种方式获取活动元素
    const activeElement = 
      iframeDoc.querySelector('[contenteditable="true"]:focus') ||
      iframeDoc.querySelector('[contenteditable="true"].drag-mode') ||
      iframeDoc.querySelector('[data-editing="true"]') ||
      (window.currentActiveTextEditor?.activeElement) ||
      null;
    
    return activeElement;
  } catch (error) {
    console.error('获取活动元素时出错:', error);
    return null;
  }
};

/**
 * 获取所有可编辑元素并按z-index排序
 * @param {Document} doc - 文档对象
 * @returns {Array<{element: HTMLElement, zIndex: number}>} 排序后的元素数组
 */
const getSortedElements = (doc) => {
  if (!doc || !doc.body) return [];
  
  try {
    // 获取所有可编辑元素，使用更多选择器确保包含所有类型的可编辑元素
    const elements = Array.from(doc.querySelectorAll([
      '[data-editable-fengmian="true"]', // 标记为可编辑的元素
      '[contenteditable="true"]', // 可编辑的元素
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6', // 标题元素
      'p', 'span', // 段落和行内元素
      'div.text-content', // 文本内容容器
      'div.title', 'div.subtitle', 'div.main-title', // 标题容器
      'div.account-info', // 账户信息容器
      'div:not([class]):not([id])' // 没有类和ID的div（可能是文本容器）
    ].join(',')));
    
    // 如果没有找到元素，尝试使用备用选择器
    if (elements.length === 0) {
      const backupElements = Array.from(doc.querySelectorAll('[draggable="true"], .editable-element'));
      if (backupElements.length > 0) {
        return backupElements
          .map(el => ({ element: el, zIndex: getZIndex(el) }))
          .sort((a, b) => a.zIndex - b.zIndex);
      }
      
      return [];
    }
    
    // 映射元素并按z-index排序
  return elements
    .map(el => ({ element: el, zIndex: getZIndex(el) }))
    .sort((a, b) => a.zIndex - b.zIndex);
  } catch (error) {
    console.error('获取可编辑元素时出错:', error);
    return [];
  }
};

/**
 * 重新分配所有元素的z-index值，确保它们是连续的
 * @param {Array<{element: HTMLElement, zIndex: number}>} sortedElements - 排序后的元素数组
 */
const reassignZIndices = (sortedElements) => {
  if (!sortedElements || sortedElements.length === 0) return;
  
  try {
    // 重新分配z-index，确保它们是连续的
    // 使用基础值2和步长2，这样可以在元素之间插入新元素，同时保持较小的z-index范围
    const baseZIndex = 2;
    const step = 2;
    
  sortedElements.forEach((item, index) => {
      // 计算新的z-index值
      const newZIndex = baseZIndex + (index * step);
      
      // 设置新的z-index值
      setZIndex(item.element, newZIndex);
      
      // 更新数组中的值，确保后续操作使用正确的z-index
      item.zIndex = newZIndex;
    });
  } catch (error) {
    console.error('重新分配z-index时出错:', error);
  }
};

/**
 * 高亮显示元素以提供视觉反馈
 * @param {HTMLElement} element - 要高亮显示的元素
 */
const highlightElement = (element) => {
  if (!element) return;
  
  try {
    // 保存原始样式
    const originalOutline = element.style.outline;
    const originalTransition = element.style.transition;
    
    // 设置高亮样式
    element.style.outline = '2px solid #1890ff';
    element.style.transition = 'outline 0.3s ease-in-out';
    
    // 触发重绘
    void element.offsetHeight;
    
    // 延迟恢复原始样式
    setTimeout(() => {
      element.style.outline = originalOutline;
      element.style.transition = originalTransition;
    }, 500);
  } catch (error) {
    console.error('高亮显示元素时出错:', error);
  }
};

/**
 * 上移一层 - 与上一层元素交换位置
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @returns {boolean} 是否成功
 */
export const moveUp = (iframe) => {
  try {
    // 获取当前活动元素
    const element = getActiveElement(iframe);
    if (!element) {
      return false;
    }
    
    // 获取iframe文档
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) {
      return false;
    }
    
    // 获取所有可编辑元素并按z-index排序
    const sortedElements = getSortedElements(iframeDoc);
    if (sortedElements.length < 2) {
      return false;
    }
    
    // 查找当前元素在排序数组中的位置
  const currentIndex = sortedElements.findIndex(item => item.element === element);
    if (currentIndex === -1) {
      return false;
    }
    
    // 如果已经是最顶层，则不需要移动
    if (currentIndex === sortedElements.length - 1) {
      return false;
    }
    
    // 与上一层元素交换位置
    const nextIndex = currentIndex + 1;
    const temp = sortedElements[nextIndex];
    sortedElements[nextIndex] = sortedElements[currentIndex];
    sortedElements[currentIndex] = temp;
    
    // 重新分配所有元素的z-index值
    reassignZIndices(sortedElements);
    
    // 提供视觉反馈
    highlightElement(element);
    
    return true;
  } catch (error) {
    console.error('上移元素时出错:', error);
    return false;
  }
};

/**
 * 下移一层 - 与下一层元素交换位置
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @returns {boolean} 是否成功
 */
export const moveDown = (iframe) => {
  try {
    // 获取当前活动元素
    const element = getActiveElement(iframe);
    if (!element) {
      return false;
    }
    
    // 获取iframe文档
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) {
      return false;
    }
    
    // 获取所有可编辑元素并按z-index排序
    const sortedElements = getSortedElements(iframeDoc);
    if (sortedElements.length < 2) {
      return false;
    }
    
    // 查找当前元素在排序数组中的位置
  const currentIndex = sortedElements.findIndex(item => item.element === element);
    if (currentIndex === -1) {
      return false;
    }
    
    // 如果已经是最底层，则不需要移动
    if (currentIndex === 0) {
      return false;
    }
    
    // 与下一层元素交换位置
    const prevIndex = currentIndex - 1;
    const temp = sortedElements[prevIndex];
    sortedElements[prevIndex] = sortedElements[currentIndex];
    sortedElements[currentIndex] = temp;
    
    // 重新分配所有元素的z-index值
    reassignZIndices(sortedElements);
    
    // 提供视觉反馈
    highlightElement(element);
    
    return true;
  } catch (error) {
    console.error('下移元素时出错:', error);
    return false;
  }
};

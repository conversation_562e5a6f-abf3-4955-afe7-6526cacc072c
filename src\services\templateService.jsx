// 提示词模板管理服务
// 所有模板请通过后端API获取
import axios from 'axios';
// 导入API缓存服务
import { fetchBasePrompts, fetchStyleTemplates, fetchCoverByCode, cachedRequest } from './apiCacheService';

// 本地存储键名
const STORAGE_KEYS = {
  BASE_TEMPLATE: 'fengmian_base_template',
  STYLE_TEMPLATES: 'fengmian_style_templates',
  SIZE_TEMPLATES: 'fengmian_size_templates',
  CURRENT_SIZE_TYPE: 'fengmian_current_size_type',
  SIZE_TYPES: 'fengmian_size_types',
  COVER_SIZE_CONFIG: 'fengmian_cover_size_config'
};

// 默认尺寸类型
const DEFAULT_SIZE_TYPE = 'xiaohongshu';

// 支持的尺寸类型
const DEFAULT_SIZE_TYPES = {
  xiaohongshu: '小红书',
  wechat: '微信公众号'
};

// 封面尺寸配置 - 预设尺寸参数
const COVER_SIZE_CONFIG = {
  xiaohongshu: {
    width: 750,
    height: 1000, // 宽高比为 3:4
    name: '小红书',
    description: '小红书笔记封面，竖向设计'
  },
  wechat: {
    width: 900,
    height: 383, // 宽高比约为 2.35:1
    name: '微信公众号',
    description: '微信公众号文章封面，横向设计'
  }
};

// 获取尺寸类型，包括自定义的
const getSizeTypesFromStorage = () => {
  try {
    const savedSizeTypes = localStorage.getItem(STORAGE_KEYS.SIZE_TYPES);
    return savedSizeTypes ? JSON.parse(savedSizeTypes) : { ...DEFAULT_SIZE_TYPES };
  } catch (error) {
    console.error('获取尺寸类型失败:', error);
    return { ...DEFAULT_SIZE_TYPES };
  }
};

// 初始化SIZE_TYPES
let SIZE_TYPES = getSizeTypesFromStorage();

// 风格模板缓存，避免重复请求
const styleTemplateCache = {};

// 请求锁，防止并发请求
const requestLocks = {};

// 模板缓存，避免重复请求
const templateCache = {};

// 防抖函数，限制函数调用频率
const debounce = (func, wait) => {
  let timeout;
  return function(...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(context, args), wait);
  };
};

/**
 * 获取当前选择的尺寸类型
 * @returns {string} 当前选择的尺寸类型
 */
export const getCurrentSizeType = () => {
  try {
    const savedType = localStorage.getItem(STORAGE_KEYS.CURRENT_SIZE_TYPE);
    return savedType || DEFAULT_SIZE_TYPE;
  } catch (error) {
    console.error('获取当前尺寸类型失败:', error);
    return DEFAULT_SIZE_TYPE;
  }
};

/**
 * 设置当前选择的尺寸类型
 * @param {string} sizeType - 要设置的尺寸类型
 */
export const setCurrentSizeType = (sizeType) => {
  try {
    if (SIZE_TYPES[sizeType]) {
      localStorage.setItem(STORAGE_KEYS.CURRENT_SIZE_TYPE, sizeType);
    }
  } catch (error) {
    console.error('设置当前尺寸类型失败:', error);
  }
};

/**
 * 获取所有支持的尺寸类型
 * @returns {Object} 尺寸类型对象，键为尺寸类型ID，值为尺寸类型名称
 */
export const getSizeTypes = () => {
  // 先从本地存储获取
  SIZE_TYPES = getSizeTypesFromStorage();

  // 尝试从后端获取并更新本地存储
  fetchSizeTypesFromBackend().then(backendSizeTypes => {
    if (backendSizeTypes) {
      // 如果成功从后端获取到封面类型，将其保存到本地存储
      saveSizeTypes(backendSizeTypes);
      SIZE_TYPES = backendSizeTypes; // 更新内存中的对象
    }
  }).catch(error => {
    console.error('获取后端封面类型失败:', error);
  });

  return { ...SIZE_TYPES };
};

/**
 * 保存尺寸类型
 * @param {Object} sizeTypes - 要保存的尺寸类型对象
 */
export const saveSizeTypes = (sizeTypes) => {
  try {
    localStorage.setItem(STORAGE_KEYS.SIZE_TYPES, JSON.stringify(sizeTypes));
    SIZE_TYPES = sizeTypes; // 更新内存中的对象
  } catch (error) {
    console.error('保存尺寸类型失败:', error);
  }
};

/**
 * 获取封面尺寸配置
 * @param {string} sizeType - 尺寸类型ID
 * @param {number} customWidth - 自定义宽度
 * @param {number} customHeight - 自定义高度
 * @returns {Object} 尺寸配置对象
 */
export const getCoverSizeConfig = (sizeType, customWidth, customHeight) => {
  if (!sizeType) {
    return null;
  }

  try {
    // 移除自定义尺寸配置

    // 先从本地存储获取自定义尺寸配置
    const customSizeConfig = localStorage.getItem(STORAGE_KEYS.COVER_SIZE_CONFIG);
    if (customSizeConfig) {
      const sizeConfigs = JSON.parse(customSizeConfig);
      if (sizeConfigs && sizeConfigs[sizeType]) {
        return sizeConfigs[sizeType];
      }
    }

    // 如果在本地存储中没有找到，尝试从 COVER_SIZE_CONFIG 获取
    if (COVER_SIZE_CONFIG[sizeType]) {
      return COVER_SIZE_CONFIG[sizeType];
    }

    // 如果还没找到，尝试从后端获取封面类型信息
    return fetchCoverSizeConfigFromBackend(sizeType);
  } catch (error) {
    // 静默处理错误，避免控制台输出
    // 如果上述方法都失败，返回默认配置（小红书尺寸）
    return COVER_SIZE_CONFIG.xiaohongshu;
  }
};

// 从后端获取封面尺寸配置
// @param {string} sizeType - 尺寸类型ID
// @returns {Promise<Object|null>} 尺寸配置对象或null
const fetchCoverSizeConfigFromBackend = async (sizeType) => {
  try {
    // 特殊处理"custom"类型，避免向后端发送不必要的请求
    if (sizeType === 'custom') {
      // 对于自定义类型，直接返回null，让调用方使用默认配置
      return null;
    }
    
    // 不再检查token，直接发送请求
    const response = await cachedRequest(`/api/cover/base-prompts?id_code=${sizeType}`);

    if (response.success && response.data && response.data.basePrompts && response.data.basePrompts.length > 0) {
      const basePrompt = response.data.basePrompts[0];

      if (basePrompt.cover_size) {
        // 解析尺寸字符串，格式如 "750x1000" 或 "900x383"
        const [width, height] = basePrompt.cover_size.split('x').map(Number);

        if (width && height) {
          // 创建尺寸配置对象
          const sizeConfig = {
            width,
            height,
            name: basePrompt.cover_type_name || basePrompt.cover_type,
            description: basePrompt.description || `${basePrompt.cover_type}封面`
          };

          // 缓存到本地存储
          saveCoverSizeConfig(sizeType, sizeConfig);

          return sizeConfig;
        }
      }
    }

    // 移除控制台警告，避免信息泄露
    return null;
  } catch (error) {
    // 移除控制台错误，避免信息泄露
    return null;
  }
};

/**
 * 保存封面尺寸配置
 * @param {string} sizeType - 尺寸类型ID
 * @param {Object} config - 尺寸配置对象
 */
export const saveCoverSizeConfig = (sizeType, config) => {
  try {
    // 从localStorage获取现有的配置
    const storedConfig = localStorage.getItem(STORAGE_KEYS.COVER_SIZE_CONFIG);
    const sizeConfig = storedConfig ? JSON.parse(storedConfig) : {};

    // 更新配置
    sizeConfig[sizeType] = {
      ...sizeConfig[sizeType],
      ...config
    };

    // 保存回本地存储
    localStorage.setItem(STORAGE_KEYS.COVER_SIZE_CONFIG, JSON.stringify(sizeConfig));

    return true;
  } catch (error) {
    console.error('保存封面尺寸配置失败:', error);
    return false;
  }
};

/**
 * 从后端获取风格模板内容
 * @returns {Promise<Object>} - 风格模板对象
 */
const fetchStyleTemplatesFromBackend = async () => {
  try {
    // 不检查token，直接获取数据
    const response = await cachedRequest('/api/style');
    
    if (response.success && Array.isArray(response.data)) {
      const styleList = response.data;
      const templates = {};
      
      // 构建风格模板对象
      styleList.forEach(style => {
        if (style.id_code && style.prompt_content) {
          templates[style.id_code] = style.prompt_content;
          
          // 更新缓存
          styleTemplateCache[style.id_code] = {
            content: style.prompt_content,
            timestamp: Date.now()
          };
        }
      });
      
      // 将模板保存到本地存储
      if (Object.keys(templates).length > 0) {
        try {
          localStorage.setItem(STORAGE_KEYS.STYLE_TEMPLATES, JSON.stringify(templates));
        } catch (storageError) {
          console.error('保存风格模板到本地存储失败:', storageError);
        }
      }
      
      return templates;
    }
    
    return {};
  } catch (error) {
    console.error('获取风格模板失败:', error);
    return {};
  }
};

/**
 * 获取风格模板
 * @returns {Object} 风格模板对象
 */
export const getStyleTemplates = () => {
  try {
    // 尝试从本地存储获取自定义风格模板
    const savedTemplates = localStorage.getItem(STORAGE_KEYS.STYLE_TEMPLATES);
    if (savedTemplates) {
      const parsedTemplates = JSON.parse(savedTemplates);
      // 确保至少有一个模板
      if (parsedTemplates && Object.keys(parsedTemplates).length > 0) {
        // 异步更新风格模板，但不等待结果
        fetchStyleTemplatesFromBackend().catch(error => 
          console.error('异步更新风格模板失败:', error)
        );
        return parsedTemplates;
      }
    }
    
    // 如果本地存储中没有模板，立即从后端获取
    // 使用一个空对象作为默认值，等待异步操作完成
    const emptyResult = {};
    fetchStyleTemplatesFromBackend()
      .then(templates => {
        if (templates && Object.keys(templates).length > 0) {
          // 如果成功获取到模板，更新本地存储
          localStorage.setItem(STORAGE_KEYS.STYLE_TEMPLATES, JSON.stringify(templates));
        }
      })
      .catch(error => console.error('获取风格模板失败:', error));
    
    return emptyResult;
  } catch (error) {
    console.error('获取风格模板失败:', error);
    return {};
  }
};

/**
 * 从后端获取所有封面类型
 * @returns {Promise<Object|null>} 封面类型对象或null
 */
export const fetchSizeTypesFromBackend = async () => {
  try {
    // 不再检查token，直接获取数据
    const response = await cachedRequest('/api/cover/base-prompts');

    if (response.success && response.data && response.data.basePrompts && response.data.basePrompts.length > 0) {
      // 从API返回的数据中提取不同的封面类型
      const sizeTypes = {};
      response.data.basePrompts.forEach(prompt => {
        // 使用id_code作为键，中文名称作为值
        if (prompt.id_code && !sizeTypes[prompt.id_code]) {
          // 使用cover_type作为显示名称（现在已经是中文名称）
          sizeTypes[prompt.id_code] = prompt.cover_type;
        }
      });

      // 如果没有获取到任何类型，返回默认类型
      if (Object.keys(sizeTypes).length === 0) {
        return { ...DEFAULT_SIZE_TYPES };
      }

      return sizeTypes;
    }

    return { ...DEFAULT_SIZE_TYPES };
  } catch (error) {
    console.error('从后端获取封面类型失败');
    return { ...DEFAULT_SIZE_TYPES };
  }
};

/**
 * 从后端获取基础提示词模板
 * @param {string} sizeType - 尺寸类型标识码
 * @param {boolean} skipCache - 是否跳过缓存（强制刷新）
 * @returns {Promise<string|null>} 基础模板或null
 */
export const fetchBaseTemplateFromBackend = async (sizeType, skipCache = false) => {
  const isDevEnvironment = process.env.NODE_ENV === 'development';

  // 使用现有模板缓存以保持兼容性
  // 如果已经有请求在进行中，返回缓存或等待请求完成
  if (requestLocks[sizeType]) {
    if (isDevEnvironment) {
      console.log('已有相同请求在进行中，等待完成:', sizeType);
    }
    return templateCache[sizeType] || null;
  }

  // 如果缓存中有数据且未过期且不跳过缓存，直接返回缓存数据
  const cacheEntry = templateCache[sizeType];
  if (!skipCache && cacheEntry && cacheEntry.timestamp > Date.now() - 5 * 60 * 1000) { // 5分钟缓存
    if (isDevEnvironment) {
      console.log('使用缓存的基础提示词模板:', sizeType);
    }
    return cacheEntry.content;
  }

  try {
    // 设置请求锁
    requestLocks[sizeType] = true;

    // 不再检查token，直接发送请求
    const url = sizeType ? `/api/cover/base-prompts?id_code=${sizeType}` : '/api/cover/base-prompts';
    const response = await cachedRequest(url);

    if (response.success && response.data && response.data.basePrompts && response.data.basePrompts.length > 0) {
      // 获取最新的模板
      const latestPrompt = response.data.basePrompts[0];

      // 更新缓存
      templateCache[sizeType] = {
        content: latestPrompt.prompt_content,
        timestamp: Date.now()
      };

      return latestPrompt.prompt_content;
    }
    return null;
  } catch (error) {
    console.error('从后端获取基础提示词模板失败:', error);
    return null;
  } finally {
    // 释放请求锁
    requestLocks[sizeType] = false;
  }
};

/**
 * 获取基础模板
 * @param {string} sizeType - 尺寸类型
 * @param {boolean} skipCache - 是否跳过缓存（强制刷新）
 * @returns {Promise<string>} 基础模板
 * @throws {Error} 如果无法获取模板
 */
export const getBaseTemplate = async (sizeType, skipCache = false) => {
  const isDevEnvironment = process.env.NODE_ENV === 'development';

  // 如果没有指定尺寸类型，使用当前选择的
  if (!sizeType) {
    sizeType = getCurrentSizeType();
  }

  try {
    // 首先尝试从本地存储获取自定义基础模板
    // 如果skipCache为true，跳过本地存储
    if (!skipCache) {
    const savedTemplates = localStorage.getItem(STORAGE_KEYS.BASE_TEMPLATE);
    if (savedTemplates) {
      const templates = JSON.parse(savedTemplates);
      if (templates && templates[sizeType]) {
        // 找到本地缓存的模板，异步更新缓存但不等待
        updateTemplateCache(sizeType);
        return templates[sizeType];
        }
      }
    }
  } catch (error) {
    if (isDevEnvironment) {
      console.error('获取本地基础模板失败:', error);
    } else {
      console.error('获取本地基础模板失败');
    }
  }

  // 如果本地没有缓存或skipCache为true，尝试从内存缓存获取
  // 如果skipCache为true，跳过内存缓存
  const cacheEntry = templateCache[sizeType];
  if (!skipCache && cacheEntry && cacheEntry.content) {
    return cacheEntry.content;
  }

  // 如果内存缓存也没有，直接从后端获取
  try {
    const backendTemplate = await fetchBaseTemplateFromBackend(sizeType, skipCache);
    if (backendTemplate) {
      return backendTemplate;
    }
  } catch (error) {
    console.error('从后端获取基础模板失败:', error);
  }

  // 如果都无法获取，抛出错误
  const errorMessage = `无法获取基础模板: ${sizeType}`;
  console.error(errorMessage);
  throw new Error(errorMessage);
};

/**
 * 异步更新模板缓存，不阻塞主流程
 * @param {string} sizeType - 尺寸类型
 */
const updateTemplateCache = debounce((sizeType) => {
  const isDevEnvironment = process.env.NODE_ENV === 'development';

  // 异步获取模板并更新缓存
  fetchBaseTemplateFromBackend(sizeType).then(backendTemplate => {
    if (backendTemplate) {
      // 如果成功从后端获取到模板，将其保存到本地存储
      try {
        const savedTemplates = JSON.parse(localStorage.getItem(STORAGE_KEYS.BASE_TEMPLATE) || '{}');
        savedTemplates[sizeType] = backendTemplate;
        localStorage.setItem(STORAGE_KEYS.BASE_TEMPLATE, JSON.stringify(savedTemplates));

      } catch (error) {
        if (isDevEnvironment) {
          console.error('保存模板到本地存储失败:', error);
        } else {
          console.error('保存模板到本地存储失败');
        }
      }
    }
  }).catch(error => {
    if (isDevEnvironment) {
      console.error('获取后端模板失败:', error);
    } else {
      console.error('获取后端模板失败');
    }
  });
}, 10000); // 10秒防抖，避免频繁请求

// 所有模板请通过后端API获取
export { STORAGE_KEYS, DEFAULT_SIZE_TYPE, DEFAULT_SIZE_TYPES, COVER_SIZE_CONFIG, fetchCoverSizeConfigFromBackend };
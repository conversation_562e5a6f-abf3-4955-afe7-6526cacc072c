/**
 * 生成任务表初始化脚本
 * 用于检查和初始化 generation_tasks 表
 */

const { sequelize, GenerationTask } = require('../models');
const logger = require('../utils/logger');

async function initGenerationTasksTable() {
  try {
    // 检查表是否已存在
    const tableExists = await sequelize.getQueryInterface().showAllTables()
      .then(tables => tables.includes('generation_tasks'));
    
    if (!tableExists) {
      logger.info('generation_tasks 表不存在，将通过模型创建');
      
      // 使用模型创建表
      await GenerationTask.sync();
      logger.info('generation_tasks 表创建成功');
    } else {
      logger.info('generation_tasks 表已存在');
      
      // 可选：检查字段是否完整
      try {
        const columns = await sequelize.getQueryInterface().describeTable('generation_tasks');
        const requiredColumns = [
          'id', 'task_id', 'user_id', 'task_type', 'status', 
          'start_time', 'end_time', 'duration_ms', 'parameters', 
          'result_id', 'error_message', 'created_at', 'updated_at'
        ];
        
        const missingColumns = requiredColumns.filter(col => !columns[col]);
        
        if (missingColumns.length > 0) {
          logger.warn(`generation_tasks 表缺少以下字段: ${missingColumns.join(', ')}`);
          // 这里可以添加表修补代码，根据需要添加缺失的字段
        } else {
          logger.info('generation_tasks 表字段检查完成，所有必要字段都存在');
        }
      } catch (error) {
        logger.error('检查表字段时出错:', error);
      }
    }
    
    // 记录初始化完成
    logger.info('生成任务表初始化完成');
    return true;
  } catch (error) {
    logger.error('初始化生成任务表时出错:', error);
    return false;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  (async () => {
    try {
      // 连接数据库
      await sequelize.authenticate();
      logger.info('数据库连接成功');
      
      // 初始化表
      const result = await initGenerationTasksTable();
      
      if (result) {
        logger.info('生成任务表初始化完成');
      } else {
        logger.error('生成任务表初始化失败');
      }
      
      // 关闭连接
      await sequelize.close();
      process.exit(0);
    } catch (error) {
      logger.error('脚本执行失败:', error);
      process.exit(1);
    }
  })();
} else {
  // 作为模块导出
  module.exports = initGenerationTasksTable;
}

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 功能控制模型
 * 对应数据库中的feature_controls表
 */
const FeatureControl = sequelize.define('FeatureControl', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '功能控制ID，唯一标识'
  },
  feature_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '功能名称，例如"生成封面-积分扣除"'
  },
  feature_description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '功能描述，详细说明功能逻辑'
  },
  user_roles: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '适用的用户角色，JSON数组格式["user", "vip"]等',
    get() {
      const value = this.getDataValue('user_roles');
      if (!value) return [];

      try {
        // 尝试解析JSON
        return JSON.parse(value);
      } catch (error) {
        // 如果解析失败，说明可能是普通字符串，将其转换为数组
        console.log(`解析user_roles失败，值为: ${value}，将其转换为数组`);
        return [value];
      }
    },
    set(val) {
      // 确保val是数组或字符串
      if (val === null || val === undefined) {
        this.setDataValue('user_roles', '[]');
      } else if (Array.isArray(val)) {
        this.setDataValue('user_roles', JSON.stringify(val));
      } else if (typeof val === 'string') {
        try {
          // 尝试解析，看是否已经是JSON字符串
          JSON.parse(val);
          this.setDataValue('user_roles', val);
        } catch (error) {
          // 不是JSON字符串，将其转换为JSON数组
          this.setDataValue('user_roles', JSON.stringify([val]));
        }
      } else {
        // 其他类型，转换为字符串后包装为数组
        this.setDataValue('user_roles', JSON.stringify([String(val)]));
      }
    }
  },
  points_cost: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '功能使用消耗的积分数量'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '功能是否激活'
  }
}, {
  tableName: 'feature_controls',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['feature_name']
    }
  ]
});

module.exports = FeatureControl;
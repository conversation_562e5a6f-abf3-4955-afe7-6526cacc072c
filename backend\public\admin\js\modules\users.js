// users.js - 用户管理模块

// 用户管理模块命名空间
window.userModule = (function() {
  // 私有变量
  let currentPage = 1;
  let pageSize = 10;
  let totalPages = 1;
  let currentSearchQuery = '';

  // 初始化函数
  function init() {
    console.log('初始化用户管理模块...');

    // 绑定事件
    bindEvents();

    // 加载用户列表
    fetchUsers();
  }

  // 绑定事件处理函数
  function bindEvents() {
    // 添加安全检查，避免DOM元素不存在时出错
    const addUserBtn = document.getElementById('addUserBtn');
    if (addUserBtn) {
      addUserBtn.addEventListener('click', function() {
        // 获取表单，添加安全检查
        const userForm = document.getElementById('userForm');
        if (userForm) {
          userForm.reset();
        }

        const userId = document.getElementById('userId');
        if (userId) {
          userId.value = '';
        }

        // 设置模态框标题
        const userModalLabel = document.getElementById('userModalLabel');
        if (userModalLabel) {
          userModalLabel.textContent = '添加用户';
        }

        // 显示模态框
        const userModal = document.getElementById('userModal');
        if (userModal) {
          const modal = new bootstrap.Modal(userModal);
          modal.show();

          // 显示密码字段（新用户必须设置密码）
          const passwordGroup = document.getElementById('passwordGroup');
          if (passwordGroup) {
            passwordGroup.classList.remove('d-none');
          }
        }
      });
    }

    // 保存用户按钮
    const userModalSubmitBtn = document.getElementById('userModalSubmitBtn');
    if (userModalSubmitBtn) {
      userModalSubmitBtn.addEventListener('click', function() {
        saveUser();
      });
    }

    // 搜索按钮 - 修正ID
    const userSearchBtn = document.getElementById('userSearchBtn');
    if (userSearchBtn) {
      userSearchBtn.addEventListener('click', function() {
        const searchInput = document.getElementById('userSearchInput');
        if (searchInput) {
          currentSearchQuery = searchInput.value.trim();
          currentPage = 1;
          fetchUsers();
        }
      });
    }

    // 搜索输入回车事件 - 修正ID
    const userSearchInput = document.getElementById('userSearchInput');
    if (userSearchInput) {
      userSearchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          currentSearchQuery = this.value.trim();
          currentPage = 1;
          fetchUsers();
        }
      });
    }

    // 刷新按钮
    const userRefreshBtn = document.getElementById('userRefreshBtn');
    if (userRefreshBtn) {
      userRefreshBtn.addEventListener('click', function() {
        document.getElementById('userSearchInput').value = '';
        currentSearchQuery = '';
        currentPage = 1;
        fetchUsers();
      });
    }
  }

  // 获取用户列表
  function fetchUsers() {
    const token = localStorage.getItem('token');
    if (!token) return;

    // 构建查询参数
    const params = new URLSearchParams({
      page: currentPage,
      limit: pageSize
    });

    if (currentSearchQuery) {
      params.append('search', currentSearchQuery);
    }

    fetch(`/api/admin/users?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // 直接使用data.data，因为这里的API返回的是数组而非对象
        // 如果将来API返回的是对象，可以增加判断逻辑
        const users = Array.isArray(data.data) ? data.data : [];
        console.log('处理后的用户数据:', users);
        renderUsers(users);
        totalPages = data.pagination?.total_pages || 1;
        renderPagination();
      } else {
        console.error('获取用户列表失败:', data.message);
        renderUsers([]);
      }
    })
    .catch(error => {
      console.error('获取用户列表失败:', error);
      renderUsers([]);
    });
  }

  // 渲染用户列表
  function renderUsers(users) {
    const tbody = document.querySelector('#usersTable tbody');
    tbody.innerHTML = '';

    if (users.length === 0) {
      const tr = document.createElement('tr');
      tr.innerHTML = '<td colspan="8" class="text-center">暂无数据</td>';
      tbody.appendChild(tr);
      return;
    }

    users.forEach(user => {
      const tr = document.createElement('tr');

      // 检查VIP是否过期
      let vipStatus = '';
      let vipStatusClass = '';

      if (user.role === 'vip' && user.vip_expire_date) {
        const expireDate = new Date(user.vip_expire_date);
        const now = new Date();

        if (expireDate > now) {
          // 未过期
          const daysLeft = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
          vipStatus = `（剩余${daysLeft}天）`;
          vipStatusClass = 'text-success';
        } else {
          // 已过期
          vipStatus = '（已过期）';
          vipStatusClass = 'text-danger';
        }
      }

      // 确定用户状态显示
      let userStatusBadge = '';
      let statusBtnText = '';
      let statusBtnClass = '';

      if (user.status === 'locked') {
        userStatusBadge = '<span class="badge bg-danger">已禁用</span>';
        statusBtnText = '启用';
        statusBtnClass = 'btn-success';
      } else {
        userStatusBadge = '<span class="badge bg-success">正常</span>';
        statusBtnText = '禁用';
        statusBtnClass = 'btn-secondary';
      }

      tr.innerHTML = `
        <td>${user.id}</td>
        <td>${user.phone}</td>
        <td>${user.nickname}</td>
        <td>
          ${user.role === 'admin' ? '<span class="badge bg-danger">管理员</span>' :
            user.role === 'vip' ? '<span class="badge bg-warning">会员</span>' :
            '<span class="badge bg-secondary">普通用户</span>'}
        </td>
        <td>${userStatusBadge}</td>
        <td>${user.points || '-'}</td>
        <td>${user.daily_points || '0'}</td>
        <td>
          ${user.vip_expire_date
            ? `<span class="${vipStatusClass}">${formatDate(user.vip_expire_date)} ${vipStatus}</span>`
            : '-'}
        </td>
        <td>${formatDate(user.createdAt)}</td>
        <td>
          <button class="btn btn-sm btn-primary edit-user" data-id="${user.id}">编辑</button>
          <button class="btn btn-sm btn-warning reset-pwd" data-id="${user.id}" data-name="${user.phone}">重置密码</button>
          <button class="btn btn-sm ${statusBtnClass} toggle-status" data-id="${user.id}" data-status="${user.status || 'active'}" data-name="${user.phone}">${statusBtnText}</button>
          ${user.role !== 'admin' ? `<button class="btn btn-sm btn-danger delete-user" data-id="${user.id}" data-name="${user.phone}">删除</button>` : ''}
        </td>
      `;

      // 绑定编辑按钮事件
      tr.querySelector('.edit-user').addEventListener('click', function() {
        editUser(user.id);
      });

      // 绑定重置密码按钮事件
      tr.querySelector('.reset-pwd').addEventListener('click', function() {
        resetPassword(user.id, user.username);
      });

      // 绑定切换状态按钮事件
      tr.querySelector('.toggle-status').addEventListener('click', function() {
        const status = this.getAttribute('data-status') || 'active';
        const name = this.getAttribute('data-name') || user.phone;
        toggleUserStatus(user.id, status, name);
      });

      // 绑定删除按钮事件（如果存在）
      const deleteBtn = tr.querySelector('.delete-user');
      if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
          const name = this.getAttribute('data-name') || user.phone;
          deleteUser(user.id, name);
        });
      }

      tbody.appendChild(tr);
    });
  }

  // 编辑用户
  function editUser(userId) {
    const token = localStorage.getItem('token');
    if (!token) return;

    console.log('获取用户详情，用户ID:', userId);

    fetch(`/api/admin/users/${userId}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const user = data.data.user;
        console.log('获取到的用户信息:', user);

        // 设置模态框标题
        document.getElementById('userModalLabel').textContent = '编辑用户';

        // 填充表单
        document.getElementById('userId').value = user.id;
        document.getElementById('userPhone').value = user.phone;
        document.getElementById('userNickname').value = user.nickname;
        document.getElementById('userEmail').value = user.email || '';
        document.getElementById('userRole').value = user.role || 'user';
        document.getElementById('userPoints').value = user.points || 0;
        document.getElementById('userDailyPoints').value = user.daily_points || 0;
        document.getElementById('userDailyPoints').value = user.daily_points || 0;

        // 清空日期和时间输入框
        document.getElementById('userVipExpireDate').value = '';
        const timeInput = document.getElementById('userVipExpireTime');
        if (timeInput) {
          timeInput.value = '';
        }

        // 如果有VIP到期时间，设置到期时间
        if (user.vip_expire_date) {
          try {
            // 解析ISO格式的日期字符串
            const expireDate = new Date(user.vip_expire_date);
            console.log('解析的到期日期:', expireDate);

            if (!isNaN(expireDate.getTime())) {
              // 格式化为YYYY-MM-DD格式的日期字符串
              const year = expireDate.getFullYear();
              const month = String(expireDate.getMonth() + 1).padStart(2, '0');
              const day = String(expireDate.getDate()).padStart(2, '0');
              const hours = String(expireDate.getHours()).padStart(2, '0');
              const minutes = String(expireDate.getMinutes()).padStart(2, '0');

              const dateStr = `${year}-${month}-${day}`;
              const timeStr = `${hours}:${minutes}`;

              console.log('格式化后的日期:', dateStr);
              console.log('格式化后的时间:', timeStr);

              // 设置日期输入框
              document.getElementById('userVipExpireDate').value = dateStr;

              // 如果存在时间输入框，也设置时间
              if (timeInput) {
                timeInput.value = timeStr;
              }
            } else {
              console.error('无效的日期格式:', user.vip_expire_date);
            }
          } catch (error) {
            console.error('日期解析错误:', error);
          }
        }

        // 密码字段保持空白（编辑用户不需要修改密码）
        document.getElementById('userPassword').value = '';

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        modal.show();
      } else {
        alert('获取用户详情失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('获取用户详情失败:', error);
      alert('获取用户详情失败，请稍后再试');
    });
  }

  // 重置用户密码
  function resetPassword(userId, phone) {
    // 填充确认对话框
    document.getElementById('resetPasswordUserId').value = userId;
    // 如果有resetPasswordUsername元素，则设置其文本
    const usernameElement = document.getElementById('resetPasswordUsername');
    if (usernameElement) {
      usernameElement.textContent = phone;
    }

    // 显示确认对话框
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();

    // 绑定确认重置密码按钮事件
    document.getElementById('confirmResetPasswordBtn').onclick = function() {
      const newPassword = document.getElementById('newPassword').value;

      if (!newPassword) {
        alert('请输入新密码');
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) return;

      // 显示加载状态
      this.disabled = true;
      this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';

      fetch(`/api/admin/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          new_password: newPassword
        })
      })
      .then(response => response.json())
      .then(data => {
        // 恢复按钮状态
        this.disabled = false;
        this.innerHTML = '确认';

        // 关闭确认对话框
        bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();

        if (data.success) {
          alert('重置密码成功');
        } else {
          alert('重置密码失败: ' + (data.message || '未知错误'));
        }
      })
      .catch(error => {
        console.error('重置密码失败:', error);

        // 恢复按钮状态
        this.disabled = false;
        this.innerHTML = '确认';

        // 关闭确认对话框
        bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();

        alert('重置密码失败，请稍后再试');
      });
    };
  }

  // 切换用户状态
  function toggleUserStatus(userId, currentStatus, username) {
    // 根据当前状态确定新状态和操作名称
    const newStatus = currentStatus === 'locked' ? 'active' : 'locked';
    const actionName = currentStatus === 'locked' ? '启用' : '禁用';

    if (confirm(`确定要${actionName}用户 ${username} 吗？`)) {
      const token = localStorage.getItem('token');
      if (!token) return;

      fetch(`/api/admin/users/${userId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          status: newStatus
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // 刷新用户列表
          fetchUsers();
          alert(`${actionName}用户成功`);
        } else {
          alert(`${actionName}用户失败: ` + (data.message || '未知错误'));
        }
      })
      .catch(error => {
        console.error(`${actionName}用户失败:`, error);
        alert(`${actionName}用户失败，请稍后再试`);
      });
    }
  }

  // 删除用户
  function deleteUser(userId, username) {
    // 一次确认
    if (!confirm(`确定要删除用户 ${username} 吗？此操作将删除该用户的所有数据且不可恢复！`)) {
      return;
    }

    // 二次确认
    if (!confirm(`⚠️ 警告：删除操作不可撤销！\n\n再次确认要删除用户 ${username} 吗？`)) {
      return;
    }

    const token = localStorage.getItem('token');
    if (!token) return;

    // 显示加载状态
    const loadingEl = document.createElement('div');
    loadingEl.className = 'loading-overlay';
    loadingEl.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">删除中...</span></div><div class="mt-2">正在删除用户数据，请稍候...</div>';
    document.body.appendChild(loadingEl);

    fetch(`/api/admin/users/${userId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      console.log('删除用户响应状态:', response.status);
      return response.json();
    })
    .then(data => {
      // 移除加载状态
      document.body.removeChild(loadingEl);

      console.log('删除用户响应数据:', data);
      if (data.success) {
        // 刷新用户列表
        fetchUsers();
        alert(`删除用户成功`);
      } else {
        alert(`删除用户失败: ` + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      // 移除加载状态
      if (document.body.contains(loadingEl)) {
        document.body.removeChild(loadingEl);
      }

      console.error('删除用户失败:', error);
      alert('删除用户失败，请稍后再试');
    });
  }

  // 保存用户
  function saveUser() {
    // 验证表单
    const form = document.getElementById('addUserForm');
    if (!form) {
      console.error('用户表单不存在');
      return;
    }
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }

    const userId = document.getElementById('userId').value;
    const username = document.getElementById('userPhone').value;
    const nickname = document.getElementById('userNickname').value;
    const email = document.getElementById('userEmail').value;
    const password = document.getElementById('userPassword').value;
    const userType = document.getElementById('userRole').value;
    const points = document.getElementById('userPoints').value;
    const dailyPoints = document.getElementById('userDailyPoints').value;
    const vipExpireDate = document.getElementById('userVipExpireDate').value;
    const timeInput = document.getElementById('userVipExpireTime');
    const vipExpireTime = timeInput ? timeInput.value : '';

    console.log('保存用户 - 原始数据:');
    console.log('用户ID:', userId);
    console.log('角色:', userType);
    console.log('VIP到期日期:', vipExpireDate);
    console.log('VIP到期时间:', vipExpireTime);

    // 新用户必须设置密码
    if (!userId && !password) {
      alert('请设置密码');
      return;
    }

      // 构建用户数据
  const userData = {
    phone: username,
    nickname: nickname,
    email: email,
    role: userType,
    points: points,
    daily_points: dailyPoints
  };

    // 如果是新用户或明确要修改密码，则添加密码字段
    if (password) {
      userData.password = password;
    }

    // 处理VIP到期时间，无论用户是什么角色，都将日期提交给后端
    // 后端会根据日期自动判断角色
    if (vipExpireDate) {
      let expiryDateTimeStr;

      if (vipExpireTime) {
        expiryDateTimeStr = `${vipExpireDate}T${vipExpireTime}:00`;
      } else {
        expiryDateTimeStr = `${vipExpireDate}T23:59:59`;
      }

      try {
        const dateObj = new Date(expiryDateTimeStr);
        if (!isNaN(dateObj.getTime())) {
          userData.vip_expire_date = dateObj.toISOString();
          console.log('发送到后端的日期时间:', userData.vip_expire_date);
        } else {
          console.error('日期格式无效:', expiryDateTimeStr);
          alert('VIP到期时间格式无效');
          return;
        }
      } catch (error) {
        console.error('解析日期时间出错:', error);
        alert('VIP到期时间格式无效');
        return;
      }
    } else if (userId) {
      // 编辑用户时，明确设置为null，让后端处理
      userData.vip_expire_date = null;
      console.log('VIP到期时间已清除，设置为null');
    }

    // 发送请求
    const url = userId ? `/api/admin/users/${userId}` : '/api/admin/users';
    const method = userId ? 'PUT' : 'POST';
    const token = localStorage.getItem('token');

    console.log('准备发送的请求:');
    console.log('URL:', url);
    console.log('Method:', method);
    console.log('用户数据:', userData);

    if (!token) return;

    // 显示加载状态
    const saveBtn = document.getElementById('userModalSubmitBtn');
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';

    fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(userData)
    })
    .then(response => {
      console.log('响应状态:', response.status);
      return response.json();
    })
    .then(data => {
      // 恢复按钮状态
      saveBtn.disabled = false;
      saveBtn.innerHTML = '保存';

      console.log('服务器响应:', data);

      if (data.success) {
        // 关闭模态框
        bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();

        // 刷新用户列表
        fetchUsers();

        alert(userId ? '更新用户成功' : '添加用户成功');
      } else {
        alert(data.message || '操作失败');
      }
    })
    .catch(error => {
      console.error('保存用户失败:', error);

      // 恢复按钮状态
      saveBtn.disabled = false;
      saveBtn.innerHTML = '保存';

      alert('操作失败，请稍后再试');
    });
  }

  // 渲染分页
  function renderPagination() {
    const paginationElement = document.getElementById('userPagination');
    if (!paginationElement) {
      console.error('分页元素不存在');
      return;
    }
    paginationElement.innerHTML = '';

    if (totalPages <= 1) {
      return;
    }

    // 上一页按钮
    const prevPageItem = document.createElement('li');
    prevPageItem.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevPageItem.innerHTML = '<a class="page-link" href="#">上一页</a>';
    prevPageItem.addEventListener('click', function(e) {
      e.preventDefault();
      if (currentPage > 1) {
        currentPage--;
        fetchUsers();
      }
    });
    paginationElement.appendChild(prevPageItem);

    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);

    for (let i = startPage; i <= endPage; i++) {
      const pageItem = document.createElement('li');
      pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;
      pageItem.innerHTML = `<a class="page-link" href="#">${i}</a>`;
      pageItem.addEventListener('click', function(e) {
        e.preventDefault();
        currentPage = i;
        fetchUsers();
      });
      paginationElement.appendChild(pageItem);
    }

    // 下一页按钮
    const nextPageItem = document.createElement('li');
    nextPageItem.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextPageItem.innerHTML = '<a class="page-link" href="#">下一页</a>';
    nextPageItem.addEventListener('click', function(e) {
      e.preventDefault();
      if (currentPage < totalPages) {
        currentPage++;
        fetchUsers();
      }
    });
    paginationElement.appendChild(nextPageItem);
  }

  // 格式化日期
  function formatDate(dateString) {
    if (!dateString) return '-';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '-'; // 如果日期无效

      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('日期格式化错误:', error);
      return '-';
    }
  }

  // 公开API
  return {
    init: init
  };
})();

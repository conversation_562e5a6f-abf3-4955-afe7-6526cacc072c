import { useState, useEffect, useCallback } from 'react';
import { Modal } from 'antd';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { clearFeatureCache } from '../services/featureService';
import { checkAuthToken, fetchUserProfile, clearCache, removeCacheItem, invalidateUserProfileCache } from '../services/apiCacheService';

/**
 * 处理Token过期的情况
 * @param {Error} error - Axios错误对象
 * @param {Function} navigate - React Router的navigate函数
 * @returns {boolean} - 如果是Token过期，返回true
 */
const handleTokenExpiration = (error, navigate) => {
  // 检查是否是401错误
  if (error.response && error.response.status === 401) {
    console.error('Token已过期，需要重新登录', error.response.data);
    
    // 清除已过期的登录信息
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    
    // 清除相关缓存
    removeCacheItem(/^GET:\/api\/auth\/me/); // 清除认证缓存
    removeCacheItem(/^GET:\/api\/user\/profile/); // 清除用户资料缓存
    
    // 显示登录提示
    Modal.error({
      title: '登录已过期',
      content: '您的登录已过期，请重新登录',
      okText: '去登录',
      onOk: () => {
        // 导航到主页
        navigate('/');
      }
    });
    return true;
  }
  return false;
};

/**
 * 自定义Hook，用于处理身份验证和积分检查
 * @returns {Object} 包含身份验证和积分检查相关的状态和方法
 */
const useAuth = () => {
  const [user, setUser] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // 检查token是否有效，使用缓存
  const checkTokenValidity = useCallback(async () => {
    const token = localStorage.getItem('token');
    if (!token) return false;
    
    try {
      // 使用缓存服务检查token有效性
      return await checkAuthToken(token);
    } catch (error) {
      console.error('Token验证失败:', error);
      
      // 处理token过期情况
      if (error.response && error.response.status === 401) {
        handleTokenExpiration(error, navigate);
        return false;
      }
      
      return false;
    }
  }, [navigate]);

  // 检查登录状态
  const checkLoginStatus = useCallback(() => {
    setIsLoading(true);
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');

    if (!token || !userStr) {
      setIsLoggedIn(false);
      setUser(null);
      setIsLoading(false);
      return;
    }

    try {
      const userData = JSON.parse(userStr);
      setUser(userData);
      setIsLoggedIn(true);
      
      // 异步验证token是否有效
      checkTokenValidity().then(isValid => {
        if (!isValid) {
          setIsLoggedIn(false);
          setUser(null);
        }
      });
    } catch (error) {
      console.error('解析用户数据失败:', error);
      localStorage.removeItem('user');
      setIsLoggedIn(false);
      setUser(null);
    }

    setIsLoading(false);
  }, [checkTokenValidity]);

  // 从服务器刷新用户信息，使用缓存
  const refreshUserInfo = useCallback(async (forceRefresh = false, retryCount = 0) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;
      
      // 使用缓存服务获取用户资料，添加强制刷新选项
      const userData = await fetchUserProfile(token, forceRefresh);
      if (userData) {
        // 检查用户数据是否有变化
        const oldUserStr = localStorage.getItem('user');
        let dataChanged = true;
        let pointsChanged = false;
        let oldPoints = 0;
        
        if (oldUserStr) {
          try {
            const oldUser = JSON.parse(oldUserStr);
            // 检查关键字段是否有变化（角色、积分、VIP到期时间）
            dataChanged = 
              oldUser.role !== userData.role ||
              oldUser.points !== userData.points ||
              oldUser.vip_expire_date !== userData.vip_expire_date;
            
            // 特别检查积分变化
            pointsChanged = oldUser.points !== userData.points;
            oldPoints = oldUser.points || 0;
          } catch (e) {
            // 解析错误，视为数据有变化
            dataChanged = true;
          }
        }
        
        // 更新本地存储和状态
        setUser(userData);
        localStorage.setItem('user', JSON.stringify(userData));
        
        // 如果关键数据有变化，清除权限缓存
        if (dataChanged) {
          clearFeatureCache();
        }
        
        // 如果积分有变化，触发积分更新事件
        if (pointsChanged) {
          // 触发自定义事件，通知其他组件用户积分已更新
          window.dispatchEvent(new CustomEvent('userPointsUpdated', {
            detail: {
              oldPoints,
              newPoints: userData.points,
              difference: userData.points - oldPoints
            }
          }));
        }
        
        return userData; // 返回用户数据，方便调用者使用
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      
      // 处理token过期情况
      if (error.response && error.response.status === 401) {
        handleTokenExpiration(error, navigate);
      } else if (retryCount < 2 && forceRefresh) {
        // 如果是强制刷新且失败，尝试重试，最多重试2次
        console.log(`刷新用户信息失败，正在进行第${retryCount + 1}次重试...`);
        
        // 清除用户资料缓存
        invalidateUserProfileCache();
        
        // 延迟一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000));
        return refreshUserInfo(true, retryCount + 1);
      }
    }
  }, [navigate]);

  // 初始化时检查登录状态，并设置较长的刷新间隔
  useEffect(() => {
    // 组件挂载时检查登录状态
    checkLoginStatus();
    
    // 当登录状态变为真时，清除权限缓存，确保获取最新权限信息
    if (isLoggedIn) {
      clearFeatureCache();
    }
    
    // 定期检查用户信息，获取最新状态（包括积分变更），延长间隔到5分钟
    const refreshInterval = setInterval(() => {
      if (isLoggedIn) {
        refreshUserInfo();
      }
    }, 5 * 60 * 1000); // 从1分钟改为5分钟检查一次
    
    return () => clearInterval(refreshInterval);
  }, [isLoggedIn, checkLoginStatus, refreshUserInfo]);

  // 获取操作所需积分
  const getOperationCost = useCallback(async (operationType) => {
    try {
      const response = await axios.get(`/api/system/operation-cost/${operationType}`);
      
      if (response.data.success) {
        return response.data.data.points_cost;
      }
      
      throw new Error('获取积分消耗失败');
    } catch (error) {
      console.error(`获取${operationType}所需积分失败:`, error);
      
      // 处理token过期情况
      if (error.response && error.response.status === 401) {
        handleTokenExpiration(error, navigate);
        return 0;
      }
      
      // 返回默认值
      return operationType === 'generate_cover' ? 10 : 0;
    }
  }, [navigate]);

  // 检查用户是否有足够积分
  const checkPoints = useCallback(async (operationType) => {
    if (!isLoggedIn || !user) {
      return { hasEnough: false, message: '请先登录', required: 0, userPoints: 0 };
    }
    
    try {
      const requiredPoints = await getOperationCost(operationType);
      const hasEnough = user.points >= requiredPoints;
      
      return {
        hasEnough,
        required: requiredPoints,
        userPoints: user.points,
        message: hasEnough ? '' : `积分不足，需要${requiredPoints}积分，当前仅有${user.points}积分`
      };
    } catch (error) {
      console.error('检查积分失败:', error);
      
      // 处理token过期情况
      if (error.response && error.response.status === 401) {
        handleTokenExpiration(error, navigate);
        return { hasEnough: false, message: '登录已过期，请重新登录', required: 0, userPoints: 0 };
      }
      
      return { hasEnough: false, message: '检查积分失败', required: 0, userPoints: 0 };
    }
  }, [isLoggedIn, user, getOperationCost, navigate]);

  // 确认登录和积分状态，成功则执行回调
  const confirmAuthAndPoints = useCallback(async (operationType, onSuccess) => {
    if (!isLoggedIn) {
      Modal.confirm({
        title: '需要登录',
        content: '该操作需要消耗积分，请先登录或注册账号',
        okText: '去登录',
        cancelText: '取消',
        onOk: () => {
          // 导航到首页
          navigate('/');
        }
      });
      return;
    }
    
    // 移除了 const isTokenValid = await checkTokenValidity();
    // isLoggedIn 状态由 checkLoginStatus 更新，它内部调用了 checkTokenValidity。
    // 如果token在此期间失效，后续API调用将返回401，由全局拦截器处理。
    
    const pointsCheck = await checkPoints(operationType);
    
    if (!pointsCheck.hasEnough) {
      Modal.error({
        title: '积分不足',
        content: pointsCheck.message,
        okText: '去充值',
        onOk: () => {
          navigate('/profile');
        }
      });
      return;
    }
    
    // 登录状态和积分都正常，执行成功回调
    if (typeof onSuccess === 'function') {
      onSuccess();
    }
  }, [isLoggedIn, checkPoints, navigate]);

  // 更新用户积分（如积分消费后）
  const updateUserPoints = useCallback((newPoints) => {
    if (user) {
      const updatedUser = { ...user, points: newPoints };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
    }
  }, [user]);

  // 用户登出
  const logout = useCallback(() => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
    setIsLoggedIn(false);
    
    // 清除所有缓存
    clearFeatureCache();
    clearCache(); // 清除API缓存
    
    // 触发自定义事件，通知组件用户已退出登录
    window.dispatchEvent(new CustomEvent('userLogout'));
    
    // 可选：如果后端有登出接口，可以在这里调用
    // await axios.post('/api/auth/logout'); 
    navigate('/'); // 改为导航到首页，而不是/auth
    console.log('User logged out, navigating to /');
  }, [navigate]);

  return {
    user,
    isLoggedIn,
    isLoading,
    checkLoginStatus,
    getOperationCost,
    checkPoints,
    confirmAuthAndPoints,
    updateUserPoints,
    refreshUserInfo,
    checkTokenValidity,
    logout
  };
};

export default useAuth; 
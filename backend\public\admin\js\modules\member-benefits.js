/**
 * 会员权益管理模块
 * 功能：管理不同会员角色的权益配置
 */

// 定义会员权益管理模块命名空间
const memberBenefitsModule = (function() {
  // 模块内部变量
  let benefits = [];
  let currentPage = 1;
  let pageSize = 10;
  let totalPages = 1;
  let currentFilters = {};
  let currentBenefitId = null;
  let isEditing = false;

  /**
   * 初始化会员权益模块
   */
  function init() {
    // 绑定事件
    const addBenefitBtn = document.getElementById('addBenefitBtn');
    if (addBenefitBtn) {
      addBenefitBtn.addEventListener('click', () => showBenefitModal(false));
    }

    const roleFilter = document.getElementById('roleFilter');
    if (roleFilter) {
      roleFilter.addEventListener('change', applyFilters);
    }

    // 监听页面显示事件
    observePageVisibility('member-benefits', () => {
      loadBenefits();
    });

    // 初始加载数据
    loadBenefits();
  }

  /**
   * 加载会员权益列表
   */
  async function loadBenefits() {
    try {
      showLoading();
      
      // 构建请求参数
      const params = new URLSearchParams({
        ...currentFilters
      });

      // 使用模拟数据，因为实际API尚未实现
      // 在实际环境中，当API实现后，可以取消注释以下代码
      /* 
      const response = await fetch(`/api/admin/payment/benefits?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('加载会员权益失败');
      }

      const data = await response.json();
      
      if (data.success) {
        benefits = data.data.benefits || [];
      } else {
        showToast('error', data.message || '加载会员权益失败');
        renderEmptyTable('加载失败，请重试');
        return;
      }
      */
      
      // 使用模拟数据，实际开发中应通过API获取
      benefits = [
        {
          id: 1,
          benefit_name: 'VIP专属客服',
          benefit_code: 'vip_support',
          apply_role: 'vip',
          benefit_icon: 'headset',
          benefit_description: 'VIP用户享有专属客服支持',
          sort_order: 1,
          is_active: true
        },
        {
          id: 2,
          benefit_name: '每日积分奖励',
          benefit_code: 'daily_points',
          apply_role: 'vip',
          benefit_icon: 'gift',
          benefit_description: 'VIP用户每日登录可获得额外积分奖励',
          sort_order: 2,
          is_active: true
        },
        {
          id: 3,
          benefit_name: '高级模板使用权',
          benefit_code: 'premium_templates',
          apply_role: 'vip',
          benefit_icon: 'palette',
          benefit_description: 'VIP用户可使用所有高级模板',
          sort_order: 3,
          is_active: true
        }
      ];
      
      // 应用过滤
      if (currentFilters.role && currentFilters.role !== 'all') {
        benefits = benefits.filter(b => b.apply_role === currentFilters.role);
      }
      
      renderBenefitsTable(benefits);
    } catch (error) {
      // 仅在开发环境中打印错误
      showToast('error', '加载会员权益失败，请重试');
      renderEmptyTable('加载失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 渲染会员权益表格
   * @param {Array} data 会员权益数据
   */
  function renderBenefitsTable(data) {
    const tableBody = document.querySelector('#benefitsTable tbody');
    if (!tableBody) return;

    if (data.length === 0) {
      tableBody.innerHTML = `<tr><td colspan="8" class="text-center">暂无数据</td></tr>`;
      return;
    }

    const rows = data.map(benefit => {
      // 创建角色标签
      let roleLabel = '';
      switch (benefit.apply_role) {
        case 'all':
          roleLabel = '<span class="badge bg-secondary">所有用户</span>';
          break;
        case 'user':
          roleLabel = '<span class="badge bg-primary">普通用户</span>';
          break;
        case 'vip':
          roleLabel = '<span class="badge bg-warning">VIP用户</span>';
          break;
        case 'admin':
          roleLabel = '<span class="badge bg-danger">管理员</span>';
          break;
        default:
          roleLabel = '<span class="badge bg-secondary">未知</span>';
      }

      // 创建状态标签
      const statusLabel = benefit.is_active 
        ? '<span class="badge bg-success">启用</span>' 
        : '<span class="badge bg-danger">禁用</span>';

      return `
        <tr>
          <td>${benefit.id}</td>
          <td>${benefit.benefit_name}</td>
          <td><code>${benefit.benefit_code}</code></td>
          <td>${roleLabel}</td>
          <td><i class="bi bi-${benefit.benefit_icon}"></i> ${benefit.benefit_icon}</td>
          <td>${benefit.sort_order}</td>
          <td>${statusLabel}</td>
          <td>
            <button class="btn btn-sm btn-primary" onclick="memberBenefitsModule.editBenefit(${benefit.id})">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-danger ms-1" onclick="memberBenefitsModule.deleteBenefit(${benefit.id})">
              <i class="bi bi-trash"></i>
            </button>
            ${benefit.is_active 
              ? `<button class="btn btn-sm btn-warning ms-1" onclick="memberBenefitsModule.toggleBenefitStatus(${benefit.id}, false)">
                  <i class="bi bi-x-circle"></i>
                </button>` 
              : `<button class="btn btn-sm btn-success ms-1" onclick="memberBenefitsModule.toggleBenefitStatus(${benefit.id}, true)">
                  <i class="bi bi-check-circle"></i>
                </button>`
            }
          </td>
        </tr>
      `;
    }).join('');

    tableBody.innerHTML = rows;
  }

  /**
   * 渲染空表格
   * @param {string} message 显示的消息
   */
  function renderEmptyTable(message) {
    const tableBody = document.querySelector('#benefitsTable tbody');
    if (tableBody) {
      tableBody.innerHTML = `<tr><td colspan="8" class="text-center">${message}</td></tr>`;
    }
  }

  /**
   * 应用筛选条件
   */
  function applyFilters() {
    const roleFilter = document.getElementById('roleFilter');
    
    // 构建筛选条件
    currentFilters = {};
    
    if (roleFilter && roleFilter.value) {
      currentFilters.role = roleFilter.value;
    }
    
    // 重新加载数据
    loadBenefits();
  }

  /**
   * 显示会员权益模态框
   * @param {boolean} isEdit 是否是编辑模式
   * @param {Object} benefit 会员权益数据（编辑模式时使用）
   */
  function showBenefitModal(isEdit = false, benefit = null) {
    // 获取或创建模态框
    let modalEl = document.getElementById('benefitModal');
    
    if (!modalEl) {
      // 如果模态框不存在，创建它
      const modalHtml = `
        <div class="modal fade" id="benefitModal" tabindex="-1" aria-labelledby="benefitModalLabel" aria-hidden="true">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="benefitModalLabel">添加会员权益</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <form id="benefitForm">
                  <input type="hidden" id="benefitId">
                  <div class="mb-3">
                    <label for="benefitName" class="form-label">权益名称</label>
                    <input type="text" class="form-control" id="benefitName" required>
                  </div>
                  <div class="mb-3">
                    <label for="benefitCode" class="form-label">权益代码</label>
                    <input type="text" class="form-control" id="benefitCode" required pattern="[a-z0-9_]+" title="只能包含小写字母、数字和下划线">
                    <div class="form-text">权益的唯一标识，只能包含小写字母、数字和下划线</div>
                  </div>
                  <div class="mb-3">
                    <label for="benefitDesc" class="form-label">权益描述</label>
                    <textarea class="form-control" id="benefitDesc" rows="3"></textarea>
                  </div>
                  <div class="mb-3">
                    <label for="benefitIcon" class="form-label">权益图标</label>
                    <input type="text" class="form-control" id="benefitIcon" placeholder="例如: star" value="star">
                    <div class="form-text">Bootstrap Icons 图标名称，不需要包含 "bi-" 前缀</div>
                  </div>
                  <div class="mb-3">
                    <label for="applyRole" class="form-label">适用角色</label>
                    <select class="form-select" id="applyRole" required>
                      <option value="all">所有用户</option>
                      <option value="user">普通用户</option>
                      <option value="vip">VIP用户</option>
                      <option value="admin">管理员</option>
                    </select>
                  </div>
                  <div class="mb-3">
                    <label for="sortOrder" class="form-label">排序顺序</label>
                    <input type="number" class="form-control" id="sortOrder" min="0" value="0">
                  </div>
                  <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="isActive" checked>
                    <label class="form-check-label" for="isActive">
                      启用权益
                    </label>
                  </div>
                </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveBenefitBtn">保存</button>
              </div>
            </div>
          </div>
        </div>
      `;
      
      document.body.insertAdjacentHTML('beforeend', modalHtml);
      modalEl = document.getElementById('benefitModal');
      
      // 绑定保存按钮事件
      const saveBenefitBtn = document.getElementById('saveBenefitBtn');
      if (saveBenefitBtn) {
        saveBenefitBtn.addEventListener('click', saveBenefit);
      }
    }
    
    // 获取Bootstrap模态框实例
    const modal = new bootstrap.Modal(modalEl);
    
    // 更新模态框标题和表单
    const modalTitle = modalEl.querySelector('.modal-title');
    if (modalTitle) {
      modalTitle.textContent = isEdit ? '编辑会员权益' : '添加会员权益';
    }
    
    // 重置表单
    const benefitForm = document.getElementById('benefitForm');
    if (benefitForm) {
      benefitForm.reset();
    }
    
    // 设置表单值（编辑模式）
    if (isEdit && benefit) {
      document.getElementById('benefitId').value = benefit.id;
      document.getElementById('benefitName').value = benefit.benefit_name;
      document.getElementById('benefitCode').value = benefit.benefit_code;
      document.getElementById('benefitDesc').value = benefit.benefit_desc || '';
      document.getElementById('benefitIcon').value = benefit.benefit_icon || 'star';
      document.getElementById('applyRole').value = benefit.apply_role;
      document.getElementById('sortOrder').value = benefit.sort_order || 0;
      document.getElementById('isActive').checked = benefit.is_active;
      
      // 禁用代码字段（编辑模式下不允许修改代码）
      document.getElementById('benefitCode').readOnly = true;
    } else {
      // 启用代码字段（添加模式）
      const benefitCode = document.getElementById('benefitCode');
      if (benefitCode) {
        benefitCode.readOnly = false;
        benefitCode.value = '';
      }
      document.getElementById('benefitId').value = '';
    }
    
    // 记录当前状态
    isEditing = isEdit;
    currentBenefitId = isEdit ? benefit.id : null;
    
    // 显示模态框
    modal.show();
  }

  /**
   * 保存会员权益
   */
  async function saveBenefit() {
    try {
      // 获取表单元素
      const benefitForm = document.getElementById('benefitForm');
      
      // 验证表单
      if (!benefitForm.checkValidity()) {
        benefitForm.reportValidity();
        return;
      }
      
      // 收集表单数据
      const benefitId = document.getElementById('benefitId').value;
      const benefitData = {
        benefit_name: document.getElementById('benefitName').value,
        benefit_code: document.getElementById('benefitCode').value,
        benefit_desc: document.getElementById('benefitDesc').value,
        benefit_icon: document.getElementById('benefitIcon').value,
        apply_role: document.getElementById('applyRole').value,
        sort_order: parseInt(document.getElementById('sortOrder').value) || 0,
        is_active: document.getElementById('isActive').checked
      };
      
      showLoading();
      
      let response;
      
      if (isEditing && benefitId) {
        // 更新现有权益
        response = await fetch(`/api/admin/payment/benefits/${benefitId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(benefitData)
        });
      } else {
        // 创建新权益
        response = await fetch('/api/admin/payment/benefits', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(benefitData)
        });
      }
      
      if (!response.ok) {
        throw new Error(isEditing ? '更新会员权益失败' : '创建会员权益失败');
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast('success', isEditing ? '会员权益已更新' : '会员权益已创建');
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('benefitModal'));
        if (modal) {
          modal.hide();
        }
        
        // 重新加载数据
        loadBenefits();
      } else {
        showToast('error', data.message || (isEditing ? '更新会员权益失败' : '创建会员权益失败'));
      }
    } catch (error) {
      console.error(isEditing ? '更新会员权益失败:' : '创建会员权益失败:', error);
      showToast('error', isEditing ? '更新会员权益失败，请重试' : '创建会员权益失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 编辑会员权益
   * @param {number} id 会员权益ID
   */
  async function editBenefit(id) {
    try {
      showLoading();
      
      // 发送API请求获取权益详情
      const response = await fetch(`/api/admin/payment/benefits/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });
      
      if (!response.ok) {
        throw new Error('获取会员权益详情失败');
      }
      
      const data = await response.json();
      
      if (data.success) {
        // 显示编辑模态框
        showBenefitModal(true, data.data.benefit);
      } else {
        showToast('error', data.message || '获取会员权益详情失败');
      }
    } catch (error) {
      console.error('获取会员权益详情失败:', error);
      showToast('error', '获取会员权益详情失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 删除会员权益
   * @param {number} id 会员权益ID
   */
  async function deleteBenefit(id) {
    if (!confirm('确定要删除此会员权益吗？此操作不可恢复。')) {
      return;
    }
    
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch(`/api/admin/payment/benefits/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        }
      });
      
      if (!response.ok) {
        throw new Error('删除会员权益失败');
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast('success', '会员权益已删除');
        
        // 重新加载数据
        loadBenefits();
      } else {
        showToast('error', data.message || '删除会员权益失败');
      }
    } catch (error) {
      console.error('删除会员权益失败:', error);
      showToast('error', '删除会员权益失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 切换会员权益状态
   * @param {number} id 会员权益ID
   * @param {boolean} active 是否激活
   */
  async function toggleBenefitStatus(id, active) {
    try {
      showLoading();
      
      // 发送API请求
      const response = await fetch(`/api/admin/payment/benefits/${id}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        },
        body: JSON.stringify({ is_active: active })
      });
      
      if (!response.ok) {
        throw new Error('更新会员权益状态失败');
      }
      
      const data = await response.json();
      
      if (data.success) {
        showToast('success', active ? '会员权益已启用' : '会员权益已禁用');
        
        // 重新加载数据
        loadBenefits();
      } else {
        showToast('error', data.message || '更新会员权益状态失败');
      }
    } catch (error) {
      console.error('更新会员权益状态失败:', error);
      showToast('error', '更新会员权益状态失败，请重试');
    } finally {
      hideLoading();
    }
  }

  /**
   * 辅助函数：获取Token
   */
  function getToken() {
    return localStorage.getItem('token') || '';
  }

  /**
   * 辅助函数：显示Loading
   */
  function showLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.style.display = 'flex';
    }
  }

  /**
   * 辅助函数：隐藏Loading
   */
  function hideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
  }

  /**
   * 辅助函数：显示提示消息
   */
  function showToast(type, message) {
    if (window.utils && window.utils.showToast) {
      window.utils.showToast(type, message);
    } else {
      alert(message);
    }
  }

  /**
   * 辅助函数：监听页面可见性变化
   */
  function observePageVisibility(pageId, callback) {
    // 创建一个观察器实例
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
          const page = document.getElementById(pageId);
          if (page && !page.classList.contains('d-none')) {
            callback();
          }
        }
      });
    });

    // 配置观察选项
    const config = { attributes: true };

    // 开始观察目标节点
    const page = document.getElementById(pageId);
    if (page) {
      observer.observe(page, config);
    }
  }

  // 暴露公共方法
  return {
    init,
    loadBenefits,
    editBenefit,
    deleteBenefit,
    toggleBenefitStatus,
    saveBenefit
  };
})();

// 初始化模块
document.addEventListener('DOMContentLoaded', function() {
  memberBenefitsModule.init();
});

import { clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * 合并classnames的工具函数，支持条件性渲染
 * @param {...string} inputs - 一个或多个className字符串，可以包含条件表达式
 * @returns {string} - 合并后的className字符串
 */
export function cn(...inputs) {
  return twMerge(clsx(inputs))
}

/**
 * 对手机号进行脱敏处理，隐藏中间4位
 * @param {string} phone - 需要脱敏的手机号
 * @returns {string} - 脱敏后的手机号，格式为"前3位 + **** + 后4位"
 */
export function maskPhoneNumber(phone) {
  if (!phone) return '';

  // 确保输入是字符串
  const phoneStr = String(phone);

  // 如果长度小于8，不进行脱敏
  if (phoneStr.length < 8) return phoneStr;

  // 对于标准的11位中国手机号，隐藏中间4位
  if (phoneStr.length === 11) {
    return phoneStr.replace(/^(\d{3})(\d{4})(\d{4})$/, '$1****$3');
  }

  // 对于其他长度的号码，保留前3位和后4位，中间用星号代替
  const firstPart = phoneStr.substring(0, 3);
  const lastPart = phoneStr.substring(phoneStr.length - 4);
  return `${firstPart}****${lastPart}`;
}
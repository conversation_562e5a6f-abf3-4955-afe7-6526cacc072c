/**
 * Zoom utilities for managing content scaling in the preview area
 */

/**
 * Calculate the optimal scale based on content dimensions and available space
 * 
 * @param {number} contentWidth - Width of the content to be scaled
 * @param {number} contentHeight - Height of the content to be scaled
 * @param {HTMLElement} previewAreaRef - Reference to the preview area container
 * @returns {number} - The calculated optimal scale factor
 */
export const calculateOptimalScale = (contentWidth, contentHeight, previewAreaRef) => {
  if (!previewAreaRef || contentWidth <= 0 || contentHeight <= 0) {
    return 1; // 默认缩放比例为1
  }

  // 获取预览区域的尺寸
  const previewAreaRect = previewAreaRef.getBoundingClientRect();
  const previewAreaWidth = previewAreaRect.width;
  const previewAreaHeight = previewAreaRect.height;

  // 安全边距，确保内容不会太靠近边缘
  const safePadding = 40;
  const safeAreaWidth = previewAreaWidth - safePadding;
  const safeAreaHeight = previewAreaHeight - safePadding;

  // 计算宽度和高度的缩放比例
  const widthScale = safeAreaWidth / contentWidth;
  const heightScale = safeAreaHeight / contentHeight;

  // 取较小的缩放比例，确保内容完全适应预览区域，允许放大和缩小
  let optimalScale = Math.min(widthScale, heightScale);
  
  // 添加一个最大和最小的缩放限制，避免极端情况
  optimalScale = Math.max(0.1, Math.min(optimalScale, 2.5));

  return optimalScale;
};

/**
 * Handle scale change and update the scale state
 * 
 * @param {number} newScale - The new scale value
 * @param {Function} setScale - Function to update the scale state
 * @param {Function} setUserScale - Function to update the user scale state
 */
export const handleScaleChange = (newScale, setScale, setUserScale) => {
  setScale(newScale);
  setUserScale(newScale); // 记录用户手动设置的缩放比例
};

/**
 * Reset the scale to the optimal value or default
 * 
 * @param {Object} htmlContentSize - The size of the HTML content
 * @param {Function} calculateOptimalScale - Function to calculate the optimal scale
 * @param {Function} setScale - Function to update the scale state
 * @param {Function} setUserScale - Function to update the user scale state
 */
export const resetScale = (htmlContentSize, calculateOptimalScale, setScale, setUserScale) => {
  // 如果有HTML内容尺寸信息，计算并应用最佳缩放比例
  if (htmlContentSize && htmlContentSize.width > 0 && htmlContentSize.height > 0) {
    const optimalScale = calculateOptimalScale(htmlContentSize.width, htmlContentSize.height);
    setScale(optimalScale);
  } else {
    setScale(1); // 如果没有内容尺寸信息，则重置为1
  }
  setUserScale(null); // 清除用户手动设置的缩放比例
};

/**
 * Get the style for the preview container
 * 
 * @param {number} scale - The current scale value
 * @returns {Object} - The style object for the preview container
 */
export const getPreviewContainerStyle = (scale) => {
  return {
    width: 'auto', // 宽度由其内容（iframe）决定
    height: 'auto', // 高度由其内容（iframe）决定
    transform: `scale(${scale})`,
    transformOrigin: 'center center',
    position: 'relative',
    transition: 'transform 0.3s ease',
    overflow: 'visible', // 确保内容不被裁剪
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  };
};

/**
 * Get the style for the outer preview area
 * 
 * @param {boolean} isPreviewActive - Whether the preview is active
 * @returns {Object} - The style object for the outer preview area
 */
export const getOuterPreviewAreaStyle = (isPreviewActive) => {
  return {
    position: 'relative',
    width: isPreviewActive ? 'calc(100% - 320px)' : '100%',  // 根据编辑台是否显示调整宽度
    height: 'calc(100vh - 100px)', // 设置为视窗高度减去顶部区域高度
    minHeight: 'calc(100vh - 100px)', // 确保最小高度与高度一致
    overflow: 'visible', // 修改为visible，确保内容不被裁剪
    backgroundColor: 'transparent', // 完全透明，移除灰色背景
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center', // 垂直居中
    alignItems: 'center' // 水平居中
  };
}; 
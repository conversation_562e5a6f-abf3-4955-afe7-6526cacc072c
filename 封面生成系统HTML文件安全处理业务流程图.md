# 封面生成系统HTML文件安全处理业务流程图

## 流程图说明

此流程图展示了封面生成系统HTML文件安全处理的完整业务流程，包含7个核心步骤：

1. **Step1: 文件上传** - 用户上传HTML文件或粘贴代码
2. **Step2: 安全检测** - 使用可扩展的安全检测引擎进行全面检测
3. **Step3: 渲染方式决策** - 系统自动选择最佳渲染方式
4. **Step4: 用户确认** - 对未通过安全检测的文件提供用户选择
5. **Step5: 内容加载** - 在预览区域完美加载HTML内容
6. **Step6: 可视化编辑** - 提供完整的可视化编辑功能
7. **Step7: 数据存储** - 将处理完成的内容安全存储到数据库

## Mermaid流程图代码

```mermaid
flowchart TD
    A[用户已登录] --> B[Step1: 文件上传]
    B --> B1[支持HTML文件上传]
    B --> B2[支持代码粘贴输入]
    B1 --> C[Step2: 安全检测]
    B2 --> C
    
    C --> C1[XSS攻击检测]
    C --> C2[恶意脚本识别]
    C --> C3[危险标签检查]
    C --> C4[外部资源验证]
    
    C1 --> D{安全检测结果}
    C2 --> D
    C3 --> D
    C4 --> D
    
    D -->|通过| E[Step3: 渲染方式决策]
    D -->|未通过| F[记录到安全审查表]
    
    F --> G[Step4: 用户确认]
    G --> G1[简化安全风险提示]
    G1 --> G2{用户选择}
    G2 -->|采用安全加载模式| I1[客户端安全加载方式]
    G2 -->|放弃上传| J[终止流程]
    
    E --> E1[读取后台配置的默认渲染方式]
    E1 --> E2{配置的渲染方式}
    E2 -->|高级模式| E3[服务端渲染模式<br/>完整功能保留]
    E2 -->|标准模式| E4[客户端安全加载模式<br/>兼容现有系统]
    
    E3 --> K[Step5: 内容加载]
    E4 --> K
    I1 --> K
    
    K --> K1[在预览区域加载HTML]
    K1 --> K2[保持原始交互功能]
    K2 --> K3[智能识别可编辑区域]
    
    K3 --> L[Step6: 可视化编辑]
    L --> L1[文本内容编辑]
    L --> L2[样式调整功能]
    L --> L3[元素位置调整]
    L --> L4[保持编辑台UI不变]
    
    L1 --> M[Step7: 数据存储]
    L2 --> M
    L3 --> M
    L4 --> M
    
    M --> M1[原始HTML存储到<br/>original_html_content]
    M --> M2[编辑后HTML存储到<br/>edited_html_content]
    M --> M3[安全检测结果存储]
    M --> M4[渲染方式记录]
    
    M1 --> N[完成流程]
    M2 --> N
    M3 --> N
    M4 --> N
    
    style A fill:#e1f5fe
    style D fill:#fff3e0
    style H fill:#fff3e0
    style E1 fill:#f3e5f5
    style K2 fill:#e8f5e8
    style N fill:#e8f5e8
```

## 关键决策节点

### 1. 安全检测结果 (D)
- **通过检测**: 进入渲染方式决策阶段
- **未通过检测**: 记录到安全审查表，需要用户确认

### 2. 用户选择 (H)
- **确认使用**: 采用当前安全加载方式继续流程
- **取消操作**: 终止整个流程

### 3. 渲染方式决策 (E1)
- **服务端渲染模式**: 完整功能支持，适用于复杂HTML
- **客户端安全加载模式**: 功能受限但安全，适用于简单HTML

## 流程特点

1. **系统自动化**: 大部分步骤由系统自动执行，减少用户操作负担
2. **安全优先**: 未通过安全检测的文件不进入主数据库
3. **智能决策**: 根据HTML复杂度和安全性自动选择最佳渲染方式
4. **用户友好**: 提供清晰的风险提示和选择说明
5. **功能完整**: 保持所有现有编辑功能和UI布局不变

---

**创建时间**: 2025-01-28  
**版本**: v1.0  
**用途**: 封面生成系统HTML文件安全处理业务流程参考

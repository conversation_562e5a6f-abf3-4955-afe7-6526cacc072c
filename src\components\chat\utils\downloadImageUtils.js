import { message } from 'antd';
import * as htmlToImage from 'html-to-image';
import html2canvas from 'html2canvas';
import { cleanForDownload, cleanHtmlContent } from './downloadUtils';

const showLoading = (content = '正在处理...') => {
  message.loading({ content, duration: 0, key: 'downloadingMessage' });
};

const hideLoading = () => {
  message.destroy('downloadingMessage');
};

/**
 * 复制元素的所有背景相关计算样式
 * @param {HTMLElement} sourceElement - 源元素
 * @param {HTMLElement} targetElement - 目标元素
 * @param {boolean} debugMode - 是否启用调试模式
 */
const copyBackgroundStyles = (sourceElement, targetElement, debugMode = false) => {
  if (!sourceElement || !targetElement) return;
  
  const computedStyle = window.getComputedStyle(sourceElement);
  
  // 背景相关属性列表
  const backgroundProps = [
    'backgroundColor',
    'backgroundImage',
    'backgroundPosition',
    'backgroundPositionX',
    'backgroundPositionY',
    'backgroundRepeat',
    'backgroundSize',
    'backgroundOrigin',
    'backgroundClip',
    'backgroundAttachment',
    'background'
  ];
  
  // 复制所有背景相关属性
  for (const prop of backgroundProps) {
    const value = computedStyle[prop];
    if (value && value !== 'none' && value !== 'initial' && value !== 'unset') {
      targetElement.style[prop] = value;
      if (debugMode) console.log(`[copyBgStyles] Copied ${prop}: ${value} to`, targetElement);
    }
  }
};

/**
 * 确保容器元素有明确的背景
 * @param {HTMLElement} element - 要处理的元素
 * @param {boolean} debugMode - 是否启用调试模式
 */
const ensureElementBackground = (element, debugMode = false) => {
  if (!element) return;
  
  const computedStyle = window.getComputedStyle(element);
  const hasBackground = 
    computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' && 
    computedStyle.backgroundColor !== 'transparent';
  const hasBackgroundImage = 
    computedStyle.backgroundImage !== 'none';
  
  // 如果元素没有明确的背景，添加一个白色背景
  if (!hasBackground && !hasBackgroundImage) {
    element.style.backgroundColor = '#ffffff';
    if (debugMode) console.log('[ensureElementBackground] Added white background to element:', element);
  }
};

/**
 * 递归应用背景样式到元素树
 * @param {HTMLElement} sourceElement - 源元素
 * @param {HTMLElement} targetElement - 目标元素
 * @param {boolean} debugMode - 是否启用调试模式
 */
const recursivelyApplyBackgroundStyles = (sourceElement, targetElement, debugMode = false) => {
  if (!sourceElement || !targetElement) return;
  
  // 复制当前元素的背景样式
  copyBackgroundStyles(sourceElement, targetElement, debugMode);
  
  // 递归处理所有子元素
  const sourceChildren = sourceElement.children;
  const targetChildren = targetElement.children;
  
  for (let i = 0; i < Math.min(sourceChildren.length, targetChildren.length); i++) {
    recursivelyApplyBackgroundStyles(sourceChildren[i], targetChildren[i], debugMode);
  }
};

/**
 * 在临时iframe中应用额外的样式，确保渐变背景和其他视觉效果能够正确显示
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {boolean} debugMode - 是否启用调试模式
 */
const applyEnhancedStyles = (iframe, debugMode = false) => {
  if (!iframe || !iframe.contentDocument) return;
  
  try {
    const iframeDoc = iframe.contentDocument;
    
    // 1. 确保html和body元素具有完整的尺寸
    const htmlElement = iframeDoc.documentElement;
    const bodyElement = iframeDoc.body;
    
    if (htmlElement) {
      htmlElement.style.width = '100%';
      htmlElement.style.height = '100%';
      htmlElement.style.margin = '0';
      htmlElement.style.padding = '0';
    }
    
    if (bodyElement) {
      bodyElement.style.width = '100%';
      bodyElement.style.height = '100%';
      bodyElement.style.margin = '0';
      bodyElement.style.padding = '0';
    }
    
    // 2. 特别处理body和html元素的背景
    const bodyComputedStyle = window.getComputedStyle(bodyElement);
    const htmlComputedStyle = window.getComputedStyle(htmlElement);
    
    // 复制body背景样式
    bodyElement.style.backgroundColor = bodyComputedStyle.backgroundColor;
    bodyElement.style.backgroundImage = bodyComputedStyle.backgroundImage;
    bodyElement.style.backgroundRepeat = bodyComputedStyle.backgroundRepeat;
    bodyElement.style.backgroundPosition = bodyComputedStyle.backgroundPosition;
    bodyElement.style.backgroundSize = bodyComputedStyle.backgroundSize;
    bodyElement.style.backgroundAttachment = 'scroll';
    bodyElement.style.backgroundClip = 'border-box';
    
    // 复制html背景样式
    htmlElement.style.backgroundColor = htmlComputedStyle.backgroundColor;
    htmlElement.style.backgroundImage = htmlComputedStyle.backgroundImage;
    htmlElement.style.backgroundRepeat = htmlComputedStyle.backgroundRepeat;
    htmlElement.style.backgroundPosition = htmlComputedStyle.backgroundPosition;
    htmlElement.style.backgroundSize = htmlComputedStyle.backgroundSize;
    htmlElement.style.backgroundAttachment = 'scroll';
    htmlElement.style.backgroundClip = 'border-box';
    
    if (debugMode) {
      console.log('[applyEnhancedStyles] Body background:', bodyComputedStyle.backgroundImage);
      console.log('[applyEnhancedStyles] HTML background:', htmlComputedStyle.backgroundImage);
    }
    
    // 3. 查找并确保所有元素的背景样式都被正确应用
    const allElements = iframeDoc.querySelectorAll('*');
    allElements.forEach(el => {
      const computedStyle = window.getComputedStyle(el);
      const backgroundImage = computedStyle.backgroundImage;
      const backgroundColor = computedStyle.backgroundColor;
      
      // 检查是否包含渐变背景或非透明背景色
      if (
        (backgroundImage && (
          backgroundImage.includes('gradient') || 
          backgroundImage.includes('linear-gradient') || 
          backgroundImage.includes('radial-gradient')
        )) || 
        (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent')
      ) {
        // 确保元素有明确的尺寸
        if (!el.style.width || el.style.width === 'auto') {
          el.style.width = `${el.offsetWidth}px`;
        }
        if (!el.style.height || el.style.height === 'auto') {
          el.style.height = `${el.offsetHeight}px`;
        }
        
        // 确保背景样式被正确应用
        el.style.backgroundImage = backgroundImage;
        el.style.backgroundColor = backgroundColor;
        el.style.backgroundRepeat = computedStyle.backgroundRepeat;
        el.style.backgroundPosition = computedStyle.backgroundPosition;
        el.style.backgroundSize = computedStyle.backgroundSize;
        el.style.backgroundAttachment = 'scroll';
        el.style.backgroundClip = 'border-box';
        
        if (debugMode && backgroundImage.includes('gradient')) {
          console.log(`[applyEnhancedStyles] Applied gradient background to element:`, el);
        }
      }
    });
    
    // 4. 特别处理内容容器
    const contentContainer = iframeDoc.querySelector('.content-container');
    if (contentContainer) {
      const computedStyle = window.getComputedStyle(contentContainer);
      
      // 确保内容容器有明确的背景
      contentContainer.style.backgroundColor = computedStyle.backgroundColor;
      contentContainer.style.backgroundImage = computedStyle.backgroundImage;
      contentContainer.style.backgroundRepeat = computedStyle.backgroundRepeat;
      contentContainer.style.backgroundPosition = computedStyle.backgroundPosition;
      contentContainer.style.backgroundSize = computedStyle.backgroundSize;
      contentContainer.style.backgroundAttachment = 'scroll';
      contentContainer.style.backgroundClip = 'border-box';
      
      // 确保内容容器有明确的尺寸
      contentContainer.style.width = `${contentContainer.offsetWidth}px`;
      contentContainer.style.height = `${contentContainer.offsetHeight}px`;
      
      if (debugMode) {
        console.log('[applyEnhancedStyles] Content container background:', computedStyle.backgroundImage);
      }
    }
    
    // 5. 添加一个全局样式，确保所有元素的背景渐变都能正确显示
    const styleElement = iframeDoc.createElement('style');
    styleElement.textContent = `
      /* 确保渐变背景正确显示 */
      html, body, .content-container, [style*="linear-gradient"], [style*="radial-gradient"] {
        background-attachment: scroll !important;
        background-clip: border-box !important;
      }
      
      /* 确保所有元素都有正确的盒模型 */
      * {
        box-sizing: border-box !important;
      }
      
      /* 确保内容容器正确显示 */
      .content-container {
        position: relative !important;
        overflow: visible !important;
      }
      
      /* 确保tiktok-cover元素正确显示 */
      .tiktok-cover, .card {
        background-attachment: scroll !important;
        background-clip: border-box !important;
      }
    `;
    iframeDoc.head.appendChild(styleElement);
    
    if (debugMode) console.log('[applyEnhancedStyles] Applied enhanced styles to iframe');
    
  } catch (error) {
    if (debugMode) console.error('[applyEnhancedStyles] Error applying enhanced styles:', error);
  }
};

/**
 * 获取完整的HTML文档字符串，包括所有样式表和动态生成的样式
 * @param {Document} doc - 文档对象
 * @returns {string} 完整的HTML文档字符串
 */
const getFullHtmlContent = (doc) => {
  // 获取DOCTYPE
  const doctype = doc.doctype ? 
    new XMLSerializer().serializeToString(doc.doctype) : 
    '<!DOCTYPE html>';
  
  // 创建一个深拷贝的HTML元素，以便我们可以修改它而不影响原始文档
  const clonedHtml = doc.documentElement.cloneNode(true);
  
  // 确保所有样式表都被包含
  const styleSheets = doc.styleSheets;
  const headElement = clonedHtml.querySelector('head');
  
  if (headElement && styleSheets && styleSheets.length > 0) {
    // 处理所有样式表
    for (let i = 0; i < styleSheets.length; i++) {
      try {
        const sheet = styleSheets[i];
        
        // 如果是外部样式表，确保它被包含
        if (sheet.href) {
          // 检查是否已经有这个样式表的link元素
          const existingLink = headElement.querySelector(`link[href="${sheet.href}"]`);
          if (!existingLink) {
            const linkElement = doc.createElement('link');
            linkElement.rel = 'stylesheet';
            linkElement.href = sheet.href;
            headElement.appendChild(linkElement);
          }
        } 
        // 如果是内部样式表或动态生成的样式，确保它们的规则被包含
        else if (sheet.cssRules && sheet.cssRules.length > 0) {
          // 检查是否已经有一个包含相同规则的style元素
          let cssText = '';
          for (let j = 0; j < sheet.cssRules.length; j++) {
            cssText += sheet.cssRules[j].cssText + '\n';
          }
          
          if (cssText.trim()) {
            const styleElement = doc.createElement('style');
            styleElement.textContent = cssText;
            headElement.appendChild(styleElement);
          }
        }
      } catch (e) {
        // 跨域限制可能会导致访问cssRules出错，忽略这些错误
        console.warn('无法访问样式表规则，可能是由于跨域限制:', e);
      }
    }
  }
  
  // 获取修改后的HTML内容
  const htmlContent = clonedHtml.outerHTML;
  
  return `${doctype}\n${htmlContent}`;
};

/**
 * 用一个带有提示信息的、尺寸正确的Canvas占位符替换指定的img元素。
 * @param {HTMLImageElement} imgNode - The image element to replace.
 * @param {boolean} debugMode - Enable debug logging.
 */
function replaceWithPlaceholder(imgNode, debugMode) {
  const { offsetWidth, offsetHeight } = imgNode;
  if (debugMode) console.log(`[replaceWithPlaceholder] Creating placeholder for image with dimensions: ${offsetWidth}x${offsetHeight}`);

  if (offsetWidth > 0 && offsetHeight > 0) {
    const canvas = document.createElement('canvas');
    canvas.width = offsetWidth;
    canvas.height = offsetHeight;
    const ctx = canvas.getContext('2d');
  
    // 绘制背景
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, offsetWidth, offsetHeight);

    // 绘制文本
    ctx.fillStyle = '#a0a0a0';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    const fontSize = Math.min(offsetWidth / 10, offsetHeight / 5, 16);
    ctx.font = `bold ${fontSize}px sans-serif`;
    ctx.fillText('图片加载失败', offsetWidth / 2, offsetHeight / 2);

    try {
      imgNode.src = canvas.toDataURL('image/png');
      imgNode.onerror = null; // 移除处理器，防止循环
    } catch (e) {
      if (debugMode) console.error('[replaceWithPlaceholder] Failed to set data URL:', e);
      // 最终备用
      imgNode.src = 'data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=';
    }
        } else {
     if (debugMode) console.warn('[replaceWithPlaceholder] Image has no dimensions, using 1x1 pixel.');
     imgNode.src = 'data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs='; // 透明像素
  }
}

/**
 * 等待iframe中的所有资源（特别是图片和字体）加载完成。
 * 对于跨域图片，它会尝试加载，如果失败（由于CORS限制），
 * 则会用一个尺寸正确的占位符替换它。
 * @param {HTMLIFrameElement} iframe - The iframe element.
 * @param {boolean} debugMode - Enable debug logging.
 * @returns {Promise<void>} A promise that resolves when all resources are ready.
 */
export const waitForIframeResources = (iframe, debugMode = false) => {
  return new Promise((resolve) => {
    const check = () => {
      if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
        if (debugMode) console.log('[waitForIframeResources] Iframe content document is complete.');

        const doc = iframe.contentDocument;
        const images = Array.from(doc.querySelectorAll('img'));
        const promises = [];

        // 1. 等待字体加载
        if (doc.fonts && doc.fonts.ready) {
          promises.push(doc.fonts.ready);
        }

        // 2. 处理并等待所有图片
        images.forEach(imgNode => {
          promises.push(new Promise(resolveImage => {
            if ((imgNode.complete && imgNode.naturalWidth !== 0) || imgNode.src.startsWith('data:')) {
              if (debugMode) console.log('[waitForIframeResources] Image already complete or is data URI:', imgNode.src.substring(0, 100));
              return resolveImage();
            }

            const tester = new Image();
            tester.crossOrigin = 'Anonymous';
            const originalSrc = imgNode.src;

            tester.onload = () => {
              if (debugMode) console.log('[waitForIframeResources] CORS-compliant image loaded via tester:', originalSrc.substring(0, 100));
              if (imgNode.complete) {
                resolveImage();
              } else {
                imgNode.onload = resolveImage;
                imgNode.onerror = () => {
                   replaceWithPlaceholder(imgNode, debugMode);
                   resolveImage();
                }
              }
            };

            tester.onerror = () => {
              if (debugMode) console.warn('[waitForIframeResources] CORS or load error for image:', originalSrc.substring(0, 100));
              replaceWithPlaceholder(imgNode, debugMode);
              resolveImage();
            };
            
            if (originalSrc) {
              tester.src = originalSrc;
  } else {
              // 如果没有src，直接认为完成
              resolveImage();
            }
          }));
        });

        Promise.all(promises)
          .then(() => {
            if (debugMode) console.log('[waitForIframeResources] All resources are now ready.');
            // 在所有资源处理完毕后，再等待一个宏任务周期，以确保DOM更新被渲染
            setTimeout(() => resolve(), 100);
          })
          .catch(err => {
            if (debugMode) console.error('[waitForIframeResources] Error waiting for resources:', err);
            setTimeout(() => resolve(), 100);
          });

      } else {
        setTimeout(check, 100);
      }
    };

    if (iframe.contentDocument) {
      check();
    } else {
      iframe.onload = check;
    }
  });
};

/**
 * 专门处理渐变背景，确保在iframe中正确显示
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {boolean} debugMode - 是否启用调试模式
 */
const processGradientBackgrounds = (iframe, debugMode = false) => {
  if (!iframe || !iframe.contentDocument) return;
  
  try {
    const iframeDoc = iframe.contentDocument;
    const iframeWin = iframe.contentWindow;
    
    // 获取所有元素
    const allElements = iframeDoc.querySelectorAll('*');
    let gradientElementsCount = 0;
    
    // 遍历所有元素，检查是否有渐变背景
    allElements.forEach(el => {
      try {
        // 获取计算样式
        const computedStyle = iframeWin.getComputedStyle(el);
        const backgroundImage = computedStyle.backgroundImage;
        
        // 检查是否包含渐变背景
        if (backgroundImage && (
          backgroundImage.includes('gradient') || 
          backgroundImage.includes('linear-gradient') || 
          backgroundImage.includes('radial-gradient')
        )) {
          gradientElementsCount++;
          
          // 直接设置内联样式，确保渐变背景能够正确显示
          el.style.backgroundImage = backgroundImage;
          el.style.backgroundRepeat = computedStyle.backgroundRepeat;
          el.style.backgroundPosition = computedStyle.backgroundPosition;
          el.style.backgroundSize = computedStyle.backgroundSize;
          el.style.backgroundAttachment = 'scroll';
          el.style.backgroundClip = 'border-box';
          
          // 确保元素有明确的尺寸
          if (!el.style.width || el.style.width === 'auto') {
            el.style.width = `${el.offsetWidth}px`;
          }
          if (!el.style.height || el.style.height === 'auto') {
            el.style.height = `${el.offsetHeight}px`;
          }
          
          if (debugMode) {
            console.log(`[processGradientBackgrounds] 处理渐变背景元素:`, el);
            console.log(`[processGradientBackgrounds] 背景图像:`, backgroundImage);
          }
        }
      } catch (error) {
        if (debugMode) {
          console.warn(`[processGradientBackgrounds] 处理元素时出错:`, error);
        }
      }
    });
    
    // 特别处理html和body元素
    const htmlElement = iframeDoc.documentElement;
    const bodyElement = iframeDoc.body;
    
    // 获取html和body的计算样式
    const htmlComputedStyle = iframeWin.getComputedStyle(htmlElement);
    const bodyComputedStyle = iframeWin.getComputedStyle(bodyElement);
    
    // 检查html元素是否有渐变背景
    const htmlBackgroundImage = htmlComputedStyle.backgroundImage;
    if (htmlBackgroundImage && htmlBackgroundImage.includes('gradient')) {
      htmlElement.style.backgroundImage = htmlBackgroundImage;
      htmlElement.style.backgroundRepeat = htmlComputedStyle.backgroundRepeat;
      htmlElement.style.backgroundPosition = htmlComputedStyle.backgroundPosition;
      htmlElement.style.backgroundSize = htmlComputedStyle.backgroundSize;
      htmlElement.style.backgroundAttachment = 'scroll';
      htmlElement.style.backgroundClip = 'border-box';
      gradientElementsCount++;
      
      if (debugMode) {
        console.log(`[processGradientBackgrounds] 处理HTML元素渐变背景:`, htmlBackgroundImage);
      }
    }
    
    // 检查body元素是否有渐变背景
    const bodyBackgroundImage = bodyComputedStyle.backgroundImage;
    if (bodyBackgroundImage && bodyBackgroundImage.includes('gradient')) {
      bodyElement.style.backgroundImage = bodyBackgroundImage;
      bodyElement.style.backgroundRepeat = bodyComputedStyle.backgroundRepeat;
      bodyElement.style.backgroundPosition = bodyComputedStyle.backgroundPosition;
      bodyElement.style.backgroundSize = bodyComputedStyle.backgroundSize;
      bodyElement.style.backgroundAttachment = 'scroll';
      bodyElement.style.backgroundClip = 'border-box';
      gradientElementsCount++;
      
      if (debugMode) {
        console.log(`[processGradientBackgrounds] 处理BODY元素渐变背景:`, bodyBackgroundImage);
      }
    }
    
    // 添加一个全局样式，确保所有渐变背景能够正确显示
    const styleElement = iframeDoc.createElement('style');
    styleElement.textContent = `
      /* 确保渐变背景正确显示 */
      [style*="linear-gradient"], [style*="radial-gradient"] {
        background-attachment: scroll !important;
        background-clip: border-box !important;
      }
      
      /* 确保html和body元素的渐变背景正确显示 */
      html, body {
        background-attachment: scroll !important;
        background-clip: border-box !important;
      }
    `;
    iframeDoc.head.appendChild(styleElement);
    
    if (debugMode) {
      console.log(`[processGradientBackgrounds] 共处理了 ${gradientElementsCount} 个渐变背景元素`);
    }
    
  } catch (error) {
    if (debugMode) {
      console.error(`[processGradientBackgrounds] 处理渐变背景时出错:`, error);
    }
  }
};

/**
 * Handles CSS properties that are poorly supported by screenshot libraries.
 * @param {HTMLDocument} doc - The document to process.
 * @param {boolean} debugMode - Enable debug logging.
 */
const normalizeUnsupportedStyles = (doc, debugMode = false) => {
    if (!doc) return;
    try {
        const allElements = doc.querySelectorAll('*');
        allElements.forEach(el => {
            const computedStyle = window.getComputedStyle(el);

            // 1. 处理text-decoration，使用border-bottom代替，确保下划线可见
            if (computedStyle.textDecoration.includes('underline')) {
                // 保留原始颜色
                const textColor = computedStyle.color;
                // 设置border-bottom以模拟下划线
                el.style.textDecoration = 'none';  // 移除原始下划线
                el.style.borderBottom = `2px solid ${textColor}`;
                el.style.paddingBottom = '2px';
                
                if (debugMode) {
                    console.log(`[normalizeUnsupportedStyles] Replaced text-decoration:underline with border-bottom on:`, el);
                }
            }

            // 2. Handle 'background-clip: text' for Webkit and standard
            const backgroundClip = computedStyle.getPropertyValue('background-clip') || computedStyle.getPropertyValue('-webkit-background-clip');
            if (backgroundClip === 'text') {
                if (debugMode) console.log(`[normalizeUnsupported] Found 'background-clip: text' on`, el);
                
                // 获取背景信息
                const backgroundImage = computedStyle.getPropertyValue('background-image');
                
                // 如果是渐变背景
                if (backgroundImage.includes('gradient')) {
                    // 提取渐变中的颜色
                    const colorStops = backgroundImage.match(/#[0-9a-fA-F]{3,6}|rgba?\([^)]+\)/g);
                    
                    if (colorStops && colorStops.length > 0) {
                        // 使用第一个颜色作为文本颜色
                        el.style.color = colorStops[0];
                        
                        // 移除背景相关属性
                        el.style.background = 'none';
                        el.style.webkitBackgroundClip = 'unset';
                        el.style.backgroundClip = 'unset';
                        
                        if (debugMode) {
                            console.log(`[normalizeUnsupportedStyles] Set text color to gradient's first color: ${colorStops[0]}`);
                        }
                    }
                } else {
                    // 非渐变背景，保留当前文本颜色
                    const currentColor = computedStyle.color;
                    el.style.background = 'none';
                    el.style.webkitBackgroundClip = 'unset';
                    el.style.backgroundClip = 'unset';
                    el.style.color = currentColor;
                }
            }
            
            // 3. 移除可能影响布局的属性
            if (computedStyle.getPropertyValue('overflow-wrap') === 'break-word' ||
                computedStyle.getPropertyValue('word-break') === 'break-all') {
                
                el.style.removeProperty('overflow-wrap');
                el.style.removeProperty('word-break');
                
                // 确保空白处理正确
                el.style.whiteSpace = 'normal';
                
                if (debugMode) {
                    console.log(`[normalizeUnsupportedStyles] Removed problematic word-break/overflow-wrap from:`, el);
                }
            }
        });
    } catch (e) {
        if (debugMode) console.error('[normalizeUnsupported] Error normalizing styles:', e);
    }
}

/**
 * 增强版的截图和下载函数，使用srcdoc iframe方法实现真正的所见即所得
 * @param {HTMLElement} targetElement - 目标元素
 * @param {string} fileName - 文件名
 * @param {boolean} debugMode - 是否启用调试模式
 * @param {number} outputWidth - 输出宽度
 * @param {number} outputHeight - 输出高度
 */
export const enhancedCaptureAndDownloadContent = async (
  targetElement,
  fileName,
  debugMode = false,
  outputWidth, 
  outputHeight
) => {
  if (debugMode) {
    console.log('[enhancedCapture] Entry. Target:', targetElement, 'Filename:', fileName, `OutputDims: ${outputWidth}x${outputHeight}`);
  }
  if (!targetElement) {
    console.error('[enhancedCapture] Target element is null or undefined.');
    message.error('图片目标元素无效。');
    hideLoading(); // 确保在早期退出时也隐藏loading
    return;
  }

  // 获取目标元素所在的文档
  const targetDocument = targetElement.ownerDocument;
  let tempIframe = null;

  try {
    // 1. 获取完整的HTML内容
    let fullHtmlContent = getFullHtmlContent(targetDocument);
    
    // 1.1 清理HTML内容中的overflow-wrap和word-break样式
    if (typeof fullHtmlContent === 'string') {
      // 通过临时解析为DOM并清理
      const parser = new DOMParser();
      const tempDoc = parser.parseFromString(fullHtmlContent, 'text/html');
      
      // 清理所有元素的overflow-wrap和word-break样式
      const allElements = tempDoc.querySelectorAll('*');
      allElements.forEach(el => {
        if (el.style) {
          el.style.removeProperty('overflow-wrap');
          el.style.removeProperty('word-break');
        }
        
        // 删除style属性中的overflow-wrap和word-break内联样式
        if (el.hasAttribute('style')) {
          const style = el.getAttribute('style');
          if (style) {
            const newStyle = style
              .replace(/overflow-wrap\s*:\s*[^;]+;?/gi, '')
              .replace(/word-break\s*:\s*[^;]+;?/gi, '')
              .trim();
            
            if (newStyle.length > 0) {
              el.setAttribute('style', newStyle);
            } else {
              el.removeAttribute('style');
            }
          }
        }
      });
      
      // 重新序列化为HTML字符串
      fullHtmlContent = new XMLSerializer().serializeToString(tempDoc);
    }
    
    if (debugMode) console.log('[enhancedCapture] Got full HTML content and cleaned styles');

    // 2. 创建一个临时iframe，设置与原始iframe一致的尺寸和样式
    tempIframe = document.createElement('iframe');
    tempIframe.style.position = 'fixed';
    tempIframe.style.left = '-9999px';
    tempIframe.style.top = '-9999px';
    tempIframe.style.width = `${outputWidth}px`;
    tempIframe.style.height = `${outputHeight}px`;
    tempIframe.style.border = 'none';
    tempIframe.style.visibility = 'hidden';
    tempIframe.style.overflow = 'hidden';
    // 不设置背景色，让iframe使用实际的背景色
    
    // 3. 将iframe添加到文档中
    document.body.appendChild(tempIframe);
    if (debugMode) console.log('[enhancedCapture] Created temporary iframe');

    // 4. 等待iframe加载完成
    await new Promise((resolve) => {
      tempIframe.onload = resolve;
      // 使用srcdoc设置iframe内容
      tempIframe.srcdoc = fullHtmlContent;
    });
    
    if (debugMode) console.log('[enhancedCapture] Temporary iframe loaded');

    // 5. Apply enhanced styles
    applyEnhancedStyles(tempIframe, debugMode);
    
    // 5.1 Simulate pseudo-elements to make them capturable
    simulatePseudoElements(tempIframe.contentDocument.body, debugMode);
    
    // 5.2 Normalize unsupported styles like background-clip: text
    normalizeUnsupportedStyles(tempIframe.contentDocument, debugMode);

    // 5.3 Process gradient backgrounds
    processGradientBackgrounds(tempIframe, debugMode);

    // 6. Wait for all resources to load
    await waitForIframeResources(tempIframe, debugMode);
    
    // 7. 对临时iframe中的整个文档进行截图
    const captureOptions = {
      quality: 1,
      pixelRatio: 1, // 使用1而不是devicePixelRatio，确保尺寸一致
      backgroundColor: null, // 使用null而不是白色，让html-to-image使用实际的背景色
      style: {
        margin: '0',
        padding: '0',
        boxSizing: 'border-box',
      },
      filter: (node) => {
        if (node.tagName === 'SCRIPT' || node.tagName === 'NOSCRIPT') {
          return false;
        }
        return true;
      },
      skipFonts: false, // 不跳过字体，确保文本渲染正确
      timeout: 30000,
      cacheBust: true, // 添加缓存破坏参数，避免缓存问题
      // 处理跨域图片问题
      imagePlaceholder: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAAnElEQVR42u3RAQ0AAAjDMO5fNCCDkC5zVwLJ0wEIghCEIAQhCEEQgiAEIQhBCEIQBCEIQQhCEIIQBEEIghCEIAQhCIIQBCEIQQhCEAQhCEIQghCEIQgBCEIQRCEIAhBCEIQgiAIQRCCEIQgBEEQgiAEIQhBCIIgBEEIghCEIARBEIIgBCEIQQiCIARBCEIQghCEIAhCEIQgBPlfwADBTwBQyGRhvwAAAABJRU5ErkJggg==', // 添加默认占位符图片
      // 处理跨域图片
      fetchOptions: {
        mode: 'cors',
        credentials: 'same-origin'
      }
    };

    if (debugMode) console.log('[enhancedCapture] Calling htmlToImage.toPng on temporary iframe document...');
    
    // 8. 生成图片，添加重试机制
    let dataUrl = null;
    let retryCount = 0;
    const maxRetries = 3;
    
    // 使用整个body作为截图目标，而不仅仅是内容容器
    const targetNode = tempIframe.contentDocument.body;
    
    while (!dataUrl && retryCount < maxRetries) {
      try {
        if (retryCount > 0) {
          if (debugMode) console.log(`[enhancedCapture] Retrying screenshot (${retryCount}/${maxRetries})...`);
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        dataUrl = await htmlToImage.toPng(targetNode, captureOptions);
      } catch (error) {
        if (debugMode) console.error(`[enhancedCapture] Screenshot attempt ${retryCount + 1} failed:`, error);
        retryCount++;
        if (retryCount >= maxRetries) {
          throw new Error(`截图失败，已重试${maxRetries}次: ${error.message || '未知错误'}`);
        }
      }
    }

    if (!dataUrl || dataUrl === 'data:,') {
      throw new Error('生成的图片数据为空，无法下载。');
    }

    // 9. 创建下载链接
    if (debugMode) console.log('[enhancedCapture] Screenshot generated, creating download link...');
    const link = document.createElement('a');
    link.download = fileName;
    link.href = dataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    if (debugMode) console.log('[enhancedCapture] Download triggered.');

  } catch (error) {
    console.error('[enhancedCapture] Error during image generation or download:', error);
    message.error(`截图失败: ${error.message || '未知错误'}`);
    throw error;
  } finally {
    // 10. 清理临时iframe，确保在任何情况下都会执行
    if (tempIframe && tempIframe.parentNode) {
      tempIframe.parentNode.removeChild(tempIframe);
      if (debugMode) console.log('[enhancedCapture] Temporary iframe removed.');
    }
  }
};

/**
 * 处理截图下载，使用增强版的截图函数
 * @param {React.RefObject} iframeRef - iframe的ref对象
 * @param {Function} setIsDownloading - 设置下载状态的函数
 * @param {string} fileName - 文件名
 * @param {boolean} debugMode - 是否启用调试模式
 * @param {Object} naturalDimensions - 自然尺寸 { width, height }
 * @param {number} scale - 当前缩放比例
 */
export const handleScreenshotDownload = async (
  iframeRef, // 这是 ref 对象
  setIsDownloading,
  fileName = 'download.png',
  debugMode = false,
  naturalDimensions, // 自然尺寸 { width, height } 
  scale = 1 // 当前缩放比例
) => {
  if (debugMode) {
    console.log('[handleScreenshotDownload] Entry. Ref:', iframeRef, 'Filename:', fileName, 'Dimensions:', naturalDimensions, 'Scale:', scale);
  }
  
  if (!iframeRef || !iframeRef.current) {
    console.error('[handleScreenshotDownload] iframeRef is null or undefined.');
    message.error('截图目标无效。');
    return;
  }

  try {
    setIsDownloading(true);
    showLoading('正在保存图片...');

    const iframe = iframeRef.current;
    const contentDocument = iframe.contentDocument;
    
    if (!contentDocument) {
      throw new Error('无法访问iframe内容。');
    }

    // 获取内容容器
    const contentContainer = contentDocument.querySelector('.content-container');
    if (!contentContainer) {
      throw new Error('找不到内容容器。');
    }

    // 优先使用naturalDimensions作为输出尺寸，如果有效的话
    let displayWidth, displayHeight;
    if (naturalDimensions && naturalDimensions.width > 0 && naturalDimensions.height > 0) {
      displayWidth = naturalDimensions.width;
      displayHeight = naturalDimensions.height;
    } else {
      // 如果naturalDimensions无效，才使用内容容器的实际尺寸作为备选
      const containerRect = contentContainer.getBoundingClientRect();
      displayWidth = containerRect.width;
      displayHeight = containerRect.height;
    }
    
    // 记录尺寸信息，用于调试
    if (debugMode) {
      console.log(`[handleScreenshotDownload] Using dimensions: ${displayWidth}x${displayHeight}`);
      if (naturalDimensions && naturalDimensions.width > 0 && naturalDimensions.height > 0) {
        console.log(`[handleScreenshotDownload] Source: naturalDimensions`);
      } else {
        console.log(`[handleScreenshotDownload] Source: containerRect (fallback)`);
      }
    }

    if (debugMode) {
      console.log(`[handleScreenshotDownload] Output dimensions: ${displayWidth}x${displayHeight}`);
    }

    try {
      // 主要截图方法：使用增强版的截图函数，传递整个文档元素
    await enhancedCaptureAndDownloadContent(
      iframe.contentDocument.documentElement, // 传递整个文档元素
      fileName,
      debugMode,
      displayWidth,
      displayHeight
    );

    message.success('图片已下载');
    } catch (mainError) {
      if (debugMode) {
        console.error('[handleScreenshotDownload] Primary screenshot method failed:', mainError);
      }
      
      // 尝试备用方案：使用canvas直接绘制iframe内容
      try {
        if (debugMode) {
          console.log('[handleScreenshotDownload] Trying fallback screenshot method...');
        }
        message.info('正在尝试备用截图方法...');
        
        // 预处理跨域图片，防止canvas污染
        const images = contentDocument.querySelectorAll('img');
        await Promise.all(Array.from(images).map(img => {
          return new Promise(resolve => {
            // 如果图片来自外部域，尝试转换为本地占位符
            if (img.src && !img.src.startsWith('data:') && !img.src.startsWith('blob:')) {
              const originalSrc = img.src;
              // 创建canvas生成占位符
              const canvas = document.createElement('canvas');
              canvas.width = img.width || 100;
              canvas.height = img.height || 100;
              const ctx = canvas.getContext('2d');
              ctx.fillStyle = '#f0f0f0';
              ctx.fillRect(0, 0, canvas.width, canvas.height);
              ctx.fillStyle = '#888';
              ctx.textAlign = 'center';
              ctx.font = '14px Arial';
              ctx.fillText('图片内容', canvas.width / 2, canvas.height / 2);
              
              try {
                img.src = canvas.toDataURL();
                if (debugMode) {
                  console.log(`[handleScreenshotDownload] Replaced cross-origin image: ${originalSrc}`);
                }
              } catch (e) {
                if (debugMode) {
                  console.warn(`[handleScreenshotDownload] Failed to create placeholder for: ${originalSrc}`, e);
                }
              }
            }
            
            if (img.complete) {
              resolve();
            } else {
              img.onload = resolve;
              img.onerror = resolve;
            }
          });
        }));
        
                 // 等待一小段时间确保DOM更新
         await new Promise(resolve => setTimeout(resolve, 100));
         
         // 使用html2canvas库作为备用方案
         const canvas = await html2canvas(contentContainer, {
           scale: 1, // 使用1而不是devicePixelRatio，确保尺寸一致
           useCORS: true, // 尝试使用CORS加载图片
           allowTaint: true, // 允许加载跨域图片（可能导致canvas污染）
           backgroundColor: '#ffffff',
           logging: debugMode, // 仅在调试模式下输出日志
           // 忽略UI控件，只截取实际内容
           ignoreElements: (element) => {
             return element.classList && 
               (element.classList.contains('control-overlay') || 
                element.classList.contains('editor-controls') ||
                element.tagName === 'BUTTON');
           },
           // 应用额外样式以确保截图质量
           onclone: (clonedDoc) => {
             if (debugMode) {
               console.log('[handleScreenshotDownload] html2canvas cloning document');
             }
             // 确保文本显示正确
             const textElements = clonedDoc.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div');
             textElements.forEach(el => {
               // 如果文本元素没有明确的颜色设置，添加默认颜色
               if (!el.style.color && !window.getComputedStyle(el).color) {
                 el.style.color = '#000000';
               }
             });
           }
         });
         
         // 转换为PNG并下载
         try {
           const dataUrl = canvas.toDataURL('image/png');
           const link = document.createElement('a');
           link.download = fileName;
           link.href = dataUrl;
           document.body.appendChild(link);
           link.click();
           document.body.removeChild(link);
          
          message.success('图片已使用备用方法下载');
          if (debugMode) {
            console.log('[handleScreenshotDownload] Fallback screenshot successful');
          }
        } catch (canvasError) {
          throw new Error(`备用截图方法失败: ${canvasError.message || '未知错误'}`);
        }
      } catch (fallbackError) {
        // 如果备用方案也失败，则使用最简单的方法：创建一个带文本的图片
        if (debugMode) {
          console.error('[handleScreenshotDownload] Fallback method failed:', fallbackError);
        }
        
        try {
          if (debugMode) {
            console.log('[handleScreenshotDownload] Using emergency fallback (text-only image)...');
          }
          
          // 创建一个简单的带文本的图像作为最后的降级方案
          const emergencyCanvas = document.createElement('canvas');
          emergencyCanvas.width = displayWidth;
          emergencyCanvas.height = displayHeight;
          const ctx = emergencyCanvas.getContext('2d');
          
          // 填充背景
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, displayWidth, displayHeight);
          
          // 添加文字说明
          ctx.font = '16px Arial';
          ctx.fillStyle = '#333333';
          ctx.textAlign = 'center';
          ctx.fillText('截图生成失败', displayWidth / 2, displayHeight / 2 - 20);
          ctx.fillText('请尝试保存HTML或刷新页面后重试', displayWidth / 2, displayHeight / 2 + 20);
          
          // 下载这个简单图片
          const dataUrl = emergencyCanvas.toDataURL('image/png');
          const link = document.createElement('a');
          link.download = fileName;
          link.href = dataUrl;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          message.warning('无法生成完整截图，已创建提示图片');
        } catch (emergencyError) {
          // 如果连紧急备用方案都失败了，抛出最初的错误
          throw mainError;
        }
      }
    }
  } catch (error) {
    console.error('[handleScreenshotDownload] Error:', error);
    message.error(`图片下载失败: ${error.message || '未知错误'}`);
  } finally {
    hideLoading();
    setIsDownloading(false);
  }
};
// Helper function to copy computed styles from a pseudo-element to a real element
const copyPseudoElementStyle = (sourceElement, pseudoSelector, targetElement, debugMode) => {
  const pseudoStyle = window.getComputedStyle(sourceElement, pseudoSelector);
  
  // 判断更宽松，主要检查是否有可见的样式，而非仅检查content
  const hasVisibleStyle = 
    // 检查是否有背景色
    (pseudoStyle.backgroundColor && pseudoStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' && pseudoStyle.backgroundColor !== 'transparent') ||
    // 检查是否有背景图
    (pseudoStyle.backgroundImage && pseudoStyle.backgroundImage !== 'none') ||
    // 检查是否有宽高
    (parseInt(pseudoStyle.width) > 0 && parseInt(pseudoStyle.height) > 0) ||
    // 检查是否有边框
    (pseudoStyle.borderWidth && pseudoStyle.borderWidth !== '0px') ||
    // 检查content是否不为none（但可以为空字符串）
    (pseudoStyle.content && pseudoStyle.content !== 'none' && pseudoStyle.content !== 'normal');

  if (pseudoStyle.display === 'none' || !hasVisibleStyle) {
    return false;
  }
  
  // 复制所有样式属性
  for (const prop of Object.keys(pseudoStyle)) {
    if (targetElement.style[prop] !== undefined && pseudoStyle[prop] !== '') {
    targetElement.style[prop] = pseudoStyle[prop];
  }
  }
  
  // 特别确保这些关键样式被复制
  const criticalStyles = [
    'position', 'top', 'left', 'bottom', 'right', 'width', 'height',
    'backgroundColor', 'backgroundImage', 'backgroundPosition', 'backgroundSize',
    'borderRadius', 'borderWidth', 'borderStyle', 'borderColor',
    'zIndex', 'opacity'
  ];
  
  criticalStyles.forEach(prop => {
    const value = pseudoStyle.getPropertyValue(prop);
    if (value && value !== 'initial' && value !== 'unset' && value !== 'none') {
      targetElement.style[prop] = value;
    }
  });

  // 设置内容（如果有）
  if (pseudoStyle.content && pseudoStyle.content !== 'none' && pseudoStyle.content !== 'normal') {
    let content = pseudoStyle.content;
    // 移除引号
    if ((content.startsWith('"') && content.endsWith('"')) || 
        (content.startsWith("'") && content.endsWith("'"))) {
      content = content.substring(1, content.length - 1);
    }
    
    targetElement.textContent = content;
  } else {
    // 即使没有内容，也保留元素以显示背景、边框等
    targetElement.innerHTML = '&nbsp;';
  }

  if (debugMode) {
    console.log(`[copyPseudoElementStyle] Copied ${pseudoSelector} styles to`, targetElement);
  }

  return true;
};

const simulatePseudoElements = (parentElement, debugMode = false) => {
  if (!parentElement) return;
  const elements = Array.from(parentElement.querySelectorAll('*'));
  elements.unshift(parentElement);

  elements.forEach(el => {
    ['::before', '::after'].forEach(pseudo => {
      const pseudoElement = el.ownerDocument.createElement('span');
      pseudoElement.className = `simulated-pseudo-${pseudo.replace('::', '')}`;
      if (copyPseudoElementStyle(el, pseudo, pseudoElement, debugMode)) {
        if (pseudo === '::before') {
          el.insertBefore(pseudoElement, el.firstChild);
        } else {
          el.appendChild(pseudoElement);
        }
    }
  });
  });
};

/**
 * 拖拽模式模块 - 处理拖拽相关功能
 */
import { detectSpecialTemplate, handleSpecialTemplateStyles } from './specialTemplateUtils';

export const createDragMode = (context) => {
  const { doc, win, containerEl, getActiveElement, setActiveElement, debugMode, isDivMode, editableElementsTracker, editMode } = context;

  // 拖拽相关变量
  let isDragging = false;
  let draggedElement = null;
  let elementStartPosition = null;
  let mouseStartPosition = null;
  let dragAnimationFrame = null;
  const dragHandles = new Map();

  // 节流控制变量
  let lastDragMoveTime = 0;
  const DRAG_THROTTLE_MS = 1; // 将节流时间减少到最小值，几乎无延迟
  
  // 添加标志变量和最新鼠标事件存储
  let isAnimationFrameRequested = false;
  let lastMouseMoveEvent = null;

  // 处理元素的动画效果
  const handleElementAnimations = (element, action = 'pause') => {
    if (!element || !element.style) return;
    
    try {
      const computedStyle = window.getComputedStyle(element);
      
      // 检查元素是否有动画
      const hasAnimation = computedStyle.animation && computedStyle.animation !== 'none';
      
      if (hasAnimation) {
        // 存储原始动画状态（用于恢复）
        if (!element.hasAttribute('data-original-animation') && (action === 'pause' || action === 'remove')) {
          element.setAttribute('data-original-animation', computedStyle.animation);
          element.setAttribute('data-original-animation-play-state', computedStyle.animationPlayState || 'running');
        }
        
        if (action === 'pause') {
          // 暂停动画
          element.style.animationPlayState = 'paused';
        } else if (action === 'resume') {
          // 恢复动画
          const originalState = element.getAttribute('data-original-animation-play-state') || 'running';
          element.style.animationPlayState = originalState;
        } else if (action === 'remove') {
          // 临时移除动画
          element.style.animation = 'none';
        } else if (action === 'restore') {
          // 恢复原始动画
          const originalAnimation = element.getAttribute('data-original-animation');
          if (originalAnimation) {
            element.style.animation = originalAnimation;
            const originalState = element.getAttribute('data-original-animation-play-state') || 'running';
            element.style.animationPlayState = originalState;
            element.removeAttribute('data-original-animation');
            element.removeAttribute('data-original-animation-play-state');
          }
        }
      }
      
      // 处理transition，防止它影响拖拽
      const hasTransition = computedStyle.transition && computedStyle.transition !== 'none';
      
      if (hasTransition) {
        if (!element.hasAttribute('data-original-transition') && (action === 'pause' || action === 'remove')) {
          element.setAttribute('data-original-transition', computedStyle.transition);
        }
        
        if (action === 'pause' || action === 'remove') {
          element.style.transition = 'none';
        } else if (action === 'restore') {
          const originalTransition = element.getAttribute('data-original-transition');
          if (originalTransition) {
            element.style.transition = originalTransition;
            element.removeAttribute('data-original-transition');
          }
        }
      }
    } catch (error) {
      // 忽略可能的DOM操作错误
      if (debugMode) console.error('Error in handleElementAnimations:', error);
    }
  };

  // 清除所有元素的拖拽模式
  const clearAllDragModes = () => {
    // 移除所有元素的拖拽模式和选中状态
    editableElementsTracker.forEach(el => {
      if (!el || !el.classList) return; // 跳过null或无效元素
      el.classList.remove('drag-mode', 'selected-for-drag', 'dragging', 'resize-mode');
      el.style.cursor = 'pointer'; // 恢复默认鼠标样式

      // 恢复文本选择
      el.style.userSelect = '';
      el.style.webkitUserSelect = '';
      el.style.msUserSelect = '';
      
      // 恢复动画效果
      handleElementAnimations(el, 'restore');

      // 恢复子元素的默认光标样式和文本选择
      const allChildren = el.querySelectorAll('*');
      allChildren.forEach(child => {
        child.style.cursor = '';
        child.style.userSelect = '';
        child.style.webkitUserSelect = '';
        child.style.msUserSelect = '';
        
        // 恢复子元素的动画效果
        handleElementAnimations(child, 'restore');
      });

      // 特殊处理：恢复内部contenteditable元素的状态
      const nestedEditableElements = el.querySelectorAll('[contenteditable="true"]');
      nestedEditableElements.forEach(nestedEl => {
        if (nestedEl !== el) {
          // 恢复嵌套contenteditable元素的鼠标事件处理
          nestedEl.style.pointerEvents = '';
        }
      });

      // 移除所有拖拽句柄
      removeDragHandles(el);
      
      // 重置可能的transform样式
      if (el.hasAttribute('data-original-transform')) {
        const originalTransform = el.getAttribute('data-original-transform');
        el.style.transform = originalTransform;
        el.removeAttribute('data-original-transform');
      }
      
      // 重置可能的z-index样式
      if (el.hasAttribute('data-original-zindex')) {
        const originalZIndex = el.getAttribute('data-original-zindex');
        el.style.zIndex = originalZIndex;
        el.removeAttribute('data-original-zindex');
      }
    });
    
    // 重置拖拽状态变量
    isDragging = false;
    draggedElement = null;
    elementStartPosition = null;
    mouseStartPosition = null;
    
    // 取消任何正在进行的动画帧
    if (dragAnimationFrame) {
      cancelAnimationFrame(dragAnimationFrame);
      dragAnimationFrame = null;
    }
    
    // 重置动画帧请求状态
    isAnimationFrameRequested = false;
    lastMouseMoveEvent = null;
  };

  // 设置元素为拖拽模式
  const setElementToDragMode = (element) => {
    if (!element || typeof element.classList === 'undefined') {
      return;
    }

    // 清除所有元素的拖拽模式
    clearAllDragModes();

    // 检查是否为SVG元素
    const isSvgElement = element.namespaceURI === 'http://www.w3.org/2000/svg' || 
                         element.tagName.toLowerCase() === 'svg';

    // 添加拖拽模式样式类
    element.classList.add('drag-mode');
    
    // 设置鼠标样式为移动
    element.style.cursor = 'move';
    
    // 防止文本选择
    element.style.userSelect = 'none';
    element.style.webkitUserSelect = 'none';
    element.style.msUserSelect = 'none';
    
    // 处理动画效果，暂停动画以便正确拖拽
    handleElementAnimations(element, 'pause');
    
    // 确保SVG元素有正确的样式
    if (isSvgElement) {
      // 设置SVG元素的位置为相对定位（如果尚未设置）
      if (!element.style.position || element.style.position === 'static') {
        element.style.position = 'relative';
      }
      
      // 确保SVG元素可以接收鼠标事件
      element.style.pointerEvents = 'auto';
      
      // 确保SVG元素在正确的层级
      if (!element.style.zIndex) {
        element.style.zIndex = '1';
      }
      
      // 为SVG元素添加可拖动标记
      if (!element.hasAttribute('data-editable-fengmian')) {
        element.setAttribute('data-editable-fengmian', 'true');
      }
      
      // 设置初始transform（如果没有）
      if (!element.style.transform || element.style.transform === 'none') {
        const currentTransform = window.getComputedStyle(element).transform;
        if (currentTransform === 'none') {
          element.style.transform = 'translate(0px, 0px)';
        } else {
          element.style.transform = currentTransform;
        }
      }
    }
    
    // 设置子元素也使用移动光标
    const allChildren = element.querySelectorAll('*');
    allChildren.forEach(child => {
      child.style.cursor = 'move';
      child.style.userSelect = 'none';
      child.style.webkitUserSelect = 'none';
      child.style.msUserSelect = 'none';
      
      // 暂停子元素的动画效果
      handleElementAnimations(child, 'pause');
    });
    
    // 特殊处理：嵌套的contenteditable元素
    const nestedEditableElements = element.querySelectorAll('[contenteditable="true"]');
    nestedEditableElements.forEach(nestedEl => {
      if (nestedEl !== element) {
        // 禁用嵌套contenteditable元素的鼠标事件处理
        nestedEl.style.pointerEvents = 'none';
      }
    });
    
    // 创建拖拽句柄
    const handles = createDragHandles(element);
    
    // 保存到Map中
    dragHandles.set(element, handles);
    
    // 更新句柄位置
    updateDragHandlePosition(element, handles);
  };

  // 拖拽开始事件处理函数
  const handleDragStart = (e) => {
    if (!e || !e.target || e.button !== 0) return;
    let element = findDraggableElement(e.target);

    if (!element && e.target.nodeType === Node.TEXT_NODE && e.target.parentElement) {
      element = findDraggableElement(e.target.parentElement);
    } else if (!element && e.target.tagName) {
      const inlineElements = ['EM', 'STRONG', 'B', 'I', 'SPAN', 'SUB', 'SUP', 'U', 'MARK', 'DEL', 'INS'];
      if (inlineElements.includes(e.target.tagName.toUpperCase()) && e.target.parentElement) {
        element = findDraggableElement(e.target.parentElement);
      }
    }

    if (!element) return;

    e.preventDefault();
    e.stopPropagation();

    draggedElement = element;
    const isSvgElement = element.namespaceURI === 'http://www.w3.org/2000/svg' || element.tagName.toLowerCase() === 'svg';

    // 保存原始z-index值
    if (!element.hasAttribute('data-original-zindex')) {
      const zIndex = window.getComputedStyle(element).zIndex;
      element.setAttribute('data-original-zindex', zIndex === 'auto' ? '0' : zIndex);
    }

    // 1. 暂停动画以获取稳定的样式快照
    handleElementAnimations(element, 'pause');

    // 2. 使用rAF确保样式更新后读取
    requestAnimationFrame(() => {
      const computedStyle = window.getComputedStyle(element);

      // 3. 确保元素是可定位的
      if (computedStyle.position === 'static' && !isSvgElement) {
        element.style.position = 'relative';
      }

      // 4. 读取动画的transform偏移量
      const transform = computedStyle.transform;
      let animX = 0, animY = 0;
      
      // 检查元素是否使用了translate(-50%, -50%)进行居中
      const isUsingCenterTransform = transform && transform !== 'none' && 
                                    (element.style.left === '50%' && element.style.top === '50%');
      
      // 初始化位置变量
      let posX = 0, posY = 0;
      
      // 如果元素使用了居中定位，则保留其transform
      if (isUsingCenterTransform) {
        // 保存原始transform，以便拖拽结束后恢复
        element.setAttribute('data-original-transform', transform);
        
        // 计算元素的实际位置（相对于容器）
        const rect = element.getBoundingClientRect();
        const containerRect = element.parentElement.getBoundingClientRect();
        
        // 设置绝对位置，但保持居中效果
        posX = rect.left - containerRect.left;
        posY = rect.top - containerRect.top;
        
        element.style.left = `${posX}px`;
        element.style.top = `${posY}px`;
      } 
      // 否则，使用常规的transform处理
      else if (transform && transform !== 'none') {
        const matrix = transform.match(/matrix.*\((.+)\)/);
        if (matrix && matrix[1]) {
          const values = matrix[1].split(', ');
          animX = parseFloat(values[4]) || 0;
          animY = parseFloat(values[5]) || 0;
      }

      // 5. 读取当前的left/top值
      const currentLeft = parseFloat(element.style.left) || 0;
      const currentTop = parseFloat(element.style.top) || 0;

      // 6. 将动画的transform偏移量合并到left/top中，作为拖拽的基准
        posX = currentLeft + animX;
        posY = currentTop + animY;

        element.style.left = `${posX}px`;
        element.style.top = `${posY}px`;
      
      // 7. 临时移除transform，避免拖拽时冲突
      element.style.transform = 'none';
      }
      // 如果没有transform，直接使用当前位置
      else {
        posX = parseFloat(element.style.left) || 0;
        posY = parseFloat(element.style.top) || 0;
      }

      // 8. 设置拖拽起始点
      elementStartPosition = { x: posX, y: posY };
      mouseStartPosition = { x: e.clientX, y: e.clientY };

      element.classList.add('dragging');
      isDragging = true;
      
      // 9. 增强：处理特殊模板中的样式冲突
      handleSpecialTemplateStyles(element, 'start');
    });
  };

  // 拖拽移动事件处理函数
  const handleDragMove = (e) => {
    if (e.button === 2 || !isDragging || !draggedElement || !mouseStartPosition) {
      return;
    }

    const now = Date.now();
    if (now - lastDragMoveTime < DRAG_THROTTLE_MS) {
      return;
    }
    lastDragMoveTime = now;
    lastMouseMoveEvent = e;

    if (!isAnimationFrameRequested) {
      isAnimationFrameRequested = true;
      dragAnimationFrame = requestAnimationFrame(() => {
        isAnimationFrameRequested = false;
        if (!draggedElement || !mouseStartPosition || !lastMouseMoveEvent) return;

        const scale = context.scale !== undefined ? context.scale : 1.0;
        const dx = (lastMouseMoveEvent.clientX - mouseStartPosition.x) / scale;
        const dy = (lastMouseMoveEvent.clientY - mouseStartPosition.y) / scale;

        const newX = elementStartPosition.x + dx;
        const newY = elementStartPosition.y + dy;

        const boundedPosition = constrainToBoundary(draggedElement, newX, newY, containerEl);

        // 只更新 left 和 top 来进行拖拽
        draggedElement.style.left = `${boundedPosition.x}px`;
        draggedElement.style.top = `${boundedPosition.y}px`;
      });
    }
  };

  // 拖拽结束事件处理函数
  const handleDragEnd = (e) => {
    if (e.button === 2 || !isDragging) return;
    
    if (dragAnimationFrame) {
      cancelAnimationFrame(dragAnimationFrame);
      dragAnimationFrame = null;
    }
    
    // 清除拖拽相关变量
    isAnimationFrameRequested = false;
    lastMouseMoveEvent = null;
    
    if (draggedElement) {
      draggedElement.classList.remove('dragging');
      
      // 检查是否有保存的原始transform（针对居中元素）
      if (draggedElement.hasAttribute('data-original-transform')) {
        // 恢复原始transform
        const originalTransform = draggedElement.getAttribute('data-original-transform');
        draggedElement.style.transform = originalTransform;
        draggedElement.removeAttribute('data-original-transform');
      } else {
      // 恢复动画效果, 此时动画的transform会叠加在新的left/top上
      handleElementAnimations(draggedElement, 'restore');
      }
      
      // 恢复子元素的动画效果
      const allChildren = draggedElement.querySelectorAll('*');
      allChildren.forEach(child => {
        handleElementAnimations(child, 'restore');
      });
      
      // 恢复拖拽开始前保存的z-index
      if (draggedElement.hasAttribute('data-original-zindex')) {
        const originalZIndex = draggedElement.getAttribute('data-original-zindex');
        draggedElement.style.zIndex = originalZIndex;
        draggedElement.removeAttribute('data-original-zindex');
      }
      
      // 增强：处理特殊模板中的样式
      handleSpecialTemplateStyles(draggedElement, 'end');
    }
    
    draggedElement = null;
    elementStartPosition = null;
    mouseStartPosition = null;
    isDragging = false;
  };

  // 创建拖拽句柄
  const createDragHandles = (element) => {
    if (!element || !doc) return [];

    // 创建调整大小的控制点
    const handles = [];
    const positions = ['top', 'right', 'bottom', 'left', 'top-left', 'top-right', 'bottom-left', 'bottom-right'];

    try {
      positions.forEach(position => {
        const handle = doc.createElement('div');
        handle.className = `resize-handle ${position}`;
        handle.title = '调整大小';

        // 设置控制点样式
        handle.style.position = 'absolute';
        handle.style.width = '10px';
        handle.style.height = '10px';
        handle.style.backgroundColor = '#1890ff';
        handle.style.border = '1px solid white';
        handle.style.borderRadius = '50%';
        handle.style.zIndex = '10001';
        handle.style.boxShadow = '0 1px 3px rgba(0,0,0,0.3)';
        // 确保控制点不继承文本编辑光标
        handle.style.cursor = 'inherit'; // 先继承，然后根据位置覆盖

        // 根据位置设置光标样式
        switch (position) {
          case 'top':
          case 'bottom':
            handle.style.cursor = 'ns-resize';
            break;
          case 'left':
          case 'right':
            handle.style.cursor = 'ew-resize';
            break;
          case 'top-left':
          case 'bottom-right':
            handle.style.cursor = 'nwse-resize';
            break;
          case 'top-right':
          case 'bottom-left':
            handle.style.cursor = 'nesw-resize';
            break;
        }

        element.appendChild(handle);
        handles.push(handle);

        // 添加调整大小的事件监听器
        handle.addEventListener('mousedown', handleResizeStart);
      });

      // 立即更新控制点位置
      updateDragHandlePosition(element, handles);

      return handles;
    } catch (error) {
      if (debugMode) console.error('Error creating drag handles:', error);
      return [];
    }
  };

  // 移除拖拽句柄
  const removeDragHandles = (element) => {
    // 移除所有调整大小的控制点
    const handles = element.querySelectorAll('.resize-handle');
    handles.forEach(handle => {
      if (handle.parentNode === element) {
        handle.removeEventListener('mousedown', handleResizeStart);
        element.removeChild(handle);
      }
    });

    // 从Map中移除
    dragHandles.delete(element);
  };

  // 更新拖拽句柄位置
  const updateDragHandlePosition = (element, handles) => {
    // 安全检查：确保元素存在
    if (!element) return;

    // 如果没有传入handles参数，尝试从Map中获取
    if (!handles) {
      handles = dragHandles.get(element);
    }

    // 如果仍然没有handles或者是空数组，则直接返回
    if (!handles || !handles.length) return;

    try {
      // 检查是否是图片或图片容器
      const isImg = element.tagName.toLowerCase() === 'img';
      const hasImg = element.querySelector('img') !== null;
      const isImageContainer = isImg || hasImg || element.hasAttribute('data-image-container');
      
      // 获取元素的实际尺寸
      const rect = element.getBoundingClientRect();
      
      // 更新每个控制点的位置
      handles.forEach(handle => {
        if (!handle) return;

        // 根据控制点的类名确定其位置
        const position = Array.from(handle.classList)
          .find(cls => cls !== 'resize-handle');

        // 根据位置更新控制点的样式
        switch (position) {
          case 'top':
            handle.style.top = '-5px';
            handle.style.left = '50%';
            handle.style.transform = 'translateX(-50%)';
            break;
          case 'right':
            handle.style.right = '-5px';
            handle.style.top = '50%';
            handle.style.transform = 'translateY(-50%)';
            break;
          case 'bottom':
            handle.style.bottom = '-5px';
            handle.style.left = '50%';
            handle.style.transform = 'translateX(-50%)';
            break;
          case 'left':
            handle.style.left = '-5px';
            handle.style.top = '50%';
            handle.style.transform = 'translateY(-50%)';
            break;
          case 'top-left':
            handle.style.top = '-5px';
            handle.style.left = '-5px';
            break;
          case 'top-right':
            handle.style.top = '-5px';
            handle.style.right = '-5px';
            break;
          case 'bottom-left':
            handle.style.bottom = '-5px';
            handle.style.left = '-5px';
            break;
          case 'bottom-right':
            handle.style.bottom = '-5px';
            handle.style.right = '-5px';
            break;
        }
      });
    } catch (error) {
      console.error('Error in updateDragHandlePosition:', error);
    }
  };

  // 处理调整大小开始
  const handleResizeStart = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // 获取调整大小控制点和目标元素
    const handle = e.target;
    const element = handle.closest('[contenteditable="true"]');
    if (!element) return;

    // 检查是否是图片或图片容器
    const isImg = element.tagName.toLowerCase() === 'img';
    const hasImg = element.querySelector('img') !== null;
    const isImageContainer = isImg || hasImg || element.hasAttribute('data-image-container');

    // 记录初始位置和大小
    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = element.offsetWidth;
    const startHeight = element.offsetHeight;

    // 获取当前的transform信息
    const style = window.getComputedStyle(element);
    let currentTransform = style.transform;
    if (currentTransform === 'none') {
      currentTransform = 'matrix(1, 0, 0, 1, 0, 0)';
    }

    // 从transform matrix中提取位移值
    let translateX = 0;
    let translateY = 0;
    const matrix = currentTransform.match(/matrix.*\((.+)\)/);
    if (matrix && matrix[1]) {
      const values = matrix[1].split(', ');
      if (values.length >= 6) {
        translateX = parseFloat(values[4]) || 0;
        translateY = parseFloat(values[5]) || 0;
      }
    }

    // 获取调整方向
    const position = Array.from(handle.classList)
      .find(cls => cls !== 'resize-handle');

    // 添加调整大小时的样式
    element.classList.add('resize-mode');

    // 处理鼠标移动事件
    const handleResize = (moveEvent) => {
      moveEvent.preventDefault();

      // 计算移动距离
      const dx = moveEvent.clientX - startX;
      const dy = moveEvent.clientY - startY;

      // 根据控制点位置调整大小和位置
      switch (position) {
        case 'top':
          // 顶部：向上拖动增加高度，向下拖动减少高度
          const newHeightTop = startHeight - dy;
          if (newHeightTop > 20) { // 最小高度限制
            element.style.height = `${newHeightTop}px`;
            element.style.transform = `translate(${translateX}px, ${translateY + dy}px)`;
          }
          break;
        case 'right':
          // 右侧：向右拖动增加宽度，向左拖动减少宽度
          // 注意: dx > 0 表示向右拖动，此时增加宽度；dx < 0 表示向左拖动，此时减少宽度
          const newWidthRight = startWidth + dx;
          if (newWidthRight > 20) { // 最小宽度限制
            element.style.width = `${newWidthRight}px`;
          }
          break;
        case 'bottom':
          // 底部：向下拖动增加高度，向上拖动减少高度
          const newHeightBottom = startHeight + dy;
          if (newHeightBottom > 20) { // 最小高度限制
            element.style.height = `${newHeightBottom}px`;
          }
          break;
        case 'left':
          // 左侧：向左拖动增加宽度，向右拖动减少宽度
          // 注意: dx < 0 表示向左拖动，此时增加宽度并移动元素位置；dx > 0 表示向右拖动，此时减少宽度
          const newWidthLeft = startWidth - dx;
          if (newWidthLeft > 20) { // 最小宽度限制
            element.style.width = `${newWidthLeft}px`;
            element.style.transform = `translate(${translateX + dx}px, ${translateY}px)`;
          }
          break;
        case 'top-left':
          // 左上角：左侧和顶部同时调整
          const newWidthTopLeft = startWidth - dx;
          const newHeightTopLeft = startHeight - dy;
          if (newWidthTopLeft > 20 && newHeightTopLeft > 20) {
            element.style.width = `${newWidthTopLeft}px`;
            element.style.height = `${newHeightTopLeft}px`;
            element.style.transform = `translate(${translateX + dx}px, ${translateY + dy}px)`;
          }
          break;
        case 'top-right':
          // 右上角：右侧和顶部同时调整
          const newWidthTopRight = startWidth + dx;
          const newHeightTopRight = startHeight - dy;
          if (newWidthTopRight > 20 && newHeightTopRight > 20) {
            element.style.width = `${newWidthTopRight}px`;
            element.style.height = `${newHeightTopRight}px`;
            element.style.transform = `translate(${translateX}px, ${translateY + dy}px)`;
          }
          break;
        case 'bottom-left':
          // 左下角：左侧和底部同时调整
          const newWidthBottomLeft = startWidth - dx;
          const newHeightBottomLeft = startHeight + dy;
          if (newWidthBottomLeft > 20 && newHeightBottomLeft > 20) {
            element.style.width = `${newWidthBottomLeft}px`;
            element.style.height = `${newHeightBottomLeft}px`;
            element.style.transform = `translate(${translateX + dx}px, ${translateY}px)`;
          }
          break;
        case 'bottom-right':
          // 右下角：右侧和底部同时调整
          const newWidthBottomRight = startWidth + dx;
          const newHeightBottomRight = startHeight + dy;
          if (newWidthBottomRight > 20 && newHeightBottomRight > 20) {
            element.style.width = `${newWidthBottomRight}px`;
            element.style.height = `${newHeightBottomRight}px`;
          }
          break;
      }

      // 更新拖拽句柄位置
      updateDragHandlePosition(element);
      
      // 如果是图片容器，确保图片也随之调整大小
      if (isImageContainer && hasImg) {
        const imgElement = element.querySelector('img');
        if (imgElement) {
          // 使图片填充整个容器
          imgElement.style.width = '100%';
          imgElement.style.height = '100%';
          imgElement.style.objectFit = 'contain';
        }
      }
    };

    // 处理鼠标释放事件
    const handleResizeEnd = () => {
      // 移除调整大小时的样式
      element.classList.remove('resize-mode');

      // 移除事件监听器
      doc.removeEventListener('mousemove', handleResize);
      doc.removeEventListener('mouseup', handleResizeEnd);
    };

    // 添加事件监听器
    doc.addEventListener('mousemove', handleResize);
    doc.addEventListener('mouseup', handleResizeEnd);
  };

  // 检查元素是否可拖拽
  const isDraggable = (element) => {
    if (!element) return false;
    
    // 检查元素类型
    const tagName = element.tagName.toLowerCase();
    
    // 不可拖拽的元素类型
    const nonDraggableTags = ['input', 'select', 'textarea', 'button', 'a', 'iframe'];
    if (nonDraggableTags.includes(tagName)) {
      return false;
    }
    
    // 检查类名
    const className = element.className || '';
    if (typeof className === 'string' && (
      className.includes('non-draggable') ||
      className.includes('button') ||
      className.includes('btn') ||
      className.includes('control')
    )) {
      return false;
    }
    
    // 检查是否处于编辑模式
    if (element.hasAttribute('data-editing') || element.classList.contains('editing')) {
      return false;
    }
    
    return true;
  };

  // 处理元素点击事件 - 进入拖拽模式
  const handleElementClick = (e) => {
    // 如果不是左键点击，不处理
    if (e.button !== 0) {
      return false; // 不处理
    }
    
    const target = e.currentTarget;

    // 检测连续点击次数
    const clickCount = e.detail;

    // 只处理单击事件
    if (clickCount !== 1) {
      return false; // 不处理
    }

    // 检查是否点击了特殊元素
    const isToolbarClick = e.target.closest('.text-editor-toolbar-container, .fixed-text-editor-toolbar');
    const isEditorControlClick = e.target.closest('.editor-control');
    const isResizeHandleClick = e.target.classList && (e.target.classList.contains('resize-handle') || e.target.closest('.resize-handle'));

    // 如果点击了工具栏、编辑器控件或调整大小控件，不做任何处理
    if (isToolbarClick || isEditorControlClick || isResizeHandleClick) {
      return false; // 不处理
    }

    // 清除所有元素的拖拽模式
    clearAllDragModes();

    // 设置当前元素为拖拽模式
    setElementToDragMode(target);

    // 防止进入编辑状态和显示文本光标
    e.preventDefault();
    e.stopPropagation();

    return true; // 已处理
  };

  // 根据选择器查找可拖拽元素
  const findDraggableElement = (target) => {
    if (!target) return null;
    
    try {
      // 0. 如果是文本节点，改为查找其父元素
      if (target.nodeType === Node.TEXT_NODE && target.parentElement) {
        return findDraggableElement(target.parentElement);
      }
      
      // 1. 查找最近的有data-editable-fengmian属性的元素
      let element = target.closest('[data-editable-fengmian="true"]');
      
      // 2. 如果没找到，查找其他可能的文本元素
      if (!element) {
        // 通用文本元素选择器，不依赖特定类名
        const textSelectors = [
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 
          'p', 'span', 
          'div.text-content', 'text', '.text',
          // 通用选择器，基于常见文本元素的类名模式
          '[class*="title"]', 
          '[class*="subtitle"]', 
          '[class*="heading"]',
          '[class*="text"]',
          '[class*="info"]'
        ];
        
        // 先尝试直接匹配目标元素
        if (target.matches) {
          for (const selector of textSelectors) {
            try {
              if (target.matches(selector)) {
                element = target;
                break;
              }
            } catch(e) {
              // 忽略匹配错误，继续尝试其他选择器
            }
          }
        }
        
        // 如果目标元素不匹配，则查找最近的祖先元素
        if (!element) {
          for (const selector of textSelectors) {
            try {
              element = target.closest(selector);
              if (element) break;
            } catch(e) {
              // 忽略匹配错误，继续尝试其他选择器
            }
          }
        }
      }
      
      // 3. 特殊处理内嵌文本元素（如em, strong等）
      if (!element && target.tagName) {
        const inlineElements = ['EM', 'STRONG', 'B', 'I', 'SPAN', 'SUB', 'SUP', 'U', 'MARK', 'DEL', 'INS'];
        
        if (inlineElements.includes(target.tagName.toUpperCase())) {
          // 查找父级文本元素
          let parentElement = target.parentElement;
          while (parentElement) {
            const parentTag = parentElement.tagName.toLowerCase();
            // 通用父元素判断，基于标签和样式特征
            if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div'].includes(parentTag)) {
              element = parentElement;
              break;
            }
            
            // 基于类名模式的判断
            if (parentElement.className && typeof parentElement.className === 'string' &&
                (parentElement.className.includes('title') || 
                 parentElement.className.includes('text') ||
                 parentElement.className.includes('info'))) {
              element = parentElement;
              break;
            }
            
            // 如果找不到合适的父元素，继续向上查找
            parentElement = parentElement.parentElement;
          }
          
          // 如果还是找不到，就直接使用当前内联元素的父元素
          if (!element && target.parentElement) {
            element = target.parentElement;
          }
        }
      }
      
      // 4. 通过内容与样式特征识别文本元素（不依赖特定类名或ID）
      if (!element && target.textContent && target.textContent.trim()) {
        // 检查是否为可能的文本容器
        const isTextContainer = (el) => {
          if (!el) return false;
          
          // 检查是否有文本内容
          if (!el.textContent || !el.textContent.trim()) return false;
          
          // 检查计算样式（如果可用）
          try {
            const style = window.getComputedStyle(el);
            // 检查文本相关的样式属性
            if (style.fontSize && parseInt(style.fontSize) > 10) return true;
            if (style.fontWeight && parseInt(style.fontWeight) >= 400) return true;
            if (['block', 'inline-block', 'flex'].includes(style.display)) return true;
            if (style.textAlign) return true;
          } catch(e) {
            // 忽略样式访问错误
          }
          
          // 检查标签名
          const tag = el.tagName.toLowerCase();
          if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div'].includes(tag)) {
            return true;
          }
          
          return false;
        };
        
        // 从目标元素开始，向上查找合适的文本容器
        let currentEl = target;
        while (currentEl && currentEl.parentElement) {
          if (isTextContainer(currentEl)) {
            element = currentEl;
            break;
          }
          currentEl = currentEl.parentElement;
        }
      }
      
      // 5. 增强SVG元素识别
      if (!element) {
        // 3.1 先检查是否点击了任何SVG元素或其子元素
        let svgTarget = target;
        
        // 向上查找最近的SVG元素（如果当前元素不是SVG）
        while (svgTarget && 
               svgTarget.namespaceURI !== 'http://www.w3.org/2000/svg' && 
               svgTarget.tagName.toLowerCase() !== 'svg') {
          if (svgTarget.parentElement) {
            svgTarget = svgTarget.parentElement;
          } else {
            svgTarget = null;
            break;
          }
        }
        
        // 如果找到了SVG元素或SVG命名空间的元素
        if (svgTarget && 
            (svgTarget.namespaceURI === 'http://www.w3.org/2000/svg' || 
             svgTarget.tagName.toLowerCase() === 'svg')) {
          
          // 3.2 如果是SVG元素本身，直接使用它
          if (svgTarget.tagName.toLowerCase() === 'svg') {
            element = svgTarget;
          } 
          // 3.3 如果是SVG内的文本元素，使用它
          else if (svgTarget.tagName.toLowerCase() === 'text') {
            element = svgTarget;
          } 
          // 3.4 如果是SVG内的其他图形元素
          else if (['path', 'rect', 'circle', 'ellipse', 'line', 'polygon', 'polyline'].includes(svgTarget.tagName.toLowerCase())) {
            // 优先使用直接包含它的SVG元素
            element = svgTarget.closest('svg') || svgTarget;
          }
          // 3.5 如果是SVG分组元素
          else if (['g', 'symbol', 'defs', 'marker', 'pattern', 'mask', 'clipPath'].includes(svgTarget.tagName.toLowerCase())) {
            // 优先使用该分组元素或其父SVG
            element = svgTarget.closest('svg') || svgTarget;
          }
        }
      }
      
      // 6. 确保SVG元素可拖动——为它们动态添加必要属性
      if (element && element.namespaceURI === 'http://www.w3.org/2000/svg' && !element.hasAttribute('data-editable-fengmian')) {
        element.setAttribute('data-editable-fengmian', 'true');
        
        // 对于SVG元素，我们不设置contenteditable，因为它们不是文本元素
        // 但我们需要确保它们可以接收mousedown事件
        if (element.style) {
          element.style.pointerEvents = 'auto';
        }
      }
      
      // 7. 最后，过滤掉不应该拖拽的元素
      if (element) {
        // 排除特定类型的元素
        const tagName = element.tagName.toLowerCase();
        if (['input', 'select', 'textarea', 'button', 'a', 'iframe'].includes(tagName)) {
          return null;
        }
        
        // 排除特定类名的元素
        const className = element.className || '';
        if (typeof className === 'string' && (
          className.includes('non-draggable') ||
          className.includes('button') ||
          className.includes('btn') ||
          className.includes('control')
        )) {
          return null;
        }
      }
      
      return element;
    } catch (error) {
      if (debugMode) console.error('Error in findDraggableElement:', error);
      return null;
    }
  };

  // 大幅简化边界约束函数，允许元素移动到容器边缘
  const constrainToBoundary = (element, desiredX, desiredY, container) => {
    // 如果没有元素或容器，直接返回期望位置
    if (!element || !container) return { x: desiredX, y: desiredY };
    
    // 获取容器尺寸
    const containerWidth = container.scrollWidth || container.offsetWidth;
    const containerHeight = container.scrollHeight || container.offsetHeight;
    
    // 允许元素完全移动到边缘，设置最小约束
    // 只在极端情况下（如元素完全超出容器）才限制
    const margin = -50; // 允许元素部分超出容器的边界
    
    // 简单的边界检查，只防止元素完全超出可视区域
    const minX = -containerWidth + margin;  
    const maxX = containerWidth - margin;
    const minY = -containerHeight + margin;
    const maxY = containerHeight - margin;
    
    // 宽松的边界限制
    const x = Math.max(minX, Math.min(desiredX, maxX));
    const y = Math.max(minY, Math.min(desiredY, maxY));
    
    return { x, y };
  };

  // 使用从文件顶部导入的特殊模板处理函数

  // 返回公共API
  return {
    clearAllDragModes,
    setElementToDragMode,
    handleDragStart,
    handleDragMove,
    handleDragEnd,
    createDragHandles,
    removeDragHandles,
    updateDragHandlePosition,
    isDraggable,
    handleElementClick,
    findDraggableElement,
    constrainToBoundary
  };
};

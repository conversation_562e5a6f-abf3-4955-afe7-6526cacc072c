const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 风格提示词模板模型
 * 对应数据库中的style_prompts表
 */
const StylePrompt = sequelize.define('StylePrompt', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '风格ID，唯一标识'
  },
  id_code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '风格模板唯一标识码，英文字母、数字和下划线'
  },
  style_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '风格的名称，不可重复'
  },
  display_name: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '风格的显示名称，用于前端展示'
  },
  prompt_content: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: '风格提示词模板内容'
  },
  display_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '风格在前端显示的排序，数字越小越靠前'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    allowNull: false,
    defaultValue: 'active',
    comment: '风格状态：启用或禁用'
  },
  example_html: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '风格示例HTML内容'
  }
}, {
  tableName: 'style_prompts',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

// 添加关联关系方法
StylePrompt.associate = (models) => {
  StylePrompt.hasMany(models.StyleExample, {
    foreignKey: 'style_id',
    as: 'StyleExamples',
    onDelete: 'CASCADE'
  });
};

module.exports = StylePrompt;

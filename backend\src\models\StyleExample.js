const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 封面风格示例模型
 * 对应数据库中的style_examples表
 */
const StyleExample = sequelize.define('StyleExample', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '示例ID，唯一标识'
  },
  style_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联风格提示词表的风格ID'
  },
  image_url: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '风格示例图片的URL'
  }
}, {
  tableName: 'style_examples',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

// 添加关联关系方法
StyleExample.associate = (models) => {
  StyleExample.belongsTo(models.StylePrompt, {
    foreignKey: 'style_id',
    as: 'stylePrompt'
  });
};

module.exports = StyleExample;

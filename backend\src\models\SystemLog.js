const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 系统日志模型
 * 对应数据库中的system_logs表
 */
const SystemLog = sequelize.define('SystemLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '日志ID，唯一标识'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联用户表的用户ID，可为空（表示系统操作）'
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '操作用户名称，冗余存储便于查询'
  },
  action: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '操作类型，如login, create, update, delete等'
  },
  module: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '操作模块，如user, cover, style, setting等'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '操作描述，简要说明操作内容'
  },
  ip_address: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '操作者IP地址'
  },
  user_agent: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '操作者浏览器信息'
  },
  status: {
    type: DataTypes.ENUM('success', 'failure'),
    defaultValue: 'success',
    comment: '操作状态：成功或失败'
  },
  level: {
    type: DataTypes.ENUM('info', 'warning', 'error'),
    defaultValue: 'info',
    comment: '日志级别：信息、警告或错误'
  },
  source: {
    type: DataTypes.STRING(20),
    allowNull: true,
    defaultValue: 'backend',
    comment: '日志来源：frontend或backend'
  },
  details: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '详细信息，可存储JSON格式的请求参数等'
  }
}, {
  tableName: 'system_logs',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

module.exports = SystemLog;

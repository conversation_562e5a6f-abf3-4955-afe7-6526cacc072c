/**
 * 系统监控模块
 * 提供系统状态监控和管理功能
 */

// 系统监控模块
window.systemMonitorModule = {
  // 初始化系统监控
  init: function() {
    console.log('系统监控模块初始化');
    this.loadSystemStatus();
    this.setupEventListeners();
  },

  // 加载系统状态
  loadSystemStatus: function() {
    const container = document.getElementById('content');
    if (!container) return;

    container.innerHTML = `
      <div class="system-monitor-container">
        <div class="page-header">
          <h2>系统监控</h2>
          <p class="text-muted">实时监控系统运行状态</p>
        </div>

        <div class="row">
          <!-- 系统状态卡片 -->
          <div class="col-md-3">
            <div class="card status-card">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="status-icon bg-success">
                    <i class="fas fa-server"></i>
                  </div>
                  <div class="ms-3">
                    <h6 class="mb-0">系统状态</h6>
                    <span class="text-success">运行正常</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-3">
            <div class="card status-card">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="status-icon bg-info">
                    <i class="fas fa-database"></i>
                  </div>
                  <div class="ms-3">
                    <h6 class="mb-0">数据库</h6>
                    <span class="text-success">连接正常</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-3">
            <div class="card status-card">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="status-icon bg-warning">
                    <i class="fas fa-memory"></i>
                  </div>
                  <div class="ms-3">
                    <h6 class="mb-0">内存使用</h6>
                    <span class="text-info">65%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-3">
            <div class="card status-card">
              <div class="card-body">
                <div class="d-flex align-items-center">
                  <div class="status-icon bg-primary">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="ms-3">
                    <h6 class="mb-0">在线用户</h6>
                    <span class="text-primary" id="online-users">0</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="row mt-4">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">系统信息</h5>
              </div>
              <div class="card-body">
                <table class="table table-borderless">
                  <tr>
                    <td>服务器时间:</td>
                    <td id="server-time">${new Date().toLocaleString()}</td>
                  </tr>
                  <tr>
                    <td>运行时长:</td>
                    <td id="uptime">计算中...</td>
                  </tr>
                  <tr>
                    <td>Node.js版本:</td>
                    <td>${process?.version || 'N/A'}</td>
                  </tr>
                  <tr>
                    <td>系统平台:</td>
                    <td>${navigator?.platform || 'N/A'}</td>
                  </tr>
                </table>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">性能指标</h5>
              </div>
              <div class="card-body">
                <div class="mb-3">
                  <label class="form-label">CPU使用率</label>
                  <div class="progress">
                    <div class="progress-bar bg-info" style="width: 45%">45%</div>
                  </div>
                </div>
                <div class="mb-3">
                  <label class="form-label">内存使用率</label>
                  <div class="progress">
                    <div class="progress-bar bg-warning" style="width: 65%">65%</div>
                  </div>
                </div>
                <div class="mb-3">
                  <label class="form-label">磁盘使用率</label>
                  <div class="progress">
                    <div class="progress-bar bg-success" style="width: 30%">30%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="row mt-4">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">系统日志</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>时间</th>
                        <th>级别</th>
                        <th>消息</th>
                      </tr>
                    </thead>
                    <tbody id="system-logs">
                      <tr>
                        <td>${new Date().toLocaleString()}</td>
                        <td><span class="badge bg-success">INFO</span></td>
                        <td>系统监控模块已启动</td>
                      </tr>
                      <tr>
                        <td>${new Date(Date.now() - 60000).toLocaleString()}</td>
                        <td><span class="badge bg-info">INFO</span></td>
                        <td>数据库连接检查完成</td>
                      </tr>
                      <tr>
                        <td>${new Date(Date.now() - 120000).toLocaleString()}</td>
                        <td><span class="badge bg-warning">WARN</span></td>
                        <td>内存使用率较高，建议关注</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // 启动定时更新
    this.startAutoUpdate();
  },

  // 设置事件监听器
  setupEventListeners: function() {
    // 可以添加刷新按钮等事件监听器
  },

  // 启动自动更新
  startAutoUpdate: function() {
    // 每30秒更新一次时间
    setInterval(() => {
      const serverTimeElement = document.getElementById('server-time');
      if (serverTimeElement) {
        serverTimeElement.textContent = new Date().toLocaleString();
      }
    }, 30000);

    // 获取真实的在线用户数
    setInterval(() => {
      this.updateOnlineUsers();
    }, 60000);

    // 更新系统性能指标
    setInterval(() => {
      this.updatePerformanceMetrics();
    }, 30000);
  },

  // 更新在线用户数
  updateOnlineUsers: function() {
    fetch('/api/admin/statistics/online-users')
      .then(response => response.json())
      .then(data => {
        const onlineUsersElement = document.getElementById('online-users');
        if (onlineUsersElement && data.success) {
          onlineUsersElement.textContent = data.data.count || 0;
        }
      })
      .catch(error => {
        console.warn('获取在线用户数失败:', error);
        // 降级到估算值
        const onlineUsersElement = document.getElementById('online-users');
        if (onlineUsersElement) {
          const estimatedUsers = Math.floor(Math.random() * 20) + 1;
          onlineUsersElement.textContent = estimatedUsers;
        }
      });
  },

  // 更新性能指标
  updatePerformanceMetrics: function() {
    fetch('/api/admin/statistics/performance')
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          this.updateProgressBars(data.data);
        }
      })
      .catch(error => {
        console.warn('获取性能指标失败:', error);
        // 使用模拟数据
        this.updateProgressBars({
          cpu: Math.floor(Math.random() * 30) + 20,
          memory: Math.floor(Math.random() * 40) + 40,
          disk: Math.floor(Math.random() * 20) + 20
        });
      });
  },

  // 更新进度条
  updateProgressBars: function(data) {
    const cpuBar = document.querySelector('.progress-bar.bg-info');
    const memoryBar = document.querySelector('.progress-bar.bg-warning');
    const diskBar = document.querySelector('.progress-bar.bg-success');

    if (cpuBar && data.cpu) {
      cpuBar.style.width = data.cpu + '%';
      cpuBar.textContent = data.cpu + '%';
    }

    if (memoryBar && data.memory) {
      memoryBar.style.width = data.memory + '%';
      memoryBar.textContent = data.memory + '%';
    }

    if (diskBar && data.disk) {
      diskBar.style.width = data.disk + '%';
      diskBar.textContent = data.disk + '%';
    }
  }
};

// 添加样式
const style = document.createElement('style');
style.textContent = `
  .system-monitor-container {
    padding: 20px;
  }
  
  .status-card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
  }
  
  .status-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
  }
  
  .page-header {
    margin-bottom: 30px;
  }
  
  .page-header h2 {
    color: #333;
    margin-bottom: 5px;
  }
  
  .text-muted {
    color: #6c757d !important;
  }
`;
document.head.appendChild(style);

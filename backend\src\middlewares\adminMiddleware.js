const { errorResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');

/**
 * 管理员身份验证中间件
 * 验证用户是否具有管理员权限
 */
const adminAuth = (req, res, next) => {
  try {
    // 检查用户是否存在（auth中间件已经设置）
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '请先登录'
      });
    }
    
    // 检查用户角色是否为管理员
    if (req.user.role !== 'admin') {
      logger.warn(`非管理员用户(ID:${req.user.id})尝试访问管理员接口`);
      return errorResponse(res, '您没有管理员权限', 403);
    }
    
    next();
  } catch (error) {
    logger.error('管理员权限验证失败:', error);
    return errorResponse(res, '权限验证失败', 500);
  }
};

module.exports = adminAuth; 
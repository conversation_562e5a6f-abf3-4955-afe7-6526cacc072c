/**
 * 检查安全违规记录表
 */

const { sequelize } = require('./backend/src/config/database');

async function checkSecurityViolationsTable() {
  try {
    console.log('🔍 检查html_security_violations表...');
    
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 检查表是否存在
    try {
      const [tables] = await sequelize.query("SHOW TABLES LIKE 'html_security_violations'");
      
      if (tables.length === 0) {
        console.log('❌ html_security_violations表不存在');
        console.log('🔧 正在创建html_security_violations表...');
        
        // 创建表
        await sequelize.query(`
          CREATE TABLE html_security_violations (
            id INT PRIMARY KEY AUTO_INCREMENT COMMENT '违规记录ID',
            file_name VARCHAR(255) NOT NULL COMMENT '文件名',
            file_path VARCHAR(500) COMMENT '文件路径',
            violation_type VARCHAR(100) NOT NULL COMMENT '违规类型',
            risk_level ENUM('SAFE', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL COMMENT '风险等级',
            violation_details JSON COMMENT '违规详情',
            detection_rules JSON COMMENT '检测规则',
            upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
            user_id INT COMMENT '用户ID',
            ip_address VARCHAR(45) COMMENT 'IP地址',
            user_agent TEXT COMMENT '用户代理',
            status ENUM('PENDING', 'REVIEWED', 'RESOLVED', 'IGNORED') DEFAULT 'PENDING' COMMENT '处理状态',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_risk_level (risk_level),
            INDEX idx_upload_time (upload_time),
            INDEX idx_user_id (user_id),
            INDEX idx_status (status)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='HTML安全违规记录表'
        `);
        
        console.log('✅ html_security_violations表创建成功');
        
        // 插入一些测试数据
        await sequelize.query(`
          INSERT INTO html_security_violations 
          (file_name, violation_type, risk_level, violation_details, detection_rules, user_id, ip_address) 
          VALUES 
          ('test1.html', 'XSS_DETECTION', 'HIGH', 
           JSON_OBJECT('script_tags', 2, 'suspicious_patterns', 1), 
           JSON_OBJECT('rule_name', 'XSS检测', 'pattern', '<script>'), 
           1, '127.0.0.1'),
          ('test2.html', 'MALICIOUS_SCRIPT', 'MEDIUM', 
           JSON_OBJECT('eval_calls', 1, 'document_write', 1), 
           JSON_OBJECT('rule_name', '恶意脚本检测', 'pattern', 'eval('), 
           1, '127.0.0.1'),
          ('test3.html', 'DANGEROUS_TAG', 'LOW', 
           JSON_OBJECT('iframe_tags', 1), 
           JSON_OBJECT('rule_name', '危险标签检测', 'pattern', '<iframe>'), 
           1, '127.0.0.1')
        `);
        
        console.log('✅ 测试数据插入成功');
        
      } else {
        console.log('✅ html_security_violations表存在');
        
        // 检查表结构
        const [structure] = await sequelize.query('DESCRIBE html_security_violations');
        console.log('\n📊 表结构:');
        console.table(structure.map(field => ({
          字段: field.Field,
          类型: field.Type,
          允许空值: field.Null,
          键: field.Key,
          默认值: field.Default
        })));
        
        // 检查数据
        const [data] = await sequelize.query('SELECT COUNT(*) as count FROM html_security_violations');
        console.log(`\n📋 数据条数: ${data[0].count}`);
        
        if (data[0].count === 0) {
          console.log('⚠️ 表中没有数据，插入测试数据...');
          await sequelize.query(`
            INSERT INTO html_security_violations 
            (file_name, violation_type, risk_level, violation_details, detection_rules, user_id, ip_address) 
            VALUES 
            ('test1.html', 'XSS_DETECTION', 'HIGH', 
             JSON_OBJECT('script_tags', 2, 'suspicious_patterns', 1), 
             JSON_OBJECT('rule_name', 'XSS检测', 'pattern', '<script>'), 
             1, '127.0.0.1'),
            ('test2.html', 'MALICIOUS_SCRIPT', 'MEDIUM', 
             JSON_OBJECT('eval_calls', 1, 'document_write', 1), 
             JSON_OBJECT('rule_name', '恶意脚本检测', 'pattern', 'eval('), 
             1, '127.0.0.1')
          `);
          console.log('✅ 测试数据插入成功');
        }
      }
      
    } catch (error) {
      console.log('❌ 表操作失败:', error.message);
    }
    
    await sequelize.close();
    console.log('\n✅ 检查完成');
    
  } catch (error) {
    console.log('❌ 检查失败:', error.message);
  }
}

checkSecurityViolationsTable();

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 积分记录模型
 * 对应数据库中的point_records表
 */
const PointRecord = sequelize.define('PointRecord', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '积分记录ID，唯一标识'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '关联用户表的用户ID'
  },
  points_change: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '积分变动数量，正数为增加，负数为减少'
  },
  points_after: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '变动后的用户总积分'
  },
  operation_type: {
    type: DataTypes.ENUM('register', 'daily_reward', 'generate', 'admin_adjust', 'vip_upgrade', 'recharge', 'daily_points_normal', 'daily_points_advanced'),
    allowNull: false,
    comment: '积分变动类型：注册奖励、每日奖励、生成封面、管理员调整、VIP升级、充值、每日积分(普通)、每日积分(高级)'
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '积分变动的详细描述'
  },
  operation_time: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: '积分操作的实际时间'
  },
  operation_id: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '操作唯一标识，用于防止重复操作',
    unique: true
  }
}, {
  tableName: 'point_records',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true  // 使用下划线命名约定
});

// 定义模型关联
PointRecord.associate = (models) => {
  PointRecord.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user'
  });
};

module.exports = PointRecord;

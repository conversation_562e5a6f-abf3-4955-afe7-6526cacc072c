import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { useAuthContext } from '@/contexts/AuthContext';
import { CheckCircle, XCircle, ArrowLeft, Home, Crown, CreditCard } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import axios from 'axios';
import { message } from 'antd';
// 导入URL参数解密工具
import { decryptOrderNo } from '@/utils/urlEncryption';

/**
 * 解析商品详情JSON字符串
 * @param {string} productDetailStr - 商品详情JSON字符串
 * @returns {Object} - 解析后的商品详情对象
 */
const parseProductDetail = (productDetailStr) => {
  if (!productDetailStr) return null;
  
  try {
    // 尝试解析JSON字符串
    const productDetail = JSON.parse(productDetailStr);
    return productDetail;
  } catch (error) {
    // 如果解析失败，返回原始字符串作为名称
    console.warn('解析商品详情失败:', error);
    return { name: productDetailStr };
  }
};

/**
 * 支付结果内容组件 - 适用于路由/payment-result/:orderNo
 * 作为会员中心和支付流程的子功能，展示支付结果信息
 */
const PaymentResultContent = () => {
  const { user: userInfo, refreshUserInfo } = useAuthContext();
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams(); // 获取路由参数
  
  // 从路由参数获取订单号
  const encryptedOrderNo = params.orderNo;
  
  // 解密订单号，添加错误处理
  let orderNo = null;
  try {
    orderNo = encryptedOrderNo ? decryptOrderNo(encryptedOrderNo) : null;
    // 如果解密后的订单号格式明显不正确（例如为空或太短），则使用原始参数
    if (!orderNo || orderNo.length < 5) {
      console.warn('订单号解密结果异常，使用原始参数');
      orderNo = encryptedOrderNo;
    }
  } catch (error) {
    console.warn('订单号解密失败，使用原始参数:', error);
    // 解密失败时，使用原始参数作为订单号
    orderNo = encryptedOrderNo;
  }
  
  // 状态管理
  const [paymentResult, setPaymentResult] = useState({
    status: 'loading', // loading, success, failed
    message: '',
    order_no: orderNo,
  });
  const [orderDetail, setOrderDetail] = useState(null);
  
  // 返回会员中心
  const handleBackToMembership = () => {
    navigate('/membership');
  };
  
  // 加载订单支付结果
  useEffect(() => {
    // 验证用户是否已登录
    if (!userInfo || !userInfo.id) {
      message.error('请先登录后再查看支付结果');
      navigate('/auth', { replace: true });
      return;
    }
    
    const fetchOrderResult = async () => {
      if (!orderNo) {
        setPaymentResult({
          status: 'failed',
          message: '订单号无效或已过期',
          order_no: null,
        });
        return;
      }
      
      try {
        // 调用后端API获取订单支付状态
        const response = await axios.get(`/api/payment/order/${orderNo}`);
        
        if (response.data && response.data.success) {
          const orderData = response.data.data.order;
          
          // 安全检查：确保订单属于当前用户
          if (orderData.user_id !== userInfo.id) {
            setPaymentResult({
              status: 'failed',
              message: '无权访问此订单',
            order_no: orderNo,
            });
            return;
          }
          
          setOrderDetail(orderData);
          setPaymentResult({
            status: orderData.payment_status === 'success' ? 'success' : 'failed',
            message: orderData.payment_status === 'success' ? '支付成功' : '支付失败',
            order_no: orderNo,
          });
          
          // 如果支付成功，刷新用户信息（更新会员状态和积分）
          if (orderData.payment_status === 'success') {
            try {
              // 强制刷新用户信息，确保获取最新数据
              await refreshUserInfo(true);
            } catch (refreshError) {
              // 静默处理刷新错误，不影响用户体验
            }
          }
        } else {
          throw new Error(response.data?.message || '获取订单支付结果失败');
        }
      } catch (error) {
        console.error('获取订单结果失败:', error);
        setPaymentResult({
          status: 'failed',
          message: '获取订单支付结果失败，请稍后再试',
          order_no: orderNo,
        });
      }
    };
    
    fetchOrderResult();
  }, [orderNo, refreshUserInfo, userInfo, navigate]);
  
  // 格式化显示时间
  const formatDateTime = (dateString) => {
    if (!dateString) return ''; // 如果日期为空，返回空字符串
    
    try {
    const date = new Date(dateString);
      if (isNaN(date.getTime())) return ''; // 如果日期无效，返回空字符串
      
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  };
  
  // 获取当前时间，用于填充支付时间为空的情况
  const getCurrentDateTime = () => {
    return new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };
  
  // 加载中状态
  if (paymentResult.status === 'loading') {
    return (
      <div className="max-w-lg mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6 text-center">
            <div className="flex flex-col items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
              <p className="text-lg text-muted-foreground">正在查询支付结果...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // 如果没有订单号或获取订单失败
  if (!orderDetail) {
    return (
      <div className="max-w-lg mx-auto py-8 px-4">
        <Card>
          <CardContent className="pt-6 text-center">
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">查询订单失败</h2>
            <p className="text-muted-foreground mb-6">
              {paymentResult.message || '无法获取订单详情，请联系客服'}
            </p>
            <div className="flex flex-col space-y-2">
              <Button onClick={handleBackToMembership}>
                <ArrowLeft className="h-4 w-4 mr-1" />
                返回会员中心
              </Button>
              <Button variant="outline" onClick={() => navigate('/')}>
                <Home className="h-4 w-4 mr-1" />
                返回首页
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // 解析商品详情
  const productDetail = parseProductDetail(orderDetail?.product_detail);
  
  // 确保支付时间显示 - 如果支付成功但支付时间为空，显示"未记录"而不是当前时间
  const displayPaymentTime = orderDetail.payment_status === 'success' && !orderDetail.payment_time 
    ? '未记录' 
    : formatDateTime(orderDetail.payment_time);
  
  return (
    <div className="max-w-lg mx-auto py-8 px-4">
      <Card className="shadow-md">
        {/* 头部导航与标题 */}
        <div className="border-b p-4 flex items-center">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={handleBackToMembership}
            className="mr-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h2 className="text-xl font-bold flex-1">支付结果</h2>
        </div>
        
        <CardContent className="pt-6">
          {/* 支付结果 */}
          <div className="text-center mb-6">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">支付成功</h2>
            <p className="text-muted-foreground">
              您的订单已支付成功，感谢您的购买
            </p>
          </div>
          
          {/* 订单详情 */}
          <div className="bg-muted p-4 rounded-md mb-6">
            <h3 className="font-medium mb-3">订单详情</h3>
            
            <div className="space-y-2 text-sm">
              <div className="grid grid-cols-3 gap-1">
                <span className="text-muted-foreground">订单号:</span>
                <span className="col-span-2">{orderDetail.order_no}</span>
              </div>
              
              {/* 商品名称 - 优化显示方式 */}
              <div className="grid grid-cols-3 gap-1">
                <span className="text-muted-foreground">商品名称:</span>
                <span className="col-span-2">{productDetail?.name || '未知商品'}</span>
              </div>
              
              {/* 如果有积分信息，单独显示 */}
              {productDetail && (productDetail.points || productDetail.bonus_points) && (
                <div className="grid grid-cols-3 gap-1">
                  <span className="text-muted-foreground">积分信息:</span>
                  <span className="col-span-2">
                    {productDetail.points ? `${productDetail.points}积分` : ''}
                    {productDetail.bonus_points ? ` + ${productDetail.bonus_points}赠送积分` : ''}
                  </span>
                </div>
              )}
              
              {/* 如果有商品描述，单独显示 */}
              {productDetail && productDetail.description && (
                <div className="grid grid-cols-3 gap-1">
                  <span className="text-muted-foreground">商品描述:</span>
                  <span className="col-span-2">{productDetail.description}</span>
                </div>
              )}
              
              <div className="grid grid-cols-3 gap-1">
                <span className="text-muted-foreground">支付金额:</span>
                <span className="col-span-2 font-medium">¥{orderDetail.amount}</span>
              </div>
              
              <div className="grid grid-cols-3 gap-1">
                <span className="text-muted-foreground">支付方式:</span>
                <span className="col-span-2">
                  {orderDetail.payment_type === 'wechat' ? '微信支付' : 
                   orderDetail.payment_type === 'alipay' ? '支付宝' : 
                   orderDetail.payment_type === 'mock' ? '模拟支付' :
                   orderDetail.payment_type}
                </span>
              </div>
              
              {/* 创建时间 - 修正字段名称，同时尝试两种可能的字段名 */}
              <div className="grid grid-cols-3 gap-1">
                <span className="text-muted-foreground">创建时间:</span>
                <span className="col-span-2">{formatDateTime(orderDetail.createdAt) || formatDateTime(orderDetail.created_at) || '未记录'}</span>
              </div>
              
              {/* 支付时间 - 优化显示 */}
              <div className="grid grid-cols-3 gap-1">
                <span className="text-muted-foreground">支付时间:</span>
                <span className="col-span-2">{displayPaymentTime || '未记录'}</span>
              </div>
              
              {orderDetail.product_type === 'vip' && (
                <div className="grid grid-cols-3 gap-1">
                  <span className="text-muted-foreground">到期时间:</span>
                  <span className="col-span-2">{formatDateTime(orderDetail.user?.vip_expire_date) || '未记录'}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* 商品信息 - 保留原有的package_info显示 */}
          {orderDetail.package_info && (
            <div className="bg-purple-50 p-4 rounded-md mb-6">
              <h3 className="font-medium mb-3">商品信息</h3>
              
              <div className="space-y-2 text-sm">
                <div className="grid grid-cols-3 gap-1">
                  <span className="text-muted-foreground">名称:</span>
                  <span className="col-span-2">{orderDetail.package_info.name}</span>
                </div>
                
                <div className="grid grid-cols-3 gap-1">
                  <span className="text-muted-foreground">有效期:</span>
                  <span className="col-span-2">{orderDetail.package_info.duration}天</span>
                </div>
                
                <div className="grid grid-cols-3 gap-1">
                  <span className="text-muted-foreground">描述:</span>
                  <span className="col-span-2">{orderDetail.package_info.description}</span>
                </div>
              </div>
            </div>
          )}
          
          {/* 按钮区域 */}
          <div className="flex justify-between mt-6">
            <Button onClick={handleBackToMembership}>
              返回会员中心
            </Button>
            <Button variant="outline" onClick={() => navigate('/')}>
              返回首页
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentResultContent; 
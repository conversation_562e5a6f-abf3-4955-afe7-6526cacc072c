import { message } from 'antd';
import { 
  detectSpecialTemplate, 
  preserveKeyStylesForSpecialTemplate, 
  restoreKeyAnimations 
} from './specialTemplateUtils';

/**
 * 清理HTML内容中的编辑属性和样式，移除虚线边框和不必要的样式
 * @param {string} htmlContent - HTML内容字符串
 * @returns {string} 清理后的HTML内容字符串
 */
export function cleanHtmlContent(htmlContent) {
  if (!htmlContent) return htmlContent;
  
  try {
    // 创建DOM解析器
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    
    // 检测特殊模板结构 - 查找是否存在特定的HTML结构模式
    const hasSpecialStructure = detectSpecialTemplate(doc);
    
    // 查找所有元素，移除可能添加的不必要样式属性
    const allElements = doc.querySelectorAll('*');
    allElements.forEach(el => {
      // 获取计算样式以检查是否需要保留某些样式
      let computedStyle;
      try {
        computedStyle = window.getComputedStyle(el);
      } catch (e) {
        // 忽略计算样式错误
      }
      
      // 如果是特殊模板结构，需要特殊处理
      if (hasSpecialStructure) {
        // 保留特殊模板中的关键样式属性
        preserveKeyStylesForSpecialTemplate(el, computedStyle);
      } else {
        // 标准清理 - 移除可能影响布局的overflow相关属性
        if (el.style) {
          el.style.removeProperty('overflow-wrap');
          el.style.removeProperty('word-break');
        }
        
        // 删除style属性中的overflow-wrap和word-break内联样式
        if (el.hasAttribute('style')) {
          const style = el.getAttribute('style');
          if (style) {
            const newStyle = style
              .replace(/overflow-wrap\s*:\s*[^;]+;?/gi, '')
              .replace(/word-break\s*:\s*[^;]+;?/gi, '')
              .trim();
            
            if (newStyle.length > 0) {
              el.setAttribute('style', newStyle);
            } else {
              el.removeAttribute('style');
            }
          }
        }
      }
      
      // 移除所有data-original-*属性
      Array.from(el.attributes).forEach(attr => {
        if (attr.name.startsWith('data-original-')) {
          el.removeAttribute(attr.name);
        }
      });
      
      // 移除所有class属性中的编辑器相关类名
      if (el.hasAttribute('class')) {
        const classNames = el.getAttribute('class').split(' ');
        const filteredClassNames = classNames.filter(className => 
          !['drag-mode', 'dragging', 'resize-mode', 'editing-active-outline', 
            'selected-for-drag', 'text-editor-active'].includes(className));
        
        if (filteredClassNames.length > 0) {
          el.setAttribute('class', filteredClassNames.join(' '));
        } else {
          el.removeAttribute('class');
        }
      }
    });
    
    // 查找所有可能包含编辑属性的元素
    const editableElements = doc.querySelectorAll('[contenteditable], [data-editing], [data-field], [data-editable-fengmian], .drag-mode, .dragging, .editing-active-outline, .selected-for-drag, .resize-mode');
    
    // 移除所有编辑相关属性
    editableElements.forEach(el => {
      el.removeAttribute('contenteditable');
      el.removeAttribute('data-editing');
      el.removeAttribute('data-field');
      el.removeAttribute('data-editable-fengmian');
      el.removeAttribute('title');
      el.removeAttribute('tabindex');
      el.classList.remove('drag-mode');
      el.classList.remove('dragging');
      el.classList.remove('resize-mode');
      el.classList.remove('editing-active-outline');
      el.classList.remove('selected-for-drag');
      
      // 清理可能影响显示的内联样式
      if (el.style) {
        el.style.cursor = '';
        el.style.outline = '';
        el.style.outlineStyle = '';
        el.style.outlineWidth = '';
        el.style.outlineColor = '';
        el.style.outlineOffset = '';
        el.style.border = '';
        el.style.borderStyle = '';
        el.style.borderWidth = '';
        el.style.borderColor = '';
        el.style.boxShadow = '';
        // 移除可能的拖拽相关内联样式
        // 注释掉清空transform的代码，保留拖拽后的位置信息
        // el.style.transform = '';
        el.style.zIndex = '';
        el.style.opacity = '';
        el.style.userSelect = '';
        el.style.webkitUserSelect = '';
        el.style.msUserSelect = '';
        el.style.pointerEvents = '';
        
        // 移除动画和过渡相关样式
        el.style.animation = '';
        el.style.transition = '';
      }
      
      // 移除所有data-*属性，除非是必要的数据属性
      Array.from(el.attributes).forEach(attr => {
        if (attr.name.startsWith('data-') && 
            !['data-id', 'data-name', 'data-value', 'data-type'].includes(attr.name)) {
          el.removeAttribute(attr.name);
        }
      });
    });
    
    // 移除所有编辑器UI相关元素
    const elementsToRemove = doc.querySelectorAll(
      '.resize-handle, .drag-handle, .text-editor-toolbar, .text-editor-toolbar-container, ' + 
      '.color-picker, .color-picker-wrapper, .color-picker-popup'
    );
    elementsToRemove.forEach(el => el.remove());
    
    // 特殊处理：如果是特殊模板，恢复关键动画样式
    if (hasSpecialStructure) {
      restoreKeyAnimations(doc);
    }
    
    // 获取清理后的HTML内容
    const cleanedContent = doc.querySelector('body').innerHTML;
    
    return cleanedContent;
  } catch (error) {
    console.error('清理HTML内容失败:', error);
    return htmlContent; // 如果清理失败，返回原始内容
  }
}



/**
 * 清理封面元素和样式以进行下载，移除所有编辑器相关的元素和样式
 * @param {Document} doc - iframe的document对象
 * @param {HTMLElement} coverElement - 封面元素
 * @returns {Object} 包含清理后的元素和样式的对象
 */
export function cleanForDownload(doc, coverElement) {
  // 1. 创建封面元素的克隆
  const cleanCoverElement = coverElement.cloneNode(true);

  // 2. 移除所有contenteditable属性和相关编辑属性
  const editableElements = cleanCoverElement.querySelectorAll('[contenteditable], [data-field], .drag-mode');
  editableElements.forEach(el => {
    el.removeAttribute('contenteditable');
    el.removeAttribute('data-field');
    el.removeAttribute('title');
    el.removeAttribute('tabindex');
    el.classList.remove('drag-mode');
    el.classList.remove('dragging');
    el.classList.remove('resize-mode');

    // 清理可能影响显示的内联样式
    if (el.style) {
      el.style.cursor = 'default';
      el.style.outline = 'none';
      // 移除可能的拖拽相关内联样式，但保留transform样式以保持拖拽后的位置
      el.style.removeProperty('z-index');
      el.style.removeProperty('opacity');
    }
  });

  // 3. 移除所有编辑器UI相关元素
  const elementsToRemove = cleanCoverElement.querySelectorAll(
    '.resize-handle, .drag-handle, .text-editor-toolbar, .text-editor-toolbar-container, ' +
    '.color-picker, .color-picker-wrapper, .color-picker-popup'
  );
  elementsToRemove.forEach(el => el.remove());

  // 4. 确保HTML和BODY的背景样式被保留
  let htmlBackgroundStyles = '';
  let bodyBackgroundStyles = '';
  
  try {
    // 获取原始HTML元素的背景样式
    const htmlElement = doc.documentElement;
    const htmlComputedStyle = window.getComputedStyle(htmlElement);
    const backgroundProps = [
      'backgroundColor', 'backgroundImage', 'backgroundPosition', 'backgroundSize', 'backgroundRepeat', 'background'
    ];
    
    backgroundProps.forEach(prop => {
      const value = htmlComputedStyle[prop];
      if (value && value !== 'none' && value !== 'initial' && value !== 'transparent') {
        htmlBackgroundStyles += `html { ${prop.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}; }\n`;
      }
    });
    
    // 获取原始BODY元素的背景样式
    const bodyElement = doc.body;
    const bodyComputedStyle = window.getComputedStyle(bodyElement);
    
    backgroundProps.forEach(prop => {
      const value = bodyComputedStyle[prop];
      if (value && value !== 'none' && value !== 'initial' && value !== 'transparent') {
        bodyBackgroundStyles += `body { ${prop.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}; }\n`;
      }
    });
  } catch (e) {
    console.warn('获取HTML/BODY背景样式失败:', e);
  }

  // 5. 过滤样式表
  let filteredStyles = '';
  const styleElements = doc.querySelectorAll('style');

  styleElements.forEach(style => {
    // 排除明确是编辑器样式的style标签
    if (style.id === 'text-editor-global-styles') {
      return;
    }

    // 过滤编辑器相关的CSS规则
    filteredStyles += filterEditorStyles(style.innerHTML) + '\n';
  });

  // 6. 添加HTML和BODY的背景样式到过滤后的样式中
  filteredStyles = htmlBackgroundStyles + bodyBackgroundStyles + filteredStyles;

  // 7. 确保内容容器有明确的背景
  const ensureBackground = (element) => {
    if (!element) return;
    
    // 检查元素是否有明确的背景
    const computedStyle = window.getComputedStyle(element);
    const hasBackground = 
      computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)' && 
      computedStyle.backgroundColor !== 'transparent';
    const hasBackgroundImage = 
      computedStyle.backgroundImage !== 'none';
    
    // 如果元素没有明确的背景，添加一个白色背景
    if (!hasBackground && !hasBackgroundImage) {
      element.style.backgroundColor = '#ffffff';
    }
  };
  
  ensureBackground(cleanCoverElement);

  return {
    element: cleanCoverElement,
    styles: filteredStyles
  };
}

/**
 * 过滤编辑器相关的CSS规则
 * @param {string} cssText - CSS文本
 * @returns {string} 过滤后的CSS文本
 */
export function filterEditorStyles(cssText) {
  // 1. 移除CSS注释
  cssText = cssText.replace(/\/\*[\s\S]*?\*\//g, '');

  // 2. 将CSS文本分割成规则块
  const cssBlocks = cssText.split('}');

  // 3. 过滤掉包含编辑器选择器的规则块
  const filteredBlocks = cssBlocks.filter(block => {
    // 跳过空块
    if (!block.trim()) return false;

    // 尝试提取选择器部分（在{之前的内容）
    const parts = block.split('{');
    if (parts.length < 2) return false;

    const selectorPart = parts[0].trim();

    // 编辑器相关选择器的列表 - 如果选择器包含任何这些，我们将过滤掉该规则
    const editorSelectors = [
      // contenteditable相关
      '[contenteditable',

      // resize-handle相关
      '.resize-handle',
      '.resize-handle.top',
      '.resize-handle.right',
      '.resize-handle.bottom',
      '.resize-handle.left',
      '.resize-handle.top-left',
      '.resize-handle.top-right',
      '.resize-handle.bottom-left',
      '.resize-handle.bottom-right',

      // 拖拽相关
      '.drag-mode',
      '.dragging',
      '.resize-mode',
      '.drag-handle',

      // 工具栏相关
      '.text-editor-toolbar',
      '.text-editor-toolbar-container',

      // 颜色选择器相关
      '.color-picker',
      '.color-picker-wrapper',
      '.color-picker-popup',
      '.color-preview',
      '.color-custom',
      '.color-input',
      '.confirm-button'
    ];

    // 检查选择器是否包含任何编辑器相关选择器
    const hasEditorSelector = editorSelectors.some(editorSelector =>
      selectorPart.includes(editorSelector)
    );

    // 返回true表示保留此块（非编辑器相关），false表示过滤掉（编辑器相关）
    return !hasEditorSelector;
  });

  // 4. 重新组合过滤后的CSS块
  return filteredBlocks.map(block => block.trim() + (block.trim() ? '}' : '')).join('\n');
}

/**
 * 下载当前预览的HTML内容
 * @param {Object} previewAreaRef - 预览区域的引用
 * @param {String} selectedStyle - 当前选择的样式
 * @param {String} generatedHTML - 生成的HTML内容
 */
export const downloadHtml = (previewAreaRef, selectedStyle, generatedHTML) => {
  if (!generatedHTML) {
    message.warning('没有可下载的内容');
    return;
  }

  try {
    // 获取当前预览区域的HTML内容
    if (!previewAreaRef.current) {
      message.error('找不到预览区域，请刷新页面后重试');
      return;
    }

    // 获取iframe元素
    let iframe;
    if (typeof previewAreaRef.current.getIframe === 'function') {
      iframe = previewAreaRef.current.getIframe();
    } else if (previewAreaRef.current.tagName === 'IFRAME') {
      iframe = previewAreaRef.current;
    } else {
      message.error('无法访问预览内容 iframe');
      return;
    }

    if (!iframe || !iframe.contentDocument) {
      message.error('无法访问预览内容，请刷新页面后重试');
      return;
    }

    // 使用新函数生成用于下载的HTML内容
    const htmlContent = generatePreviewHtmlForDownload(iframe);

    // 下载HTML文件
    const link = document.createElement('a');
    link.href = `data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`;
    link.download = `cover_${selectedStyle}_${Date.now()}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    message.success('HTML已开始下载');
  } catch (error) {
    console.error('下载失败:', error);
    message.error('下载失败：' + error.message);
  }
};

/**
 * 找到对应的原始元素
 * @param {HTMLElement} originalRoot - 原始DOM树的根元素
 * @param {HTMLElement} clonedElement - 克隆DOM树中的元素
 * @returns {HTMLElement|null} 找到的对应元素或null
 */
function findCorrespondingElement(originalRoot, clonedElement) {
  // 尝试通过选择器查找对应元素
  try {
    // 按优先级尝试不同方式查找元素
    if (clonedElement.id) {
      return originalRoot.querySelector(`#${clonedElement.id}`);
    }

    if (clonedElement.className) {
      // 创建一个精确的类选择器
      const classSelector = `.${clonedElement.className.split(' ').join('.')}`;
      const found = originalRoot.querySelector(classSelector);
      if (found) return found;
    }

    // 尝试通过标签名和内容查找
    const tagName = clonedElement.tagName.toLowerCase();
    const elements = originalRoot.querySelectorAll(tagName);
    for (const el of elements) {
      if (el.textContent === clonedElement.textContent) {
        return el;
      }
    }

    // 尝试通过位置查找
    if (clonedElement.parentNode) {
      const parentInOriginal = findCorrespondingElement(originalRoot, clonedElement.parentNode);
      if (parentInOriginal) {
        const index = Array.from(clonedElement.parentNode.children).indexOf(clonedElement);
        if (index >= 0 && parentInOriginal.children.length > index) {
          return parentInOriginal.children[index];
        }
      }
    }
  } catch (error) {
    console.warn('查找对应元素失败:', error);
  }

  return null;
}

/**
 * 复制计算样式
 * @param {HTMLElement} sourceElement - 源元素
 * @param {HTMLElement} targetElement - 目标元素
 */
function copyComputedStyles(sourceElement, targetElement) {
  try {
    if (!sourceElement || !targetElement) return;

    const computedStyle = window.getComputedStyle(sourceElement);

    // 重要的样式属性
    const criticalStyles = [
      'font-family', 'font-size', 'font-weight', 'line-height', 'color',
      'background-color', 'border', 'border-radius', 'padding', 'margin',
      'display', 'position', 'top', 'left', 'right', 'bottom', 'zIndex',
      'transform', 'opacity', 'visibility',
      'textShadow', 'letterSpacing', 'textTransform'
    ];

    // 应用关键样式
    criticalStyles.forEach(style => {
      const value = computedStyle.getPropertyValue(style);
      if (value) {
        targetElement.style.setProperty(style, value);
      }
    });

    // 特别处理字体
    targetElement.style.fontFamily = computedStyle.fontFamily;
  } catch (error) {
    console.warn('复制样式失败:', error);
  }
}

/**
 * 处理伪元素
 * @param {HTMLElement} originalElement - 原始元素
 * @param {HTMLElement} clonedElement - 克隆元素
 * @param {string} pseudo - 伪元素名称 ('before' 或 'after')
 */
function handlePseudoElements(originalElement, clonedElement, pseudo) {
  try {
    const pseudoStyle = window.getComputedStyle(originalElement, `::${pseudo}`);
    const content = pseudoStyle.getPropertyValue('content');

    // 如果伪元素有内容，创建一个真实DOM元素来模拟它
    if (content && content !== 'none' && content !== '""' && content !== "''") {
      const pseudoElement = document.createElement('span');

      // 设置基本样式，模拟伪元素
      pseudoElement.style.position = 'absolute';

      // 复制伪元素的样式到新元素
      for (let i = 0; i < pseudoStyle.length; i++) {
        const property = pseudoStyle[i];
        const value = pseudoStyle.getPropertyValue(property);
        pseudoElement.style[property] = value;
      }

      // 伪元素的位置、颜色等关键属性
      const criticalProps = ['position', 'top', 'left', 'right', 'bottom',
                           'color', 'backgroundColor', 'fontSize', 'width',
                           'height', 'zIndex', 'transform'];

      criticalProps.forEach(prop => {
        pseudoElement.style[prop] = pseudoStyle[prop];
      });

      // 设置伪元素内容
      let cleanContent = content;
      if (content.startsWith('"') && content.endsWith('"')) {
        cleanContent = content.substring(1, content.length - 1);
      }

      // 如果是图标或特殊字符，尝试设置内容
      if (cleanContent === '"' || cleanContent === "'") {
        pseudoElement.innerHTML = cleanContent;
      } else if (cleanContent === 'url(' || cleanContent.startsWith('url(')) {
        // 如果是图片URL，创建一个img元素
        pseudoElement.style.backgroundImage = content;
      } else {
        pseudoElement.innerHTML = cleanContent;
      }

      // 根据伪元素类型设置位置
      if (pseudo === 'after') {
        clonedElement.appendChild(pseudoElement);
      } else {
        clonedElement.insertBefore(pseudoElement, clonedElement.firstChild);
      }
    }
  } catch (error) {
    console.warn(`处理伪元素 ::${pseudo} 失败:`, error);
  }
}

/**
 * 移除HTML中的所有注释和不必要的指令以及平台特定信息
 * @param {string} htmlContent - HTML内容字符串
 * @returns {string} 移除注释、指令和平台特定信息后的HTML内容
 */
export function removeCommentsAndInstructions(htmlContent) {
  if (!htmlContent) return htmlContent;
  
  // 移除HTML注释
  htmlContent = htmlContent.replace(/<!--[\s\S]*?-->/g, '');
  
  // 移除AI指令注释
  htmlContent = htmlContent.replace(/\/\*\s*AI指令[\s\S]*?\*\//g, '');
  
  return htmlContent;
}

/**
 * 生成用于下载的预览HTML内容
 * 基于previewUtils.js中的openHtmlPreview函数，但专门用于生成下载用的HTML内容
 * @param {HTMLIFrameElement} iframe - 预览区域iframe元素
 * @returns {string} 生成的HTML内容
 */
export const generatePreviewHtmlForDownload = (iframe) => {
  try {
    if (!iframe || !iframe.contentWindow || !iframe.contentDocument) {
      throw new Error('无法访问预览框架内容');
    }

    const iframeDoc = iframe.contentDocument;

    // 1. 提取iframe head中的完整内容（包括脚本、样式、链接、meta等）
    const headElements = Array.from(iframeDoc.head.children)
      .filter(el => {
        // 保留所有重要的head元素，但排除编辑器相关的
        const tagName = el.tagName.toLowerCase();
        const isEditorRelated = el.classList.contains('editor-style') ||
                               el.id?.includes('editor') ||
                               el.className?.includes('editor');

        return ['style', 'link', 'script', 'meta', 'title', 'base'].includes(tagName) && !isEditorRelated;
      })
      .map(el => {
        // 对于script标签，确保内容完整
        if (el.tagName.toLowerCase() === 'script') {
          return el.outerHTML;
        }
        // 对于style标签，清理编辑器相关样式
        if (el.tagName.toLowerCase() === 'style') {
          const cleanedCSS = filterEditorStyles(el.innerHTML);
          return `<style>${cleanedCSS}</style>`;
        }
        return el.outerHTML;
      })
      .join('\n');

    // 2. 提取iframe body中的内容并清理编辑属性
    // 创建一个临时的DOM文档
    const tempDoc = document.implementation.createHTMLDocument('');
    tempDoc.body.innerHTML = iframeDoc.body.innerHTML;
    
    // 清理编辑相关的属性
    const attributesToRemove = ['contenteditable', 'data-editing', 'data-editable-fengmian', 'data-field', 'tabindex', 'title'];
    attributesToRemove.forEach(attr => {
      tempDoc.querySelectorAll(`[${attr}]`).forEach(el => {
        el.removeAttribute(attr);
      });
    });
    
    // 清理编辑相关的类
    const editingClasses = ['drag-mode', 'dragging', 'selected-for-drag', 'resize-mode', 'editing-active-outline'];
    editingClasses.forEach(className => {
      tempDoc.querySelectorAll(`.${className}`).forEach(el => {
        el.classList.remove(className);
      });
    });
    
    // 清理可能影响外观的样式属性
    tempDoc.querySelectorAll('[style]').forEach(el => {
      const style = el.style;
      if (style.outline) style.outline = '';
      if (style.outlineStyle) style.outlineStyle = '';
      if (style.outlineWidth) style.outlineWidth = '';
      if (style.outlineColor) style.outlineColor = '';
      if (style.outlineOffset) style.outlineOffset = '';
      if (style.border && (style.border.includes('dashed') || style.border.includes('solid'))) style.border = '';
      if (style.cursor && (style.cursor === 'text' || style.cursor === 'move')) style.cursor = '';
    });
    
    // 移除编辑器UI元素
    const uiSelectors = [
      '.text-editor-toolbar',
      '.resize-handle',
      '.drag-handle',
      '[data-role="text-editor-toolbar"]',
      '[data-role="resize-handle"]',
      '[data-role="drag-handle"]'
    ];
    tempDoc.querySelectorAll(uiSelectors.join(', ')).forEach(el => {
      el.remove();
    });
    
    // 获取清理后的body内容
    const bodyContent = tempDoc.body.innerHTML;

    // 3. 检查是否为完整HTML结构
    const isCompleteHtml = iframeDoc.documentElement.outerHTML.includes('<html');

    // 4. 组装最终的HTML文档
    const finalHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>封面</title>
  ${headElements}
  <style>
    /* 基础样式，确保内容正确显示 */
    html, body {
      height: 100%;
      width: 100%;
      margin: 0;
      padding: 0;
      background-color: #ffffff;
    }

    /* 如果是完整HTML结构，保持原有布局 */
    ${isCompleteHtml ? `
    body {
      position: relative;
    }
    ` : `
    /* 如果是HTML片段，使用居中布局 */
    body {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }
    #content-wrapper {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      transform-origin: center center;
    }
    `}

    /* 隐藏编辑器特定元素 */
    .text-editor-toolbar, .resize-handle, .drag-handle,
    [data-role="text-editor-toolbar"], [data-role="resize-handle"], [data-role="drag-handle"],
    .editor-overlay, .editor-controls, .text-editor-panel {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      pointer-events: none !important;
    }

    /* 确保所有元素不显示编辑相关的样式 */
    * {
      outline: none !important;
    }

    /* 确保contenteditable元素不显示编辑框 */
    [contenteditable], [contenteditable="true"], [data-editable="true"] {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
      cursor: default !important;
    }

    /* 确保背景图片正确显示 */
    [style*="background-image"] {
      background-repeat: no-repeat !important;
      background-size: cover !important;
      background-position: center !important;
    }
  </style>
</head>
<body>
  ${isCompleteHtml ? bodyContent : `<div id="content-wrapper">${bodyContent}</div>`}
</body>
</html>`;

    return finalHtml;
  } catch (error) {
    console.error('生成预览HTML内容失败:', error);
    throw error;
  }
};

/**
 * 生成并打开HTML预览窗口
 * @param {HTMLElement} iframe - 预览区域iframe元素
 * @returns {Promise<string>} 预览窗口的URL
 */
export const openHtmlPreview = async (iframe) => {
  try {
    console.log('开始生成预览...');

    // 检查iframe是否有效
    if (!iframe || !iframe.contentWindow || !iframe.contentDocument) {
      throw new Error('无法访问预览框架内容');
    }

    const iframeDoc = iframe.contentDocument;

    // 获取所有样式表内容
    let styles = Array.from(iframeDoc.querySelectorAll('style'))
      .map(style => style.innerHTML)
      .join('\n');

    // 获取内容容器
    const contentContainer = iframeDoc.querySelector('.content-container');
    if (!contentContainer) {
      throw new Error('找不到内容容器');
    }

    // 清理封面元素和样式，移除编辑器相关的内容
    const cleaned = cleanForDownload(iframeDoc, contentContainer);

    // 使用清理后的样式
    styles = cleaned.styles;

    // 获取body内容（使用清理后的内容）
    const bodyContent = cleaned.element.outerHTML;

    // 创建完整的HTML文档
    const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>封面预览</title>
  <style>
    ${styles}
    body {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
    }
    .content-wrapper {
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<body>
  <div class="content-wrapper">
    ${bodyContent}
  </div>
</body>
</html>`;

    // 创建一个Blob对象
    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);

    // 创建一个新窗口来显示HTML内容
    const newWindow = window.open(url, '_blank');

    if (!newWindow) {
      // 如果弹出窗口被阻止，则提供下载链接
      const link = document.createElement('a');
      link.href = url;
      link.download = `封面预览_${Date.now()}.html`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success('HTML已下载，请打开文件查看预览');
    } else {
      // 如果成功打开了新窗口
      message.success('预览窗口已打开');
    }

    // 返回URL，以便后续清理
    return url;
  } catch (error) {
    console.error('预览生成失败:', error);
    message.error('预览生成失败: ' + error.message);
    throw error;
  }
};

/**
 * 打开HTML预览窗口
 * @param {Object} previewAreaRef - 预览区域的引用
 * @param {Function} setIsDownloading - 设置加载状态的函数
 */
export const handleOpenPreview = async (previewAreaRef, setIsPreviewLoading) => {
  try {
    console.log('开始生成预览...');
    setIsPreviewLoading && setIsPreviewLoading(true);

    if (!previewAreaRef.current) {
      message.error('找不到预览区域，请刷新页面后重试');
      setIsPreviewLoading && setIsPreviewLoading(false);
      return;
    }

    // 获取iframe元素
    const iframe = previewAreaRef.current.getIframe?.();
    if (!iframe) {
      message.error('无法访问预览内容，请刷新页面后重试');
      setIsPreviewLoading && setIsPreviewLoading(false);
      return;
    }

    // 显示加载提示
    message.loading('正在生成预览窗口...', 1);

    // 打开预览窗口
    await openHtmlPreview(iframe);
  } catch (error) {
    console.error('预览生成失败:', error);
    message.error('预览生成失败: ' + error.message);
  } finally {
    // 无论成功或失败，都重置加载状态
    setIsPreviewLoading && setIsPreviewLoading(false);
    console.log('预览操作完成');
  }
};

/**
 * 下载HTML文件，支持清理编辑UI并在下载后恢复
 * @param {Function} cleanForDownloadAndRestore - 清理编辑UI并在下载后恢复的函数
 * @param {Object} appState - 应用状态对象，包含coverDetails等信息
 * @param {Object} logger - 日志记录对象
 */
export const downloadHtmlWithCleanup = (cleanForDownloadAndRestore, appState, logger) => {
  cleanForDownloadAndRestore(() => {
    try {
      // 获取iframe元素
      const iframe = document.querySelector('.chat-preview-container iframe');
      if (!iframe || !iframe.contentDocument) {
        message.error('无法访问预览内容，请刷新页面后重试');
        return;
      }

      // 添加小延迟确保处理正确完成
      setTimeout(async () => {
        try {
          // 使用新函数生成用于下载的HTML内容
          const htmlContent = generatePreviewHtmlForDownload(iframe);
          
          // 下载HTML文件，使用通用命名
          const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `cover_${new Date().toISOString().slice(0, 10)}.html`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          
          message.success('HTML已开始下载');
        } catch (error) {
          if (logger) {
            logger.error('下载HTML失败:', error);
          } else {
            console.error('下载HTML失败:', error);
          }
          message.error('下载HTML失败，请重试');
        }
      }, 50);
    } catch (error) {
      if (logger) {
        logger.error('下载HTML失败:', error);
      } else {
        console.error('下载HTML失败:', error);
      }
      message.error('下载HTML失败，请重试');
    }
  });
};

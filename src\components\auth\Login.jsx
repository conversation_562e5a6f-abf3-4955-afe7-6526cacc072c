import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  Smartphone,
  KeyRound,
  Lock,
  Eye,
  EyeOff,
  LogIn,
  Loader2,
  CheckCircle2
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

// API调用函数
const sendSmsCodeOriginal = async (phone, purpose) => {
  try {
    const response = await axios.post('/api/sms/send', { phone, purpose });
    return response.data;
  } catch (error) {
    console.error('发送验证码失败:', error);
    throw error;
  }
};

const loginOriginal = async (data) => {
  try {
    const response = await axios.post('/api/auth/login', data);
    return response.data;
  } catch (error) {
    console.error('登录失败:', error);
    const errorMsg = error.response?.data?.message || '登录失败，请重试';
      const errorData = {
      message: errorMsg,
      success: false
      };
      throw errorData;
  }
};

const loginByVerifyCodeOriginal = async (data) => {
  try {
    const response = await axios.post('/api/auth/login/verify-code', data);
    return response.data;
  } catch (error) {
    console.error('验证码登录失败:', error);
    const errorMsg = error.response?.data?.message || '验证码登录失败，请重试';
      const errorData = {
      message: errorMsg,
      success: false
      };
      throw errorData;
    }
};

const Login = () => {
  const [loginType, setLoginType] = useState('verifyCode');  // 'password' | 'verifyCode'
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [verifyCode, setVerifyCode] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [sendingCode, setSendingCode] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();

  // Countdown timer effect
  useEffect(() => {
    let timerId;
    if (countdown > 0) {
      timerId = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    } else if (countdown === 0 && timerId) {
      clearInterval(timerId);
    }
    return () => clearInterval(timerId);
  }, [countdown]);

  // Handle Send SMS Code
  const handleSendCode = async () => {
    if (!phone) {
      setErrorMessage('请输入手机号');
      return;
    }
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      setErrorMessage('请输入有效的手机号');
      return;
    }

    setErrorMessage('');
    setSendingCode(true);

    try {
      const response = await sendSmsCodeOriginal(phone, 'login');
      if (response.success) {
        setErrorMessage('');
        setCountdown(60);
      } else {
        setErrorMessage(response.message || '验证码发送失败');
      }
    } catch (error) {
      setErrorMessage(error.response?.data?.message || error.message || '发送验证码失败，请检查网络连接');
    } finally {
      setSendingCode(false);
    }
  };

  // Handle Form Submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');
    setLoading(true);

    try {
      let response;
      if (loginType === 'password') {
        if (!phone || !password) {
          setErrorMessage('手机号和密码不能为空');
          setLoading(false);
          return;
        }
        response = await loginOriginal({ phone, password });
      } else { // verifyCode login
        if (!phone || !verifyCode) {
          setErrorMessage('手机号和验证码不能为空');
          setLoading(false);
          return;
        }
        response = await loginByVerifyCodeOriginal({ phone, verifyCode });
      }

      if (response.success) {
        setErrorMessage('');
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.user));

        toast({
          title: '登录成功',
          description: '即将跳转到首页',
          variant: 'default'
        });

        setTimeout(() => {
          window.location.href = '/';
        }, 800);
      } else {
        setErrorMessage(response.message || '登录失败，请重试');
      }
    } catch (error) {
      setErrorMessage(error.message || '登录请求失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full flex flex-col items-center">
      <div className="w-full bg-card shadow-lg border border-border/20 rounded-xl">
        <div className="text-center pt-8 pb-4 sm:pt-6 sm:pb-4">
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
            {loginType === 'password' ? '用户登录' : '验证码登录'}
          </h1>
        </div>

        <div className="space-y-6 px-6 sm:px-8">
          <div className="flex border border-border rounded-md p-1 bg-muted/40">
            <button
              className={`flex-1 h-9 sm:h-10 rounded-md ${loginType === 'verifyCode' ? 'bg-primary text-primary-foreground shadow-sm' : 'text-muted-foreground hover:bg-accent/70'}`}
              onClick={() => setLoginType('verifyCode')}
            >
              验证码登录
            </button>
            <button
              className={`flex-1 h-9 sm:h-10 rounded-md ${loginType === 'password' ? 'bg-primary text-primary-foreground shadow-sm' : 'text-muted-foreground hover:bg-accent/70'}`}
              onClick={() => setLoginType('password')}
            >
              密码登录
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="login_phone" className="text-sm font-medium text-muted-foreground">手机号码</label>
              <div className="relative">
                <Smartphone className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <input
                  id="login_phone"
                  type="tel"
                  placeholder="请输入您的手机号"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  required
                  className="pl-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                />
              </div>
            </div>

            {loginType === 'password' && (
              <div className="space-y-2">
                <label htmlFor="login_password" className="text-sm font-medium text-muted-foreground">登录密码</label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <input
                    id="login_password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="请输入您的密码"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="pl-10 pr-10 h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  />
                  <button
                    type="button"
                    className="absolute right-1.5 top-1/2 -translate-y-1/2 h-8 w-8 text-muted-foreground hover:bg-accent/80 rounded-md flex items-center justify-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    <span className="sr-only">{showPassword ? '隐藏密码' : '显示密码'}</span>
                  </button>
                </div>
                <div className="text-right mt-1">
                  <Link to="/forgot-password"
                    className="text-xs sm:text-sm text-primary hover:underline hover:text-primary/80 font-medium"
                  >
                    忘记密码？
                  </Link>
                </div>
              </div>
            )}

            {loginType === 'verifyCode' && (
              <div className="space-y-2">
                <label htmlFor="login_verifyCode" className="text-sm font-medium text-muted-foreground">短信验证码</label>
                <div className="flex space-x-2">
                  <div className="relative flex-grow">
                    <input
                      id="login_verifyCode"
                      type="text"
                      placeholder="请输入6位验证码"
                      value={verifyCode}
                      onChange={(e) => setVerifyCode(e.target.value)}
                      required
                      className="h-10 sm:h-11 bg-background/70 border-border focus:border-primary w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={handleSendCode}
                    disabled={sendingCode || countdown > 0}
                    className="shrink-0 h-10 sm:h-11 border-primary text-primary hover:bg-primary/10 disabled:opacity-70 rounded-md border px-3 py-2 text-sm font-medium"
                  >
                    {sendingCode ? <Loader2 className="h-4 w-4 animate-spin"/> : (countdown > 0 ? `${countdown}秒` : '获取验证码')}
                  </button>
                </div>
              </div>
            )}

            {errorMessage && (
              <div className="text-sm text-red-500 text-center">{errorMessage}</div>
            )}

            <button
              type="submit"
              className="w-full bg-primary text-primary-foreground hover:bg-primary/90 text-base py-2.5 sm:py-3 h-auto shadow-md hover:shadow-lg transition-all duration-300 rounded-md flex items-center justify-center"
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              ) : (
                <LogIn className="mr-2 h-5 w-5" />
              )}
              {loading ? '登录中...' : '登 录'}
            </button>
          </form>
        </div>

        <div className="text-center pb-4 sm:pb-5 px-6 sm:px-8 mt-3">
          <p className="text-sm text-muted-foreground w-full">
            还没有账户？{' '}
            <Link to="/register" className="font-semibold text-primary hover:underline hover:text-primary/80">
              立即注册
            </Link>
          </p>
          <p className="text-xs text-muted-foreground mt-2">
            登录视为您已阅读并同意本站的{' '}
            <Link to="/policies/agreement" target="_blank" className="text-primary hover:underline">
              用户协议
            </Link>
            {' '}和{' '}
            <Link to="/policies/privacy" target="_blank" className="text-primary hover:underline">
              隐私政策
            </Link>
            ，未注册的手机号将自动注册
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
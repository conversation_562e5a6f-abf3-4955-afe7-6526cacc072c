import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog';
import { Crown, X, Sparkles } from 'lucide-react';

/**
 * 权限提示弹出层组件
 * @param {Object} props - 组件属性
 * @param {boolean} props.open - 是否显示弹出层
 * @param {Function} props.onClose - 关闭弹出层的回调函数
 * @param {string} props.featureName - 功能名称
 * @param {string} props.reason - 权限不足的原因
 * @returns {JSX.Element} 权限提示弹出层
 */
const PermissionDialog = ({
  open = false,
  onClose = () => {},
  featureName = '该功能',
  reason = '您的账号权限不足，无法使用此功能'
}) => {

  // 功能名称映射
  const featureNameMap = {
    '分享链接': '分享',
    'HTML下载': '下载html',
    '自定义图片': '自定义图片',
    '文本编辑': '编辑文本'
  };

  // 获取显示的功能名称
  const displayFeatureName = featureNameMap[featureName] || featureName;

  const handleUpgrade = () => {
    // 预留：跳转到会员充值页面
    console.log('跳转到会员充值页面');
    // TODO: 实现跳转到会员充值页面的逻辑
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md" hideDefaultCloseButton>
        <DialogHeader className="text-center pb-3">
          <div className="mx-auto w-14 h-14 bg-gradient-to-br from-orange-100 to-yellow-100 rounded-full flex items-center justify-center mb-3">
            <Crown className="w-7 h-7 text-orange-500" />
          </div>
          <DialogTitle className="text-base font-medium text-gray-800 leading-relaxed px-2 text-center">
            抱歉，您的账号权限不足，无法使用{displayFeatureName}功能
          </DialogTitle>
          <DialogDescription className="text-sm text-gray-500 mt-2">
            {reason}
          </DialogDescription>
        </DialogHeader>

        <div className="text-center space-y-3">
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
            <Sparkles className="w-4 h-4 text-yellow-500" />
            <span>开通高级会员，畅享全部功能</span>
          </div>

          <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4 border border-orange-100">
            <div className="text-sm text-gray-700">
              <div className="font-medium text-orange-700 mb-3 text-center">高级会员特权</div>
              <ul className="space-y-2">
                <li className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-orange-400 rounded-full flex-shrink-0"></div>
                  <span className="text-sm">编辑文本</span>
                </li>
                <li className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-orange-400 rounded-full flex-shrink-0"></div>
                  <span className="text-sm">下载html</span>
                </li>
                <li className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-orange-400 rounded-full flex-shrink-0"></div>
                  <span className="text-sm">分享链接</span>
                </li>
                <li className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-orange-400 rounded-full flex-shrink-0"></div>
                  <span className="text-sm">自定义图片</span>
                </li>
                <li className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-orange-400 rounded-full flex-shrink-0"></div>
                  <span className="text-sm">每日积分赠送</span>
                </li>
                <li className="flex items-center justify-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-orange-400 rounded-full flex-shrink-0"></div>
                  <span className="text-sm">更多特权随更新开放</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="flex space-x-3 pt-4">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2.5 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <X className="w-4 h-4" />
            <span>暂不升级</span>
          </button>
          <button
            onClick={handleUpgrade}
            className="flex-1 px-4 py-2.5 text-white bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg"
          >
            <Crown className="w-4 h-4" />
            <span>立即开通</span>
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PermissionDialog;

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';

const UserAgreement = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [agreement, setAgreement] = useState({
    content: '',
    version: '',
    updated_at: ''
  });

  useEffect(() => {
    const fetchUserAgreement = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/system/policies/agreement');
        if (response.data.success) {
          setAgreement(response.data.data);
        } else {
          setError('获取用户协议失败');
        }
      } catch (error) {
        console.error('获取用户协议失败:', error);
        setError('获取用户协议失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };

    fetchUserAgreement();
  }, []);

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="bg-card shadow-md rounded-lg p-6 md:p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl md:text-3xl font-bold text-foreground">用户协议</h1>
          <Link to="/" className="text-primary hover:underline text-sm">
            返回首页
          </Link>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">加载中...</span>
          </div>
        ) : error ? (
          <div className="bg-destructive/10 text-destructive p-4 rounded-md">
            {error}
          </div>
        ) : (
          <>
            <div className="mb-6 text-sm text-muted-foreground">
              <p>版本: {agreement.version}</p>
              <p>更新日期: {formatDate(agreement.updated_at)}</p>
            </div>
            
            <div className="prose prose-sm md:prose-base max-w-none dark:prose-invert prose-headings:text-foreground prose-p:text-muted-foreground prose-a:text-primary">
              {agreement.content ? (
                <div dangerouslySetInnerHTML={{ __html: agreement.content }} />
              ) : (
                <p className="text-muted-foreground italic">暂无用户协议内容</p>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default UserAgreement;

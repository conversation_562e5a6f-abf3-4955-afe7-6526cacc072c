import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';

const PrivacyPolicy = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [policy, setPolicy] = useState({
    content: '',
    version: '',
    updated_at: ''
  });

  useEffect(() => {
    const fetchPrivacyPolicy = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/system/policies/privacy');
        if (response.data.success) {
          setPolicy(response.data.data);
        } else {
          setError('获取隐私政策失败');
        }
      } catch (error) {
        console.error('获取隐私政策失败:', error);
        setError('获取隐私政策失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    };

    fetchPrivacyPolicy();
  }, []);

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="bg-card shadow-md rounded-lg p-6 md:p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl md:text-3xl font-bold text-foreground">隐私政策</h1>
          <Link to="/" className="text-primary hover:underline text-sm">
            返回首页
          </Link>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-muted-foreground">加载中...</span>
          </div>
        ) : error ? (
          <div className="bg-destructive/10 text-destructive p-4 rounded-md">
            {error}
          </div>
        ) : (
          <>
            <div className="mb-6 text-sm text-muted-foreground">
              <p>版本: {policy.version}</p>
              <p>更新日期: {formatDate(policy.updated_at)}</p>
            </div>
            
            <div className="prose prose-sm md:prose-base max-w-none dark:prose-invert prose-headings:text-foreground prose-p:text-muted-foreground prose-a:text-primary">
              {policy.content ? (
                <div dangerouslySetInnerHTML={{ __html: policy.content }} />
              ) : (
                <p className="text-muted-foreground italic">暂无隐私政策内容</p>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PrivacyPolicy;

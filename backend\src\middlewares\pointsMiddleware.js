const { errorResponse } = require('../utils/responseUtils');
const pointsService = require('../services/pointsService');
const logger = require('../utils/logger');

/**
 * 检查用户积分是否足够执行特定操作
 * @param {string} operationType 操作类型
 * @returns {Function} Express中间件
 */
const checkPoints = (operationType) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return errorResponse(res, '用户未登录', 401);
      }

      const userId = req.user.id;
      const user = req.user;

      // 获取操作所需积分
      const pointsRequired = await pointsService.getPointsCost(operationType);

      // 检查积分是否足够
      if (user.points < pointsRequired) {
        logger.warn(`用户${userId}积分不足，${operationType}需要${pointsRequired}积分，当前仅有${user.points}积分`);
        return errorResponse(res, `积分不足，${operationType}需要${pointsRequired}积分，当前仅有${user.points}积分`, 403);
      }

      // 将操作类型和所需积分添加到请求对象，以便后续使用
      req.pointsOperation = {
        type: operationType,
        required: pointsRequired
      };

      // 积分足够，继续执行
      next();
    } catch (error) {
      logger.error(`检查用户积分中间件出错:`, error);
      return errorResponse(res, '检查用户积分失败，请稍后再试', 500);
    }
  };
};

module.exports = {
  checkPoints
};

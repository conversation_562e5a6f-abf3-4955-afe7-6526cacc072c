const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 登录日志模型
 * 记录用户的登录信息
 */
const LoginLog = sequelize.define('LoginLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '日志ID'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  login_type: {
    type: DataTypes.ENUM('phone', 'password', 'wechat', 'qq', 'weibo'),
    allowNull: false,
    comment: '登录类型：手机验证码、密码、微信、QQ、微博'
  },
  login_time: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: '登录时间'
  },
  login_ip: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '登录IP地址'
  },
  login_device: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '登录设备信息'
  },
  login_status: {
    type: DataTypes.ENUM('success', 'failed'),
    allowNull: false,
    defaultValue: 'success',
    comment: '登录状态：成功、失败'
  },
  fail_reason: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '失败原因，仅在登录失败时记录'
  }
}, {
  tableName: 'login_logs',
  timestamps: true,
  underscored: true
});

module.exports = LoginLog;

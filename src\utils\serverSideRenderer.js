/**
 * 服务端渲染模块（高级模式核心）
 * 为静态HTML文件提供完整功能保留的服务端渲染支持
 * 第二阶段：智能渲染方式选择实现 - 步骤2
 */

import logger from '../services/logs/frontendLogger.js';

/**
 * 编辑标记配置
 */
export const EDITING_MARKERS = {
  // 可编辑元素标记
  EDITABLE_ATTRIBUTE: 'data-editable-fengmian',
  EDITABLE_TYPE: 'data-editable-type',
  EDITABLE_ID: 'data-editable-id',
  
  // 编辑控制标记
  DRAG_HANDLE: 'data-drag-handle',
  RESIZE_HANDLE: 'data-resize-handle',
  
  // 样式类名
  EDITABLE_CLASS: 'fengmian-editable',
  EDITING_CLASS: 'fengmian-editing',
  DRAG_MODE_CLASS: 'fengmian-drag-mode'
};

/**
 * 可编辑元素类型
 */
export const EDITABLE_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  CONTAINER: 'container',
  BUTTON: 'button',
  LINK: 'link'
};

/**
 * 服务端渲染器类
 */
export class ServerSideRenderer {
  /**
   * 生成功能完整的HTML
   * @param {string} originalHtml - 原始HTML内容
   * @param {Object} options - 渲染选项
   * @returns {Object} 渲染结果
   */
  static generateCompleteHtml(originalHtml, options = {}) {
    try {
      logger.info('开始服务端渲染处理', { advancedMode: options.advancedMode });

      // 1. 保留原始功能
      let processedHtml = this.preserveOriginalFunction(originalHtml);

      // 2. 注入编辑标记
      processedHtml = this.injectEditingMarkers(processedHtml, options);

      // 3. 编辑功能优化
      processedHtml = this.optimizeForEditing(processedHtml, options);

      // 4. 添加必要的样式和脚本
      processedHtml = this.injectEditingAssets(processedHtml);

      // 5. 如果启用高级模式，应用额外的增强
      if (options.advancedMode) {
        processedHtml = this.applyAdvancedModeEnhancements(processedHtml, options);
      }

      logger.info('服务端渲染处理完成', {
        advancedMode: options.advancedMode,
        originalLength: originalHtml.length,
        finalLength: processedHtml.length
      });

      return {
        success: true,
        html: processedHtml,
        originalHtml,
        timestamp: new Date().toISOString(),
        renderingMode: 'SERVER_SIDE_RENDERING',
        advancedModeEnabled: options.advancedMode || false
      };

    } catch (error) {
      logger.error('服务端渲染失败:', error);
      return {
        success: false,
        html: originalHtml, // 失败时返回原始HTML
        error: error.message,
        renderingMode: 'FALLBACK',
        advancedModeEnabled: options.advancedMode || false
      };
    }
  }

  /**
   * 保留原始功能
   * 确保所有JavaScript交互、导航、样式等功能完全保留
   * @param {string} html - HTML内容
   * @returns {string} 处理后的HTML
   */
  static preserveOriginalFunction(html) {
    try {
      // 检查是否在浏览器环境中
      if (typeof DOMParser === 'undefined') {
        // Node.js环境下直接返回原始HTML
        logger.info('Node.js环境，跳过DOM解析');
        return html;
      }

      // 解析HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // 1. 保护所有脚本标签
      this.protectScriptTags(doc);
      
      // 2. 保护内联事件处理器
      this.protectInlineEventHandlers(doc);
      
      // 3. 保护样式和CSS
      this.protectStyles(doc);
      
      // 4. 保护链接和导航
      this.protectNavigation(doc);
      
      // 5. 保护表单和交互元素
      this.protectInteractiveElements(doc);

      return doc.documentElement.outerHTML;

    } catch (error) {
      logger.error('保留原始功能失败:', error);
      return html; // 失败时返回原始HTML
    }
  }

  /**
   * 注入编辑标记
   * 为可编辑元素添加标记，但不影响原有功能
   * @param {string} html - HTML内容
   * @param {Object} options - 注入选项
   * @returns {string} 处理后的HTML
   */
  static injectEditingMarkers(html, options = {}) {
    try {
      // 检查是否在浏览器环境中
      if (typeof DOMParser === 'undefined') {
        // Node.js环境下使用正则表达式添加标记
        return this.injectEditingMarkersWithRegex(html, options);
      }

      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // 1. 识别可编辑的文本元素
      this.markEditableTextElements(doc);
      
      // 2. 识别可编辑的图片元素
      this.markEditableImageElements(doc);
      
      // 3. 识别可编辑的容器元素
      this.markEditableContainerElements(doc);
      
      // 4. 添加编辑控制元素
      if (options.addControls !== false) {
        this.addEditingControls(doc);
      }

      return doc.documentElement.outerHTML;

    } catch (error) {
      logger.error('注入编辑标记失败:', error);
      return html;
    }
  }

  /**
   * 编辑功能优化
   * 优化HTML结构以支持可视化编辑
   * @param {string} html - HTML内容
   * @param {Object} options - 优化选项
   * @returns {string} 优化后的HTML
   */
  static optimizeForEditing(html, options = {}) {
    try {
      // 检查是否在浏览器环境中
      if (typeof DOMParser === 'undefined') {
        // Node.js环境下直接返回HTML
        logger.info('Node.js环境，跳过编辑优化');
        return html;
      }

      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');

      // 1. 优化元素定位
      this.optimizeElementPositioning(doc);
      
      // 2. 添加编辑辅助样式
      this.addEditingHelperStyles(doc);
      
      // 3. 优化文本编辑区域
      this.optimizeTextEditingAreas(doc);
      
      // 4. 确保元素可选择性
      this.ensureElementSelectability(doc);

      return doc.documentElement.outerHTML;

    } catch (error) {
      logger.error('编辑功能优化失败:', error);
      return html;
    }
  }

  /**
   * 保护脚本标签
   * @param {Document} doc - 文档对象
   */
  static protectScriptTags(doc) {
    const scripts = doc.querySelectorAll('script');
    scripts.forEach(script => {
      // 添加保护标记，确保脚本不被编辑器修改
      script.setAttribute('data-protected', 'true');
      script.setAttribute('data-original-type', script.type || 'text/javascript');
    });
  }

  /**
   * 保护内联事件处理器
   * @param {Document} doc - 文档对象
   */
  static protectInlineEventHandlers(doc) {
    const elements = doc.querySelectorAll('*');
    elements.forEach(element => {
      // 检查所有属性，保护事件处理器
      Array.from(element.attributes).forEach(attr => {
        if (attr.name.startsWith('on')) {
          element.setAttribute(`data-original-${attr.name}`, attr.value);
          element.setAttribute('data-has-event-handler', 'true');
        }
      });
    });
  }

  /**
   * 保护样式和CSS
   * @param {Document} doc - 文档对象
   */
  static protectStyles(doc) {
    // 保护style标签
    const styleTags = doc.querySelectorAll('style');
    styleTags.forEach(style => {
      style.setAttribute('data-protected', 'true');
    });

    // 保护link标签（CSS文件）
    const linkTags = doc.querySelectorAll('link[rel="stylesheet"]');
    linkTags.forEach(link => {
      link.setAttribute('data-protected', 'true');
    });

    // 保护内联样式
    const elementsWithStyle = doc.querySelectorAll('[style]');
    elementsWithStyle.forEach(element => {
      element.setAttribute('data-original-style', element.getAttribute('style'));
    });
  }

  /**
   * 保护链接和导航
   * @param {Document} doc - 文档对象
   */
  static protectNavigation(doc) {
    const links = doc.querySelectorAll('a[href]');
    links.forEach(link => {
      link.setAttribute('data-original-href', link.href);
      link.setAttribute('data-protected-navigation', 'true');
    });
  }

  /**
   * 保护表单和交互元素
   * @param {Document} doc - 文档对象
   */
  static protectInteractiveElements(doc) {
    const interactiveElements = doc.querySelectorAll('form, input, button, select, textarea');
    interactiveElements.forEach(element => {
      element.setAttribute('data-protected-interactive', 'true');
      
      // 保护表单属性
      if (element.tagName === 'FORM') {
        if (element.action) element.setAttribute('data-original-action', element.action);
        if (element.method) element.setAttribute('data-original-method', element.method);
      }
    });
  }

  /**
   * 标记可编辑的文本元素
   * @param {Document} doc - 文档对象
   */
  static markEditableTextElements(doc) {
    const textSelectors = [
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'p', 'span', 'div:not([class*="container"]):not([id*="container"])',
      'td', 'th', 'li', 'blockquote', 'figcaption'
    ];

    textSelectors.forEach(selector => {
      const elements = doc.querySelectorAll(selector);
      elements.forEach((element, index) => {
        // 只标记包含文本内容的元素
        if (this.hasSignificantTextContent(element)) {
          element.setAttribute(EDITING_MARKERS.EDITABLE_ATTRIBUTE, 'true');
          element.setAttribute(EDITING_MARKERS.EDITABLE_TYPE, EDITABLE_TYPES.TEXT);
          element.setAttribute(EDITING_MARKERS.EDITABLE_ID, `text_${selector.replace(/[^a-zA-Z]/g, '')}_${index}`);
          element.classList.add(EDITING_MARKERS.EDITABLE_CLASS);
        }
      });
    });
  }

  /**
   * 标记可编辑的图片元素
   * @param {Document} doc - 文档对象
   */
  static markEditableImageElements(doc) {
    const images = doc.querySelectorAll('img');
    images.forEach((img, index) => {
      img.setAttribute(EDITING_MARKERS.EDITABLE_ATTRIBUTE, 'true');
      img.setAttribute(EDITING_MARKERS.EDITABLE_TYPE, EDITABLE_TYPES.IMAGE);
      img.setAttribute(EDITING_MARKERS.EDITABLE_ID, `image_${index}`);
      img.classList.add(EDITING_MARKERS.EDITABLE_CLASS);
    });
  }

  /**
   * 标记可编辑的容器元素
   * @param {Document} doc - 文档对象
   */
  static markEditableContainerElements(doc) {
    const containerSelectors = [
      'div[class*="container"]',
      'div[id*="container"]',
      'section',
      'article',
      'header',
      'footer',
      'aside'
    ];

    containerSelectors.forEach(selector => {
      const elements = doc.querySelectorAll(selector);
      elements.forEach((element, index) => {
        element.setAttribute(EDITING_MARKERS.EDITABLE_ATTRIBUTE, 'true');
        element.setAttribute(EDITING_MARKERS.EDITABLE_TYPE, EDITABLE_TYPES.CONTAINER);
        element.setAttribute(EDITING_MARKERS.EDITABLE_ID, `container_${selector.replace(/[^a-zA-Z]/g, '')}_${index}`);
        element.classList.add(EDITING_MARKERS.EDITABLE_CLASS);
      });
    });
  }

  /**
   * 添加编辑控制元素
   * @param {Document} doc - 文档对象
   */
  static addEditingControls(doc) {
    // 这里可以添加拖拽手柄、调整大小控制等
    // 但要确保不影响原始功能
    const editableElements = doc.querySelectorAll(`[${EDITING_MARKERS.EDITABLE_ATTRIBUTE}="true"]`);
    
    editableElements.forEach(element => {
      // 添加数据属性用于编辑控制
      element.setAttribute('data-editing-enabled', 'true');
    });
  }

  /**
   * 优化元素定位
   * @param {Document} doc - 文档对象
   */
  static optimizeElementPositioning(doc) {
    const editableElements = doc.querySelectorAll(`[${EDITING_MARKERS.EDITABLE_ATTRIBUTE}="true"]`);
    
    editableElements.forEach(element => {
      // 确保元素可以被定位和移动
      const computedStyle = window.getComputedStyle ? window.getComputedStyle(element) : {};
      if (computedStyle.position === 'static') {
        element.style.position = 'relative';
      }
    });
  }

  /**
   * 添加编辑辅助样式
   * @param {Document} doc - 文档对象
   */
  static addEditingHelperStyles(doc) {
    const styleElement = doc.createElement('style');
    styleElement.setAttribute('data-editing-helper', 'true');
    styleElement.textContent = `
      .${EDITING_MARKERS.EDITABLE_CLASS}:hover {
        outline: 1px dashed #007bff;
        cursor: pointer;
      }
      .${EDITING_MARKERS.EDITING_CLASS} {
        outline: 2px solid #007bff;
      }
      .${EDITING_MARKERS.DRAG_MODE_CLASS} {
        cursor: move;
      }
    `;
    
    if (doc.head) {
      doc.head.appendChild(styleElement);
    }
  }

  /**
   * 优化文本编辑区域
   * @param {Document} doc - 文档对象
   */
  static optimizeTextEditingAreas(doc) {
    const textElements = doc.querySelectorAll(`[${EDITING_MARKERS.EDITABLE_TYPE}="${EDITABLE_TYPES.TEXT}"]`);
    
    textElements.forEach(element => {
      // 确保文本元素可以被选择和编辑
      element.setAttribute('data-text-editable', 'true');
    });
  }

  /**
   * 确保元素可选择性
   * @param {Document} doc - 文档对象
   */
  static ensureElementSelectability(doc) {
    const editableElements = doc.querySelectorAll(`[${EDITING_MARKERS.EDITABLE_ATTRIBUTE}="true"]`);
    
    editableElements.forEach(element => {
      // 确保元素可以被选择
      element.style.userSelect = 'text';
      element.setAttribute('tabindex', '0');
    });
  }

  /**
   * 注入编辑资源
   * @param {string} html - HTML内容
   * @returns {string} 注入资源后的HTML
   */
  static injectEditingAssets(html) {
    // 这里可以注入必要的CSS和JavaScript资源
    // 但要确保不破坏原有功能
    return html;
  }

  /**
   * 检查元素是否有重要文本内容
   * @param {Element} element - 元素
   * @returns {boolean} 是否有重要文本内容
   */
  static hasSignificantTextContent(element) {
    const textContent = element.textContent?.trim() || '';
    return textContent.length > 0 && textContent.length < 1000; // 避免标记过长的文本块
  }

  /**
   * 生成渲染报告
   * @param {Object} result - 渲染结果
   * @returns {string} 格式化的报告
   */
  static generateRenderingReport(result) {
    if (!result.success) {
      return `❌ 服务端渲染失败: ${result.error}`;
    }

    return `✅ 服务端渲染成功\n渲染模式: ${result.renderingMode}\n处理时间: ${result.timestamp}\n高级模式: ${result.advancedModeEnabled ? '启用' : '禁用'}`;
  }

  /**
   * Node.js环境下使用正则表达式注入编辑标记
   * @param {string} html - HTML内容
   * @param {Object} options - 注入选项
   * @returns {string} 处理后的HTML
   */
  static injectEditingMarkersWithRegex(html, options = {}) {
    try {
      logger.info('使用正则表达式注入编辑标记');

      let processedHtml = html;

      // 为常见的文本元素添加编辑标记
      const textElements = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div'];

      textElements.forEach(tag => {
        const regex = new RegExp(`<${tag}([^>]*)>`, 'gi');
        processedHtml = processedHtml.replace(regex, (match, attributes) => {
          // 如果已经有编辑标记，跳过
          if (attributes.includes('data-editable-fengmian')) {
            return match;
          }

          // 添加编辑标记
          const editableAttr = ` ${EDITING_MARKERS.EDITABLE_ATTRIBUTE}="true" ${EDITING_MARKERS.EDITABLE_TYPE}="text"`;
          return `<${tag}${attributes}${editableAttr}>`;
        });
      });

      logger.info('正则表达式编辑标记注入完成');
      return processedHtml;

    } catch (error) {
      logger.error('正则表达式编辑标记注入失败:', error);
      return html;
    }
  }

  /**
   * 应用高级模式增强
   * @param {string} html - HTML内容
   * @param {Object} options - 选项
   * @returns {string} 增强后的HTML
   */
  static applyAdvancedModeEnhancements(html, options = {}) {
    try {
      logger.info('应用高级模式增强');

      // 性能优化CSS
      const performanceCSS = `
        <style data-advanced-mode="performance">
          /* 高级模式性能优化 */
          * {
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }

          img {
            image-rendering: optimizeQuality;
            loading: lazy;
          }

          .img-error {
            display: inline-block;
            padding: 10px;
            background: #f5f5f5;
            border: 1px dashed #ccc;
            color: #666;
            font-size: 12px;
            text-align: center;
            min-width: 100px;
            min-height: 50px;
          }
        </style>
      `;

      // 兼容性增强CSS
      const compatibilityCSS = `
        <style data-advanced-mode="compatibility">
          /* 兼容性增强 */
          * {
            -webkit-box-sizing: border-box;
            -moz-box-sizing: border-box;
            box-sizing: border-box;
          }
        </style>
      `;

      // 高级模式标识
      const advancedModeMarker = `
        <script data-advanced-mode="marker">
          if (typeof window !== 'undefined') {
            window.advancedModeEnabled = true;
            console.log('高级模式已启用');
          }
        </script>
      `;

      let enhancedHtml = html;

      // 注入增强样式和脚本
      if (enhancedHtml.includes('</head>')) {
        enhancedHtml = enhancedHtml.replace('</head>',
          `${performanceCSS}${compatibilityCSS}${advancedModeMarker}</head>`);
      } else {
        enhancedHtml = `${performanceCSS}${compatibilityCSS}${advancedModeMarker}${enhancedHtml}`;
      }

      logger.info('高级模式增强应用完成');
      return enhancedHtml;

    } catch (error) {
      logger.error('应用高级模式增强失败:', error);
      return html;
    }
  }
}

export default ServerSideRenderer;

import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FileCode, Eye, Save, AlertTriangle, Eraser } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { message } from 'antd';
import axios from 'axios';
import { getCurrentUser } from '../../services/authService';

/**
 * 简单的HTML清理函数，移除可能的危险标签和属性
 * @param {string} html HTML内容
 * @returns {string} 清理后的HTML
 */
const sanitizeHtml = (html) => {
  // 移除script标签
  let sanitized = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // 移除iframe标签
  sanitized = sanitized.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '');
  
  // 移除事件处理属性
  sanitized = sanitized.replace(/\son\w+\s*=\s*(['"]).*?\1/gi, '');
  
  // 移除javascript:协议
  sanitized = sanitized.replace(/href\s*=\s*(['"])javascript:.*?\1/gi, 'href="javascript:void(0)"');
  
  return sanitized;
};

/**
 * 代码粘贴组件 - 允许用户粘贴HTML代码并预览
 */
const CodePasteView = () => {
  const navigate = useNavigate();
  const previewIframeRef = useRef(null);
  const textareaRef = useRef(null);
  
  const [htmlCode, setHtmlCode] = useState('');
  const [codeTitle, setCodeTitle] = useState('');
  const [previewHtml, setPreviewHtml] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [previewReady, setPreviewReady] = useState(false);
  const [hasCodeError, setHasCodeError] = useState(false);
  const [codeErrorMessage, setCodeErrorMessage] = useState('');
  const [currentUser, setCurrentUser] = useState(null);
  
  useEffect(() => {
    const user = getCurrentUser();
    if (user) {
      setCurrentUser(user);
    }
  }, []);
  
  // 处理代码输入
  const handleCodeChange = (e) => {
    const code = e.target.value;
    setHtmlCode(code);
    
    if (code.trim() === '') {
      setPreviewReady(false);
      setPreviewHtml('');
      setHasCodeError(false);
      setCodeErrorMessage('');
      return;
    }
    
    try {
      // 检查是否是有效的HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(code, 'text/html');
      const parseError = doc.querySelector('parsererror');
      
      if (parseError) {
        setHasCodeError(true);
        setCodeErrorMessage('无效的HTML代码');
        setPreviewReady(false);
        return;
      }
      
      // 使用自定义函数清理HTML内容，替代DOMPurify
      const sanitizedContent = sanitizeHtml(code);
      
      setPreviewHtml(sanitizedContent);
      setPreviewReady(true);
      setHasCodeError(false);
      setCodeErrorMessage('');
      
      // 如果没有设置标题，尝试从HTML中提取
      if (!codeTitle) {
        const titleElement = doc.querySelector('title');
        if (titleElement && titleElement.textContent) {
          // 不再自动设置标题
          // setCodeTitle(titleElement.textContent);
        }
      }
    } catch (error) {
      console.error('解析HTML代码失败:', error);
      setHasCodeError(true);
      setCodeErrorMessage('解析HTML代码失败');
      setPreviewReady(false);
    }
  };
  
  // 处理标题输入
  const handleTitleChange = (e) => {
    setCodeTitle(e.target.value);
  };
  
  // 清除代码
  const handleClearCode = () => {
    setHtmlCode('');
    setPreviewHtml('');
    setPreviewReady(false);
    setHasCodeError(false);
    setCodeErrorMessage('');
    
    if (textareaRef.current) {
      textareaRef.current.value = '';
      textareaRef.current.focus();
    }
  };
  
  // 处理保存封面
  const handleSaveCover = async () => {
    if (!previewHtml) {
      message.error('请先输入HTML代码');
      return;
    }
    
    try {
      setIsSaving(true);
      
      // 从 localStorage 获取 token
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        navigate('/auth');
        return;
      }
      
      // 生成默认标题
      let finalTitle = codeTitle;
      if (!finalTitle) {
        const userPhone = currentUser?.phone || '';
        const phoneSuffix = userPhone.slice(-4);
        const timestamp = Date.now();
        finalTitle = `自定义${phoneSuffix}${timestamp}`;
      }
      
      // 准备封面数据
      const coverData = {
        cover_text: finalTitle,
        cover_type: 'custom',
        cover_type_name: '自定义封面',
        cover_style: 'custom',
        cover_style_name: '自定义风格',
        html_content: previewHtml,
        record_type: '代码粘贴'
      };
      
      // 发送请求保存封面记录
      const response = await axios.post('/api/cover/save-custom', coverData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        message.success('封面保存成功');
        
        // 如果返回了 cover_code，可以跳转到编辑页面
        if (response.data.data?.cover_code) {
          const coverCode = response.data.data.cover_code;
          
          // 直接导航到主页，带上code和source参数
          navigate(`/?code=${coverCode}&source=paste`, { replace: true });
        } else {
          // 如果没有返回cover_code，导航到封面记录页面
          navigate('/covers', { replace: true });
        }
      } else {
        message.error(response.data.message || '保存失败，请重试');
      }
    } catch (error) {
      console.error('保存封面失败:', error);
      message.error('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };
  
  // 更新预览内容
  useEffect(() => {
    if (previewIframeRef.current && previewHtml && previewReady) {
      const iframe = previewIframeRef.current;
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      
      if (iframeDoc) {
        iframeDoc.open();
        iframeDoc.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              html, body {
                margin: 0;
                padding: 0;
                width: 100%;
                height: 100%;
                overflow: auto;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
              }
              body {
                display: flex;
                justify-content: center;
                align-items: center;
              }
              * {
                max-width: 100%;
              }
              img, svg, video {
                max-width: 100%;
                height: auto;
                display: block;
                margin: 0 auto;
              }
              /* 使用CSS隐藏可能遗漏的脚本和iframe */
              script, iframe {
                display: none !important;
              }
            </style>
          </head>
          <body>
            ${previewHtml}
          </body>
          </html>
        `);
        iframeDoc.close();
      }
    }
  }, [previewHtml, previewReady]);
  
  return (
    <div className="flex flex-col h-full p-4 md:p-6 lg:p-8">
      <Card className="flex flex-col flex-1 shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0">
          <CardTitle className="text-2xl font-bold">粘贴HTML代码</CardTitle>
          <CardDescription>
            粘贴HTML代码生成封面，请确保代码格式正确
          </CardDescription>
        </CardHeader>
        
        <CardContent className="flex-1 flex p-6 gap-6 overflow-hidden">
          {/* 左侧：代码输入区域 */}
          <div className="w-2/5 flex flex-col gap-6">
            {/* 标题输入区域 */}
            <div className="flex-shrink-0">
              <label htmlFor="code-title" className="block text-sm font-medium text-slate-700 mb-1">
                标题（可选）
              </label>
              <input
                id="code-title"
                type="text"
                value={codeTitle}
                onChange={handleTitleChange}
                placeholder="输入封面标题"
                className="w-full p-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            
            {/* 代码输入区域 */}
            <div className="flex-1 flex flex-col">
              <label htmlFor="html-code" className="block text-sm font-medium text-slate-700 mb-1 flex-shrink-0">
                HTML代码
              </label>
              <div className="relative flex-1">
                <textarea
                  ref={textareaRef}
                  id="html-code"
                  value={htmlCode}
                  onChange={handleCodeChange}
                  placeholder="在此粘贴HTML代码..."
                  className={`w-full h-full p-4 border rounded-md font-mono text-sm resize-y
                    ${hasCodeError ? 'border-red-300 focus:ring-red-500' : 'border-slate-300 focus:ring-primary'}
                    focus:outline-none focus:ring-2 focus:border-transparent`}
                />
                
                {htmlCode && (
                  <button 
                    onClick={handleClearCode}
                    className="absolute top-2 right-2 p-1 rounded-md hover:bg-slate-200 text-slate-500"
                    title="清除代码"
                  >
                    <Eraser size={16} />
                  </button>
                )}
              </div>
              
              {hasCodeError && (
                <div className="mt-2 flex items-center text-red-500 flex-shrink-0">
                  <AlertTriangle size={16} className="mr-1" />
                  <span className="text-sm">{codeErrorMessage}</span>
                </div>
              )}
            </div>
          </div>
          
          {/* 右侧：预览区域 */}
          <div className="flex-1 flex flex-col">
            <h3 className="text-lg font-semibold mb-3 flex items-center flex-shrink-0">
              <Eye size={18} className="mr-2" />
              预览
            </h3>
            
            <div className="border rounded-lg overflow-hidden bg-white flex-1">
              {!previewReady ? (
                <div className="h-full flex items-center justify-center text-slate-400">
                  <p>输入HTML代码后在此处预览</p>
                </div>
              ) : (
                <iframe
                  ref={previewIframeRef}
                  className="w-full h-full border-none"
                  title="HTML预览"
                  sandbox="allow-same-origin allow-scripts"
                />
              )}
            </div>
          </div>
        </CardContent>
        
        <CardFooter className="bg-slate-50 p-4 flex justify-between flex-shrink-0">
          <Button variant="outline" onClick={() => {
            navigate('/');
            // 触发自定义事件，通知其他组件视图已更改为chat
            window.dispatchEvent(new CustomEvent('viewChange', { detail: { view: 'chat' } }));
          }}>
            返回
          </Button>
          
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleClearCode} disabled={!htmlCode || isSaving}>
              清除
            </Button>
            <Button 
              onClick={handleSaveCover} 
              disabled={!previewReady || hasCodeError || isSaving}
              className="bg-primary text-white hover:bg-primary/90"
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  保存中...
                </>
              ) : (
                <>
                  <Save size={16} className="mr-1.5" />
                  保存封面
                </>
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default CodePasteView; 
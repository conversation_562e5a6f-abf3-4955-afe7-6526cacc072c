/**
 * 通用权限检查Hook
 * 用于检查用户是否有权限使用特定功能
 */
import { useState, useEffect } from 'react';
import { checkFeatureAvailability } from '../services/featureService';
import { useFeatures } from '../contexts/FeatureContext';

// 简单的内存缓存 - 不再使用独立缓存，而是统一使用FeatureContext和apiCacheService
const CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5分钟，与apiCacheService保持一致

/**
 * 通用权限检查Hook
 * @param {string[]} featureNames - 功能名称数组
 * @param {Object} defaultPermissions - 默认权限对象
 * @returns {Object} 包含权限状态、加载状态和错误状态的对象
 */
const useFeaturePermissions = (featureNames = [], defaultPermissions = {}) => {
  const [permissions, setPermissions] = useState(defaultPermissions);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // 使用FeatureContext中的缓存数据
  const { features: contextFeatures, loading: contextLoading, checkFeature } = useFeatures();

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        setLoading(true);
        const permissionResults = { ...defaultPermissions };

        // 并行请求所有功能权限，优先使用Context缓存
        const results = await Promise.all(
          featureNames.map(async (name) => {
            // 优先使用Context中的缓存数据
            if (!contextLoading && contextFeatures[name] !== undefined) {
              return { name, result: contextFeatures[name] };
            }

            // 使用checkFeature，它会优先使用Context缓存，如果没有则通过apiCacheService发起API请求
            const result = await checkFeature(name);
            return { name, result };
          })
        );

        // 整理结果
        results.forEach(({ name, result }) => {
          permissionResults[name] = result.available;
        });

        setPermissions(permissionResults);
        setError(null);
      } catch (err) {
        console.error('检查功能权限失败:', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    if (featureNames.length > 0) {
      checkPermissions();
    } else {
      setLoading(false);
    }
  }, [JSON.stringify(featureNames), contextFeatures, contextLoading, checkFeature]);

  return { permissions, loading, error };
};

export default useFeaturePermissions;

/**
 * EditorHistory.js
 * 编辑器历史记录管理模块，用于记录和管理编辑操作的历史记录
 * 支持撤销和重做功能
 */

/**
 * 编辑器历史记录管理类
 */
class EditorHistory {
  /**
   * 构造函数
   * @param {Object} options 配置选项
   * @param {number} options.maxHistoryLength 最大历史记录长度，默认为50
   * @param {Function} options.applyStateFn 应用状态的回调函数
   */
  constructor(options = {}) {
    this.history = [];
    this.currentIndex = -1;
    this.maxHistoryLength = options.maxHistoryLength || 50;
    this.applyStateFn = options.applyStateFn || null;
    this.isApplyingState = false; // 用于防止在应用状态时触发新的记录
  }

  /**
   * 记录一个状态快照
   * @param {*} state 当前的编辑器状态
   * @returns {boolean} 是否记录成功
   */
  record(state) {
    if (this.isApplyingState) {
      return false;
    }

    // 如果新状态与当前状态相同，不记录
    if (this.currentIndex >= 0) {
      const currentState = this.history[this.currentIndex];
      // 使用更可靠的比较方式，以防状态对象结构复杂
      if (JSON.stringify(state) === JSON.stringify(currentState)) {
        return false;
      }
    }

    // 如果当前指针不在历史记录的末尾（意味着已经执行了撤销操作），
    // 则丢弃当前指针之后的所有历史记录
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1);
    }

    // 添加新的状态快照
    this.history.push(state);

    // 如果历史记录超过最大长度，删除最旧的记录
    if (this.history.length > this.maxHistoryLength) {
      this.history.shift();
      // 当挤掉一个旧状态时，指针需要保持在数组的末尾
      this.currentIndex = this.history.length - 1;
    } else {
      this.currentIndex++;
    }

    return true;
  }

  /**
   * 撤销操作
   * @returns {boolean} 是否撤销成功
   */
  undo() {
    if (!this.canUndo()) {
      return false;
    }

    try {
      this.isApplyingState = true;
      this.currentIndex--;
      const stateToApply = this.history[this.currentIndex];

      if (this.applyStateFn) {
        this.applyStateFn(stateToApply);
      }
      return true;
    } catch (error) {
      return false;
    } finally {
      this.isApplyingState = false;
    }
  }

  /**
   * 重做操作
   * @returns {boolean} 是否重做成功
   */
  redo() {
    if (!this.canRedo()) {
      return false;
    }

    try {
      this.isApplyingState = true;
      this.currentIndex++;
      const stateToApply = this.history[this.currentIndex];

      if (this.applyStateFn) {
        this.applyStateFn(stateToApply);
      }
      return true;
    } catch (error) {
      return false;
    } finally {
      this.isApplyingState = false;
    }
  }

  /**
   * 检查是否可以撤销
   * @returns {boolean} 是否可以撤销
   */
  canUndo() {
    return this.currentIndex > 0;
  }

  /**
   * 检查是否可以重做
   * @returns {boolean} 是否可以重做
   */
  canRedo() {
    return this.currentIndex < this.history.length - 1;
  }

  /**
   * 清除所有历史记录
   */
  clear() {
    this.history = [];
    this.currentIndex = -1;
  }
  
  /**
   * 记录初始状态，这对于"撤销到空白"的场景很重要
   * @param {*} initialState 
   */
  setInitialState(initialState) {
    this.clear();
    this.record(initialState);
  }

  /**
   * 获取历史记录长度
   * @returns {number} 历史记录长度
   */
  getHistoryLength() {
    return this.history.length;
  }

  /**
   * 获取当前位置
   * @returns {number} 当前位置
   */
  getCurrentIndex() {
    return this.currentIndex;
  }

  /**
   * 获取历史记录
   * @returns {Array} 历史记录数组
   */
  getHistory() {
    return [...this.history];
  }
}

export default EditorHistory; 
// logs.js - 系统日志管理模块

// 使用模块模式，确保window.logModule对象正确定义
window.logModule = (function() {
  // 模块内部变量
  let currentPage = 1;
  let pageSize = 20;
  let totalPages = 1;
  let currentModule = '';
  let currentAction = '';
  let currentStatus = '';
  let currentUsername = '';
  let currentStartDate = '';
  let currentEndDate = '';
  let currentSourceFilter = ''; // 添加来源筛选变量

// 模块名称映射表
const moduleNameMap = {
  // 标准模块
  'user': '用户管理',
  'users': '用户管理',
  'cover': '封面管理',
  'covers': '封面管理',
  'style': '样式管理',
  'styles': '样式管理',
  'feature': '功能控制',
  'features': '功能控制',
  'setting': '系统设置',
  'settings': '系统设置',
  'log': '系统日志',
  'logs': '系统日志',
  'payment': '支付管理',
  'dashboard': '控制面板',
  'auth': '认证授权',
  'system': '系统管理',
  'stat': '数据统计',
  'stats': '数据统计',
  'statistics': '数据统计',
  'api': 'API接口',
  'task': '计划任务',
  'tasks': '计划任务',
  'admin': '系统管理',
  'ai-services': 'AI服务',
  'ai_services': 'AI服务',
  'sms': '短信服务',
  'message': '消息服务',
  'upload': '文件上传',
  'uploads': '文件上传',

  // 特定模块名称(与数据库记录匹配)
  'unknown': '未知模块',
  'style-prompts': '样式提示词',
  'base-prompts': '基础提示词',
  'user-manage': '用户管理',
  'point-records': '积分记录',
  'cover-records': '封面记录'
};

// 操作类型映射表
const actionNameMap = {
  'query': '查询',
  'create': '创建',
  'update': '更新',
  'delete': '删除',
  'login': '登录',
  'logout': '登出',
  'export': '导出',
  'import': '导入',
  'upload': '上传',
  'download': '下载',
  'generate': '生成',
  'cancel': '取消',
  'approve': '审批',
  'reject': '拒绝',
  'enable': '启用',
  'disable': '禁用',
  'register': '注册',
  'reset': '重置',
  'recharge': '充值',
  'consume': '消费',
  'cleanup': '清理',
  'optimize': '优化',
  'visit': '访问',
  'view': '查看',
  'test': '测试',
  'verify': '验证',
  'confirm': '确认',
  'other': '其他'
};

/**
 * 翻译模块名称
 * @param {string} moduleName 模块名称
 * @returns {string} 翻译后的模块名称
 */
function translateModuleName(moduleName) {
  if (!moduleName) return '未知模块';

  // 直接匹配
  if (moduleNameMap[moduleName]) {
    return moduleNameMap[moduleName];
  }

  // 转为小写便于匹配
  const lowerName = moduleName.toLowerCase();
  if (moduleNameMap[lowerName]) {
    return moduleNameMap[lowerName];
  }

  // 否则返回原名称
  return moduleName;
}

/**
 * 翻译操作类型
 * @param {string} actionName 操作类型
 * @returns {string} 翻译后的操作类型
 */
function translateActionName(actionName) {
  if (!actionName) return '未知操作';

  // 直接匹配
  if (actionNameMap[actionName]) {
    return actionNameMap[actionName];
  }

  // 转为小写便于匹配
  const lowerName = actionName.toLowerCase();
  if (actionNameMap[lowerName]) {
    return actionNameMap[lowerName];
  }

  // 否则返回原名称
  return actionName;
}

/**
 * 处理日志描述文本
 * @param {string} description 描述文本
 * @param {string} moduleName 模块名称
 * @returns {string} 处理后的描述文本
 */
function processLogDescription(description, moduleName) {
  if (!description) return '-';

  // 如果模块名为unknown，且描述包含"查询unknown"，替换为"查询未知模块"
  if (moduleName === 'unknown' && description.includes('查询unknown')) {
    return description.replace(/查询unknown/g, '查询未知模块');
  }

  // 如果描述中直接包含模块名，且我们有这个模块的翻译
  for (const [key, value] of Object.entries(moduleNameMap)) {
    if (description.includes(key)) {
      description = description.replace(new RegExp(key, 'g'), value);
    }
  }

  return description;
}

// 确保Chart.js已加载
function ensureChartJsLoaded(callback) {
  console.log('检查Chart.js是否已加载');

  // 如果Chart对象已存在，直接调用回调
  if (typeof Chart !== 'undefined') {
    console.log('Chart.js已加载，版本:', Chart.version);
    callback();
    return;
  }

  console.log('Chart.js未加载，尝试加载');

  // 如果Chart对象不存在，加载它
  const script = document.createElement('script');
  script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';

  script.onload = function() {
    console.log('Chart.js加载成功，版本:', Chart.version);
    callback();
  };

  script.onerror = function(error) {
    console.error('Chart.js加载失败:', error);
    // 即使加载失败也继续初始化其他功能
    callback();
  };

  document.head.appendChild(script);
}

// 初始化函数
function init() {
  console.log('日志模块初始化开始...');

  try {
    // 设置默认日期选择器
    initDatePicker();

    // 初始页码和页面大小
    currentPage = 1;
    pageSize = 10;

    // 添加事件监听
    setupEventListeners();

    // 获取初始数据
    fetchLogs();
    fetchLogStats();

    // 添加手动刷新按钮到日志页面标题旁
    const logsTitle = document.querySelector('#logs h2');
    if (logsTitle) {
      // 检查是否已经存在刷新按钮
      if (!document.getElementById('logRefreshBtn')) {
        const refreshBtn = document.createElement('button');
        refreshBtn.id = 'logRefreshBtn';
        refreshBtn.className = 'btn btn-sm btn-outline-primary ms-2';
        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 刷新';
        refreshBtn.onclick = function() {
          console.log('手动刷新日志...');
          fetchLogs();
          fetchLogStats();
        };
        logsTitle.appendChild(refreshBtn);
      }
    }

    console.log('日志模块初始化完成');
  } catch (error) {
    console.error('日志模块初始化失败:', error);
  }
}

// 初始化日期选择器
function initDatePicker() {
  // 初始化日期选择器 - 设置更合适的时间范围
  const today = new Date();
  // 默认查看最近48小时的日志
  const twoDaysAgo = new Date();
  twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);

  const startDateInput = document.getElementById('logStartDate');
  const endDateInput = document.getElementById('logEndDate');

  if (startDateInput) startDateInput.valueAsDate = twoDaysAgo;
  if (endDateInput) endDateInput.valueAsDate = today;

  currentStartDate = formatDateForAPI(twoDaysAgo);
  currentEndDate = formatDateForAPI(today);
}

// 设置事件监听器
function setupEventListeners() {
  const searchBtn = document.getElementById('logSearchBtn');
  const resetBtn = document.getElementById('logResetBtn');
  const cleanupBtn = document.getElementById('logCleanupBtn');
  const confirmCleanupBtn = document.getElementById('confirmCleanupBtn');
  const pageSizeSelect = document.getElementById('logPageSize');
  const sourceFilter = document.getElementById('logSourceFilter');

  if (searchBtn) {
    searchBtn.addEventListener('click', function() {
      console.log('点击了搜索按钮');
      currentPage = 1; // 搜索时重置到第一页
      fetchLogs();
    });
  }

  if (resetBtn) {
    resetBtn.addEventListener('click', function() {
      console.log('点击了重置按钮');
      resetFilters(); // 调用新的重置函数
      // 不再需要 fetchLogs()，因为 resetFilters 内部会调用
    });
  }

  if (cleanupBtn) {
    cleanupBtn.addEventListener('click', function() {
      console.log('点击了清理按钮');
      // 弹出清理模态框的逻辑应该在这里，或者由HTML的data-bs-toggle处理
      try {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
          const cleanupModal = new bootstrap.Modal(document.getElementById('logCleanupModal'));
          cleanupModal.show();
        } else {
          console.error('Bootstrap Modal未定义，无法显示模态框');
          // 尝试使用jQuery模态框作为备选
          if (typeof $ !== 'undefined' && $.fn.modal) {
            $('#logCleanupModal').modal('show');
          } else {
            alert('无法显示清理日志模态框，请检查页面加载状态');
          }
        }
      } catch (error) {
        console.error('显示清理模态框失败:', error);
        alert('无法显示清理日志模态框，请刷新页面后重试');
      }
    });
  }

  if (confirmCleanupBtn) {
    confirmCleanupBtn.addEventListener('click', cleanupLogs); // 确认按钮执行清理
  }

  if (pageSizeSelect) {
    pageSizeSelect.addEventListener('change', function() {
      pageSize = parseInt(this.value, 10);
      currentPage = 1; // 更改每页数量时重置到第一页
      fetchLogs();
    });
  }

  // 添加来源筛选事件监听
  if (sourceFilter) {
    sourceFilter.addEventListener('change', function() {
      currentSourceFilter = this.value;
      currentPage = 1; // 筛选时重置到第一页
      fetchLogs();
    });
  }

  // 初始化日期选择器
  // 检查是否有flatpickr库
  if (typeof flatpickr !== 'undefined') {
    try {
      flatpickr("#logStartDate", { dateFormat: "Y-m-d" });
      flatpickr("#logEndDate", { dateFormat: "Y-m-d" });
      console.log('Flatpickr日期选择器初始化成功');
    } catch (error) {
      console.error('Flatpickr初始化失败:', error);
    }
  } else {
    console.log('Flatpickr未加载，使用原生日期选择器');
  }
}

// 获取日志数据
async function fetchLogs() {
  console.log('获取日志数据...');
  const logsTableBody = document.getElementById('logsTableBody');

  if (logsTableBody) {
    logsTableBody.innerHTML = '<tr><td colspan="8" class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></td></tr>';
  }

  try {
    // 构建API请求URL
    const moduleFilter = document.getElementById('logModuleFilter')?.value || '';
    const actionFilter = document.getElementById('logActionFilter')?.value || '';
    const statusFilter = document.getElementById('logStatusFilter')?.value || '';
    const usernameFilter = document.getElementById('logUsernameFilter')?.value || '';
    const startDate = document.getElementById('logStartDate')?.value || currentStartDate;
    const endDate = document.getElementById('logEndDate')?.value || currentEndDate;

    // 获取当前选择的每页显示记录数
    const pageSizeSelect = document.getElementById('logPageSize');
    if (pageSizeSelect) {
      pageSize = parseInt(pageSizeSelect.value, 10) || 10;
    }

    console.log(`当前分页参数: page=${currentPage}, pageSize=${pageSize}`);

    // 请求参数
    const params = new URLSearchParams({
      page: currentPage,
      pageSize: pageSize,
      module: moduleFilter,
      action: actionFilter,
      status: statusFilter,
      username: usernameFilter,
      source: currentSourceFilter // 添加来源参数
    });

    if (startDate) params.append('startDate', formatDateForAPI(new Date(startDate)));
    if (endDate) params.append('endDate', formatDateForAPI(new Date(endDate)));

    console.log('请求参数:', params.toString());

    // 获取日志数据
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('未找到认证令牌');
    }

    const response = await fetch(`/api/admin/logs?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP错误，状态: ${response.status}`);
    }

    const data = await response.json();
    console.log('获取到的日志数据:', data);

    if (!data.success) {
      throw new Error(data.message || '获取日志失败');
    }

    // 如果没有日志数据且处于开发环境，创建一些测试日志数据
    if (!data.logs || data.logs.length === 0) {
      // 显示无数据提示，不再自动创建测试日志
      const logsTableBody = document.getElementById('logsTableBody');
      if (logsTableBody) {
        logsTableBody.innerHTML = `<tr><td colspan="8" class="text-center">暂无日志数据</td></tr>`;
      }
      
      // 清空分页区域
      const paginationContainer = document.getElementById('logsPagination');
      if (paginationContainer) {
        paginationContainer.innerHTML = '';
      }
      
      // 使用空数据初始化图表
      ensureChartJsLoaded(() => {
        updateModuleChart([]);
        updateActionChart([]);
        updateStatusChart([]);
      });
    } else {
      renderLogs(data);
    }
  } catch (error) {
    console.error('获取日志出错:', error);
    // 在表格中显示错误消息
    const logsTableBody = document.getElementById('logsTableBody');
    if (logsTableBody) {
      logsTableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">获取日志失败: ${error.message}</td></tr>`;
    }
    // 显示空图表
    ensureChartJsLoaded(() => {
      updateModuleChart([]);
      updateActionChart([]);
      updateStatusChart([]);
    });
  }
}

// 获取日志统计数据
async function fetchLogStats() {
  console.log('获取日志统计数据...');

  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('未找到认证令牌');
    }

    // 获取统计时间范围
    const startDate = document.getElementById('logStartDate')?.value || '';
    const endDate = document.getElementById('logEndDate')?.value || '';

    // 构建API请求URL
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', formatDateForAPI(new Date(startDate)));
    if (endDate) params.append('endDate', formatDateForAPI(new Date(endDate)));

    // 直接获取所有统计数据
    const response = await fetch(`/api/admin/logs/stats?${params.toString()}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error(`获取日志统计失败，状态码: ${response.status}`);
    }

    const data = await response.json();
    console.log('获取日志统计数据成功:', data);

    // 确保响应包含必要的数据结构
    if (!data.success) {
      throw new Error(data.message || '获取统计数据失败');
    }

    // 更新统计图表 - 传入正确的数组
    ensureChartJsLoaded(() => {
      if (data.moduleStats && Array.isArray(data.moduleStats)) {
        updateModuleChart(data.moduleStats); // 传入 moduleStats 数组
      } else {
        console.warn('未收到有效的模块统计数据');
        updateModuleChart([]); // 传入空数组避免错误
      }
      if (data.actionStats && Array.isArray(data.actionStats)) {
        updateActionChart(data.actionStats); // 传入 actionStats 数组
      } else {
        console.warn('未收到有效的操作统计数据');
        updateActionChart([]); // 传入空数组避免错误
      }
      if (data.statusStats && Array.isArray(data.statusStats)) {
        updateStatusChart(data.statusStats); // 传入 statusStats 数组
      } else {
        console.warn('未收到有效的状态统计数据');
        updateStatusChart([]); // 传入空数组避免错误
      }

      console.log('统计图表更新完成');
    });
  } catch (error) {
    console.error('获取日志统计数据出错:', error);
    // 创建一些基本的演示数据以显示图表
    const demoModuleStats = [
      { module: 'users', count: 10 },
      { module: 'covers', count: 8 },
      { module: 'logs', count: 5 }
    ];
    const demoActionStats = [
      { action: 'query', count: 15 },
      { action: 'create', count: 5 },
      { action: 'update', count: 3 }
    ];
    const demoStatusStats = [
      { status: 'success', count: 20 },
      { status: 'failure', count: 3 }
    ];

    ensureChartJsLoaded(() => {
      updateModuleChart(demoModuleStats);
      updateActionChart(demoActionStats);
      updateStatusChart(demoStatusStats);
    });
  }
}

// 渲染日志列表
function renderLogs(data) {
  console.log('开始渲染日志列表，数据:', data);

  try {
    const tableBody = document.getElementById('logsTableBody');
    if (!tableBody) {
      console.error('找不到logsTableBody元素，无法渲染日志');
      return;
    }

    // 检查logs字段是否存在
    if (!data || !data.logs || !Array.isArray(data.logs) || data.logs.length === 0) {
      console.log('无日志数据可显示');
      tableBody.innerHTML = '<tr><td colspan="8" class="text-center">暂无日志数据</td></tr>';
      return;
    }

    console.log('构建日志HTML');
    let html = '';
    data.logs.forEach((log, index) => {
      console.log(`处理第${index+1}条日志，ID: ${log.id}`);

      // 标准化日志对象字段，处理可能的命名差异
      const normalizedLog = {
        id: log.id,
        username: log.username || (log.user && log.user.nickname) || '系统',
        source: log.source || 'backend',
        module: log.module || 'unknown',
        action: log.action || 'unknown',
        description: log.description || '',
        level: log.level || 'info',
        status: log.status || 'unknown',
        created_at: log.created_at || log.createdAt || new Date().toISOString(),
      };

      // 确保source字段是字符串类型
      if (normalizedLog.source !== undefined && normalizedLog.source !== null) {
        normalizedLog.source = String(normalizedLog.source);
      }

      // 调试输出，帮助排查来源显示问题
      console.log(`日志ID ${log.id} 的来源字段值:`, {
        originalSource: log.source,
        originalSourceType: typeof log.source,
        normalizedSource: normalizedLog.source,
        normalizedSourceType: typeof normalizedLog.source
      });

      // 翻译模块名称
      const translatedModule = translateModuleName(normalizedLog.module);

      // 翻译操作类型
      const translatedAction = translateActionName(normalizedLog.action);

      // 处理描述，将模块名称考虑进去
      const processedDescription = processLogDescription(normalizedLog.description, normalizedLog.module);

      // 根据日志级别设置不同的颜色
      let levelBadgeClass = 'bg-info';
      if (normalizedLog.level === 'warning') {
        levelBadgeClass = 'bg-warning';
      } else if (normalizedLog.level === 'error') {
        levelBadgeClass = 'bg-danger';
      }

      // 根据来源设置不同的标签
      let sourceLabel = '后台管理';
      let sourceBadgeClass = 'bg-secondary';

      // 直接检查source字段是否等于'frontend'，并输出详细调试信息
      console.log(`日志ID ${normalizedLog.id} 的source字段值: "${normalizedLog.source}", 类型: ${typeof normalizedLog.source}`);

      if (normalizedLog.source === 'frontend') {
        sourceLabel = '前台用户';
        sourceBadgeClass = 'bg-primary';
        console.log(`日志ID ${normalizedLog.id} 被识别为前台用户日志`);
      } else {
        console.log(`日志ID ${normalizedLog.id} 被识别为后台管理日志`);
      }

      html += `
        <tr>
          <td>${normalizedLog.id}</td>
          <td>${normalizedLog.username}</td>
          <td><span class="badge ${sourceBadgeClass}">${sourceLabel}</span></td>
          <td>${translatedModule}</td>
          <td>${translatedAction}</td>
          <td>${processedDescription}</td>
          <td><span class="badge ${levelBadgeClass}">${normalizedLog.level === 'info' ? '信息' : normalizedLog.level === 'warning' ? '警告' : '错误'}</span></td>
          <td><span class="badge ${normalizedLog.status === 'success' ? 'bg-success' : 'bg-danger'}">${normalizedLog.status === 'success' ? '成功' : '失败'}</span></td>
          <td>${formatDate(normalizedLog.created_at)}</td>
          <td>
            <button class="btn btn-sm btn-info" onclick="logModule.viewLogDetails(${normalizedLog.id})">
              <i class="bi bi-eye"></i>
            </button>
          </td>
        </tr>
      `;
    });

    console.log('更新表格内容');
    tableBody.innerHTML = html;

    // 更新分页信息
    if (data.pagination) {
      totalPages = data.pagination.total_pages || Math.ceil(data.pagination.total / pageSize);
      currentPage = data.pagination.page || currentPage;
      renderPagination();
    } else if (data.totalPages) {
      totalPages = data.totalPages;
      renderPagination();
    }

    console.log('日志列表渲染完成');
  } catch (error) {
    console.error('渲染日志列表时出错:', error);
    // 在表格中显示错误消息
    const tableBody = document.getElementById('logsTableBody');
    if (tableBody) {
      tableBody.innerHTML = `<tr><td colspan="8" class="text-center text-danger">渲染日志数据时出错: ${error.message}</td></tr>`;
    }
  }
}

// 渲染分页
function renderPagination() {
  const pagination = document.getElementById('logsPagination');
  if(!pagination) {
    console.error('找不到分页容器');
    return;
  }

  if (totalPages <= 1) {
    pagination.innerHTML = '';
    return;
  }

  let html = `
    <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
      <a class="page-link" href="#" onclick="event.preventDefault(); window.logModule.changePage(${currentPage - 1});">上一页</a>
    </li>
  `;

  // 显示最多5个页码
  const startPage = Math.max(1, currentPage - 2);
  const endPage = Math.min(totalPages, startPage + 4);

  for (let i = startPage; i <= endPage; i++) {
    html += `
      <li class="page-item ${i === currentPage ? 'active' : ''}">
        <a class="page-link" href="#" onclick="event.preventDefault(); window.logModule.changePage(${i});">${i}</a>
      </li>
    `;
  }

  html += `
    <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
      <a class="page-link" href="#" onclick="event.preventDefault(); window.logModule.changePage(${currentPage + 1});">下一页</a>
    </li>
  `;

  pagination.innerHTML = html;
  console.log('分页导航已更新');
}

// 切换页码
function changePage(page) {
  if (page < 1 || page > totalPages) return;

  currentPage = page;
  fetchLogs();
}

// 重置筛选条件
function resetFilters() {
  console.log('重置筛选条件...');
  const moduleFilter = document.getElementById('logModuleFilter');
  const actionFilter = document.getElementById('logActionFilter');
  const statusFilter = document.getElementById('logStatusFilter');
  const usernameFilter = document.getElementById('logUsernameFilter');
  const startDateInput = document.getElementById('logStartDate');
  const endDateInput = document.getElementById('logEndDate');

  if (moduleFilter) moduleFilter.value = '';
  if (actionFilter) actionFilter.value = '';
  if (statusFilter) statusFilter.value = '';
  if (usernameFilter) usernameFilter.value = '';

  // 重置日期为默认值（例如，最近7天）
  const today = new Date();
  const pastDate = new Date();
  pastDate.setDate(today.getDate() - 6); // 默认查最近7天

  const formatDate = (date) => {
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  };

  if (startDateInput) startDateInput.value = formatDate(pastDate);
  if (endDateInput) endDateInput.value = formatDate(today);

  // 重置当前页码和每页数量
  currentPage = 1;
  // pageSize = 10; // 或者保持用户选择的pageSize
  // const pageSizeSelect = document.getElementById('logPageSize');
  // if (pageSizeSelect) pageSizeSelect.value = pageSize;

  console.log('筛选条件已重置');
  // 重置后自动重新获取日志
  fetchLogs();
}

// 查看日志详情
function viewLogDetails(logId) {
  const token = localStorage.getItem('token');
  if (!token) return;

  // 从当前表格中获取日志数据
  const rows = document.querySelectorAll('#logsTableBody tr');
  let logRow = null;

  for (let i = 0; i < rows.length; i++) {
    const firstCell = rows[i].cells[0];
    if (firstCell && firstCell.textContent == logId) {
      logRow = rows[i];
      break;
    }
  }

  if (!logRow) {
    console.error('找不到日志行:', logId);
    return;
  }

  const log = {
    id: logId,
    username: logRow.cells[1].textContent,
    source: logRow.cells[2].querySelector('.badge').textContent === '前台用户' ? 'frontend' : 'backend',
    module: logRow.cells[3].textContent,
    action: logRow.cells[4].textContent,
    description: logRow.cells[5].textContent,
    level: logRow.cells[6].querySelector('.badge').textContent === '信息' ? 'info' :
           logRow.cells[6].querySelector('.badge').textContent === '警告' ? 'warning' : 'error',
    status: logRow.cells[7].textContent.includes('成功') ? 'success' : 'failure',
    created_at: logRow.cells[8].textContent
  };

  // 确保source字段是字符串类型
  if (log.source !== undefined && log.source !== null) {
    log.source = String(log.source);
  }

  console.log('从表格行提取的日志对象:', {
    id: log.id,
    source: log.source,
    sourceType: typeof log.source
  });

  // 显示详情模态框
  let modal;
  try {
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
      modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
    } else {
      console.error('Bootstrap Modal未定义，尝试使用jQuery模态框');
      if (typeof $ !== 'undefined' && $.fn.modal) {
        $('#logDetailModal').modal('show');
      } else {
        alert('无法显示日志详情模态框，请检查页面加载状态');
        return;
      }
    }
  } catch (error) {
    console.error('创建模态框失败:', error);
    alert('无法显示日志详情模态框，请刷新页面后重试');
    return;
  }

  document.getElementById('logDetailId').textContent = log.id;
  document.getElementById('logDetailUser').textContent = log.username;

  // 设置来源标签
  let sourceLabel = '后台管理';
  let sourceBadgeClass = 'bg-secondary';

  // 直接检查source字段是否等于'frontend'，并输出详细调试信息
  console.log(`日志详情ID ${log.id} 的source字段值: "${log.source}", 类型: ${typeof log.source}`);

  if (log.source === 'frontend') {
    sourceLabel = '前台用户';
    sourceBadgeClass = 'bg-primary';
    console.log(`日志详情ID ${log.id} 被识别为前台用户日志`);
  } else {
    console.log(`日志详情ID ${log.id} 被识别为后台管理日志`);
  }
  document.getElementById('logDetailSource').textContent = sourceLabel;
  document.getElementById('logDetailSource').className = `badge ${sourceBadgeClass}`;

  document.getElementById('logDetailModule').textContent = log.module;
  document.getElementById('logDetailAction').textContent = log.action;
  document.getElementById('logDetailDescription').textContent = log.description;

  // 设置日志级别标签
  let levelBadgeClass = 'bg-info';
  let levelLabel = '信息';
  if (log.level === 'warning') {
    levelBadgeClass = 'bg-warning';
    levelLabel = '警告';
  } else if (log.level === 'error') {
    levelBadgeClass = 'bg-danger';
    levelLabel = '错误';
  }
  document.getElementById('logDetailLevel').textContent = levelLabel;
  document.getElementById('logDetailLevel').className = `badge ${levelBadgeClass}`;

  document.getElementById('logDetailStatus').textContent = log.status === 'success' ? '成功' : '失败';
  document.getElementById('logDetailStatus').className = `badge ${log.status === 'success' ? 'bg-success' : 'bg-danger'}`;
  document.getElementById('logDetailTime').textContent = log.created_at;

  // 获取详细信息
  fetch(`/api/admin/logs/${logId}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      const details = data.data.details;
      if (details) {
        try {
          const detailsObj = typeof details === 'string' ? JSON.parse(details) : details;
          document.getElementById('logDetailInfo').textContent = JSON.stringify(detailsObj, null, 2);
        } catch (e) {
          document.getElementById('logDetailInfo').textContent = details;
        }
      } else {
        document.getElementById('logDetailInfo').textContent = '无详细信息';
      }
    } else {
      document.getElementById('logDetailInfo').textContent = '获取详细信息失败';
    }
  })
  .catch(error => {
    console.error('获取日志详情失败:', error);
    document.getElementById('logDetailInfo').textContent = '获取详细信息失败';
  });

  // 显示模态框
  if (modal && typeof modal.show === 'function') {
    try {
      modal.show();
    } catch (error) {
      console.error('显示模态框失败:', error);
      // 尝试使用jQuery作为备选
      if (typeof $ !== 'undefined' && $.fn.modal) {
        $('#logDetailModal').modal('show');
      }
    }
  }
}

// 显示清理日志模态框
function showCleanupModal() {
  try {
    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
      const modal = new bootstrap.Modal(document.getElementById('logCleanupModal'));
      // 默认值为各级别日志的保留天数
      document.getElementById('infoLogsDays').value = 15;
      document.getElementById('warningLogsDays').value = 30;
      document.getElementById('errorLogsDays').value = 60;
      document.getElementById('maxLogsCount').value = 10000;
      modal.show();
    } else {
      console.error('Bootstrap Modal未定义，无法显示模态框');
      // 尝试使用jQuery模态框作为备选
      if (typeof $ !== 'undefined' && $.fn.modal) {
        // 设置默认值
        document.getElementById('infoLogsDays').value = 15;
        document.getElementById('warningLogsDays').value = 30;
        document.getElementById('errorLogsDays').value = 60;
        document.getElementById('maxLogsCount').value = 10000;
        $('#logCleanupModal').modal('show');
      } else {
        alert('无法显示清理日志模态框，请检查页面加载状态');
      }
    }
  } catch (error) {
    console.error('显示清理模态框失败:', error);
    alert('无法显示清理日志模态框，请刷新页面后重试');
  }
}

// 执行日志清理
function cleanupLogs() {
  const infoLogsDays = document.getElementById('infoLogsDays').value;
  const warningLogsDays = document.getElementById('warningLogsDays').value;
  const errorLogsDays = document.getElementById('errorLogsDays').value;
  const maxLogsCount = document.getElementById('maxLogsCount').value;

  if (!infoLogsDays || isNaN(parseInt(infoLogsDays)) ||
      !warningLogsDays || isNaN(parseInt(warningLogsDays)) ||
      !errorLogsDays || isNaN(parseInt(errorLogsDays)) ||
      !maxLogsCount || isNaN(parseInt(maxLogsCount))) {
    alert('请提供有效的天数和最大记录数');
    return;
  }

  if (parseInt(infoLogsDays) < 1 || parseInt(warningLogsDays) < 1 || parseInt(errorLogsDays) < 1 || parseInt(maxLogsCount) < 100) {
    alert('信息日志至少保留1天，警告日志至少保留1天，错误日志至少保留1天，最大记录数至少为100');
    return;
  }

  if (confirm(`确定要清理过期日志吗？\n信息级日志将保留最近${infoLogsDays}天\n警告级日志将保留最近${warningLogsDays}天\n错误级日志将保留最近${errorLogsDays}天\n日志总数将限制在${maxLogsCount}条以内`)) {
    performLogCleanup({
      infoKeepDays: parseInt(infoLogsDays),
      warningKeepDays: parseInt(warningLogsDays),
      errorKeepDays: parseInt(errorLogsDays),
      maxLogs: parseInt(maxLogsCount)
    });
  }
}

// 执行日志清理
async function performLogCleanup(options) {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('未找到认证令牌');
    }

    try {
      if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        const modal = bootstrap.Modal.getInstance(document.getElementById('logCleanupModal'));
        if (modal) {
          modal.hide();
        } else {
          console.log('未找到模态框实例，可能已关闭');
        }
      } else if (typeof $ !== 'undefined' && $.fn.modal) {
        $('#logCleanupModal').modal('hide');
      }
    } catch (error) {
      console.error('关闭模态框失败:', error);
      // 继续执行，不影响后续操作
    }

    showLoading('正在清理日志，请稍候...');

    const response = await fetch('/api/admin/logs/cleanup', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(options)
    });

    hideLoading();

    if (!response.ok) {
      throw new Error(`清理日志失败，状态码: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      alert(`日志清理成功!\n清理结果:\n信息级日志: ${data.data.infoRemoved}条\n警告级日志: ${data.data.warningRemoved}条\n错误级日志: ${data.data.errorRemoved}条\n额外清理: ${data.data.extraRemoved}条\n总计: ${data.data.totalRemoved}条\n当前剩余: ${data.data.remainingCount}条`);
      // 重新加载日志数据
      fetchLogs();
      // 重新加载统计数据
      fetchLogStats();
    } else {
      alert(`日志清理失败: ${data.message}`);
    }
  } catch (error) {
    hideLoading();
    console.error('日志清理出错:', error);
    alert(`日志清理出错: ${error.message}`);
  }
}

// 更新模块统计图表
function updateModuleChart(data) {
  console.log('开始更新模块统计图表，数据:', data);

  try {
    // 检查Chart对象是否存在
    if (typeof Chart === 'undefined') {
      console.error('Chart对象不存在，尝试加载后再更新图表');
      ensureChartJsLoaded(function() {
        updateModuleChart(data); // 重新尝试更新
      });
      return;
    }

    const chartElement = document.getElementById('moduleChart');
    if (!chartElement) {
      console.error('找不到moduleChart元素，无法创建图表');
      return;
    }

    // 检查元素是否在可见的DOM中
    if (chartElement.offsetParent === null) {
      console.log('moduleChart元素不可见，可能在隐藏的页面中，跳过创建图表');
      return;
    }

    try {
      const ctx = chartElement.getContext('2d');
      if (!ctx) {
        console.error('无法获取2D上下文');
        return;
      }

      // 销毁旧图表
      if (window.moduleChart) {
        console.log('销毁旧的模块图表');
        try {
          if (typeof window.moduleChart === 'object' && typeof window.moduleChart.destroy === 'function') {
            window.moduleChart.destroy();
          }
          window.moduleChart = null;
        } catch (e) {
          console.error('销毁旧图表时出错:', e);
          window.moduleChart = null;
          // 继续执行，尝试创建新图表
        }
      }

      if (!data || data.length === 0) {
        console.log('模块统计数据为空，不创建图表');
        return;
      }

      // 准备数据
      const labels = data.map(item => translateModuleName(item.module));
      const values = data.map(item => item.count);

      console.log('创建新的模块图表，标签:', labels, '数值:', values);
      // 创建新图表
      window.moduleChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: '操作次数',
            data: values,
            backgroundColor: 'rgba(54, 162, 235, 0.5)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              display: false
            },
            title: {
              display: true,
              text: '模块操作统计'
            }
          },
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      });

      console.log('模块统计图表创建成功');
    } catch (canvasError) {
      console.error('创建图表时出错:', canvasError);
    }
  } catch (error) {
    console.error('更新模块统计图表时出错:', error);
  }
}

// 更新操作类型统计图表
function updateActionChart(data) {
  console.log('开始更新操作类型统计图表，数据:', data);

  try {
    // 检查Chart对象是否存在
    if (typeof Chart === 'undefined') {
      console.error('Chart对象不存在，尝试加载后再更新图表');
      ensureChartJsLoaded(function() {
        updateActionChart(data); // 重新尝试更新
      });
      return;
    }

    const chartElement = document.getElementById('actionChart');
    if (!chartElement) {
      console.error('找不到actionChart元素，无法创建图表');
      return;
    }

    // 检查元素是否在可见的DOM中
    if (chartElement.offsetParent === null) {
      console.log('actionChart元素不可见，可能在隐藏的页面中，跳过创建图表');
      return;
    }

    try {
      const ctx = chartElement.getContext('2d');
      if (!ctx) {
        console.error('无法获取2D上下文');
        return;
      }

      // 销毁旧图表
      if (window.actionChart) {
        console.log('销毁旧的操作类型图表');
        try {
          if (typeof window.actionChart === 'object' && typeof window.actionChart.destroy === 'function') {
            window.actionChart.destroy();
          }
          window.actionChart = null;
        } catch (e) {
          console.error('销毁旧图表时出错:', e);
          window.actionChart = null;
          // 继续执行，尝试创建新图表
        }
      }

      if (!data || data.length === 0) {
        console.log('操作类型统计数据为空，不创建图表');
        return;
      }

      // 准备数据
      const labels = data.map(item => item.action);
      const values = data.map(item => item.count);

      console.log('创建新的操作类型图表，标签:', labels, '数值:', values);
      // 创建新图表
      window.actionChart = new Chart(ctx, {
        type: 'pie',
        data: {
          labels: labels,
          datasets: [{
            data: values,
            backgroundColor: [
              'rgba(255, 99, 132, 0.5)',
              'rgba(54, 162, 235, 0.5)',
              'rgba(255, 206, 86, 0.5)',
              'rgba(75, 192, 192, 0.5)',
              'rgba(153, 102, 255, 0.5)'
            ],
            borderColor: [
              'rgba(255, 99, 132, 1)',
              'rgba(54, 162, 235, 1)',
              'rgba(255, 206, 86, 1)',
              'rgba(75, 192, 192, 1)',
              'rgba(153, 102, 255, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'right'
            },
            title: {
              display: true,
              text: '操作类型统计'
            }
          }
        }
      });

      console.log('操作类型统计图表创建成功');
    } catch (canvasError) {
      console.error('创建图表时出错:', canvasError);
    }
  } catch (error) {
    console.error('更新操作类型统计图表时出错:', error);
  }
}

// 更新状态统计图表
function updateStatusChart(data) {
  console.log('开始更新状态统计图表，数据:', data);

  try {
    // 检查Chart对象是否存在
    if (typeof Chart === 'undefined') {
      console.error('Chart对象不存在，尝试加载后再更新图表');
      ensureChartJsLoaded(function() {
        updateStatusChart(data); // 重新尝试更新
      });
      return;
    }

    const chartElement = document.getElementById('statusChart');
    if (!chartElement) {
      console.error('找不到statusChart元素，无法创建图表');
      return;
    }

    // 检查元素是否在可见的DOM中
    if (chartElement.offsetParent === null) {
      console.log('statusChart元素不可见，可能在隐藏的页面中，跳过创建图表');
      return;
    }

    try {
      const ctx = chartElement.getContext('2d');
      if (!ctx) {
        console.error('无法获取2D上下文');
        return;
      }

      // 销毁旧图表
      if (window.statusChart) {
        console.log('销毁旧的状态图表');
        try {
          if (typeof window.statusChart === 'object' && typeof window.statusChart.destroy === 'function') {
            window.statusChart.destroy();
          }
          window.statusChart = null;
        } catch (e) {
          console.error('销毁旧图表时出错:', e);
          window.statusChart = null;
          // 继续执行，尝试创建新图表
        }
      }

      if (!data || data.length === 0) {
        console.log('状态统计数据为空，不创建图表');
        return;
      }

      // 准备数据
      const labels = data.map(item => item.status === 'success' ? '成功' : '失败');
      const values = data.map(item => item.count);

      console.log('创建新的状态图表，标签:', labels, '数值:', values);
      // 创建新图表
      window.statusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [{
            data: values,
            backgroundColor: [
              'rgba(75, 192, 192, 0.5)',
              'rgba(255, 99, 132, 0.5)'
            ],
            borderColor: [
              'rgba(75, 192, 192, 1)',
              'rgba(255, 99, 132, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'bottom'
            },
            title: {
              display: true,
              text: '操作状态统计'
            }
          }
        }
      });

      console.log('状态统计图表创建成功');
    } catch (canvasError) {
      console.error('创建图表时出错:', canvasError);
    }
  } catch (error) {
    console.error('更新状态统计图表时出错:', error);
  }
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '-';

  try {
    // 尝试直接解析日期
    let date = new Date(dateString);

    // 检查是否为有效日期
    if (isNaN(date.getTime())) {
      console.error('无效的日期字符串:', dateString);
      return '-';
    }

    // 确保日期格式化正确
    const options = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    };

    // 使用toLocaleString进行本地化格式化
    const formattedDate = date.toLocaleString('zh-CN', options);
    console.log('日期格式化结果:', dateString, '->', formattedDate);
    return formattedDate;
  } catch (error) {
    console.error('日期格式化错误:', error, '日期字符串:', dateString);

    // 尝试备用格式化方法
    try {
      // 处理可能的ISO字符串或其他特殊格式
      if (typeof dateString === 'string') {
        // 处理ISO格式
        if (dateString.includes('T')) {
          const parts = dateString.split('T');
          const datePart = parts[0];
          const timePart = parts[1].split('.')[0];
          return `${datePart} ${timePart}`;
        }

        // 处理其他可能的格式
        if (dateString.includes('-') || dateString.includes('/')) {
          return dateString;
        }
      }
    } catch (backupError) {
      console.error('备用日期格式化也失败:', backupError);
    }

    return '-';
  }
}

// 格式化日期为API格式 (YYYY-MM-DD)
function formatDateForAPI(date) {
  if (!date) return '';

  try {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '';
  }
}

// 在模块内部不需要这段代码，因为我们已经在IIFE外部添加了DOMContentLoaded事件监听器
// 并且在dashboard.js中也有加载模块的逻辑

// 显示加载提示
function showLoading(message = '加载中...') {
  // 检查是否已存在加载提示
  if (document.getElementById('loadingIndicator')) {
    return;
  }

  // 创建加载提示元素
  const loadingIndicator = document.createElement('div');
  loadingIndicator.id = 'loadingIndicator';
  loadingIndicator.className = 'position-fixed d-flex justify-content-center align-items-center bg-dark bg-opacity-25';
  loadingIndicator.style.top = '0';
  loadingIndicator.style.left = '0';
  loadingIndicator.style.width = '100%';
  loadingIndicator.style.height = '100%';
  loadingIndicator.style.zIndex = '9999';
  loadingIndicator.innerHTML = `
    <div class="bg-white p-5 rounded shadow">
      <div class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">处理中...</span>
        </div>
        <p class="mt-3 mb-0">${message}</p>
      </div>
    </div>
  `;
  document.body.appendChild(loadingIndicator);
}

// 隐藏加载提示
function hideLoading() {
  const loadingIndicator = document.getElementById('loadingIndicator');
  if (loadingIndicator) {
    loadingIndicator.remove();
  }
}

// 导出日志功能
function exportLogs() {
  console.log('导出日志功能尚未实现');
  alert('导出日志功能尚未实现');
}

// 返回公共API
return {
  init,
  fetchLogs,
  fetchLogStats,
  changePage,
  resetFilters,
  viewLogDetails,
  showCleanupModal,
  cleanupLogs,
  exportLogs
};

})(); // 结束IIFE

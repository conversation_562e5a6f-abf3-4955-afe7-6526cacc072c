const { Op } = require('sequelize');
const { sequelize } = require('../config/database');
const { SystemLog, User } = require('../models');
const logger = require('../utils/logger');
const LogService = require('../services/logService');
const { successResponse, errorResponse, paginationResponse } = require('../utils/responseUtils');

/**
 * 获取系统日志列表
 * @route GET /api/admin/logs
 */
const getLogs = async (req, res) => {
  try {
    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log('获取日志列表，请求参数:', req.query);
    }

    // 获取分页参数和筛选参数
    const page = parseInt(req.query.page, 10) || 1;
    const pageSize = parseInt(req.query.pageSize, 10) || 10;
    
    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`使用分页参数: page=${page}, pageSize=${pageSize}`);
    }

    const moduleFilter = req.query.module;
    const actionFilter = req.query.action;
    const statusFilter = req.query.status;
    const usernameFilter = req.query.username;
    const sourceFilter = req.query.source;
    const startDate = req.query.startDate;
    const endDate = req.query.endDate;

    // 参数验证
    if (pageSize > 100) {
      return res.status(400).json({
        success: false,
        message: '每页最大记录数不能超过100条'
      });
    }

    // 构建查询条件
    const where = {};

    if (moduleFilter) where.module = moduleFilter;
    if (actionFilter) where.action = actionFilter;
    if (statusFilter) where.status = statusFilter;
    if (usernameFilter) where.username = { [Op.like]: `%${usernameFilter}%` };
    if (sourceFilter) where.source = sourceFilter;

    // 日期范围查询
    if (startDate || endDate) {
      where.created_at = {};
      if (startDate) where.created_at[Op.gte] = new Date(startDate);
      if (endDate) {
        // 将结束日期设置为当天的最后一秒，以包含当天的日志
        const endDateObj = new Date(endDate);
        endDateObj.setHours(23, 59, 59, 999);
        where.created_at[Op.lte] = endDateObj;
      }
    }

    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log('构建的查询条件:', where);
    }

    // 查询日志总数
    const total = await SystemLog.count({ where });
    
    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`符合条件的日志总数: ${total}`);
      console.log(`查询参数: limit=${pageSize}, offset=${(page - 1) * pageSize}`);
    }

    // 查询分页日志数据
    const logs = await SystemLog.findAll({
      where,
      limit: parseInt(pageSize, 10),
      offset: (parseInt(page, 10) - 1) * parseInt(pageSize, 10),
      order: [['created_at', 'DESC']]
    });
    
    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`查询到${logs.length}条日志记录`);
    }

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    // 规范化返回结果，确保字段名称与前端一致
    const formattedLogs = logs.map(log => {
      const plainLog = log.get({ plain: true });

      // 确保created_at字段存在
      if (plainLog.createdAt && !plainLog.created_at) {
        plainLog.created_at = plainLog.createdAt;
      }

      // 确保source字段值正确，明确设置为'frontend'或'backend'
      if (plainLog.source && String(plainLog.source).trim() === 'frontend') {
        // 确保前台用户日志的source字段值完全等于'frontend'
        plainLog.source = 'frontend';
        
        // 只在开发环境输出详细日志
        if (process.env.NODE_ENV === 'development') {
          console.log(`日志ID ${plainLog.id} 是前台用户日志，source=${plainLog.source}`);
        }
      } else {
        // 其他情况统一设置为'backend'
        plainLog.source = 'backend';
      }

      return plainLog;
    });

    res.json({
      success: true,
      currentPage: page,
      pageSize: pageSize,
      totalPages: totalPages,
      totalRecords: total,
      logs: formattedLogs
    });
  } catch (error) {
    console.error('获取日志列表失败:', error);
    logger.error('获取日志列表失败', { error: error.message, stack: error.stack });

    res.status(500).json({
      success: false,
      message: '获取日志列表失败',
      error: error.message
    });
  }
};

/**
 * 获取日志详情
 * @route GET /api/admin/logs/:id
 */
const getLogDetail = async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID参数
    if (!id || isNaN(parseInt(id))) {
      return errorResponse(res, '日志ID无效', 400);
    }

    // 获取日志详情
    const log = await SystemLog.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'phone', 'role'],
          required: false
        }
      ]
    });

    if (!log) {
      return errorResponse(res, '日志不存在', 404);
    }

    // 规范化日志对象，确保source字段值正确
    const logData = log.get({ plain: true });
    if (logData.source && String(logData.source).trim() === 'frontend') {
      // 确保前台用户日志的source字段值完全等于'frontend'
      logData.source = 'frontend';
      console.log(`日志详情ID ${logData.id} 是前台用户日志，source=${logData.source}`);
    } else {
      // 其他情况统一设置为'backend'
      logData.source = 'backend';
    }

    return successResponse(res, '获取日志详情成功', logData);
  } catch (error) {
    logger.error('获取日志详情失败:', error);
    return errorResponse(res, '获取日志详情失败', 500);
  }
};

/**
 * 获取日志统计信息
 * @route GET /api/admin/logs/stats
 */
const getLogStats = async (req, res) => {
  try {
    // 获取时间范围参数
    const startDate = req.query.startDate ? new Date(req.query.startDate) : null;
    const endDate = req.query.endDate ? new Date(req.query.endDate) : null;

    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log('获取日志统计，时间范围:', { startDate, endDate });
    }

    // 构建时间范围条件
    const timeCondition = {};
    if (startDate || endDate) {
      timeCondition.created_at = {};
      if (startDate) timeCondition.created_at[Op.gte] = startDate;
      if (endDate) {
        const endOfDay = new Date(endDate);
        endOfDay.setHours(23, 59, 59, 999);
        timeCondition.created_at[Op.lte] = endOfDay;
      }
    }

    try {
      // 查询模块统计
      const moduleResults = await SystemLog.findAll({
        attributes: ['module', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        where: timeCondition,
        group: ['module'],
        raw: true
      });

      // 查询操作统计
      const actionResults = await SystemLog.findAll({
        attributes: ['action', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        where: timeCondition,
        group: ['action'],
        raw: true
      });

      // 查询状态统计
      const statusResults = await SystemLog.findAll({
        attributes: ['status', [sequelize.fn('COUNT', sequelize.col('id')), 'count']],
        where: timeCondition,
        group: ['status'],
        raw: true
      });

      // 格式化数据为统一的数组格式
      const moduleStats = moduleResults.map(item => ({
        module: item.module || 'unknown',
        count: parseInt(item.count, 10)
      }));

      const actionStats = actionResults.map(item => ({
        action: item.action || 'unknown',
        count: parseInt(item.count, 10)
      }));

      const statusStats = statusResults.map(item => ({
        status: item.status || 'unknown',
        count: parseInt(item.count, 10)
      }));

      // 只在开发环境输出详细日志
      if (process.env.NODE_ENV === 'development') {
        console.log('日志统计数据已准备:', {
          moduleStats: moduleStats.length,
          actionStats: actionStats.length,
          statusStats: statusStats.length
        });
      }

      // 返回统一格式的数据
      return res.json({
        success: true,
        moduleStats,
        actionStats,
        statusStats
      });
    } catch (dbError) {
      console.error('查询日志统计数据库错误:', dbError);
      throw dbError;
    }
  } catch (error) {
    console.error('获取日志统计失败:', error);
    logger.error('获取日志统计失败', { error: error.message, stack: error.stack });
    res.status(500).json({
      success: false,
      message: '获取日志统计失败',
      error: error.message
    });
  }
};

/**
 * 清理过期日志
 * @route DELETE /api/admin/logs/cleanup
 */
const cleanupLogs = async (req, res) => {
  try {
    // 获取清理选项
    const {
      infoKeepDays = 15,
      warningKeepDays = 30,
      errorKeepDays = 60,
      maxLogs = 10000
    } = req.body;

    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log('清理日志，参数:', req.body);
    }

    // 验证参数
    const options = {
      infoKeepDays: Math.max(1, parseInt(infoKeepDays, 10) || 15),
      warningKeepDays: Math.max(1, parseInt(warningKeepDays, 10) || 30),
      errorKeepDays: Math.max(1, parseInt(errorKeepDays, 10) || 60),
      maxLogs: Math.max(100, parseInt(maxLogs, 10) || 10000)
    };

    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log('使用清理配置:', options);
    }

    // 执行清理操作
    const result = await LogService.cleanupLogs(options);

    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log('日志清理结果:', result);
    }

    // 记录清理操作到日志
    await LogService.createLog({
      username: req.user ? req.user.nickname || req.user.phone : '系统管理员',
      user_id: req.user ? req.user.id : null,
      action: 'cleanup',
      module: 'logs',
      description: `清理了${result.totalRemoved}条日志记录，当前剩余${result.remainingCount}条`,
      status: 'success',
      level: 'info',
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({
      success: true,
      message: `成功清理了${result.totalRemoved}条日志记录`,
      data: result
    });
  } catch (error) {
    console.error('清理日志失败:', error);
    logger.error('清理日志失败', { error: error.message, stack: error.stack });
    res.status(500).json({
      success: false,
      message: '清理日志失败',
      error: error.message
    });
  }
};

/**
 * 创建测试日志（仅用于开发环境）
 * @route POST /api/admin/logs/test
 */
const createTestLogs = async (req, res) => {
  try {
    // 检查当前环境，只允许在开发环境中创建测试日志
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).json({
        success: false,
        message: '此功能仅在开发环境可用'
      });
    }
    
    console.log('创建测试日志');

    // 测试日志数据
    const testLogs = [
      { module: 'user', action: 'login', status: 'success', userId: 1, details: '用户登录成功', ip: '127.0.0.1' },
      { module: 'user', action: 'register', status: 'success', userId: 2, details: '新用户注册', ip: '127.0.0.1' },
      { module: 'cover', action: 'create', status: 'success', userId: 1, details: '创建了新封面', ip: '127.0.0.1' },
      { module: 'cover', action: 'update', status: 'success', userId: 1, details: '更新了封面信息', ip: '127.0.0.1' },
      { module: 'feature', action: 'purchase', status: 'success', userId: 2, details: '购买了高级功能', ip: '127.0.0.1' },
      { module: 'admin', action: 'settings', status: 'failure', userId: 1, details: '修改系统设置失败', ip: '127.0.0.1' },
      { module: 'payment', action: 'recharge', status: 'success', userId: 2, details: '充值100积分', ip: '127.0.0.1' },
    ];

    // 创建测试日志
    const createdLogs = await Promise.all(
      testLogs.map(logData => SystemLog.create(logData))
    );

    console.log(`成功创建${createdLogs.length}条测试日志`);

    res.status(201).json({
      success: true,
      message: `成功创建${createdLogs.length}条测试日志`,
      logs: createdLogs
    });
  } catch (error) {
    console.error('创建测试日志失败:', error);
    logger.error('创建测试日志失败', { error: error.message, stack: error.stack });
    res.status(500).json({
      success: false,
      message: '创建测试日志失败',
      error: error.message
    });
  }
};

module.exports = {
  getLogs,
  getLogDetail,
  getLogStats,
  cleanupLogs,
  createTestLogs
};

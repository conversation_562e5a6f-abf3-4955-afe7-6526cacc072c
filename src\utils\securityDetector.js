/**
 * HTML安全检测引擎 - 基于OWASP最佳实践
 * 专注于检测真正的恶意代码，避免误报正常静态文件
 * 参考：OWASP Cross Site Scripting Prevention Cheat Sheet
 */

import logger from '../services/logs/frontendLogger.js';

/**
 * 安全检测引擎版本
 */
export const SECURITY_ENGINE_VERSION = '2.0.0';

/**
 * 基于OWASP最佳实践的安全检测规则配置
 * 只检测真正的安全威胁，允许正常的HTML/CSS/JS功能
 */
export const SECURITY_RULES = {
  // 真正的XSS攻击检测（基于OWASP指南）
  xss: {
    patterns: [
      // 只检测真正危险的XSS模式
      /<script[^>]*>[\s\S]*?alert\s*\(/gi, // 包含alert的script
      /<script[^>]*>[\s\S]*?document\.cookie/gi, // 窃取cookie的script
      /<script[^>]*>[\s\S]*?window\.location\s*=/gi, // 重定向攻击
      /javascript\s*:\s*alert\s*\(/gi, // javascript:alert()
      /javascript\s*:\s*document\.cookie/gi, // javascript:document.cookie
      /on\w+\s*=\s*["'][^"']*alert\s*\(/gi, // 事件处理器中的alert
      /on\w+\s*=\s*["'][^"']*document\.cookie/gi, // 事件处理器中的cookie窃取
      /<iframe[^>]*src\s*=\s*["']javascript\s*:\s*alert/gi, // iframe中的javascript:alert
      // 检测明显的XSS载荷
      /&lt;script&gt;/gi, // HTML编码的script标签
      /%3Cscript%3E/gi, // URL编码的script标签
    ],
    severity: 'HIGH',
    description: '真正的XSS攻击检测',
    enabled: true
  },

  // 恶意脚本注入检测（严格模式）
  maliciousScript: {
    patterns: [
      // 只检测真正危险的脚本注入
      /eval\s*\(\s*["'][^"']*<script/gi, // eval注入script
      /eval\s*\(\s*["'][^"']*alert/gi, // eval注入alert
      /setTimeout\s*\(\s*["'][^"']*alert/gi, // setTimeout注入alert
      /setInterval\s*\(\s*["'][^"']*alert/gi, // setInterval注入alert
      /Function\s*\(\s*["'][^"']*alert/gi, // Function构造器注入
      // 检测明显的恶意载荷编码
      /String\.fromCharCode\s*\([^)]*\)\s*\+[\s\S]*alert/gi,
      /\\x[0-9a-f]{2}[\s\S]*alert/gi, // 十六进制编码绕过
      /\\u[0-9a-f]{4}[\s\S]*alert/gi, // Unicode编码绕过
    ],
    severity: 'HIGH',
    description: '恶意脚本注入检测',
    enabled: true
  },

  // 危险标签检测（仅检测真正危险的用法）
  dangerousTags: {
    patterns: [
      // 只检测包含明显恶意内容的标签
      /<script[^>]*>[\s\S]*?alert\s*\(/gi, // 包含alert的script
      /<script[^>]*>[\s\S]*?document\.cookie/gi, // 包含cookie窃取的script
      /<script[^>]*>[\s\S]*?eval\s*\(/gi, // 包含eval的script
      /<object[^>]*data\s*=\s*["']javascript:/gi, // object标签的javascript协议
      /<embed[^>]*src\s*=\s*["']javascript:/gi, // embed标签的javascript协议
      /<applet[^>]*>/gi, // applet标签（已废弃且危险）
      // 检测危险的iframe用法
      /<iframe[^>]*src\s*=\s*["']javascript\s*:\s*alert/gi,
      /<iframe[^>]*src\s*=\s*["']data\s*:\s*text\/html[^"']*alert/gi,
    ],
    severity: 'HIGH',
    description: '危险HTML标签检测',
    enabled: true
  },

  // 外部资源安全检测（宽松模式）
  externalResources: {
    // 只检测明显可疑的外部资源
    patterns: [
      // 检测可疑的外部脚本加载
      /src\s*=\s*["']https?:\/\/[^"']*\.(?:tk|ml|ga|cf)\/[^"']*\.js/gi, // 可疑域名的JS
      /src\s*=\s*["']https?:\/\/[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\/[^"']*\.js/gi, // IP地址的JS
      // 检测可疑的重定向
      /href\s*=\s*["']https?:\/\/[^"']*\.(?:tk|ml|ga|cf)\//gi, // 可疑域名链接
    ],
    severity: 'MEDIUM',
    description: '可疑外部资源检测',
    enabled: true
  },

  // 高危内容检测（严格模式）
  suspiciousContent: {
    patterns: [
      // 只检测真正危险的内容
      /data\s*:\s*text\/html[^;]*;[^,]*,[\s\S]*<script/gi, // data URI中的脚本
      /data\s*:\s*text\/html[^;]*;[^,]*,[\s\S]*alert/gi, // data URI中的alert
      /vbscript\s*:\s*[^"'\s]/gi, // VBScript协议
      /expression\s*\(\s*[^)]*alert/gi, // CSS expression中的alert
      // 检测明显的恶意base64载荷
      /data\s*:\s*text\/html\s*;\s*base64\s*,\s*[A-Za-z0-9+\/=]*PHNjcmlwdA/gi, // base64编码的<script
    ],
    severity: 'HIGH',
    description: '高危内容检测',
    enabled: true
  },

  // 路径遍历攻击检测
  pathTraversal: {
    patterns: [
      // 只检测明显的路径遍历攻击
      /\.\.\/\.\.\/\.\.\/.*(?:etc\/passwd|windows\/system32)/gi, // 明显的系统文件访问
      /%2e%2e%2f%2e%2e%2f%2e%2e%2f.*(?:etc|windows)/gi, // URL编码的路径遍历
      /file\s*:\s*\/\/\/.*(?:etc\/passwd|c:\/windows)/gi, // file协议访问系统文件
    ],
    severity: 'HIGH',
    description: '路径遍历攻击检测',
    enabled: true
  },

  // 自定义规则数组
  customRules: []
};

/**
 * 安全检测引擎核心类
 */
export class SecurityDetector {
  /**
   * 执行HTML内容安全检测
   * @param {string} htmlContent - 要检测的HTML内容
   * @param {Object} options - 检测选项
   * @returns {Object} 检测结果
   */
  static detect(htmlContent, options = {}) {
    try {
      if (!htmlContent || typeof htmlContent !== 'string') {
        return {
          passed: false,
          riskLevel: 'HIGH',
          details: [{
            rule: 'validation',
            severity: 'HIGH',
            violations: ['HTML内容为空或格式无效']
          }],
          engineVersion: SECURITY_ENGINE_VERSION,
          timestamp: new Date().toISOString()
        };
      }

      const results = [];
      const enabledRules = options.enabledRules || Object.keys(SECURITY_RULES);

      // 遍历所有启用的检测规则
      for (const ruleName of enabledRules) {
        if (ruleName === 'customRules') continue;
        
        const rule = SECURITY_RULES[ruleName];
        if (!rule || !rule.enabled) continue;

        const ruleResult = this.applyRule(htmlContent, rule, ruleName);
        if (ruleResult.violations.length > 0) {
          results.push({
            rule: ruleName,
            severity: rule.severity,
            description: rule.description,
            violations: ruleResult.violations
          });
        }
      }

      // 应用自定义规则
      if (SECURITY_RULES.customRules && SECURITY_RULES.customRules.length > 0) {
        const customResults = this.applyCustomRules(htmlContent);
        results.push(...customResults);
      }

      const riskLevel = this.calculateRiskLevel(results);
      const passed = results.length === 0;

      logger.info(`安全检测完成: ${passed ? '通过' : '未通过'}, 风险等级: ${riskLevel}`);

      return {
        passed,
        riskLevel,
        details: results,
        engineVersion: SECURITY_ENGINE_VERSION,
        timestamp: new Date().toISOString(),
        summary: {
          totalViolations: results.reduce((sum, r) => sum + r.violations.length, 0),
          criticalCount: results.filter(r => r.severity === 'CRITICAL').length,
          highCount: results.filter(r => r.severity === 'HIGH').length,
          mediumCount: results.filter(r => r.severity === 'MEDIUM').length,
          lowCount: results.filter(r => r.severity === 'LOW').length
        }
      };
    } catch (error) {
      logger.error('安全检测引擎执行失败:', error);
      return {
        passed: false,
        riskLevel: 'CRITICAL',
        details: [{
          rule: 'system_error',
          severity: 'CRITICAL',
          violations: ['安全检测引擎执行失败']
        }],
        engineVersion: SECURITY_ENGINE_VERSION,
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * 应用单个检测规则
   * @param {string} htmlContent - HTML内容
   * @param {Object} rule - 检测规则
   * @param {string} ruleName - 规则名称
   * @returns {Object} 规则检测结果
   */
  static applyRule(htmlContent, rule, ruleName) {
    const violations = [];

    try {
      switch (ruleName) {
        case 'xss':
        case 'maliciousScript':
          violations.push(...this.detectPatterns(htmlContent, rule.patterns));
          break;
        
        case 'dangerousTags':
          violations.push(...this.detectPatterns(htmlContent, rule.patterns));
          break;

        case 'externalResources':
          violations.push(...this.validateExternalResources(htmlContent, rule));
          break;

        case 'suspiciousContent':
          violations.push(...this.detectPatterns(htmlContent, rule.patterns));
          break;

        case 'pathTraversal':
          violations.push(...this.detectPatterns(htmlContent, rule.patterns));
          break;

        default:
          logger.warn(`未知的检测规则: ${ruleName}`, rule);
      }
    } catch (error) {
      logger.error(`规则 ${ruleName} 执行失败:`, error);
      violations.push(`规则执行失败: ${error.message}`);
    }

    return { violations };
  }

  /**
   * 检测模式匹配
   * @param {string} content - 内容
   * @param {Array} patterns - 模式数组
   * @returns {Array} 违规项
   */
  static detectPatterns(content, patterns) {
    const violations = [];
    
    patterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          violations.push(`检测到可疑模式: ${match.substring(0, 100)}...`);
        });
      }
    });

    return violations;
  }

  /**
   * 检测危险标签
   * @param {string} content - HTML内容
   * @param {Array} blockedTags - 禁止的标签列表
   * @returns {Array} 违规项
   */
  static detectDangerousTags(content, blockedTags) {
    const violations = [];
    
    blockedTags.forEach(tag => {
      const pattern = new RegExp(`<${tag}[^>]*>`, 'gi');
      const matches = content.match(pattern);
      if (matches) {
        violations.push(`检测到危险标签: ${tag} (${matches.length}次)`);
      }
    });

    return violations;
  }

  /**
   * 验证外部资源
   * @param {string} content - HTML内容
   * @param {Object} rule - 外部资源规则
   * @returns {Array} 违规项
   */
  static validateExternalResources(content, rule) {
    const violations = [];
    
    rule.patterns.forEach(pattern => {
      const matches = [...content.matchAll(pattern)];
      matches.forEach(match => {
        const domain = match[1];
        if (domain) {
          const domainName = domain.split('/')[0];
          
          // 检查是否在黑名单中
          if (rule.blockedDomains.some(blocked => domainName.includes(blocked))) {
            violations.push(`检测到黑名单域名: ${domainName}`);
          }
          
          // 检查是否在白名单中
          if (!rule.allowedDomains.some(allowed => domainName.includes(allowed))) {
            violations.push(`检测到未授权外部资源: ${domainName}`);
          }
        }
      });
    });

    return violations;
  }

  /**
   * 应用自定义规则
   * @param {string} content - HTML内容
   * @returns {Array} 检测结果
   */
  static applyCustomRules(content) {
    const results = [];
    
    SECURITY_RULES.customRules.forEach((customRule, index) => {
      try {
        if (typeof customRule.detector === 'function') {
          const violations = customRule.detector(content);
          if (violations && violations.length > 0) {
            results.push({
              rule: `custom_${index}`,
              severity: customRule.severity || 'MEDIUM',
              description: customRule.description || '自定义规则',
              violations
            });
          }
        }
      } catch (error) {
        logger.error(`自定义规则 ${index} 执行失败:`, error);
      }
    });

    return results;
  }

  /**
   * 计算风险等级
   * @param {Array} results - 检测结果
   * @returns {string} 风险等级
   */
  static calculateRiskLevel(results) {
    if (results.length === 0) return 'SAFE';
    
    const hasCritical = results.some(r => r.severity === 'CRITICAL');
    const hasHigh = results.some(r => r.severity === 'HIGH');
    const hasMedium = results.some(r => r.severity === 'MEDIUM');
    
    if (hasCritical) return 'CRITICAL';
    if (hasHigh) return 'HIGH';
    if (hasMedium) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * 添加自定义检测规则
   * @param {Object} customRule - 自定义规则
   */
  static addCustomRule(customRule) {
    if (customRule && typeof customRule.detector === 'function') {
      SECURITY_RULES.customRules.push(customRule);
      logger.info('添加自定义安全检测规则:', customRule.description);
    }
  }

  /**
   * 生成安全检测报告
   * @param {Object} result - 检测结果
   * @returns {string} 格式化的报告
   */
  static generateReport(result) {
    if (result.passed) {
      return '✅ 安全检测通过，未发现安全风险';
    }

    let report = `⚠️ 安全检测未通过 (风险等级: ${result.riskLevel})\n\n`;
    
    result.details.forEach((detail, index) => {
      report += `${index + 1}. ${detail.description} (${detail.severity})\n`;
      detail.violations.forEach(violation => {
        report += `   - ${violation}\n`;
      });
      report += '\n';
    });

    return report;
  }
}

export default SecurityDetector;

import axios from 'axios';
// 导入模板服务
import { getBaseTemplate, getStyleTemplates, getCoverSizeConfig, getCurrentSizeType } from './templateService';

// 使用后端代理API而不是直接调用DeepSeek API
// 模型配置
const MAX_TOKENS = 4096;

// 风格模板从 templateService 获取

// 存储当前生成任务的ID
let currentTaskId = null;

// 获取当前任务ID的方法，用于其他模块访问
const getCurrentTaskId = () => currentTaskId;

// 获取最新的风格模板
const getLatestStyleTemplates = async () => {
  const templates = await getStyleTemplates();
  if (Object.keys(templates).length > 0) {
    const example = Object.keys(templates)[0];
  }
  return templates;
};

/**
 * 从后端获取风格模板内容
 * @param {string} styleId - 风格ID
 * @returns {Promise<string|null>} - 风格模板内容或null
 */
const fetchStyleTemplateFromBackend = async (styleId) => {
  try {
    // 获取token，如果有的话添加到请求头中
    const token = localStorage.getItem('token');
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    // 获取所有风格列表
    const response = await axios.get('/api/style', { headers });

    if (response.data.success && Array.isArray(response.data.data)) {
      // 在风格列表中查找匹配的风格
      const matchedStyle = response.data.data.find(style =>
        style.style_name === styleId || style.id_code === styleId || style.id.toString() === styleId
      );

      if (matchedStyle) {
        return matchedStyle.prompt_content;
      }
    }

    return null;
  } catch (error) {
    return null;
  }
};

/**
 * 获取特定风格的模板内容
 * @param {string} styleId - 风格ID
 * @param {boolean} skipCache - 是否跳过缓存（强制刷新）
 * @returns {Promise<string>} - 风格模板内容
 */
const getStyleTemplate = async (styleId, skipCache = false) => {
  // 先尝试从后端获取风格模板
  try {
    // 始终从后端获取风格模板，确保使用最新的模板
    const backendTemplate = await fetchStyleTemplateFromBackend(styleId);
    if (backendTemplate) {
      return backendTemplate;
    }
  } catch (error) {
    // 静默处理错误
  }

  // 如果从后端获取失败，且不跳过缓存，则从本地存储中获取
  if (!skipCache) {
  const styleTemplates = await getLatestStyleTemplates();
  const firstStyleId = Object.keys(styleTemplates)[0];
  
  if (styleTemplates[styleId]) {
    return styleTemplates[styleId];
  } else if (firstStyleId) {
    // 如果指定的风格不存在，但有其他风格可用，使用第一个可用风格
    return styleTemplates[firstStyleId];
    }
  }
  
  // 如果从后端获取失败，且skipCache为true，或者本地缓存中没有可用风格
  // 或者没有任何可用的风格模板，返回一个简单的默认模板
    return `
## 设计风格

- **简约设计**：采用简约设计风格，突出内容
- **清晰布局**：使用清晰、有序的布局
- **适当留白**：保持足够的留白，增强可读性

## 文字排版风格

- **现代字体**：使用现代无衬线字体
- **层次分明**：使用不同字号和粗细创造层次感
`;
};

/**
 * 构建发送给AI的提示词
 * @param {Object} formData - 表单数据
 * @param {string} styleId - 风格ID
 * @param {string} sizeType - 尺寸类型
 * @param {number} customWidthParam - 自定义宽度
 * @param {number} customHeightParam - 自定义高度
 * @param {boolean} skipCache - 是否跳过缓存（强制刷新）
 * @returns {Promise<string>} - 构建好的提示词
 */
const buildPrompt = async (formData, styleId, sizeType, customWidthParam, customHeightParam, skipCache = true) => {
  // 解构表单数据，提取所需信息
  const {
    coverText = '',
    accountName = '',
    subtitle = '',
    autoOptimize = true,
    showCoverText = true,
    showAccountName = false,
    showSubtitle = false,
    customImageUrl = '',
    customImageType = '',
  } = formData;

  // 获取风格模板
  const styleTemplate = await getStyleTemplate(styleId, skipCache);

  // 从templateService获取基础模板，使用指定的尺寸类型
  // 始终跳过缓存，确保使用最新的模板
  const baseTemplate = await getBaseTemplate(sizeType, skipCache);

  // 如果基础模板不存在，抛出错误
  if (!baseTemplate) {
    throw new Error(`基础模板不存在: ${sizeType}`);
  }

  // 组织用户输入内容部分
  let userInputSection = '## 用户输入\n\n';

  // 根据showCoverText判断是否添加封面文案
  if (showCoverText) {
    // 如果文案为空，告知AI生成一个空白封面
    if (!coverText || !coverText.trim()) {
      userInputSection += `用户没有输入任何文本信息，你生成的封面不要包含任何文本内容\n\n`;
    } else if (autoOptimize) {
      userInputSection += `-封面文案：${coverText.trim()}（提炼文案内容作为标题，不超过10个字）\n\n`;
    } else {
      userInputSection += `-封面文案：${coverText.trim()}\n\n`;
    }
  } else {
    userInputSection += `用户没有输入任何文本信息，你生成的封面不要包含任何文本内容\n\n`;
  }

  // 根据账号名称显示设置添加账号信息行
  if (showAccountName && accountName) {
    userInputSection += `-账号信息：${accountName.trim()}\n\n`;
  }

  // 根据副标题显示设置添加副标题行
  if (showSubtitle && subtitle) {
    userInputSection += `-副标题：${subtitle.trim()}\n\n`;
  }

  // 处理自定义图片
  if (customImageUrl) {
    if (customImageType === 'background') {
      userInputSection += `-必须将这个图片作为设计背景图：${customImageUrl}\n\n`;
    } else if (customImageType === 'illustration') {
      userInputSection += `-设计中必须融入这个图片：${customImageUrl}\n\n`;
    }
  }

  // 替换基础模板中的变量
  let prompt = baseTemplate;

  // 检查并替换风格模板占位符
  if (baseTemplate.includes('{styleTemplate}')) {
    prompt = prompt.replace(/{styleTemplate}/g, styleTemplate);
  } else if (baseTemplate.includes('{STYLE_TEMPLATE}')) {
    prompt = prompt.replace(/{STYLE_TEMPLATE}/g, styleTemplate);
  } else {
    // 如果基础模板中没有风格模板占位符，手动插入风格模板
    if (prompt.includes('## 基本要求')) {
      // 在基本要求后面添加风格模板
      const basicReqIndex = prompt.indexOf('## 基本要求');
      const basicReqEndIndex = prompt.indexOf('\n\n', basicReqIndex + 10);
      if (basicReqEndIndex > basicReqIndex) {
        // 在基本要求部分结束后插入风格模板
        prompt = prompt.substring(0, basicReqEndIndex) + '\n\n## 设计风格\n\n' + styleTemplate + prompt.substring(basicReqEndIndex);
      } else {
        // 如果找不到基本要求结束位置，就在基本要求后面直接添加
        prompt = prompt.replace(/## 基本要求/, `## 基本要求\n\n## 设计风格\n\n${styleTemplate}`);
      }
    } else {
      // 如果没有找到基本要求部分，就在开头添加风格模板
      prompt = prompt + '\n\n## 设计风格\n\n' + styleTemplate;
    }
  }

  // 检查是否已经包含用户输入部分
  if (!prompt.includes('## 用户输入')) {
    // 只有在没有用户输入部分时才添加
    prompt = prompt + '\n\n' + userInputSection;
  } else {
    // 如果模板已包含用户输入部分，则替换占位符
    if (prompt.includes('{COVER_TEXT}')) {
      prompt = prompt.replace(/{COVER_TEXT}/g, coverText);
    }
    if (prompt.includes('{ACCOUNT_NAME}')) {
      prompt = prompt.replace(/{ACCOUNT_NAME}/g, showAccountName && accountName ? accountName : '');
    }
    if (prompt.includes('{SUBTITLE}')) {
      prompt = prompt.replace(/{SUBTITLE}/g, showSubtitle && subtitle ? subtitle : '');
    }
    if (prompt.includes('{AUTO_OPTIMIZE}')) {
      prompt = prompt.replace(/{AUTO_OPTIMIZE}/g, autoOptimize ? '是' : '否');
    }
  }

  return prompt;
};

/**
 * 清理和验证HTML内容 - 减少对原始HTML的干预
 * @param {string} htmlCode - 原始HTML代码
 * @param {string} sizeType - 尺寸类型，用于确保空白HTML包含正确的尺寸
 * @returns {string} - 清理后的HTML代码
 */
const cleanAndValidateHTML = (htmlCode, sizeType) => {
  // 如果输入为空，返回一个基础的空白封面HTML
  if (!htmlCode) {
    // 获取尺寸配置，确保空白封面有正确的尺寸
    const sizeConfig = getCoverSizeConfig(sizeType || getCurrentSizeType());
    const width = sizeConfig?.width || 900;
    const height = sizeConfig?.height || 383;

    return `<div class="ai-generated-content">
      <div class="cover-container" style="width: ${width}px; height: ${height}px; display: flex; justify-content: center; align-items: center; background-color: #f8f9fa; border-radius: 8px;">
        <div style="text-align: center; color: #888;">空白封面</div>
      </div>
    </div>`;
  }

  // 提取HTML代码部分，去除可能的Markdown代码块
  let cleanedHtml = htmlCode;
  if (htmlCode.includes('```html')) {
    const matches = htmlCode.match(/```html([\s\S]*?)```/);
    if (matches && matches[1]) {
      cleanedHtml = matches[1].trim();
    }
  } else if (htmlCode.includes('```')) {
    const matches = htmlCode.match(/```([\s\S]*?)```/);
    if (matches && matches[1]) {
      cleanedHtml = matches[1].trim();
    }
  }

  // 检查是否包含有效的HTML标签
  const hasValidHtmlTags = /<[a-z][\s\S]*>/i.test(cleanedHtml);

  // 如果不包含有效的HTML标签，将其包装为有效的HTML
  if (!hasValidHtmlTags) {
    console.warn('未检测到有效的HTML标签，包装为基础结构');
    cleanedHtml = `<div class="cover-container" style="width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; background-color: #f8f9fa; border-radius: 8px;">${cleanedHtml}</div>`;
  }

  // 最小化对原始HTML的修改，仅添加必要的类
  // 如果HTML不包含ai-generated-content类，添加该类
  if (!cleanedHtml.includes('ai-generated-content')) {
    // 查找第一个div标签，并添加ai-generated-content类
    cleanedHtml = cleanedHtml.replace(
      /(<div[^>]*class\s*=\s*["']?)([^"'>]*)(['"]?[^>]*>)/i,
      '$1$2 ai-generated-content$3'
    );

    // 如果没有找到带class的div，查找第一个div标签
    if (!cleanedHtml.includes('ai-generated-content')) {
      cleanedHtml = cleanedHtml.replace(
        /(<div)([^>]*>)/i,
        '$1 class="ai-generated-content"$2'
      );
    }

    // 如果仍然没有找到div标签，包装整个内容
    if (!cleanedHtml.includes('ai-generated-content')) {
      cleanedHtml = `<div class="ai-generated-content">${cleanedHtml}</div>`;
    }
  }

  return cleanedHtml;
};

/**
 * 根据表单数据和风格生成封面HTML代码
 * @param {Object} formData - 表单数据
 * @param {string} styleId - 风格ID
 * @param {string} sizeType - 尺寸类型，默认为当前选择的尺寸类型
 * @returns {Promise<Object>} - 返回原始API响应和处理后的HTML代码
 */
const generateCoverHTML = async (formData, styleId, sizeType) => {
  try {
    // 如果有上一个任务正在进行中，先取消它
    // 这是正常的安全机制，确保不会有多个任务同时运行
    // 当用户快速连续点击生成按钮或切换风格/尺寸时，会触发这个逻辑
    if (currentTaskId) {
      try {
        await cancelCoverGeneration();
      } catch (cancelError) {
        // 静默处理错误
      }
    }

    // 强制清除当前任务ID，确保状态一致
    currentTaskId = null;

    // 保存最近一次的formData到全局，用于可能的文字修复
    window.lastFormData = formData;

    // 注意：移除对空白文案的强制检查，与buildPrompt函数保持一致
    // 允许生成空白封面，buildPrompt函数会处理空白文案的情况

    // 构建提示词（现在是异步的）
    // 强制跳过缓存，确保获取最新的模板
    const prompt = await buildPrompt(formData, styleId, sizeType, formData.customWidth, formData.customHeight, true);

    // 获取token，如果有的话添加到请求头中
    const token = localStorage.getItem('token');
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    // 使用当前时间戳作为任务ID的一部分
    const timestamp = new Date().getTime();
    const randomStr = Math.random().toString(36).substring(2, 10);
    const newTaskId = `cover_${timestamp}_${randomStr}`;

    // 设置全局任务ID，以便取消功能可以使用
    currentTaskId = newTaskId;

    // 从localStorage获取用户ID（如果有的话）
    let userId = null;
    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo'));
      userId = userInfo?.id;
    } catch (e) {
      // 解析错误，忽略
    }

    // 调用后端API生成封面HTML
    const response = await axios.post('/api/cover/generate-html', {
      prompt,
      taskId: newTaskId,
      userId,
      formData: {
        ...formData,
        styleId, // 确保styleId被添加到formData中
        sizeType // 确保sizeType被添加到formData中
      } // 发送表单数据，便于后端记录
    }, { headers });

    if (response.data.success) {
      // 取得后端返回的任务ID（可能已经被修改）
      if (response.data.data.taskId) {
        currentTaskId = response.data.data.taskId;
      }

      // 将sizeType传递给cleanAndValidateHTML函数，确保空白封面有正确尺寸
      const processedHtml = response.data.data.html || cleanAndValidateHTML(response.data.data.rawResponse, sizeType);

      return {
        rawHtml: response.data.data.rawResponse,
        processedHtml,
        taskId: currentTaskId,
        styleId,  // 保存风格ID，便于恢复
        sizeType,  // 保存尺寸类型，便于恢复
        coverCode: response.data.data.cover_code // 保存cover_code，用于分享功能
      };
    } else {
      // 生成失败，清除任务ID
      currentTaskId = null;
      throw new Error(response.data.message || '生成失败');
    }
  } catch (error) {
    // 生成失败，清除任务ID
    currentTaskId = null;

    // 检查是否是网络错误或其他服务器错误
    if (error.response && error.response.status >= 500) {
      const serverError = new Error('服务器暂时无法处理您的请求，请稍后再试');
      serverError.styleId = styleId;  // 保存风格ID
      serverError.sizeType = sizeType; // 保存尺寸类型
      throw serverError;
    }

    throw error;
  }
};

/**
 * 取消当前正在进行的封面生成任务
 * @returns {Promise<boolean>} - 是否成功发送取消请求
 */
const cancelCoverGeneration = async () => {
  // 如果没有当前任务ID，不需要取消
  if (!currentTaskId) {
    return false;
  }

  try {
    const token = localStorage.getItem('token');
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    // 保存当前任务ID，以便清除后仍能使用
    const taskIdToCancel = currentTaskId;

    // 先清除当前任务ID，避免重复取消
    currentTaskId = null;

    const response = await axios.post('/api/cover/cancel', {
      taskId: taskIdToCancel
    }, {
      headers,
      // 设置较短的超时时间，避免取消请求阻塞太久
      timeout: 5000
    });

    if (response.data.success) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
};

/**
 * 使用sendBeacon发送取消请求
 * 适用于页面卸载时确保请求能发送
 * @returns {boolean} - 是否成功发起请求
 */
const cancelCoverGenerationBeacon = () => {
  const taskId = currentTaskId;
  if (!taskId) {
    return false;
  }

  // 立即清除任务ID，避免重复取消
  currentTaskId = null;

  try {
    // 保存任务ID到localStorage，以便在页面重新加载时检查
    try {
      // 获取当前未完成的任务列表
      const unfinishedTasksStr = localStorage.getItem('fengmian_unfinished_tasks') || '[]';
      let unfinishedTasks = JSON.parse(unfinishedTasksStr);

      // 确保是数组
      if (!Array.isArray(unfinishedTasks)) {
        unfinishedTasks = [];
      }

      // 添加当前任务，避免重复
      if (!unfinishedTasks.includes(taskId)) {
        unfinishedTasks.push(taskId);
        localStorage.setItem('fengmian_unfinished_tasks', JSON.stringify(unfinishedTasks));
      }
    } catch (storageError) {
      // 静默处理错误
    }

    // 检查sendBeacon是否可用
    if (navigator.sendBeacon) {
      // 组装请求数据，包括token（如果有）
      let requestData = { taskId };

      // 尝试添加认证信息
      const token = localStorage.getItem('token');
      if (token) {
        requestData.token = token;
      }

      const data = JSON.stringify(requestData);
      const blob = new Blob([data], { type: 'application/json' });
      const success = navigator.sendBeacon('/api/cover/cancel', blob);

      if (success) {
        return true;
      } else {
        // 静默处理错误
      }
    }

    // 回退方案：使用同步XHR（更可靠但可能阻塞）
    try {
      const xhr = new XMLHttpRequest();
      xhr.open('POST', '/api/cover/cancel', false); // 同步请求
      xhr.setRequestHeader('Content-Type', 'application/json');

      // 添加认证头（如果有）
      const token = localStorage.getItem('token');
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      }

      xhr.send(JSON.stringify({ taskId }));
      return true;
    } catch (xhrError) {
      // 最后尝试使用fetch API的keepalive选项
      try {
        fetch('/api/cover/cancel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
          },
          body: JSON.stringify({ taskId }),
          keepalive: true
        });
        return true;
      } catch (fetchError) {
        return false;
      }
    }
  } catch (error) {
    return false;
  }
};

/**
 * 调用AI API生成HTML封面代码（通过后端代理）
 * @param {string} prompt - 构建好的提示词
 * @returns {Promise<string>} - 返回生成的HTML代码
 */
const callDeepSeekAPI = async (prompt) => {
  try {
    // 尝试调用后端API
    const token = localStorage.getItem('token');
    const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

    const response = await axios.post('/api/cover/generate-html', {
      prompt,
      max_tokens: MAX_TOKENS,
      temperature: 0.7
    }, { headers });

    if (response.data.success) {
      const result = response.data.data.rawResponse || response.data.data.html;
      return result;
    } else {
      throw new Error(response.data.message || '生成失败');
    }
  } catch (error) {
    throw new Error('生成封面失败，请稍后重试');
  }
};

/**
 * 直接使用提示词生成封面HTML代码
 * @param {string} prompt - 完整的提示词
 * @returns {Promise<Object>} - 返回原始API响应和处理后的HTML代码
 */
const generateFromPrompt = async (prompt) => {
  try {
    // 检查提示词是否存在
    if (!prompt || !prompt.trim()) {
      throw new Error('提示词不能为空');
    }

    try {
      // 尝试调用后端API
      const token = localStorage.getItem('token');
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

      const response = await axios.post('/api/cover/generate-html', {
        prompt
      }, { headers });

      if (response.data.success) {
        return {
          rawHtml: response.data.data.rawResponse,
          processedHtml: response.data.data.html
        };
      } else {
        throw new Error(response.data.message || '生成失败');
      }
    } catch (backendError) {
      // 如果后端API调用失败，回退到直接调用
      // 调用API生成HTML
      const rawHtml = await callDeepSeekAPI(prompt);

      // 清理和验证HTML内容
      const processedHtml = cleanAndValidateHTML(rawHtml);

      // 返回原始API响应和处理后的HTML
      return {
        rawHtml,      // 完全未处理的原始API响应
        processedHtml // 处理后的HTML
      };
    }
  } catch (error) {
    throw error;
  }
};

/**
 * 从原始API响应中提取HTML代码部分
 * @param {string|Object} rawResponse - 原始API响应，可能是字符串或对象
 * @returns {Object} - 包含htmlCode属性的对象
 */
const extractHTMLFromResponse = (rawResponse) => {
  if (!rawResponse) return { htmlCode: '' };

  // 如果rawResponse是对象，尝试提取字符串内容
  let responseText = '';
  if (typeof rawResponse === 'object') {
    // 尝试从对象中提取文本内容
    if (rawResponse.choices && rawResponse.choices.length > 0) {
      if (rawResponse.choices[0].message && rawResponse.choices[0].message.content) {
        responseText = rawResponse.choices[0].message.content;
      } else if (rawResponse.choices[0].text) {
        responseText = rawResponse.choices[0].text;
      }
    } else if (rawResponse.rawHtml) {
      responseText = rawResponse.rawHtml;
    } else if (rawResponse.processedHtml) {
      responseText = rawResponse.processedHtml;
    } else if (rawResponse.html) {
      responseText = rawResponse.html;
    } else {
      // 如果无法提取，转换为字符串
      try {
        responseText = JSON.stringify(rawResponse);
      } catch (e) {
        responseText = '';
      }
    }
  } else {
    // 如果已经是字符串，直接使用
    responseText = rawResponse;
  }

  // 尝试提取HTML代码块
  let htmlCode = '';

  // 尝试提取```html```代码块
  if (responseText.includes && responseText.includes('```html')) {
    const matches = responseText.match(/```html([\s\S]*?)```/);
    if (matches && matches[1]) {
      htmlCode = matches[1].trim();
      return { htmlCode };
    }
  }

  // 尝试提取```代码块
  if (responseText.includes && responseText.includes('```')) {
    const matches = responseText.match(/```([\s\S]*?)```/);
    if (matches && matches[1]) {
      htmlCode = matches[1].trim();
      return { htmlCode };
    }
  }

  // 尝试提取<!DOCTYPE html>开头的HTML代码
  if ((responseText.includes && responseText.includes('<!DOCTYPE html>')) ||
      (responseText.includes && responseText.includes('<html'))) {
    const matches = responseText.match(/(<\!DOCTYPE html>|<html)[\s\S]*/);
    if (matches && matches[0]) {
      htmlCode = matches[0].trim();
      return { htmlCode };
    }
  }

  // 尝试提取<div开头的HTML代码
  if (responseText.includes && responseText.includes('<div')) {
    const matches = responseText.match(/<div[\s\S]*/);
    if (matches && matches[0]) {
      htmlCode = matches[0].trim();
      return { htmlCode };
    }
  }

  // 如果没有找到HTML代码，返回原始响应
  return { htmlCode: responseText };
};

/**
 * 检查并取消未完成的任务
 * 在页面加载时调用，检查localStorage中是否有未完成的任务需要取消
 * @returns {Promise<number>} - 取消的任务数量
 */
const checkAndCancelUnfinishedTasks = async () => {
  try {
    // 获取未完成的任务列表
    const unfinishedTasksStr = localStorage.getItem('fengmian_unfinished_tasks') || '[]';
    let unfinishedTasks = [];

    try {
      unfinishedTasks = JSON.parse(unfinishedTasksStr);
      if (!Array.isArray(unfinishedTasks)) {
        unfinishedTasks = [];
      }
    } catch (parseError) {
      unfinishedTasks = [];
    }

    // 获取已取消的任务列表
    const canceledTasksStr = localStorage.getItem('fengmian_canceled_tasks') || '[]';
    let canceledTasks = [];

    try {
      canceledTasks = JSON.parse(canceledTasksStr);
      if (!Array.isArray(canceledTasks)) {
        canceledTasks = [];
      }
    } catch (parseError) {
      canceledTasks = [];
    }

    // 过滤出未取消的任务
    const tasksToCancel = unfinishedTasks.filter(taskId => !canceledTasks.includes(taskId));

    if (tasksToCancel.length === 0) {
      return 0;
    }

    // 取消每个未完成的任务
    let canceledCount = 0;
    for (const taskId of tasksToCancel) {
      try {
        // 获取token，如果有的话添加到请求头中
        const token = localStorage.getItem('token');
        const headers = token ? { 'Authorization': `Bearer ${token}` } : {};

        // 发送取消请求
        const response = await fetch('/api/cover/cancel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers
          },
          body: JSON.stringify({ taskId })
        });

        if (response.ok) {
          canceledCount++;

          // 将任务添加到已取消列表
          if (!canceledTasks.includes(taskId)) {
            canceledTasks.push(taskId);
          }
        }
      } catch (error) {
        // 静默处理错误
      }
    }

    // 更新已取消任务列表
    localStorage.setItem('fengmian_canceled_tasks', JSON.stringify(canceledTasks));

    // 清空未完成任务列表
    localStorage.setItem('fengmian_unfinished_tasks', '[]');

    return canceledCount;
  } catch (error) {
    return 0;
  }
};

// 在页面加载时自动检查并取消未完成的任务
if (typeof window !== 'undefined') {
  // 使用setTimeout确保在页面完全加载后执行
  setTimeout(() => {
    checkAndCancelUnfinishedTasks().catch(() => {});
  }, 2000);
}

// 导出API
export {
  generateCoverHTML,
  generateFromPrompt,
  buildPrompt,
  getLatestStyleTemplates,
  extractHTMLFromResponse,
  cancelCoverGeneration,
  cancelCoverGenerationBeacon,
  getCurrentTaskId,
  checkAndCancelUnfinishedTasks,
  getStyleTemplate
};

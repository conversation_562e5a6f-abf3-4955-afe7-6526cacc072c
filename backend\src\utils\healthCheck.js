/**
 * API健康检查工具
 * 用于监控API状态并报告问题
 */
const { sequelize } = require('../models');
const logger = require('./logger');

/**
 * 执行系统健康检查
 * @returns {Promise<Object>} 健康状态信息
 */
const checkHealth = async () => {
  try {
    // 检查数据库连接
    let dbStatus = 'UNKNOWN';
    try {
      await sequelize.authenticate();
      dbStatus = 'UP';
    } catch (err) {
      dbStatus = 'DOWN';
      logger.error(`数据库连接检查失败: ${err.message}`);
    }

    // 构建健康报告
    const healthReport = {
      status: dbStatus === 'UP' ? 'UP' : 'DOWN',
      timestamp: new Date().toISOString(),
      components: {
        database: {
          status: dbStatus
        },
        server: {
          status: 'UP'
        }
      },
      details: {
        version: process.env.npm_package_version || '未知'
      }
    };

    // 记录健康状态（仅当出现问题时）
    if (healthReport.status !== 'UP') {
      logger.warn(`系统健康检查报告问题: ${JSON.stringify(healthReport)}`);
    }

    return healthReport;
  } catch (error) {
    logger.error(`执行健康检查时出错: ${error.message}`);
    return {
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
};

/**
 * 启动定期健康检查
 * @param {number} interval 检查间隔，单位毫秒
 * @returns {Object} 定时器对象
 */
const startPeriodicHealthCheck = (interval = 60000) => {
  if (!process.env.HEALTH_CHECK_ENABLED || process.env.HEALTH_CHECK_ENABLED.toLowerCase() !== 'true') {
    logger.info('定期健康检查已禁用');
    return null;
  }

  logger.info(`启动定期健康检查，间隔 ${interval}ms`);
  const timer = setInterval(async () => {
    try {
      const report = await checkHealth();
      if (report.status !== 'UP') {
        logger.warn(`健康检查失败: ${JSON.stringify(report)}`);
      }
    } catch (error) {
      logger.error(`执行定期健康检查时出错: ${error.message}`);
    }
  }, interval);

  return timer;
};

module.exports = {
  checkHealth,
  startPeriodicHealthCheck
}; 
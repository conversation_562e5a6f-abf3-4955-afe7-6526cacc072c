import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff, ArrowLeft, KeyRound, Check } from 'lucide-react';
import { message } from 'antd';
import { maskPhoneNumber } from "@/lib/utils";

/**
 * 密码设置对话框组件
 * 
 * 用于设置或修改用户密码
 * 
 * @param {Object} props
 * @param {boolean} props.open - 对话框是否打开
 * @param {Function} props.onOpenChange - 控制对话框打开状态的函数
 * @param {boolean} props.hasPassword - 用户是否已设置密码
 * @param {string} props.currentUserPhone - 用户手机号码
 */
const PasswordDialog = ({ open, onOpenChange, hasPassword, currentUserPhone }) => {
  // 基本状态
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // 密码显示状态
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // 密码输入状态
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // 忘记密码相关状态
  const [forgotPasswordMode, setForgotPasswordMode] = useState(false);
  const [verifyStep, setVerifyStep] = useState(1); // 1: 输入手机号和验证码, 2: 设置新密码
  const [phone, setPhone] = useState('');
  const [displayPhone, setDisplayPhone] = useState(''); // 用于显示脱敏手机号
  const [verifyCode, setVerifyCode] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [sendingCode, setSendingCode] = useState(false);

  // 初始化时，如果有当前用户手机号，则自动填充
  useEffect(() => {
    if (currentUserPhone && open) {
      setPhone(currentUserPhone);
      setDisplayPhone(maskPhoneNumber(currentUserPhone));
    }
  }, [currentUserPhone, open]);

  // 重置所有状态
  const resetAllStates = () => {
    setOldPassword('');
    setNewPassword('');
    setConfirmPassword('');
    setForgotPasswordMode(false);
    setVerifyStep(1);
    setPhone('');
    setDisplayPhone('');
    setVerifyCode('');
    setError('');
  };

  // 处理对话框关闭
  const handleDialogClose = (isOpen) => {
    if (!isOpen) {
      // 延迟重置状态，确保动画完成后再重置
      setTimeout(() => {
        resetAllStates();
      }, 300);
    }
    onOpenChange(isOpen);
  };

  // 处理发送验证码
  const handleSendVerifyCode = async () => {
    if (!phone) {
      setError('请输入手机号');
      return;
    }

    if (!/^1\d{10}$/.test(phone)) {
      setError('请输入有效的手机号');
      return;
    }

    setSendingCode(true);
    setError('');

    try {
      const response = await axios.post('/api/sms/send', {
        phone,
        purpose: 'reset_password'
      });

      if (response.data.success) {
        message.success('验证码已发送，请查收短信');
        setDisplayPhone(maskPhoneNumber(phone));
        // 设置倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        setError(response.data.message || '发送验证码失败');
      }
    } catch (error) {
      console.error('发送验证码失败:', error);
      if (error.response && error.response.data) {
        setError(error.response.data.message || '发送验证码失败');
      } else {
        setError('发送验证码失败，请稍后再试');
      }
    } finally {
      setSendingCode(false);
    }
  };

  // 处理验证码验证
  const handleVerifyCode = async () => {
    if (!verifyCode) {
      setError('请输入验证码');
      return;
    }

    if (!/^\d{6}$/.test(verifyCode)) {
      setError('请输入6位数字验证码');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await axios.post('/api/sms/verify', {
        phone,
        verifyCode,
        purpose: 'reset_password'
      });

      if (response.data.success) {
        setVerifyStep(2);
      } else {
        setError(response.data.message || '验证码验证失败');
      }
    } catch (error) {
      console.error('验证码验证失败:', error);
      if (error.response && error.response.data) {
        setError(error.response.data.message || '验证码验证失败');
      } else {
        setError('验证码验证失败，请稍后再试');
      }
    } finally {
      setLoading(false);
    }
  };

  // 通过验证码重置密码
  const handleResetPasswordByCode = async () => {
    // 验证密码格式
    const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/;
    if (!passwordRegex.test(newPassword)) {
      setError('密码必须包含字母和数字，长度必须在8-32个字符之间');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await axios.post('/api/auth/reset-password', {
        phone,
        verifyCode,
        newPassword
      });

      if (response.data.success) {
        message.success('密码重置成功，请重新登录');
        // 清除登录信息并跳转到登录页面
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        handleDialogClose(false);
        // 使用延时确保对话框关闭动画完成后再跳转
        setTimeout(() => {
          window.location.href = '/auth';
        }, 500);
      } else {
        setError(response.data.message || '密码重置失败');
      }
    } catch (error) {
      console.error('重置密码失败:', error);
      if (error.response && error.response.data) {
        setError(error.response.data.message || '重置密码失败');
      } else {
        setError('重置密码失败，请稍后再试');
      }
    } finally {
      setLoading(false);
    }
  };

  // 常规密码设置/修改
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    // 验证密码格式
    const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{8,32}$/;
    if (!passwordRegex.test(newPassword)) {
      setError('密码必须包含字母和数字，长度必须在8-32个字符之间');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('两次输入的密码不一致');
      return;
    }

    // 如果已设置密码，需要验证旧密码
    if (hasPassword && !oldPassword) {
      setError('请输入当前密码');
      return;
    }

    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        message.error("您的登录已过期，请重新登录");
        return;
      }

      const response = await axios.post('/api/user/set-password', {
        password: newPassword,
        oldPassword: hasPassword ? oldPassword : undefined
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        message.success(hasPassword ? "您的密码已成功修改，请重新登录" : "您的密码已成功设置，请重新登录");
        // 清除登录信息并跳转到登录页面
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        handleDialogClose(false);
        // 使用延时确保对话框关闭动画完成后再跳转
        setTimeout(() => {
          window.location.href = '/auth';
        }, 500);
      } else {
        setError(response.data.message || '密码设置失败');
      }
    } catch (error) {
      console.error('密码设置失败:', error);
      if (error.response && error.response.data) {
        setError(error.response.data.message || '密码设置失败');
      } else {
        setError('密码设置失败，请稍后再试');
      }
    } finally {
      setLoading(false);
    }
  };

  // 渲染常规密码设置/修改表单
  const renderPasswordForm = () => (
    <>
      <DialogHeader>
        <DialogTitle className="flex items-center">
          <KeyRound className="mr-2 h-5 w-5 text-primary" />
          {hasPassword ? '修改密码' : '设置密码'}
        </DialogTitle>
        <DialogDescription>
          {hasPassword
            ? '请输入您的当前密码和新密码'
            : '设置密码后，您可以使用手机号和密码登录'}
        </DialogDescription>
      </DialogHeader>

      <form onSubmit={handleSubmit} className="space-y-4 py-4">
        {hasPassword && (
          <div className="space-y-2">
            <Label htmlFor="oldPassword">当前密码</Label>
            <div className="relative">
              <Input
                id="oldPassword"
                type={showOldPassword ? "text" : "password"}
                value={oldPassword}
                onChange={(e) => setOldPassword(e.target.value)}
                className="pr-10"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 flex items-center px-3"
                onClick={() => setShowOldPassword(!showOldPassword)}
              >
                {showOldPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="newPassword">新密码</Label>
          <div className="relative">
            <Input
              id="newPassword"
              type={showNewPassword ? "text" : "password"}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="pr-10"
              placeholder="密码必须包含字母和数字，长度必须在8-32个字符之间"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center px-3"
              onClick={() => setShowNewPassword(!showNewPassword)}
            >
              {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">确认密码</Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="pr-10"
              placeholder="请再次输入密码"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center px-3"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
        </div>

        {error && (
          <div className="text-sm text-destructive mt-2">{error}</div>
        )}

        {hasPassword && (
          <div className="text-right text-sm">
            <button
              type="button"
              className="text-primary hover:underline"
              onClick={() => {
                setForgotPasswordMode(true);
                setError('');
                // 自动填充当前用户手机号
                if (currentUserPhone) {
                  setPhone(currentUserPhone);
                  setDisplayPhone(maskPhoneNumber(currentUserPhone));
                }
              }}
            >
              忘记密码?
            </button>
          </div>
        )}

        <DialogFooter className="mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => handleDialogClose(false)}
            disabled={loading}
          >
            取消
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? '处理中...' : (hasPassword ? '修改密码' : '设置密码')}
          </Button>
        </DialogFooter>
      </form>
    </>
  );

  // 渲染验证码验证表单
  const renderVerifyCodeForm = () => (
    <>
      <DialogHeader>
        <div className="flex items-center">
          <button
            type="button"
            className="mr-2 p-1 rounded-full hover:bg-accent"
            onClick={() => {
              setForgotPasswordMode(false);
              setError('');
            }}
          >
            <ArrowLeft size={16} />
          </button>
          <DialogTitle>忘记密码</DialogTitle>
        </div>
        <DialogDescription>
          请输入您的手机号和验证码
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4 py-4">
        <div className="space-y-2">
          <Label htmlFor="phone">手机号</Label>
          <Input
            id="phone"
            type="tel"
            value={currentUserPhone && currentUserPhone === phone ? displayPhone : phone}
            onChange={(e) => {
              setPhone(e.target.value);
              setDisplayPhone(e.target.value ? maskPhoneNumber(e.target.value) : '');
            }}
            placeholder="请输入手机号"
            disabled={currentUserPhone && currentUserPhone === phone}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="verifyCode">验证码</Label>
          <div className="flex space-x-2">
            <Input
              id="verifyCode"
              value={verifyCode}
              onChange={(e) => setVerifyCode(e.target.value)}
              placeholder="请输入验证码"
              className="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleSendVerifyCode}
              disabled={countdown > 0 || sendingCode}
              className="whitespace-nowrap min-w-[120px]"
            >
              {countdown > 0 ? `${countdown}秒后重发` : (sendingCode ? '发送中...' : '获取验证码')}
            </Button>
          </div>
        </div>

        {error && (
          <div className="text-sm text-destructive mt-2">{error}</div>
        )}

        <DialogFooter className="mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => handleDialogClose(false)}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="button"
            onClick={handleVerifyCode}
            disabled={loading}
          >
            {loading ? '处理中...' : '下一步'}
          </Button>
        </DialogFooter>
      </div>
    </>
  );

  // 渲染设置新密码表单
  const renderSetNewPasswordForm = () => (
    <>
      <DialogHeader>
        <div className="flex items-center">
          <button
            type="button"
            className="mr-2 p-1 rounded-full hover:bg-accent"
            onClick={() => {
              setVerifyStep(1);
              setError('');
            }}
          >
            <ArrowLeft size={16} />
          </button>
          <DialogTitle>设置新密码</DialogTitle>
        </div>
        <DialogDescription>
          请设置您的新密码
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4 py-4">
        <div className="space-y-2">
          <Label htmlFor="newPassword">新密码</Label>
          <div className="relative">
            <Input
              id="newPassword"
              type={showNewPassword ? "text" : "password"}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="pr-10"
              placeholder="密码必须包含字母和数字，长度必须在8-32个字符之间"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center px-3"
              onClick={() => setShowNewPassword(!showNewPassword)}
            >
              {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword">确认密码</Label>
          <div className="relative">
            <Input
              id="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="pr-10"
              placeholder="请再次输入密码"
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 flex items-center px-3"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
        </div>

        {error && (
          <div className="text-sm text-destructive mt-2">{error}</div>
        )}

        <DialogFooter className="mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => handleDialogClose(false)}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            type="button"
            onClick={handleResetPasswordByCode}
            disabled={loading}
          >
            {loading ? '处理中...' : '确认修改'}
          </Button>
        </DialogFooter>
      </div>
    </>
  );

  // 根据当前状态渲染不同的表单
  const renderContent = () => {
    if (forgotPasswordMode) {
      return verifyStep === 1 ? renderVerifyCodeForm() : renderSetNewPasswordForm();
    }
    return renderPasswordForm();
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[425px]">
        {renderContent()}
      </DialogContent>
    </Dialog>
  );
};

export default PasswordDialog;
/**
 * 简化的高级模式开关组件
 * 第四阶段：高级模式改造与服务端渲染集成
 * 
 * 替代复杂的PreviewModeSwitch，提供简洁的高级模式开关
 */

import React, { useState, useEffect } from 'react';
import { Crown, Zap, Shield } from 'lucide-react';
import { advancedModeController } from '../../utils/advancedModeIntegration.js';
import logger from '../../services/logs/frontendLogger.js';

/**
 * 高级模式开关组件
 */
const AdvancedModeSwitch = ({ className = '' }) => {
  const [isAdvancedMode, setIsAdvancedMode] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // 初始化高级模式控制器
  useEffect(() => {
    const initializeController = async () => {
      try {
        const success = await advancedModeController.initialize();
        if (success) {
          setIsAdvancedMode(advancedModeController.isAdvancedModeEnabled());
          setIsInitialized(true);
          
          // 添加监听器
          advancedModeController.addListener(handleModeChange);
        }
      } catch (error) {
        logger.error('高级模式控制器初始化失败', { error: error.message });
        setIsInitialized(true); // 即使失败也标记为已初始化，避免无限重试
      }
    };

    initializeController();

    // 清理函数
    return () => {
      if (isInitialized) {
        advancedModeController.removeListener(handleModeChange);
      }
    };
  }, []);

  /**
   * 处理模式变化事件
   */
  const handleModeChange = (event, data) => {
    if (event === 'modeChanged') {
      setIsAdvancedMode(data.newState);
      logger.info('高级模式状态更新', { newState: data.newState });
    }
  };

  /**
   * 切换高级模式
   */
  const handleToggle = () => {
    if (!isInitialized) {
      logger.warn('高级模式控制器未初始化');
      return;
    }

    const success = advancedModeController.toggleAdvancedMode();
    if (!success) {
      logger.error('高级模式切换失败');
    }
  };

  // 如果未初始化，显示加载状态
  if (!isInitialized) {
    return (
      <div className={`flex items-center ${className}`}>
        <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium bg-gray-100 text-gray-500">
          <div className="w-4 h-4 border-2 border-gray-300 border-t-transparent rounded-full animate-spin"></div>
          <span>初始化中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center ${className}`}>
      <button
        onClick={handleToggle}
        className={`
          inline-flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium
          transition-all duration-200 border relative
          ${isAdvancedMode
            ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white border-purple-500 hover:from-purple-600 hover:to-purple-700'
            : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
          }
          cursor-pointer
        `}
        title={`当前：${isAdvancedMode ? '高级模式' : '标准模式'}，点击切换`}
      >
        {isAdvancedMode ? (
          <>
            <Zap className="h-4 w-4" />
            <span>高级模式</span>
            <Crown className="h-3 w-3 text-yellow-300" />
          </>
        ) : (
          <>
            <Shield className="h-4 w-4" />
            <span>标准模式</span>
          </>
        )}
      </button>
    </div>
  );
};

export default AdvancedModeSwitch;

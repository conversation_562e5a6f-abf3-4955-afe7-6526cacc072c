import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { useNavigate, useSearchParams } from 'react-router-dom';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { message } from 'antd';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { 
  Edit, Save, X, UserCircle2, Mail, Smartphone, CircleDollarSign, 
  Award, RefreshCw, Camera, Eye, History, Trash2, LinkIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useUserProfile } from '../../contexts/UserContext';

// 自定义工具函数：手机号码脱敏显示
const maskPhoneNumber = (phone) => {
  if (!phone) return '';
  return phone.toString().replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// 加载中的spinner组件
const LoadingSpinner = ({ className }) => (
  <div className={cn("animate-spin rounded-full h-8 w-8 border-b-2 border-primary", className)}></div>
);

const ProfileContent = ({ contentType = 'profile', onClose }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const initialPage = parseInt(searchParams.get('page') || '1', 10);
  const navigate = useNavigate();

  // 使用UserContext获取用户资料
  const { userProfile, loading: userLoading, fetchUserProfile, updateUserProfile } = useUserProfile();
  
  // 移除原有的加载状态和userProfile状态，使用Context提供的状态
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const [editNickname, setEditNickname] = useState('');
  const [editEmail, setEditEmail] = useState('');

  // 积分记录状态
  const [pointRecords, setPointRecords] = useState([]);
  const [pointsLoading, setPointsLoading] = useState(false);
  const [pointsCurrentPage, setPointsCurrentPage] = useState(initialPage);
  const [pointsTotalPages, setPointsTotalPages] = useState(0);

  // 封面记录状态
  const [covers, setCovers] = useState([]);
  const [coversLoading, setCoversLoading] = useState(false);
  const [coversCurrentPage, setCoversCurrentPage] = useState(initialPage);
  const [coversTotalPages, setCoversTotalPages] = useState(0);

  // 预览状态
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [currentCover, setCurrentCover] = useState(null);
  const [previewHtml, setPreviewHtml] = useState('');
  const [currentPreviewRecordId, setCurrentPreviewRecordId] = useState(null);
  const [shareLinkDialogOpen, setShareLinkDialogOpen] = useState(false);
  const [shareUrl, setShareUrl] = useState('');

  // 删除封面状态
  const [deletingCoverId, setDeletingCoverId] = useState(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // 操作类型映射
  const operationTypeMap = {
    'register': '注册奖励',
    'daily_reward': '每日奖励',
    'generate': '生成封面',
    'admin_adjust': '管理员调整',
    'vip_upgrade': 'VIP升级',
    'daily_points_normal': '平台赠送',
    'daily_points_advanced': '会员赠送'
  };

  // 描述文本映射
  const descriptionMap = {
    '每日积分（普通）重置': '平台赠送积分',
    '每日积分（普通）手动更新': '平台赠送积分',
    '每日积分（高级）重置': '每日会员赠送',
    '每日积分（高级）手动更新': '每日会员赠送'
  };

  // 根据contentType加载对应数据
  useEffect(() => {
    if (contentType === 'profile') {
      // 不需要再调用fetchUserProfile，因为UserProvider已经在挂载时获取了用户资料
      // 如果需要强制刷新，可以调用fetchUserProfile(true)
    } else if (contentType === 'points') {
      fetchPointRecords();
    } else if (contentType === 'covers') {
      fetchCoverRecords();
    }
  }, [contentType]);

  // 获取积分记录
  const fetchPointRecords = async (page = 1, limit = 10) => {
    try {
      setPointsLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { return; }
      const response = await axios.get(`/api/user/point-records?page=${page}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      if (response.data.success) {
        setPointRecords(response.data.data);
        const total = response.data.pagination.total;
        const calculatedTotalPages = Math.ceil(total / limit);
        setPointsTotalPages(response.data.pagination.totalPages || calculatedTotalPages);
        setPointsCurrentPage(page);
      } else {
        setPointRecords([]);
        setPointsTotalPages(0);
        setPointsCurrentPage(1);
      }
    } catch (error) {
      console.error('获取积分记录失败:', error);
      setPointRecords([]);
      setPointsTotalPages(0);
      setPointsCurrentPage(1);
    } finally { 
      setPointsLoading(false);
      fetchUserProfile(); // 同时更新用户资料以获取最新积分
    }
  };

  // 获取封面记录
  const fetchCoverRecords = async (page = 1, limit = 10) => {
    try {
      setCoversLoading(true);
      const token = localStorage.getItem('token');
      if (!token) { return; }
      
      const response = await axios.get(`/api/user/covers?page=${page}&limit=${limit}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        setCovers(response.data.data);
        const total = response.data.pagination.total;
        const calculatedTotalPages = Math.ceil(total / limit);
        setCoversTotalPages(response.data.pagination.totalPages || calculatedTotalPages);
        setCoversCurrentPage(page);
      } else {
        setCovers([]);
        setCoversTotalPages(0);
        setCoversCurrentPage(1);
      }
    } catch (error) {
      console.error('获取封面记录失败:', error);
      setCovers([]);
      setCoversTotalPages(0);
      setCoversCurrentPage(1);
    } finally { 
      setCoversLoading(false); 
    }
  };

  // 更新用户资料
  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const values = { nickname: editNickname, email: editEmail };

      if (!values.nickname) {
        message.error('昵称不能为空');
        setLoading(false);
        return;
      }

      // 使用Context的updateUserProfile方法
      const success = await updateUserProfile(values);
      
      if (success) {
        setEditing(false);
        message.success('个人资料更新成功');
      } else {
        message.error('更新个人资料失败');
      }
    } catch (error) {
      console.error('更新个人资料失败:', error);
      message.error('更新个人资料失败，请检查网络或联系管理员');
    } finally {
      setLoading(false);
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditing(false);
    setEditNickname(userProfile?.nickname || '');
    setEditEmail(userProfile?.email || '');
  };

  // 删除封面
  const handleDeleteCover = async (coverId) => {
    try {
      setDeleteLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.delete(`/api/cover/${coverId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        message.success('封面已成功删除');
        fetchCoverRecords(coversCurrentPage);
      } else {
        message.error(response.data.message || '删除封面失败');
      }
    } catch (error) {
      console.error('删除封面失败:', error);
      message.error('删除封面失败，请稍后重试');
    } finally {
      setDeleteLoading(false);
      setIsDeleteConfirmOpen(false);
    }
  };

  // 打开删除确认框
  const openDeleteConfirm = (coverId) => {
    setDeletingCoverId(coverId);
    setIsDeleteConfirmOpen(true);
  };

  // 处理分页变化
  const handlePageChange = (newPage, type) => {
    if (type === 'points') {
      setPointsCurrentPage(newPage);
      fetchPointRecords(newPage);
    } else if (type === 'covers') {
      setCoversCurrentPage(newPage);
      fetchCoverRecords(newPage);
    }
  };

  // 生成分页号
  const generatePageNumbers = (currentPage, totalPages, pageNeighbours = 1) => {
    if (totalPages <= 5) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const startPage = Math.max(2, currentPage - pageNeighbours);
    const endPage = Math.min(totalPages - 1, currentPage + pageNeighbours);
    
    let pages = [1];
    
    if (startPage > 2) {
      pages.push('ellipsis');
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    if (endPage < totalPages - 1) {
      pages.push('ellipsis');
    }
    
    pages.push(totalPages);
    
    return pages;
  };

  // 查看封面HTML
  const handleLoadCover = async (record) => {
    const coverCode = record?.cover_code;
    const coverId = record?.id;

    if (!coverCode && !coverId) {
      message.error('封面记录无效，无法查看');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      setIsPreviewOpen(true);
      if (coverId) setCurrentPreviewRecordId(coverId);
      setPreviewHtml('<div style="display:flex;justify-content:center;align-items:center;height:100%"><div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div></div>');

      let response;
      if (coverCode) {
        response = await axios.get(`/api/cover/code/${coverCode}/edit`, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } else if (coverId) {
        response = await axios.get(`/api/cover/${coverId}/edit`, { 
          headers: { Authorization: `Bearer ${token}` }
        });
      } else {
        throw new Error('没有有效的封面标识符');
      }

      if (response.data.success) {
        const coverDataFromApi = response.data.data;
        setCurrentCover(coverDataFromApi);

        const htmlContent = coverDataFromApi.edited_html_content || coverDataFromApi.html_content;
        if (htmlContent) {
          setPreviewHtml(htmlContent);
        } else {
          const imageUrl = coverDataFromApi?.image_url || '';
          setPreviewHtml(`<div style="color:#666; text-align:center; padding:20px;">
            <p>暂无HTML内容</p>
            ${imageUrl ? `<img src=\"${imageUrl}\" style=\"max-width:100%; margin-top:20px; border:1px solid #eee;\" />` : ''}
          </div>`);
        }
      } else {
        setPreviewHtml(`<div style="color:#666; text-align:center; padding:20px;">
          <p>获取封面详情失败</p>
          <p>${response.data.message || '未知错误'}</p>
        </div>`);
      }
    } catch (error) {
      console.error('加载封面数据失败:', error);
      setPreviewHtml(`<div style="color:#666; text-align:center; padding:20px;">
        <p>获取封面详情失败</p>
        <p>${error.message || '未知错误'}</p>
      </div>`);
    }
  };

  // 分享封面
  const handleShareCover = async () => {
    try {
      if (!currentCover || !currentCover.id) {
        message.error('无法获取封面信息，无法分享');
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        message.error('未登录或会话已过期，请先登录');
        return;
      }

      const response = await axios.get(`/api/cover/${currentCover.id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success && response.data.data) {
        const coverCode = response.data.data.cover_code;

        if (!coverCode) {
          message.error('此封面不支持分享');
          return;
        }

        const shareUrl = `${window.location.origin}/share/${coverCode}`;
        await navigator.clipboard.writeText(shareUrl);
        message.success('分享链接已复制到剪贴板');

        setShareLinkDialogOpen(true);
        setShareUrl(shareUrl);
      } else {
        message.error(response.data.message || '获取封面信息失败');
      }
    } catch (error) {
      console.error('生成分享链接失败:', error);
      message.error('生成分享链接失败，请重试');
    }
  };

  // 编辑封面（回到编辑页面）
  const handleEditCover = () => {
    setIsPreviewOpen(false);

    if (currentCover) {
      try {
        localStorage.setItem('editCoverData', JSON.stringify(currentCover));
        
        // 触发自定义事件，通知ChatGenerate组件直接加载封面数据
        const loadCoverEvent = new CustomEvent('loadCoverData', { 
          detail: { coverData: currentCover, source: 'profile' } 
        });
        window.dispatchEvent(loadCoverEvent);
        
        window.location.replace('/chat-generate?mode=edit');
      } catch (error) {
        console.error('存储封面数据失败:', error);
        message.error('加载编辑器失败，请稍后再试');
      }
    }
  };

  // 刷新数据
  const handleRefreshData = () => {
    if (contentType === 'profile') {
      fetchUserProfile();
    } else if (contentType === 'points') {
      fetchPointRecords(pointsCurrentPage);
    } else if (contentType === 'covers') {
      fetchCoverRecords(coversCurrentPage);
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    return dayjs(dateString).format('YYYY-MM-DD HH:mm');
  };

  // 渲染个人资料
  const renderProfile = () => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center py-16">
          <LoadingSpinner />
          <p className="mt-4 text-gray-500">加载个人资料中...</p>
        </div>
      );
    }

    if (!userProfile) {
      return (
        <div className="text-center py-16">
          <p className="text-gray-500">无法加载个人资料，请检查登录状态</p>
          <Button 
            onClick={() => navigate('/auth')}
            className="mt-4"
          >
            去登录
          </Button>
        </div>
      );
    }

    return (
      <Card className="bg-card text-card-foreground shadow-lg w-full">
        <CardHeader className="pb-5 flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-semibold text-foreground">个人资料</CardTitle>
            <CardDescription>管理您的账户信息</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleRefreshData}
              className="h-8 w-8 hover:bg-accent/80"
            >
              <RefreshCw className="h-4 w-4" />
              <span className="sr-only">刷新</span>
            </Button>
            {!editing && userProfile && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setEditing(true)}
                className="text-muted-foreground border-border hover:bg-accent/50 hover:text-accent-foreground"
              >
                <Edit className="mr-2 h-4 w-4" /> 编辑资料
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {!editing ? (
              <div className="space-y-6">
                {[
                  { label: "昵称", value: userProfile.nickname, icon: UserCircle2 },
                  { label: "手机号", value: userProfile.phone ? maskPhoneNumber(userProfile.phone) : '未设置', icon: Smartphone },
                  { label: "邮箱", value: userProfile.email || '未设置', icon: Mail },
                  { label: "可用积分", value: <span className="font-semibold text-primary">{userProfile.points}</span>, icon: CircleDollarSign },
                  { label: "会员状态", value: userProfile.is_vip ? `高级会员 (到期时间: ${dayjs(userProfile.vip_expire_date).format('YYYY-MM-DD')})` : '普通会员', icon: Award },
                ].map((item, index) => (
                  <div key={index} className="grid grid-cols-3 md:grid-cols-4 gap-4 items-center">
                    <div className="flex items-center space-x-3 col-span-1 md:col-span-1">
                      {item.icon && React.createElement(item.icon, {
                        className: cn(
                          "h-5 w-5 text-muted-foreground flex-shrink-0",
                          item.label === "会员状态" && userProfile.is_vip && "text-yellow-500"
                        )
                      })}
                      <Label className="text-base text-muted-foreground text-left">{item.label}</Label>
                    </div>
                    <div className="text-base text-foreground col-span-2 md:col-span-3">{item.value}</div>
                  </div>
                ))}
              </div>
            ) : (
              <form onSubmit={handleUpdateProfile} className="space-y-6">
                <div className="grid gap-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="nickname" className="text-right text-muted-foreground">
                      昵称
                    </Label>
                    <Input
                      id="nickname"
                      value={editNickname}
                      onChange={(e) => setEditNickname(e.target.value)}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="phone" className="text-right text-muted-foreground">
                      手机号
                    </Label>
                    <Input
                      id="phone"
                      value={userProfile.phone ? maskPhoneNumber(userProfile.phone) : '未设置'}
                      disabled
                      className="col-span-3 bg-muted text-muted-foreground"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="email" className="text-right text-muted-foreground">
                      邮箱
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={editEmail}
                      onChange={(e) => setEditEmail(e.target.value)}
                      className="col-span-3"
                      placeholder="请输入您的邮箱"
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={handleCancelEdit} 
                    disabled={loading}
                    type="button"
                  >
                    <X className="mr-2 h-4 w-4" /> 取消
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={loading}
                    className="bg-gradient-primary text-primary-foreground hover:opacity-90"
                  >
                    {loading ? <LoadingSpinner className="mr-2 h-4 w-4" /> : <Save className="mr-2 h-4 w-4" />}
                    保存
                  </Button>
                </div>
              </form>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  // 渲染积分记录
  const renderPoints = () => {
    if (pointsLoading) {
      return (
        <div className="flex flex-col items-center justify-center py-16">
          <LoadingSpinner />
          <p className="mt-4 text-gray-500">加载积分记录中...</p>
        </div>
      );
    }

    if (pointRecords.length === 0) {
      return (
        <div className="text-center py-16">
          <p className="text-gray-500">暂无积分记录</p>
        </div>
      );
    }

    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">积分记录</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => fetchPointRecords(pointsCurrentPage)}
            className="h-8 w-8 hover:bg-accent/80"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16 text-center">序号</TableHead>
                <TableHead className="text-center">日期</TableHead>
                <TableHead className="text-center">类型</TableHead>
                <TableHead className="text-center">积分变化</TableHead>
                <TableHead className="text-center">描述</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {pointRecords.map((record, index) => (
                <TableRow key={record.id}>
                  <TableCell className="text-center font-medium">{record.id || index + 1}</TableCell>
                  <TableCell className="text-center">{formatDate(record.created_at)}</TableCell>
                  <TableCell className="text-center">{record.operation_type ? operationTypeMap[record.operation_type] || record.operation_type : '生成封面'}</TableCell>
                  <TableCell className={cn(
                    "text-center font-medium",
                    record.points_change > 0 ? "text-green-500" : "text-red-500"
                  )}>
                    {record.points_change > 0 ? `+${record.points_change}` : record.points_change}
                  </TableCell>
                  <TableCell className="text-center">{record.description ? descriptionMap[record.description] || record.description : '生成封面消费积分'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        {pointsTotalPages > 1 && (
          <div className="mt-4 flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => handlePageChange(Math.max(1, pointsCurrentPage - 1), 'points')}
                    className={cn(pointsCurrentPage <= 1 && "pointer-events-none opacity-50")}
                  />
                </PaginationItem>
                
                {generatePageNumbers(pointsCurrentPage, pointsTotalPages).map((page, i) => (
                  page === 'ellipsis' ? (
                    <PaginationItem key={`ellipsis-${i}`}>
                      <PaginationEllipsis />
                    </PaginationItem>
                  ) : (
                    <PaginationItem key={page}>
                      <PaginationLink 
                        isActive={page === pointsCurrentPage}
                        onClick={() => handlePageChange(page, 'points')}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  )
                ))}
                
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => handlePageChange(Math.min(pointsTotalPages, pointsCurrentPage + 1), 'points')}
                    className={cn(pointsCurrentPage >= pointsTotalPages && "pointer-events-none opacity-50")}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    );
  };

  // 渲染封面记录
  const renderCovers = () => {
    if (coversLoading) {
      return (
        <div className="flex flex-col items-center justify-center py-16">
          <LoadingSpinner />
          <p className="mt-4 text-gray-500">加载封面记录中...</p>
        </div>
      );
    }

    if (covers.length === 0) {
      return (
        <div className="text-center py-16">
          <p className="text-gray-500">暂无封面记录</p>
        </div>
      );
    }

    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">封面记录</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => fetchCoverRecords(coversCurrentPage)}
            className="h-8 w-8 hover:bg-accent/80"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16 text-center">序号</TableHead>
                <TableHead className="text-center">封面编码</TableHead>
                <TableHead className="text-center">类型</TableHead>
                <TableHead className="text-center">风格</TableHead>
                <TableHead className="text-center">生成时间</TableHead>
                <TableHead className="text-center">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {covers.map((record, index) => (
                <TableRow key={record.id}>
                  <TableCell className="text-center font-medium">{record.id || index + 1}</TableCell>
                  <TableCell className="text-center">{record.cover_code}</TableCell>
                  <TableCell className="text-center">{record.cover_type_name || '小红书封面'}</TableCell>
                  <TableCell className="text-center">{record.style_name || '默认风格'}</TableCell>
                  <TableCell className="text-center">{formatDate(record.created_at)}</TableCell>
                  <TableCell className="text-center">
                    <div className="flex items-center justify-center space-x-2">
                      <Button 
                        variant="ghost" 
                        size="icon"
                        className="h-8 w-8 text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                        onClick={() => handleLoadCover(record)}
                        title="预览"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        className="h-8 w-8 text-red-600 hover:text-red-800 hover:bg-red-100"
                        onClick={() => openDeleteConfirm(record.id)}
                        title="删除"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        {coversTotalPages > 1 && (
          <div className="mt-4 flex justify-center">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => handlePageChange(Math.max(1, coversCurrentPage - 1), 'covers')}
                    className={cn(coversCurrentPage <= 1 && "pointer-events-none opacity-50")}
                  />
                </PaginationItem>
                
                {generatePageNumbers(coversCurrentPage, coversTotalPages).map((page, i) => (
                  page === 'ellipsis' ? (
                    <PaginationItem key={`ellipsis-${i}`}>
                      <PaginationEllipsis />
                    </PaginationItem>
                  ) : (
                    <PaginationItem key={page}>
                      <PaginationLink 
                        isActive={page === coversCurrentPage}
                        onClick={() => handlePageChange(page, 'covers')}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  )
                ))}
                
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => handlePageChange(Math.min(coversTotalPages, coversCurrentPage + 1), 'covers')}
                    className={cn(coversCurrentPage >= coversTotalPages && "pointer-events-none opacity-50")}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    );
  };

  // 根据contentType渲染对应内容
  const renderContent = () => {
    switch (contentType) {
      case 'profile':
        return renderProfile();
      case 'points':
        return renderPoints();
      case 'covers':
        return renderCovers();
      default:
        return renderProfile();
    }
  };

  return (
    <div className="w-full h-full overflow-auto p-4 md:p-6">
      {renderContent()}

      {/* 封面预览对话框 */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="sm:max-w-[800px] h-[80vh] max-h-[800px] p-0 gap-0">
          <DialogHeader className="p-4 border-b">
            <DialogTitle>封面预览</DialogTitle>
            <DialogDescription>
              {currentCover?.cover_text || '预览封面内容'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-grow overflow-auto h-full">
            <iframe
              srcDoc={previewHtml}
              className="w-full h-full border-none"
              title="封面预览"
              sandbox="allow-same-origin"
            />
          </div>
          
          <div className="p-4 border-t flex justify-between items-center">
            <Button variant="outline" onClick={() => setIsPreviewOpen(false)}>
              关闭
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleShareCover}>
                <LinkIcon className="mr-2 h-4 w-4" />
                分享
              </Button>
              <Button onClick={handleEditCover}>
                <Edit className="mr-2 h-4 w-4" />
                编辑
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>删除封面</DialogTitle>
            <DialogDescription>
              此操作不可逆，确定要删除这个封面吗？
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteConfirmOpen(false)}
              disabled={deleteLoading}
            >
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleDeleteCover(deletingCoverId)}
              disabled={deleteLoading}
            >
              {deleteLoading ? <LoadingSpinner className="mr-2 h-4 w-4" /> : null}
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 分享链接对话框 */}
      <Dialog open={shareLinkDialogOpen} onOpenChange={setShareLinkDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>分享封面</DialogTitle>
            <DialogDescription>
              复制以下链接分享给好友
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            <Input
              value={shareUrl}
              readOnly
              className="w-full"
              onClick={(e) => e.target.select()}
            />
            <p className="mt-2 text-sm text-muted-foreground">
              链接有效期为30天，请及时保存
            </p>
          </div>
          <DialogFooter className="mt-4">
            <Button
              onClick={async () => {
                try {
                  await navigator.clipboard.writeText(shareUrl);
                  message.success('链接已复制到剪贴板');
                } catch (error) {
                  console.error('复制链接失败:', error);
                }
              }}
            >
              复制链接
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProfileContent; 
const { SystemConfig, HtmlSecurityConfig } = require('../models');
const { successResponse, errorResponse } = require('../utils/responseUtils');
const logger = require('../utils/logger');
const pointsService = require('../services/pointsService');

/**
 * 获取操作所需积分
 * @route GET /api/system/operation-cost/:type
 */
const getOperationCost = async (req, res) => {
  const { type } = req.params;

  try {
    const cost = await pointsService.getPointsCost(type);

    return successResponse(res, '获取操作积分消耗成功', {
      operation: type,
      points_cost: cost
    });
  } catch (error) {
    logger.error(`获取操作积分消耗失败: ${error.message}`);
    return errorResponse(res, '获取操作积分消耗失败', 500);
  }
};

/**
 * 获取所有系统配置
 * @route GET /api/system/configs
 */
const getSystemConfigs = async (req, res) => {
  try {
    const configs = await SystemConfig.findAll({
      attributes: ['config_key', 'config_value', 'description']
    });

    const configObj = {};
    configs.forEach(config => {
      configObj[config.config_key] = {
        value: config.config_value,
        description: config.description
      };
    });

    return successResponse(res, '获取系统配置成功', { configs: configObj });
  } catch (error) {
    logger.error(`获取系统配置失败: ${error.message}`);
    return errorResponse(res, '获取系统配置失败', 500);
  }
};

/**
 * 获取所有政策内容（隐私政策和用户协议）
 * @route GET /api/system/policies
 */
const getPolicies = async (req, res) => {
  try {
    const policies = await SystemConfig.findAll({
      where: {
        config_key: ['privacy_policy', 'user_agreement', 'privacy_policy_version', 'user_agreement_version', 'privacy_policy_updated_at', 'user_agreement_updated_at']
      },
      attributes: ['config_key', 'config_value']
    });

    const policyObj = {};
    policies.forEach(policy => {
      policyObj[policy.config_key] = policy.config_value;
    });

    // 如果没有找到政策内容，返回默认值
    if (!policyObj.privacy_policy) {
      policyObj.privacy_policy = '隐私政策内容尚未设置';
    }
    if (!policyObj.user_agreement) {
      policyObj.user_agreement = '用户协议内容尚未设置';
    }
    if (!policyObj.privacy_policy_version) {
      policyObj.privacy_policy_version = '1.0';
    }
    if (!policyObj.user_agreement_version) {
      policyObj.user_agreement_version = '1.0';
    }
    if (!policyObj.privacy_policy_updated_at) {
      policyObj.privacy_policy_updated_at = new Date().toISOString();
    }
    if (!policyObj.user_agreement_updated_at) {
      policyObj.user_agreement_updated_at = new Date().toISOString();
    }

    return successResponse(res, '获取政策内容成功', policyObj);
  } catch (error) {
    logger.error(`获取政策内容失败: ${error.message}`);
    return errorResponse(res, '获取政策内容失败', 500);
  }
};

/**
 * 获取隐私政策内容
 * @route GET /api/system/policies/privacy
 */
const getPrivacyPolicy = async (req, res) => {
  try {
    const policy = await SystemConfig.findOne({
      where: { config_key: 'privacy_policy' },
      attributes: ['config_value']
    });

    const version = await SystemConfig.findOne({
      where: { config_key: 'privacy_policy_version' },
      attributes: ['config_value']
    });

    const updatedAt = await SystemConfig.findOne({
      where: { config_key: 'privacy_policy_updated_at' },
      attributes: ['config_value']
    });

    return successResponse(res, '获取隐私政策成功', {
      content: policy ? policy.config_value : '隐私政策内容尚未设置',
      version: version ? version.config_value : '1.0',
      updated_at: updatedAt ? updatedAt.config_value : new Date().toISOString()
    });
  } catch (error) {
    logger.error(`获取隐私政策失败: ${error.message}`);
    return errorResponse(res, '获取隐私政策失败', 500);
  }
};

/**
 * 获取用户协议内容
 * @route GET /api/system/policies/agreement
 */
const getUserAgreement = async (req, res) => {
  try {
    const agreement = await SystemConfig.findOne({
      where: { config_key: 'user_agreement' },
      attributes: ['config_value']
    });

    const version = await SystemConfig.findOne({
      where: { config_key: 'user_agreement_version' },
      attributes: ['config_value']
    });

    const updatedAt = await SystemConfig.findOne({
      where: { config_key: 'user_agreement_updated_at' },
      attributes: ['config_value']
    });

    return successResponse(res, '获取用户协议成功', {
      content: agreement ? agreement.config_value : '用户协议内容尚未设置',
      version: version ? version.config_value : '1.0',
      updated_at: updatedAt ? updatedAt.config_value : new Date().toISOString()
    });
  } catch (error) {
    logger.error(`获取用户协议失败: ${error.message}`);
    return errorResponse(res, '获取用户协议失败', 500);
  }
};

/**
 * 获取渲染方式配置（普通用户可访问）
 * @route GET /api/system/rendering-mode-config
 */
const getRenderingModeConfig = async (req, res) => {
  try {
    // 暂时使用默认配置，避免数据库表结构问题
    const defaultConfig = {
      defaultRenderingMode: 'CLIENT_SIDE_SAFE_LOADING', // 默认使用标准模式
      enableAdvancedMode: true,
      fallbackMode: 'CLIENT_SIDE_SAFE_LOADING'
    };

    // 尝试从数据库获取配置，如果失败则使用默认配置
    let finalConfig = defaultConfig;
    try {
      const config = await HtmlSecurityConfig.findOne({
        where: { config_key: 'rendering_mode' }
      });

      if (config) {
        // 安全解析JSON配置值
        if (typeof config.config_value === 'string') {
          try {
            const parsedConfig = JSON.parse(config.config_value);
            finalConfig = { ...defaultConfig, ...parsedConfig };
          } catch (parseError) {
            logger.warn('渲染方式配置JSON解析失败，使用默认配置', {
              error: parseError.message,
              rawValue: config.config_value
            });
          }
        } else if (typeof config.config_value === 'object') {
          finalConfig = { ...defaultConfig, ...config.config_value };
        }
      }
    } catch (dbError) {
      logger.warn('数据库查询失败，使用默认配置', { error: dbError.message });
    }

    return successResponse(res, '获取渲染方式配置成功', finalConfig);
  } catch (error) {
    logger.error(`获取渲染方式配置失败: ${error.message}`);
    return errorResponse(res, '获取渲染方式配置失败', 500);
  }
};

/**
 * 记录安全违规（普通用户可访问）
 */
const logSecurityViolation = async (req, res) => {
  try {
    const { content, securityResult, source } = req.body;

    // 获取用户信息（如果已登录）
    const userId = req.user?.id || 1; // 默认用户ID为1（测试用）
    const userPhone = req.user?.phone || null;

    // 获取客户端信息
    const ipAddress = req.ip || req.connection.remoteAddress || '127.0.0.1';
    const userAgent = req.get('User-Agent') || 'Unknown';

    // 导入HtmlSecurityViolation模型
    const { HtmlSecurityViolation } = require('../models');

    // 创建安全违规记录
    const violationRecord = await HtmlSecurityViolation.create({
      user_id: userId,
      user_phone: userPhone,
      file_name: 'uploaded_file.html',
      upload_time: new Date(),
      risk_level: securityResult.riskLevel || 'MEDIUM',
      violation_reasons: securityResult.details || [],
      detected_threats: securityResult.threats || [],
      file_size: content ? content.length : 0,
      detection_engine_version: '2.0.0',
      ip_address: ipAddress,
      user_agent: userAgent,
      source: source || 'unknown'
    });

    logger.info('安全违规记录已存储到数据库', {
      recordId: violationRecord.id,
      userId,
      riskLevel: violationRecord.risk_level,
      source: source || 'unknown',
      threatsCount: securityResult.threats?.length || 0
    });

    return successResponse(res, '安全违规记录成功', {
      recorded: true,
      recordId: violationRecord.id,
      timestamp: violationRecord.upload_time.toISOString()
    });

  } catch (error) {
    logger.error(`记录安全违规失败: ${error.message}`);
    return errorResponse(res, '记录安全违规失败', 500);
  }
};

/**
 * 获取安全规则配置（普通用户可访问，只读）
 * @route GET /api/system/security-rules
 */
const getSecurityRulesConfig = async (req, res) => {
  try {
    // 从HtmlSecurityConfig表获取安全规则配置
    const config = await HtmlSecurityConfig.findOne({
      where: { config_key: 'security_rules' }
    });

    // 默认安全配置
    const defaultConfig = {
      enableXssDetection: true,
      enableMaliciousScriptDetection: true,
      enableDangerousTagDetection: true,
      enableExternalResourceValidation: true,
      riskThreshold: 'MEDIUM',
      autoBlock: false
    };

    // 安全解析JSON配置值
    let configValue = defaultConfig;
    if (config) {
      if (typeof config.config_value === 'string') {
        try {
          configValue = JSON.parse(config.config_value);
        } catch (error) {
          logger.warn('安全规则配置JSON解析失败，使用默认配置:', error.message);
          configValue = defaultConfig;
        }
      } else if (typeof config.config_value === 'object' && config.config_value !== null) {
        configValue = config.config_value;
      }
    }

    logger.info('用户获取安全规则配置', { 
      userId: req.user?.id, 
      configSource: config ? 'database' : 'default' 
    });

    return successResponse(res, '获取安全规则配置成功', configValue);
  } catch (error) {
    logger.error('获取安全规则配置失败:', error);
    return errorResponse(res, '获取安全规则配置失败: ' + error.message, 500);
  }
};

module.exports = {
  getOperationCost,
  getSystemConfigs,
  getPolicies,
  getPrivacyPolicy,
  getUserAgreement,
  getRenderingModeConfig,
  logSecurityViolation,
  getSecurityRulesConfig
};
# 封面AI系统 - 支付功能接入指南

本文档提供了接入微信支付和支付宝支付的详细步骤和注意事项，以供技术人员使用。

## 目录

1. [前言](#前言)
2. [系统支付流程概述](#系统支付流程概述)
3. [微信支付接入指南](#微信支付接入指南)
4. [支付宝支付接入指南](#支付宝支付接入指南)
5. [常见问题](#常见问题)
6. [安全与风控](#安全与风控)

## 前言

封面AI系统目前支持两种主流支付方式：微信支付和支付宝支付。为了确保支付功能安全、稳定地运行，请严格按照本文档的指引进行接入工作。

## 系统支付流程概述

封面AI系统的支付流程如下：

1. 用户在会员中心选择套餐，点击"立即开通"或"立即充值"按钮
2. 系统创建订单，生成订单号
3. 跳转到支付页面，展示订单信息
4. 用户选择支付方式（微信支付或支付宝）
5. 系统根据选择的支付方式生成支付二维码
6. 用户扫码完成支付
7. 支付平台异步通知系统支付结果
8. 系统处理支付结果，更新订单状态和用户权益

## 微信支付接入指南

### 一、申请步骤

1. **注册微信支付商户号**
   - 访问[微信支付商户平台](https://pay.weixin.qq.com)
   - 根据指引完成商户入驻
   - 获取商户号(mchid)和API密钥(v3密钥)

2. **申请支付权限**
   - 在商户平台申请Native支付（扫码支付）和JSAPI支付（公众号支付）权限
   - 提交相应的资质文件

### 二、配置要求

1. **证书配置**
   - 下载并保存API证书
   - 配置APIv3密钥

2. **关键参数**
   - 商户号(mchid)
   - 商户API证书序列号
   - 商户私钥
   - APIv3密钥

3. **系统配置项**
   - 将上述参数配置到系统的`payment_configs`表中
   - 确保`is_encrypted`字段对敏感信息（如密钥）设置为`true`

### 三、接口集成

```javascript
// 示例：微信支付Native下单（生成二维码）
async function createWechatPayment(orderNo, amount, description) {
  const url = 'https://api.mch.weixin.qq.com/v3/pay/transactions/native';
  
  const data = {
    appid: config.appid,
    mchid: config.mchid,
    out_trade_no: orderNo,
    description: description,
    notify_url: config.notifyUrl,
    amount: {
      total: Math.floor(amount * 100), // 单位：分
      currency: 'CNY'
    }
  };
  
  // 签名过程
  const timestamp = Math.floor(Date.now() / 1000);
  const nonce = crypto.randomBytes(16).toString('hex');
  const message = `POST\n/v3/pay/transactions/native\n${timestamp}\n${nonce}\n${JSON.stringify(data)}\n`;
  
  const signature = crypto.createSign('RSA-SHA256')
    .update(message)
    .sign(privateKey, 'base64');
  
  const authorization = `WECHATPAY2-SHA256-RSA2048 mchid="${config.mchid}",serial_no="${config.serialNo}",nonce_str="${nonce}",timestamp="${timestamp}",signature="${signature}"`;
  
  // 发送请求
  const response = await axios.post(url, data, {
    headers: {
      'Authorization': authorization,
      'Content-Type': 'application/json'
    }
  });
  
  return response.data.code_url; // 返回支付二维码URL
}
```

### 四、回调处理

1. **配置回调URL**
   - 在商户平台设置回调通知URL
   - 确保URL可以正常访问

2. **验证回调请求**
   - 验证签名
   - 验证请求来源IP（白名单）
   - 验证订单号与金额

```javascript
// 示例：微信支付回调处理
app.post('/api/payment/callback/wechat', async (req, res) => {
  try {
    // 验证签名
    const signature = req.headers['wechatpay-signature'];
    const timestamp = req.headers['wechatpay-timestamp'];
    const nonce = req.headers['wechatpay-nonce'];
    const body = req.rawBody;
    
    const message = `${timestamp}\n${nonce}\n${body}\n`;
    const isValid = crypto.createVerify('RSA-SHA256')
      .update(message)
      .verify(publicKey, signature, 'base64');
    
    if (!isValid) {
      throw new Error('签名验证失败');
    }
    
    // 处理支付结果
    const data = JSON.parse(body);
    const orderNo = data.out_trade_no;
    const tradeState = data.trade_state;
    
    if (tradeState === 'SUCCESS') {
      // 更新订单状态为支付成功
      await updateOrderStatus(orderNo, 'success', data.transaction_id);
    }
    
    // 返回成功
    res.status(200).send({
      code: 'SUCCESS',
      message: '成功'
    });
  } catch (error) {
    console.error('处理微信支付回调失败:', error);
    res.status(200).send({
      code: 'FAIL',
      message: '失败'
    });
  }
});
```

## 支付宝支付接入指南

### 一、申请步骤

1. **注册支付宝商家账户**
   - 访问[支付宝开放平台](https://open.alipay.com/)注册账户
   - 完成企业认证

2. **创建应用**
   - 在开放平台创建网页/移动应用
   - 申请支付功能授权

### 二、配置要求

1. **密钥配置**
   - 生成RSA密钥对
   - 在开放平台设置应用公钥
   - 获取支付宝公钥

2. **关键参数**
   - APPID
   - 应用私钥
   - 支付宝公钥
   - 支付网关URL

3. **系统配置项**
   - 将上述参数配置到系统的`payment_configs`表中
   - 确保`is_encrypted`字段对敏感信息（如密钥）设置为`true`

### 三、接口集成

```javascript
// 示例：支付宝电脑网站支付
const Alipay = require('alipay-sdk');
const AlipayFormData = require('alipay-sdk/lib/form').default;

async function createAlipayment(orderNo, amount, subject) {
  const alipay = new Alipay({
    appId: config.appId,
    privateKey: config.privateKey,
    signType: 'RSA2',
    alipayPublicKey: config.alipayPublicKey,
    gateway: 'https://openapi.alipay.com/gateway.do',
    charset: 'utf-8'
  });
  
  const formData = new AlipayFormData();
  formData.setMethod('alipay.trade.precreate');
  formData.addField('returnUrl', config.returnUrl);
  formData.addField('notifyUrl', config.notifyUrl);
  formData.addField('bizContent', {
    out_trade_no: orderNo,
    total_amount: amount.toFixed(2),
    subject: subject,
    product_code: 'FAST_INSTANT_TRADE_PAY'
  });
  
  const result = await alipay.exec(formData);
  return result.qrCode; // 返回支付二维码
}
```

### 四、回调处理

1. **配置回调URL**
   - 配置异步通知URL
   - 配置返回URL（可选）

2. **验证回调请求**
   - 验证签名
   - 验证交易状态
   - 验证金额和商户订单号

```javascript
// 示例：支付宝支付回调处理
app.post('/api/payment/callback/alipay', async (req, res) => {
  try {
    const params = req.body;
    
    // 验证签名
    const signStr = Object.keys(params)
      .filter(key => key !== 'sign' && key !== 'sign_type')
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    
    const isValid = crypto.createVerify('RSA-SHA256')
      .update(signStr)
      .verify(alipayPublicKey, params.sign, 'base64');
    
    if (!isValid) {
      throw new Error('签名验证失败');
    }
    
    // 验证交易状态
    if (params.trade_status === 'TRADE_SUCCESS') {
      // 更新订单状态为支付成功
      await updateOrderStatus(params.out_trade_no, 'success', params.trade_no);
    }
    
    // 返回成功
    res.send('success');
  } catch (error) {
    console.error('处理支付宝回调失败:', error);
    res.send('fail');
  }
});
```

## 常见问题

1. **签名验证失败**
   - 检查密钥格式是否正确
   - 确认签名使用的算法是否一致
   - 验证签名内容的组织方式是否正确

2. **回调接收不到**
   - 检查回调URL是否正确配置
   - 确认服务器是否能够正常访问
   - 查看支付平台的回调日志

3. **订单状态不同步**
   - 实现定时任务主动查询未完成订单状态
   - 确保回调处理逻辑正确
   - 增加日志记录，方便排查问题

## 安全与风控

1. **IP白名单**
   - 配置支付平台回调IP白名单
   - 系统中验证回调请求的来源IP

2. **签名验证**
   - 严格验证每次回调的签名
   - 保护私钥安全，避免泄露

3. **订单信息验证**
   - 验证回调中的订单号、金额等信息是否与系统记录一致
   - 防止篡改订单信息

4. **日志记录**
   - 记录所有支付相关操作
   - 记录回调请求的原始数据
   - 定期审计支付日志

5. **敏感信息加密**
   - 对支付密钥等敏感信息进行加密存储
   - 避免日志中记录完整的敏感信息 
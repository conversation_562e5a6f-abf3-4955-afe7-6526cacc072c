import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'

// Theme Context
import ThemeContext, { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider, useAuthContext } from './contexts/AuthContext.jsx';
import { FeatureProvider } from './contexts/FeatureContext';

// Axios拦截器配置
import setupAxiosInterceptors from './utils/axiosConfig'

// 布局组件
import MainLayout from './layouts/MainLayout';

// 页面组件
import ChatGenerate from './components/chat/ChatGenerate'
import AuthContent from './components/chat/AuthContent'
import ProfileContent from './components/chat/ProfileContent'
import SharePage from './components/chat/SharePage'
import PrivacyPolicy from './components/policies/PrivacyPolicy'
import UserAgreement from './components/policies/UserAgreement'
import MembershipPage from './components/membership/MembershipPage'
import PaymentContent from './components/payment/PaymentContent'
import PaymentResultContent from './components/payment/PaymentResultContent'
import FileUploadView from './components/chat/FileUploadView'
import CodePasteView from './components/chat/CodePasteView'

// 管理后台重定向组件
const AdminRedirect = () => {
  React.useEffect(() => {
    window.location.href = 'http://localhost:3002/admin/dashboard.html';
  }, []);
  return <div className="p-4">正在跳转到管理后台...</div>;
};

// 初始化axios拦截器
setupAxiosInterceptors();

// 需要登录才能访问的路由包装组件
const ProtectedRoute = ({ children }) => {
  const { isLoggedIn, isLoading } = useAuthContext();
  
  // 如果认证状态正在加载中，显示加载状态
  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">加载中...</div>;
  }
  
  // 如果用户未登录，重定向到登录页面
  if (!isLoggedIn) {
    return <Navigate to="/auth" replace />;
  }
  
  // 用户已登录，渲染子组件
  return children;
};

// 认证相关路由包装组件，已登录用户将被重定向到首页
const AuthRoute = ({ children }) => {
  const { isLoggedIn, isLoading } = useAuthContext();
  
  // 如果认证状态正在加载中，显示加载状态
  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">加载中...</div>;
  }
  
  // 如果用户已登录，重定向到首页
  if (isLoggedIn) {
    return <Navigate to="/" replace />;
  }
  
  // 用户未登录，渲染子组件
  return children;
};

// Main App Component Content
const AppContent = () => {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Routes>
        {/* 特殊页面路由 - 不使用MainLayout */}
        <Route path="/share/:code" element={<SharePage />} />
        <Route path="/policies/privacy" element={<PrivacyPolicy />} />
        <Route path="/policies/agreement" element={<UserAgreement />} />
        <Route path="/admin" element={<AdminRedirect />} />
        
        {/* 使用MainLayout的路由 */}
        <Route element={<MainLayout />}>
          {/* 主页/聊天页面 */}
          <Route path="/" element={<ChatGenerate />} />
          <Route path="/home" element={<Navigate to="/" replace />} />
          
          {/* 上传文件和代码粘贴页面 */}
          <Route path="/file-upload" element={<FileUploadView />} />
          <Route path="/code-paste" element={<CodePasteView />} />
          
          {/* 登录/注册页面 - 使用AuthRoute包装，已登录用户将被重定向到首页 */}
          <Route path="/auth" element={<AuthRoute><AuthContent /></AuthRoute>} />
          <Route path="/login" element={<Navigate to="/auth" replace />} />
          <Route path="/register" element={<AuthRoute><AuthContent /></AuthRoute>} />
          <Route path="/forgot-password" element={<AuthRoute><AuthContent /></AuthRoute>} />
          
          {/* 个人中心相关页面 */}
          <Route path="/profile" element={<ProtectedRoute><ProfileContent contentType="profile" /></ProtectedRoute>} />
          <Route path="/points" element={<ProtectedRoute><ProfileContent contentType="points" /></ProtectedRoute>} />
          <Route path="/orders" element={<ProtectedRoute><ProfileContent contentType="orders" /></ProtectedRoute>} />
          <Route path="/covers" element={<ProtectedRoute><ProfileContent contentType="covers" /></ProtectedRoute>} />
          <Route path="/my-creations" element={<ProtectedRoute><ProfileContent contentType="my-creations" /></ProtectedRoute>} />
          
          {/* 会员中心页面 */}
          <Route path="/membership" element={<MembershipPage />} />
          
          {/* 支付相关页面 */}
          <Route path="/payment" element={<ProtectedRoute><PaymentContent /></ProtectedRoute>} />
          <Route path="/payment-result" element={<ProtectedRoute><PaymentResultContent /></ProtectedRoute>} />
          
          {/* 新增：支付相关页面 - 带路由参数 */}
          <Route path="/payment/:orderNo" element={<ProtectedRoute><PaymentContent /></ProtectedRoute>} />
          <Route path="/payment-result/:orderNo" element={<ProtectedRoute><PaymentResultContent /></ProtectedRoute>} />
          
          {/* 重定向旧路径 */}
          <Route path="/generate" element={<Navigate to="/" replace />} />
          <Route path="/chat-generate" element={<Navigate to="/" replace />} />
          <Route path="/view-my-creations" element={<Navigate to="/my-creations" replace />} />
        </Route>
        
        {/* 捕获所有其他路径，重定向到首页 */}
        <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
    </div>
  )
}

// Wrap AppContent with ThemeProvider and FeatureProvider
const AppWithProviders = () => {
  return (
    <FeatureProvider>
    <ThemeProvider>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ThemeProvider>
    </FeatureProvider>
  );
}

export default AppWithProviders;
/**
 * 增强型HTML加载器
 * 为复杂静态页面提供增强的安全隔离和资源处理能力
 * 与现有htmlStructureUtils.js完全兼容，作为增强层使用
 */

import logger from '../../../services/logs/frontendLogger';
import {
  isCompleteHtmlStructure,
  extractHeadAndBodyContent,
  processCompleteHtmlContent,
  generateSizeCalculationScript,
  generateDomLoadedScript,
  setupFallbackContent
} from './htmlStructureUtils';
import resourceManager, { monitorIframeResources } from './resourceManager';
import { processAdvancedMode } from './advancedModeProcessor';

/**
 * 页面复杂度检测结果枚举
 */
export const PAGE_COMPLEXITY = {
  SIMPLE: 'simple',           // 简单页面，使用标准模式
  MODERATE: 'moderate',       // 中等复杂度，使用增强模式
  COMPLEX: 'complex',         // 复杂页面，使用高级模式
  UNKNOWN: 'unknown'          // 无法确定，使用降级模式
};

/**
 * 安全模式配置
 */
export const SECURITY_MODES = {
  STANDARD: {
    name: 'standard',
    sandbox: 'allow-scripts allow-same-origin allow-forms allow-downloads',
    csp: "default-src 'self' *; img-src * data: blob: https: http:; style-src * 'unsafe-inline'; font-src * data: https: http:; script-src * 'unsafe-inline' 'unsafe-eval';",
    description: '标准安全模式，兼容性优先，支持背景图片和外部资源'
  },
  ENHANCED: {
    name: 'enhanced',
    sandbox: 'allow-scripts allow-same-origin allow-forms allow-downloads',
    csp: "default-src 'self' https: http: *; img-src * data: blob: https: http:; style-src * 'unsafe-inline' https: http:; font-src * https: http: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; connect-src *;",
    description: '增强安全模式，支持外部资源和CORS代理，保持脚本执行能力'
  },
  STRICT: {
    name: 'strict',
    sandbox: 'allow-scripts allow-same-origin allow-forms', // 保持脚本执行以支持功能性页面
    csp: "default-src 'self' https: http: *; img-src * data: blob: https: http:; style-src * 'unsafe-inline' https: http:; font-src * https: http: data:; script-src 'self' 'unsafe-inline';",
    description: '严格安全模式，支持外部资源和脚本执行'
  }
};

/**
 * 检测页面复杂度
 * @param {string} htmlContent - HTML内容
 * @returns {string} - 页面复杂度级别
 */
export const detectPageComplexity = (htmlContent) => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return PAGE_COMPLEXITY.UNKNOWN;
  }

  try {
    let complexityScore = 0;
    
    // 检测完整HTML结构
    if (isCompleteHtmlStructure(htmlContent)) {
      complexityScore += 2;
    }
    
    // 检测外部资源引用
    const externalResourcePatterns = [
      /<link[^>]*href=["']https?:\/\/[^"']+["']/gi,  // 外部CSS
      /<script[^>]*src=["']https?:\/\/[^"']+["']/gi, // 外部JS
      /<img[^>]*src=["']https?:\/\/[^"']+["']/gi,    // 外部图片
      /@import\s+url\(["']?https?:\/\/[^"')]+["']?\)/gi // CSS中的外部资源
    ];
    
    externalResourcePatterns.forEach(pattern => {
      const matches = htmlContent.match(pattern);
      if (matches) {
        complexityScore += matches.length;
      }
    });
    
    // 检测复杂元素
    const complexElementPatterns = [
      /<nav[^>]*>/gi,           // 导航元素
      /<form[^>]*>/gi,          // 表单元素
      /<iframe[^>]*>/gi,        // 嵌入框架
      /<video[^>]*>/gi,         // 视频元素
      /<audio[^>]*>/gi,         // 音频元素
      /<canvas[^>]*>/gi,        // 画布元素
      /<svg[^>]*>/gi,           // SVG元素
      /onclick=["'][^"']*["']/gi, // 内联事件处理器
      /javascript:/gi           // JavaScript协议
    ];
    
    complexElementPatterns.forEach(pattern => {
      const matches = htmlContent.match(pattern);
      if (matches) {
        complexityScore += matches.length * 2;
      }
    });
    
    // 根据评分确定复杂度
    if (complexityScore === 0) {
      return PAGE_COMPLEXITY.SIMPLE;
    } else if (complexityScore <= 5) {
      return PAGE_COMPLEXITY.MODERATE;
    } else {
      return PAGE_COMPLEXITY.COMPLEX;
    }
    
  } catch (error) {
    logger.error('检测页面复杂度失败', { error: error.message });
    return PAGE_COMPLEXITY.UNKNOWN;
  }
};

/**
 * 根据页面复杂度选择安全模式
 * @param {string} complexity - 页面复杂度
 * @returns {Object} - 安全模式配置
 */
export const selectSecurityMode = (complexity) => {
  switch (complexity) {
    case PAGE_COMPLEXITY.SIMPLE:
      return SECURITY_MODES.STANDARD;
    case PAGE_COMPLEXITY.MODERATE:
      return SECURITY_MODES.ENHANCED;
    case PAGE_COMPLEXITY.COMPLEX:
      return SECURITY_MODES.STRICT;
    default:
      return SECURITY_MODES.STANDARD; // 默认使用标准模式
  }
};

/**
 * 增强的安全配置设置
 * @param {Object} securityMode - 安全模式配置
 * @param {HTMLIFrameElement} iframe - iframe元素
 */
export const enhancedSecuritySetup = (securityMode, iframe) => {
  try {
    if (!iframe) {
      throw new Error('iframe元素不存在');
    }
    
    // 设置沙箱属性
    iframe.setAttribute('sandbox', securityMode.sandbox);
    
    // 设置其他安全属性
    iframe.setAttribute('referrerpolicy', 'no-referrer');
    iframe.setAttribute('loading', 'lazy');
    
    logger.info('增强安全配置已应用', { 
      mode: securityMode.name,
      sandbox: securityMode.sandbox 
    });
    
  } catch (error) {
    logger.error('应用增强安全配置失败', { error: error.message });
    throw error;
  }
};

/**
 * 资源加载错误处理
 * @param {string} htmlContent - HTML内容
 * @returns {string} - 处理后的HTML内容
 */
export const resourceErrorHandling = (htmlContent) => {
  if (!htmlContent) return htmlContent;

  try {
    // 为图片添加错误处理
    let processedContent = htmlContent.replace(
      /<img([^>]*?)>/gi,
      '<img$1 onerror="this.style.display=\'none\'; this.insertAdjacentHTML(\'afterend\', \'<div class=\\\"img-error\\\">图片加载失败</div>\');">'
    );

    // 为外部CSS添加错误处理
    processedContent = processedContent.replace(
      /<link([^>]*?)rel=["']stylesheet["']([^>]*?)>/gi,
      '<link$1rel="stylesheet"$2 onerror="console.warn(\'CSS加载失败:\', this.href);">'
    );

    // 增强背景图片处理 - 确保背景图片能正常显示
    processedContent = processedContent.replace(
      /background-image\s*:\s*url\(\s*(['"]?)([^'")\s]+)\1\s*\)/gi,
      (match, quote, url) => {
        // 清理URL
        const cleanUrl = url.trim();

        // 如果是data URL，保持原样
        if (cleanUrl.startsWith('data:')) {
          return match;
        }

        // 确保URL格式正确，支持相对路径和绝对路径
        return `background-image: url("${cleanUrl}")`;
      }
    );

    return processedContent;

  } catch (error) {
    logger.error('资源错误处理失败', { error: error.message });
    return htmlContent; // 返回原始内容
  }
};

/**
 * 生成增强的完整HTML内容
 * @param {Object} params - 参数对象
 * @param {Object} params.securityMode - 安全模式配置
 * @param {string} params.doctype - DOCTYPE声明
 * @param {string} params.headContent - head标签内容
 * @param {string} params.bodyContent - body标签内容
 * @param {string} params.bodyAttributes - body标签属性
 * @param {string} params.baseUrl - 基础URL
 * @param {string} params.editorStyles - 编辑器样式
 * @param {string} params.keyboardScript - 键盘导航脚本
 * @param {Function} params.textElementInitScript - 文本元素初始化脚本
 * @returns {string} - 完整的HTML内容
 */
export const generateEnhancedCompleteHtmlContent = ({
  securityMode = SECURITY_MODES.STANDARD,
  doctype = '<!DOCTYPE html>',
  headContent = '',
  bodyContent = '',
  bodyAttributes = '',
  baseUrl = '',
  editorStyles = '',
  keyboardScript = '',
  textElementInitScript = null
}) => {
  const baseTagHtml = baseUrl ? `<base href="${baseUrl}">` : '';
  
  // 智能处理head内容，确保CSP标签在正确位置
  let processedHeadContent = headContent;

  // 检查原始head内容是否已包含CSP标签
  const existingCSP = headContent.match(/<meta[^>]*http-equiv\s*=\s*["']Content-Security-Policy["'][^>]*>/i);

  if (existingCSP) {
    // 如果已存在CSP标签，替换为新的
    processedHeadContent = headContent.replace(
      /<meta[^>]*http-equiv\s*=\s*["']Content-Security-Policy["'][^>]*>/i,
      `<meta http-equiv="Content-Security-Policy" content="${securityMode.csp}">`
    );
  }

  // 构建完整的head内容，确保CSP在正确位置
  const headElements = `
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    ${!existingCSP ? `<meta http-equiv="Content-Security-Policy" content="${securityMode.csp}">` : ''}
    ${baseTagHtml}
    ${processedHeadContent}
    <style>
      /* 高级模式增强样式 - 不破坏原始布局 */
      .advanced-mode-enhanced {
        /* 资源加载优化 */
        image-rendering: optimizeQuality;
        /* 字体渲染优化 */
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      /* 资源加载错误样式 */
      .img-error {
        display: inline-block;
        padding: 10px;
        background: #f5f5f5;
        border: 1px dashed #ccc;
        color: #666;
        font-size: 12px;
        text-align: center;
        min-width: 100px;
        min-height: 50px;
      }
      ${editorStyles}
    </style>
  `;

  return `
    ${doctype}
    <html>
    <head>
      ${headElements}
    </head>
    <body${bodyAttributes} class="advanced-mode-enhanced">
      ${bodyContent}
      <script>
        ${generateSizeCalculationScript(true)}
        ${generateDomLoadedScript(true, textElementInitScript)}
        ${keyboardScript}
      </script>
    </body>
    </html>
  `;
};

/**
 * 降级到标准模式
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {string} content - HTML内容
 * @param {Function} getTextEditorStyles - 获取文本编辑器样式的函数
 * @param {Function} getKeyboardNavigationScript - 获取键盘导航脚本的函数
 * @param {Function} getTextElementInitScript - 获取文本元素初始化脚本的函数
 * @param {string} baseUrl - 基础URL
 * @returns {boolean} - 是否成功降级
 */
export const fallbackToStandard = (
  iframe,
  content,
  getTextEditorStyles,
  getKeyboardNavigationScript,
  getTextElementInitScript,
  baseUrl = ''
) => {
  try {
    logger.warn('降级到标准模式');

    // 移除增强的安全属性，恢复标准配置
    iframe.removeAttribute('sandbox');
    iframe.removeAttribute('referrerpolicy');
    iframe.removeAttribute('loading');

    // 直接返回false，让调用方使用标准模式处理
    // 避免在这里调用setupFallbackContent造成循环依赖
    return false;

  } catch (error) {
    logger.error('降级到标准模式失败', { error: error.message });
    return false;
  }
};

/**
 * 增强型HTML片段内容设置
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {string} content - HTML片段内容
 * @param {Function} getTextEditorStyles - 获取文本编辑器样式的函数
 * @param {Function} getKeyboardNavigationScript - 获取键盘导航脚本的函数
 * @param {Function} getTextElementInitScript - 获取文本元素初始化脚本的函数
 * @param {string} baseUrl - 基础URL
 * @returns {boolean} - 是否成功设置
 */
export const setupAdvancedFragmentContent = (
  iframe,
  content,
  getTextEditorStyles,
  getKeyboardNavigationScript,
  getTextElementInitScript,
  baseUrl = ''
) => {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) {
      throw new Error('无法访问iframe文档');
    }

    // 检测页面复杂度
    const complexity = detectPageComplexity(content);
    logger.info('HTML片段复杂度检测结果', { complexity });

    // 选择安全模式（对片段使用更宽松的安全策略）
    const securityMode = complexity.score > 5 ? SECURITY_MODES.ENHANCED : SECURITY_MODES.STANDARD;
    logger.info('选择的安全模式', { mode: securityMode.name });

    // 应用增强安全配置
    enhancedSecuritySetup(securityMode, iframe);

    // 使用新的高级模式处理器，保持原始布局
    const advancedResult = processAdvancedMode(content, {
      preserveLayout: true,
      enableResourceOptimization: true
    });

    if (!advancedResult.success) {
      logger.warn('高级模式处理失败，使用原始内容', { error: advancedResult.error });
    }

    const processedContent = advancedResult.enhancedContent;

    // 为HTML片段生成完整的HTML结构，保持原始布局不变
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="${securityMode.csp}">
  ${baseUrl ? `<base href="${baseUrl}">` : ''}
  <title>预览</title>
  <style>
    /* 保持原始布局的基础样式 */
    * {
      box-sizing: border-box;
    }
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    /* 确保背景图片正确显示 */
    [style*="background-image"], .wechat-cover {
      background-repeat: no-repeat !important;
      background-size: cover !important;
      background-position: center !important;
    }
    /* 编辑器样式 */
    ${getTextEditorStyles ? getTextEditorStyles() : ''}
  </style>
</head>
<body>
  ${processedContent}
  <script>
    ${generateSizeCalculationScript(true)}
    ${generateDomLoadedScript(true, getTextElementInitScript)}
    ${getKeyboardNavigationScript ? getKeyboardNavigationScript() : ''}
  </script>
</body>
</html>`;

    // 写入HTML内容 - 增加错误处理
    try {
      // 验证HTML内容的完整性
      if (!htmlContent || htmlContent.trim().length === 0) {
        throw new Error('HTML内容为空');
      }

      // 预处理HTML内容，确保格式正确
      let processedHtml = htmlContent;

      // 检查并修复常见的HTML问题
      if (!processedHtml.includes('<!DOCTYPE')) {
        // 如果没有DOCTYPE，添加一个
        if (processedHtml.includes('<html')) {
          processedHtml = '<!DOCTYPE html>\n' + processedHtml;
        }
      }

      // 确保HTML标签正确闭合
      if (processedHtml.includes('<html') && !processedHtml.includes('</html>')) {
        processedHtml += '\n</html>';
      }

      // 检查HTML内容是否包含未闭合的标签或语法错误
      try {
        const tempDiv = document.createElement('div');
        // 只检查body部分，避免完整HTML解析问题
        const bodyMatch = processedHtml.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
        if (bodyMatch) {
          tempDiv.innerHTML = bodyMatch[1];
        }
      } catch (parseError) {
        logger.warn('HTML解析检查失败，继续尝试写入', { error: parseError.message });
      }

      iframeDoc.write(processedHtml);
      iframeDoc.close();

      logger.info('HTML内容写入成功', {
        originalLength: htmlContent.length,
        processedLength: processedHtml.length
      });
    } catch (writeError) {
      logger.error('HTML写入失败', { error: writeError.message });

      // 降级处理：使用更安全的方式设置内容
      try {
        iframeDoc.close(); // 确保文档已关闭
        iframeDoc.open(); // 重新打开

        // 尝试提取原始内容的关键部分
        let fallbackContent = '';
        try {
          // 尝试提取body内容
          const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
          if (bodyMatch) {
            fallbackContent = bodyMatch[1];
          } else {
            // 如果没有body标签，使用原始内容
            fallbackContent = htmlContent.replace(/<\/?html[^>]*>/gi, '')
                                        .replace(/<\/?head[^>]*>/gi, '')
                                        .replace(/<\/?body[^>]*>/gi, '');
          }
        } catch (extractError) {
          logger.warn('内容提取失败', { error: extractError.message });
          fallbackContent = '<p>内容加载失败</p>';
        }

        // 使用简化的HTML结构包装内容
        const safeHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>预览</title>
  <style>
    body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
    .content-wrapper { width: 100%; height: 100%; }
  </style>
</head>
<body>
  <div class="content-wrapper">
    ${fallbackContent}
  </div>
</body>
</html>`;

        iframeDoc.write(safeHtml);
        iframeDoc.close();

        logger.warn('使用降级HTML内容', { fallbackLength: safeHtml.length });
      } catch (fallbackError) {
        logger.error('降级处理也失败', { error: fallbackError.message });
        throw fallbackError;
      }
    }

    // 启动资源监控 - 等待iframe内容完全加载
    setTimeout(async () => {
      try {
        // 确保iframe和其内容都已加载
        if (iframe && iframe.contentDocument && iframe.contentWindow) {
          const resourceSummary = await monitorIframeResources(iframe);
          logger.info('增强型HTML片段内容设置成功', {
            complexity,
            securityMode: securityMode.name,
            resources: resourceSummary
          });
        } else {
          logger.warn('iframe内容未完全加载，跳过资源监控');
        }
      } catch (error) {
        logger.warn('资源监控失败', { error: error.message });
      }
    }, 500);

    return true;
  } catch (error) {
    logger.error('设置增强型HTML片段内容失败', { error: error.message });
    return false;
  }
};

/**
 * 增强型HTML内容设置（完整HTML结构）
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {string} content - HTML内容
 * @param {Function} getTextEditorStyles - 获取文本编辑器样式的函数
 * @param {Function} getKeyboardNavigationScript - 获取键盘导航脚本的函数
 * @param {Function} getTextElementInitScript - 获取文本元素初始化脚本的函数
 * @param {string} baseUrl - 基础URL
 * @returns {boolean} - 是否成功设置
 */
export const setupAdvancedCompleteHtmlContent = (
  iframe,
  content,
  getTextEditorStyles,
  getKeyboardNavigationScript,
  getTextElementInitScript,
  baseUrl = ''
) => {
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) {
      throw new Error('无法访问iframe文档');
    }

    // 检测页面复杂度
    const complexity = detectPageComplexity(content);
    logger.info('页面复杂度检测结果', { complexity });

    // 选择安全模式
    const securityMode = selectSecurityMode(complexity);
    logger.info('选择的安全模式', { mode: securityMode.name });

    // 应用增强安全配置
    enhancedSecuritySetup(securityMode, iframe);

    // 处理完整HTML结构
    const { doctype, headContent, bodyContent, bodyAttributes, baseUrl: contentBaseUrl } = processCompleteHtmlContent(content);

    // 使用base标签处理相对路径
    const effectiveBaseUrl = contentBaseUrl || baseUrl;

    // 使用新的高级模式处理器处理完整HTML
    const completeHtml = `${doctype}\n<html>\n<head>\n${headContent}\n</head>\n<body${bodyAttributes}>\n${bodyContent}\n</body>\n</html>`;

    const advancedResult = processAdvancedMode(completeHtml, {
      preserveLayout: true,
      enableResourceOptimization: true
    });

    if (!advancedResult.success) {
      logger.warn('完整HTML高级模式处理失败，使用原始内容', { error: advancedResult.error });
    }

    let htmlContent = advancedResult.enhancedContent;

    // 确保CSP标签在正确位置
    if (!htmlContent.includes('Content-Security-Policy')) {
      htmlContent = htmlContent.replace(
        /<head>/i,
        `<head>\n  <meta http-equiv="Content-Security-Policy" content="${securityMode.csp}">`
      );
    }

    // 添加编辑器样式和脚本
    if (htmlContent.includes('</head>')) {
      const editorStyles = getTextEditorStyles ? getTextEditorStyles() : '';
      if (editorStyles) {
        htmlContent = htmlContent.replace('</head>', `  <style>\n    ${editorStyles}\n  </style>\n</head>`);
      }
    }

    if (htmlContent.includes('</body>')) {
      const scripts = `
    <script>
      ${generateSizeCalculationScript(true)}
      ${generateDomLoadedScript(true, getTextElementInitScript)}
      ${getKeyboardNavigationScript ? getKeyboardNavigationScript() : ''}
    </script>`;
      htmlContent = htmlContent.replace('</body>', scripts + '\n</body>');
    }

    // 写入HTML内容 - 增加错误处理
    try {
      // 验证HTML内容的完整性
      if (!htmlContent || htmlContent.trim().length === 0) {
        throw new Error('HTML内容为空');
      }

      iframeDoc.write(htmlContent);
      iframeDoc.close();

      logger.info('完整HTML内容写入成功', { contentLength: htmlContent.length });
    } catch (writeError) {
      logger.error('完整HTML写入失败', { error: writeError.message });

      // 降级处理
      try {
        iframeDoc.close();
        iframeDoc.open();

        const safeHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>预览</title>
  <style>
    body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
    .error-message { color: #e74c3c; background: #fdf2f2; padding: 15px; border-radius: 5px; }
  </style>
</head>
<body>
  <div class="error-message">
    <h3>完整HTML内容加载失败</h3>
    <p>HTML内容存在语法错误，请检查内容格式。</p>
    <p>错误信息：${writeError.message}</p>
  </div>
</body>
</html>`;

        iframeDoc.write(safeHtml);
        iframeDoc.close();

        logger.warn('使用降级HTML内容（完整HTML模式）');
      } catch (fallbackError) {
        logger.error('完整HTML降级处理也失败', { error: fallbackError.message });
        throw fallbackError;
      }
    }

    // 启动资源监控 - 等待iframe内容完全加载
    setTimeout(async () => {
      try {
        // 确保iframe和其内容都已加载
        if (iframe && iframe.contentDocument && iframe.contentWindow) {
          const resourceSummary = await monitorIframeResources(iframe);
          logger.info('增强型HTML内容设置成功', {
            complexity,
            securityMode: securityMode.name,
            resources: resourceSummary
          });
        } else {
          logger.warn('iframe内容未完全加载，跳过资源监控');
        }
      } catch (error) {
        logger.warn('资源监控失败', { error: error.message });
      }
    }, 500);

    return true;
  } catch (error) {
    logger.error('设置增强型HTML内容失败', { error: error.message });
    return false;
  }
};

/**
 * 增强型iframe内容设置（主入口函数）
 * 与现有setupIframeContentWithStructureDetection完全兼容
 * @param {HTMLIFrameElement} iframe - iframe元素
 * @param {string} content - HTML内容
 * @param {Function} getTextEditorStyles - 获取文本编辑器样式的函数
 * @param {Function} getKeyboardNavigationScript - 获取键盘导航脚本的函数
 * @param {Function} getTextElementInitScript - 获取文本元素初始化脚本的函数
 * @param {string} baseUrl - 基础URL
 * @param {Object} options - 选项配置
 * @param {boolean} options.useAdvancedMode - 是否使用增强模式，默认true
 * @param {boolean} options.enableFallback - 是否启用降级机制，默认true
 * @returns {boolean} - 是否成功设置
 */
export const setupAdvancedIframeContent = (
  iframe,
  content,
  getTextEditorStyles,
  getKeyboardNavigationScript,
  getTextElementInitScript,
  baseUrl = '',
  options = {}
) => {
  const {
    useAdvancedMode = true,
    enableFallback = true
  } = options;

  if (!iframe) {
    logger.error('iframe元素不存在');
    return false;
  }

  const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
  if (!iframeDoc) {
    logger.error('无法访问iframe文档');
    return false;
  }

  try {
    // 打开iframe文档
    iframeDoc.open();

    let success = false;

    if (useAdvancedMode) {
      // 尝试使用增强模式
      if (isCompleteHtmlStructure(content)) {
        success = setupAdvancedCompleteHtmlContent(
          iframe,
          content,
          getTextEditorStyles,
          getKeyboardNavigationScript,
          getTextElementInitScript,
          baseUrl
        );
      } else {
        // 对于HTML片段，使用增强的片段处理
        logger.info('HTML片段使用增强模式处理');
        success = setupAdvancedFragmentContent(
          iframe,
          content,
          getTextEditorStyles,
          getKeyboardNavigationScript,
          getTextElementInitScript,
          baseUrl
        );
      }
    }

    // 如果增强模式失败且启用降级，则降级到标准模式
    if (!success && enableFallback) {
      success = fallbackToStandard(
        iframe,
        content,
        getTextEditorStyles,
        getKeyboardNavigationScript,
        getTextElementInitScript,
        baseUrl
      );
    }

    // 关闭iframe文档
    iframeDoc.close();

    return success;
  } catch (error) {
    logger.error('设置增强型iframe内容失败', { error: error.message });
    try {
      // 最后的降级尝试
      if (enableFallback) {
        fallbackToStandard(iframe, content);
        iframeDoc.close();
      }
    } catch (e) {
      logger.error('最终降级也失败了', { error: e.message });
    }
    return false;
  }
};

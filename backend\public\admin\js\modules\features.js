window.featureModule = (function() {
  // 分页参数
  let currentPage = 1;
  let pageSize = 10;
  let totalPages = 1;
  let keyword = '';
  let activeFilter = '';

  // 初始化模块
  function init() {
    console.log('功能控制模块初始化开始...');

    // 绑定事件
    bindEvents();

    // 加载功能控制列表
    fetchFeatures();

    console.log('功能控制模块初始化完成');
  }

  // 绑定事件处理函数
  function bindEvents() {
    // 添加功能按钮
    document.getElementById('addFeatureBtn').addEventListener('click', () => {
      showFeatureModal('add');
    });

    // 搜索按钮
    document.getElementById('featureSearchBtn').addEventListener('click', () => {
      keyword = document.getElementById('featureSearchInput').value.trim();
      currentPage = 1;
      fetchFeatures();
    });

    // 搜索框回车
    document.getElementById('featureSearchInput').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        keyword = e.target.value.trim();
        currentPage = 1;
        fetchFeatures();
      }
    });

    // 状态筛选
    document.getElementById('featureActiveFilter').addEventListener('change', (e) => {
      activeFilter = e.target.value;
      currentPage = 1;
      fetchFeatures();
    });

    // 刷新按钮
    document.getElementById('featureRefreshBtn').addEventListener('click', () => {
      document.getElementById('featureSearchInput').value = '';
      document.getElementById('featureActiveFilter').value = '';
      keyword = '';
      activeFilter = '';
      currentPage = 1;
      fetchFeatures();
    });

    // 页码大小选择
    document.getElementById('featurePageSize').addEventListener('change', (e) => {
      pageSize = parseInt(e.target.value);
      currentPage = 1;
      fetchFeatures();
    });

    // 保存功能按钮
    document.getElementById('featureModalSubmitBtn').addEventListener('click', saveFeature);
  }

  // 获取功能控制列表
  function fetchFeatures() {
    const token = localStorage.getItem('token');
    if (!token) return;

    // 构建查询参数
    const params = new URLSearchParams({
      page: currentPage,
      limit: pageSize
    });

    if (keyword) {
      params.append('keyword', keyword);
    }

    if (activeFilter) {
      params.append('is_active', activeFilter);
    }

    // 显示加载中状态
    const tableBody = document.querySelector('#featuresTable tbody');
    tableBody.innerHTML = '<tr><td colspan="6" class="text-center">加载中...</td></tr>';

    // 发送请求
    fetch(`/api/features/admin/list?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        renderFeaturesList(data.data, data.pagination);
      } else {
        showError('获取功能控制列表失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('获取功能控制列表失败:', error);
      showError('获取功能控制列表失败，请稍后再试');
    });
  }

  // 渲染功能控制列表
  function renderFeaturesList(features, pagination) {
    try {
      const tableBody = document.querySelector('#featuresTable tbody');

      if (!tableBody) {
        console.error('找不到featuresTable tbody元素，无法渲染功能列表');
        return;
      }

      // 更新分页信息
      totalPages = pagination.total_pages;
      currentPage = pagination.page;

      // 清空表格
      tableBody.innerHTML = '';

      // 检查是否有数据
      if (!features || features.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center">暂无数据</td></tr>';
        // 隐藏分页
        document.getElementById('featurePagination').parentElement.parentElement.style.display = 'none';
        return;
      }

      // 显示分页
      document.getElementById('featurePagination').parentElement.parentElement.style.display = 'flex';

      // 渲染功能列表
      features.forEach((feature, index) => {
        const rowNumber = (pagination.page - 1) * pagination.limit + index + 1;

        // 格式化角色
        let roles = '';
        try {
          let userRoles = feature.user_roles;

          // 确保userRoles是数组
          if (typeof userRoles === 'string') {
            try {
              userRoles = JSON.parse(userRoles);
            } catch (e) {
              userRoles = [userRoles];
            }
          }

          if (!Array.isArray(userRoles)) {
            userRoles = [String(userRoles)];
          }

          roles = userRoles.map(role => {
            switch(role) {
              case 'user': return '<span class="badge bg-success">普通用户</span>';
              case 'vip': return '<span class="badge bg-warning">VIP用户</span>';
              case 'admin': return '<span class="badge bg-danger">管理员</span>';
              default: return `<span class="badge bg-secondary">${role}</span>`;
            }
          }).join(' ');
        } catch (e) {
          console.error('解析角色失败:', e, feature.user_roles);
          roles = '<span class="badge bg-secondary">数据异常</span>';
        }

        // 创建行
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${rowNumber}</td>
          <td>${feature.feature_name}</td>
          <td>${roles}</td>
          <td>${feature.points_cost || 0}</td>
          <td>${feature.is_active ? '<span class="badge bg-success">已启用</span>' : '<span class="badge bg-secondary">已禁用</span>'}</td>
          <td>
            <button class="btn btn-sm btn-outline-primary me-1" onclick="featureModule.editFeature(${feature.id})">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="featureModule.deleteFeature(${feature.id})">
              <i class="bi bi-trash"></i>
            </button>
          </td>
        `;

        tableBody.appendChild(row);
      });

      // 更新分页控件
      renderPagination();
    } catch (error) {
      console.error('渲染功能列表失败:', error);
      const tableBody = document.querySelector('#featuresTable tbody');
      if (tableBody) {
        tableBody.innerHTML = `<tr><td colspan="6" class="text-center text-danger">渲染功能列表失败: ${error.message}</td></tr>`;
      }
    }
  }

  // 渲染分页控件
  function renderPagination() {
    const paginationElement = document.getElementById('featurePagination');
    paginationElement.innerHTML = '';

    // 上一页按钮
    const prevLi = document.createElement('li');
    prevLi.className = `page-item${currentPage === 1 ? ' disabled' : ''}`;
    const prevLink = document.createElement('a');
    prevLink.className = 'page-link';
    prevLink.href = '#';
    prevLink.setAttribute('aria-label', '上一页');
    prevLink.innerHTML = '上一页';
    prevLink.addEventListener('click', (e) => {
      e.preventDefault();
      if (currentPage > 1) {
        currentPage--;
        fetchFeatures();
      }
    });
    prevLi.appendChild(prevLink);
    paginationElement.appendChild(prevLi);

    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
      const pageLi = document.createElement('li');
      pageLi.className = `page-item${i === currentPage ? ' active' : ''}`;
      const pageLink = document.createElement('a');
      pageLink.className = 'page-link';
      pageLink.href = '#';
      pageLink.innerText = i;
      pageLink.addEventListener('click', (e) => {
        e.preventDefault();
        currentPage = i;
        fetchFeatures();
      });
      pageLi.appendChild(pageLink);
      paginationElement.appendChild(pageLi);
    }

    // 下一页按钮
    const nextLi = document.createElement('li');
    nextLi.className = `page-item${currentPage === totalPages ? ' disabled' : ''}`;
    const nextLink = document.createElement('a');
    nextLink.className = 'page-link';
    nextLink.href = '#';
    nextLink.setAttribute('aria-label', '下一页');
    nextLink.innerHTML = '下一页';
    nextLink.addEventListener('click', (e) => {
      e.preventDefault();
      if (currentPage < totalPages) {
        currentPage++;
        fetchFeatures();
      }
    });
    nextLi.appendChild(nextLink);
    paginationElement.appendChild(nextLi);
  }

  // 显示功能控制模态框
  function showFeatureModal(type, featureId) {
    const modal = new bootstrap.Modal(document.getElementById('featureModal'));
    const modalTitle = document.getElementById('featureModalLabel');
    const idInput = document.getElementById('featureId');
    const form = document.getElementById('addFeatureForm');

    // 重置表单
    form.reset();

    // 设置模态框标题和ID
    if (type === 'edit') {
      modalTitle.textContent = '编辑功能控制';
      idInput.value = featureId;

      // 加载功能控制详情
      fetchFeatureDetail(featureId);
    } else {
      modalTitle.textContent = '添加功能控制';
      idInput.value = '';

      // 默认选中普通用户角色
      document.getElementById('roleUser').checked = true;
      // 默认启用
      document.getElementById('isActive').checked = true;

      modal.show();
    }
  }

  // 获取功能控制详情
  function fetchFeatureDetail(id) {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`/api/features/admin/detail/${id}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        fillFeatureForm(data.data.feature);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('featureModal'));
        modal.show();
      } else {
        showError('获取功能控制详情失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      console.error('获取功能控制详情失败:', error);
      showError('获取功能控制详情失败，请稍后再试');
    });
  }

  // 填充功能控制表单
  function fillFeatureForm(feature) {
    document.getElementById('featureName').value = feature.feature_name || '';
    document.getElementById('featureDescription').value = feature.feature_description || '';
    document.getElementById('pointsCost').value = feature.points_cost || 0;
    document.getElementById('isActive').checked = feature.is_active || false;

    // 设置角色复选框
    document.getElementById('roleUser').checked = false;
    document.getElementById('roleVip').checked = false;
    document.getElementById('roleAdmin').checked = false;

    try {
      let userRoles = feature.user_roles;

      // 确保userRoles是数组
      if (typeof userRoles === 'string') {
        try {
          userRoles = JSON.parse(userRoles);
        } catch (e) {
          userRoles = [userRoles];
        }
      }

      if (!Array.isArray(userRoles)) {
        userRoles = [String(userRoles)];
      }

      userRoles.forEach(role => {
        switch(role) {
          case 'user':
            document.getElementById('roleUser').checked = true;
            break;
          case 'vip':
            document.getElementById('roleVip').checked = true;
            break;
          case 'admin':
            document.getElementById('roleAdmin').checked = true;
            break;
        }
      });
    } catch (e) {
      console.error('解析角色失败:', e, feature.user_roles);
    }
  }

  // 保存功能控制
  function saveFeature() {
    // 验证表单
    const form = document.getElementById('addFeatureForm');
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }

    const featureId = document.getElementById('featureId').value;
    const featureName = document.getElementById('featureName').value;
    const featureDescription = document.getElementById('featureDescription').value;
    const pointsCost = parseInt(document.getElementById('pointsCost').value) || 0;
    const isActive = document.getElementById('isActive').checked;

    // 获取选中的角色
    const roles = [];
    if (document.getElementById('roleUser').checked) roles.push('user');
    if (document.getElementById('roleVip').checked) roles.push('vip');
    if (document.getElementById('roleAdmin').checked) roles.push('admin');

    // 验证至少选择一个角色
    if (roles.length === 0) {
      alert('请至少选择一个适用角色');
      return;
    }

    // 构建功能数据
    const featureData = {
      feature_name: featureName,
      feature_description: featureDescription,
      user_roles: roles,
      points_cost: pointsCost,
      is_active: isActive
    };

    // 发送请求
    const url = featureId ? `/api/features/admin/update/${featureId}` : '/api/features/admin/create';
    const method = featureId ? 'PUT' : 'POST';
    const token = localStorage.getItem('token');

    if (!token) return;

    // 显示加载状态
    const saveBtn = document.getElementById('featureModalSubmitBtn');
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';

    fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(featureData)
    })
    .then(response => response.json())
    .then(data => {
      // 恢复按钮状态
      saveBtn.disabled = false;
      saveBtn.textContent = '保存';

      if (data.success) {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('featureModal'));
        modal.hide();

        // 刷新列表
        fetchFeatures();

        // 显示成功消息
        alert(featureId ? '功能控制更新成功' : '功能控制添加成功');
      } else {
        showError('保存功能控制失败: ' + (data.message || '未知错误'));
      }
    })
    .catch(error => {
      // 恢复按钮状态
      saveBtn.disabled = false;
      saveBtn.textContent = '保存';

      console.error('保存功能控制失败:', error);
      showError('保存功能控制失败，请稍后再试');
    });
  }

  // 编辑功能控制
  function editFeature(id) {
    showFeatureModal('edit', id);
  }

  // 删除功能控制
  function deleteFeature(id) {
    if (confirm('确定要删除此功能控制吗？此操作不可恢复。')) {
      const token = localStorage.getItem('token');
      if (!token) return;

      fetch(`/api/features/admin/delete/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          fetchFeatures();
          alert('功能控制删除成功');
        } else {
          showError('删除功能控制失败: ' + (data.message || '未知错误'));
        }
      })
      .catch(error => {
        console.error('删除功能控制失败:', error);
        showError('删除功能控制失败，请稍后再试');
      });
    }
  }

  // 显示错误消息
  function showError(message) {
    alert(message);
  }

  // 公开的API
  return {
    init,
    editFeature,
    deleteFeature
  };
})();
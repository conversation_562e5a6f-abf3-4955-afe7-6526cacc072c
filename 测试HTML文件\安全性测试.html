<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全性测试 - 架构安全隔离验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .warning-banner {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .warning-icon {
            font-size: 48px;
            color: #f39c12;
            margin-bottom: 10px;
        }
        
        .title {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: bold;
            text-align: center;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .test-title {
            color: #444;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .test-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background: #ff6b6b;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .security-test-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px 5px;
        }
        
        .security-test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .result-safe {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .result-warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .result-danger {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        .status-safe { background: #4CAF50; }
        .status-warning { background: #FF9800; }
        .status-danger { background: #F44336; }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .security-info {
            background: #e8f5e8;
            border-left: 4px solid #4CAF50;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="warning-banner">
            <div class="warning-icon">⚠️</div>
            <h3>安全测试文件</h3>
            <p>此文件包含安全测试代码，用于验证架构的安全隔离能力。在生产环境中请谨慎使用。</p>
        </div>
        
        <h1 class="title">🛡️ 安全性测试</h1>
        <p class="subtitle">验证新旧架构在安全隔离方面的差异表现</p>
        
        <div class="security-info">
            <strong>测试目的：</strong>通过执行各种潜在的安全风险操作，验证架构的安全隔离能力。新架构应该能够更好地防止恶意代码影响主系统。
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">1</span>
                父窗口访问测试
            </div>
            <p>测试是否能够访问父窗口对象：</p>
            <button class="security-test-button" onclick="testParentAccess()">
                测试父窗口访问
            </button>
            <div class="code-block">
try {
    console.log('Parent window:', window.parent);
    console.log('Top window:', window.top);
    console.log('Frame element:', window.frameElement);
} catch (error) {
    console.log('访问被阻止:', error.message);
}
            </div>
            <div class="test-result" id="parentAccessResult">
                <span class="status-indicator status-warning"></span>
                等待测试...
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">2</span>
                本地存储访问测试
            </div>
            <p>测试是否能够访问localStorage和sessionStorage：</p>
            <button class="security-test-button" onclick="testStorageAccess()">
                测试存储访问
            </button>
            <div class="code-block">
try {
    localStorage.setItem('test', 'security_test');
    sessionStorage.setItem('test', 'security_test');
    console.log('存储访问成功');
} catch (error) {
    console.log('存储访问被阻止:', error.message);
}
            </div>
            <div class="test-result" id="storageAccessResult">
                <span class="status-indicator status-warning"></span>
                等待测试...
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">3</span>
                网络请求测试
            </div>
            <p>测试是否能够发起外部网络请求：</p>
            <button class="security-test-button" onclick="testNetworkRequest()">
                测试网络请求
            </button>
            <div class="code-block">
fetch('https://api.github.com/users/octocat')
    .then(response => response.json())
    .then(data => console.log('网络请求成功:', data))
    .catch(error => console.log('网络请求被阻止:', error));
            </div>
            <div class="test-result" id="networkRequestResult">
                <span class="status-indicator status-warning"></span>
                等待测试...
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">4</span>
                表单提交测试
            </div>
            <p>测试是否能够提交表单到外部地址：</p>
            <form id="testForm" action="https://httpbin.org/post" method="post" target="_blank">
                <input type="hidden" name="test" value="security_test">
                <button type="button" class="security-test-button" onclick="testFormSubmission()">
                    测试表单提交
                </button>
            </form>
            <div class="test-result" id="formSubmissionResult">
                <span class="status-indicator status-warning"></span>
                等待测试...
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">5</span>
                脚本执行测试
            </div>
            <p>测试动态脚本执行能力：</p>
            <button class="security-test-button" onclick="testScriptExecution()">
                测试脚本执行
            </button>
            <div class="code-block">
const script = document.createElement('script');
script.textContent = 'console.log("动态脚本执行成功");';
document.head.appendChild(script);
            </div>
            <div class="test-result" id="scriptExecutionResult">
                <span class="status-indicator status-warning"></span>
                等待测试...
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">
                <span class="test-icon">6</span>
                架构安全评估
            </div>
            <div id="securityAssessment" class="test-result result-warning">
                <span class="status-indicator status-warning"></span>
                点击上方测试按钮进行安全评估...
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            parentAccess: null,
            storageAccess: null,
            networkRequest: null,
            formSubmission: null,
            scriptExecution: null
        };
        
        function testParentAccess() {
            try {
                const parentInfo = {
                    hasParent: window.parent !== window,
                    hasTop: window.top !== window,
                    hasFrameElement: !!window.frameElement,
                    sandboxed: window.frameElement ? window.frameElement.hasAttribute('sandbox') : false
                };
                
                console.log('父窗口访问测试:', parentInfo);
                
                if (parentInfo.sandboxed) {
                    document.getElementById('parentAccessResult').innerHTML = 
                        '<span class="status-indicator status-safe"></span>✓ 安全：运行在沙箱环境中';
                    document.getElementById('parentAccessResult').className = 'test-result result-safe';
                    testResults.parentAccess = 'safe';
                } else {
                    document.getElementById('parentAccessResult').innerHTML = 
                        '<span class="status-indicator status-warning"></span>⚠️ 警告：可以访问父窗口';
                    document.getElementById('parentAccessResult').className = 'test-result result-warning';
                    testResults.parentAccess = 'warning';
                }
            } catch (error) {
                document.getElementById('parentAccessResult').innerHTML = 
                    '<span class="status-indicator status-safe"></span>✓ 安全：父窗口访问被阻止';
                document.getElementById('parentAccessResult').className = 'test-result result-safe';
                testResults.parentAccess = 'safe';
            }
            
            updateSecurityAssessment();
        }
        
        function testStorageAccess() {
            try {
                localStorage.setItem('security_test', 'test_value');
                sessionStorage.setItem('security_test', 'test_value');
                
                document.getElementById('storageAccessResult').innerHTML = 
                    '<span class="status-indicator status-warning"></span>⚠️ 警告：可以访问本地存储';
                document.getElementById('storageAccessResult').className = 'test-result result-warning';
                testResults.storageAccess = 'warning';
                
                // 清理测试数据
                localStorage.removeItem('security_test');
                sessionStorage.removeItem('security_test');
            } catch (error) {
                document.getElementById('storageAccessResult').innerHTML = 
                    '<span class="status-indicator status-safe"></span>✓ 安全：本地存储访问被阻止';
                document.getElementById('storageAccessResult').className = 'test-result result-safe';
                testResults.storageAccess = 'safe';
            }
            
            updateSecurityAssessment();
        }
        
        function testNetworkRequest() {
            fetch('https://api.github.com/users/octocat')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('networkRequestResult').innerHTML = 
                        '<span class="status-indicator status-warning"></span>⚠️ 警告：可以发起网络请求';
                    document.getElementById('networkRequestResult').className = 'test-result result-warning';
                    testResults.networkRequest = 'warning';
                    updateSecurityAssessment();
                })
                .catch(error => {
                    document.getElementById('networkRequestResult').innerHTML = 
                        '<span class="status-indicator status-safe"></span>✓ 安全：网络请求被阻止';
                    document.getElementById('networkRequestResult').className = 'test-result result-safe';
                    testResults.networkRequest = 'safe';
                    updateSecurityAssessment();
                });
        }
        
        function testFormSubmission() {
            try {
                const form = document.getElementById('testForm');
                // 尝试提交表单（实际不会提交，只是测试能否执行）
                console.log('表单提交测试 - 表单元素:', form);
                
                document.getElementById('formSubmissionResult').innerHTML = 
                    '<span class="status-indicator status-warning"></span>⚠️ 警告：可以访问表单元素';
                document.getElementById('formSubmissionResult').className = 'test-result result-warning';
                testResults.formSubmission = 'warning';
            } catch (error) {
                document.getElementById('formSubmissionResult').innerHTML = 
                    '<span class="status-indicator status-safe"></span>✓ 安全：表单访问被阻止';
                document.getElementById('formSubmissionResult').className = 'test-result result-safe';
                testResults.formSubmission = 'safe';
            }
            
            updateSecurityAssessment();
        }
        
        function testScriptExecution() {
            try {
                const script = document.createElement('script');
                script.textContent = 'console.log("动态脚本执行成功 - 时间戳:", Date.now());';
                document.head.appendChild(script);
                
                document.getElementById('scriptExecutionResult').innerHTML = 
                    '<span class="status-indicator status-warning"></span>⚠️ 警告：可以执行动态脚本';
                document.getElementById('scriptExecutionResult').className = 'test-result result-warning';
                testResults.scriptExecution = 'warning';
                
                // 清理脚本元素
                document.head.removeChild(script);
            } catch (error) {
                document.getElementById('scriptExecutionResult').innerHTML = 
                    '<span class="status-indicator status-safe"></span>✓ 安全：动态脚本执行被阻止';
                document.getElementById('scriptExecutionResult').className = 'test-result result-safe';
                testResults.scriptExecution = 'safe';
            }
            
            updateSecurityAssessment();
        }
        
        function updateSecurityAssessment() {
            const completedTests = Object.values(testResults).filter(result => result !== null).length;
            const safeTests = Object.values(testResults).filter(result => result === 'safe').length;
            const warningTests = Object.values(testResults).filter(result => result === 'warning').length;
            
            if (completedTests === 0) return;
            
            let assessmentText = '';
            let assessmentClass = '';
            let statusIcon = '';
            
            if (safeTests >= 4) {
                assessmentText = `🛡️ 高安全性架构：${safeTests}/5 项测试显示良好的安全隔离`;
                assessmentClass = 'result-safe';
                statusIcon = 'status-safe';
            } else if (safeTests >= 2) {
                assessmentText = `⚠️ 中等安全性架构：${safeTests}/5 项测试安全，${warningTests} 项需要注意`;
                assessmentClass = 'result-warning';
                statusIcon = 'status-warning';
            } else {
                assessmentText = `🚨 低安全性架构：仅 ${safeTests}/5 项测试安全，存在安全风险`;
                assessmentClass = 'result-danger';
                statusIcon = 'status-danger';
            }
            
            document.getElementById('securityAssessment').innerHTML = 
                `<span class="status-indicator ${statusIcon}"></span>${assessmentText}`;
            document.getElementById('securityAssessment').className = `test-result ${assessmentClass}`;
        }
        
        // 页面加载完成后显示初始信息
        window.addEventListener('load', function() {
            console.log('安全测试页面加载完成');
            console.log('当前环境信息:', {
                userAgent: navigator.userAgent,
                location: window.location.href,
                timestamp: new Date().toISOString()
            });
        });
    </script>
</body>
</html>

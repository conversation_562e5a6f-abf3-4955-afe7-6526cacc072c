import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Sparkles, 
  Upload, 
  Code, 
  ArrowRight, 
  Zap, 
  FileText, 
  PenTool
} from 'lucide-react';

/**
 * 首页预览组件 - 展示平台主要功能和引导用户进入核心业务
 * 
 * @returns {React.ReactElement} 首页预览组件
 */
const HomePreview = ({ onStartCreate }) => {
  const navigate = useNavigate();

  // 处理上传HTML按钮点击
  const handleUploadClick = () => {
    navigate('/file-upload');
    // 触发自定义事件，通知其他组件视图已更改
    window.dispatchEvent(new CustomEvent('viewChange', { detail: { view: 'file-upload' } }));
  };

  // 处理粘贴HTML代码按钮点击
  const handleCodePasteClick = () => {
    navigate('/code-paste');
    // 触发自定义事件，通知其他组件视图已更改
    window.dispatchEvent(new CustomEvent('viewChange', { detail: { view: 'code-paste' } }));
  };

  return (
    <div className="w-full h-full flex flex-col items-center justify-center p-6" style={{ backgroundColor: '#f0f2f5' }}>
      {/* 顶部区域：平台介绍 */}
      <div className="text-center mb-10">
        <h1 className="text-3xl font-bold text-gray-800 mb-3 flex items-center justify-center">
          <Zap className="mr-2 text-blue-500" size={28} />
          HTML在线编辑平台
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          快速创建、编辑和分享精美的HTML内容，无需编程知识，轻松制作专业级网页封面
        </p>
      </div>

      {/* 中间区域：三个主要功能卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-5xl mb-10">
        {/* AI创建HTML卡片 */}
        <div className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow p-6 border border-blue-100 flex flex-col">
          <div className="bg-blue-50 rounded-full w-12 h-12 flex items-center justify-center mb-4">
            <Sparkles className="text-blue-500" size={24} />
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">AI创建HTML</h3>
          <p className="text-gray-600 mb-4 flex-grow">
            使用AI智能生成精美HTML内容，只需输入简单描述即可
          </p>
          <button 
            onClick={onStartCreate}
            className="flex items-center justify-center bg-gradient-primary text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            开始创建
            <ArrowRight size={16} className="ml-1" />
          </button>
        </div>

        {/* 上传HTML卡片 */}
        <div className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow p-6 border border-green-100 flex flex-col">
          <div className="bg-green-50 rounded-full w-12 h-12 flex items-center justify-center mb-4">
            <Upload className="text-green-500" size={24} />
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">上传HTML</h3>
          <p className="text-gray-600 mb-4 flex-grow">
            上传您已有的HTML文件，在线编辑并优化展示效果
          </p>
          <button 
            onClick={handleUploadClick}
            className="flex items-center justify-center bg-gradient-primary text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            上传文件
            <ArrowRight size={16} className="ml-1" />
          </button>
        </div>

        {/* 复制HTML卡片 */}
        <div className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow p-6 border border-purple-100 flex flex-col">
          <div className="bg-purple-50 rounded-full w-12 h-12 flex items-center justify-center mb-4">
            <Code className="text-purple-500" size={24} />
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">粘贴HTML代码</h3>
          <p className="text-gray-600 mb-4 flex-grow">
            直接粘贴HTML代码，快速预览和编辑内容效果
          </p>
          <button 
            onClick={handleCodePasteClick}
            className="flex items-center justify-center bg-gradient-primary text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            粘贴代码
            <ArrowRight size={16} className="ml-1" />
          </button>
        </div>
      </div>

      {/* 底部区域：平台特点 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-5xl">
        <div className="flex items-center">
          <div className="bg-gray-100 rounded-full p-2 mr-3">
            <PenTool size={18} className="text-gray-600" />
          </div>
          <div>
            <h4 className="font-medium text-gray-800">所见即所得</h4>
            <p className="text-sm text-gray-500">实时预览编辑效果</p>
          </div>
        </div>
        <div className="flex items-center">
          <div className="bg-gray-100 rounded-full p-2 mr-3">
            <FileText size={18} className="text-gray-600" />
          </div>
          <div>
            <h4 className="font-medium text-gray-800">多种模板</h4>
            <p className="text-sm text-gray-500">丰富的风格和尺寸选择</p>
          </div>
        </div>
        <div className="flex items-center">
          <div className="bg-gray-100 rounded-full p-2 mr-3">
            <Zap size={18} className="text-gray-600" />
          </div>
          <div>
            <h4 className="font-medium text-gray-800">一键分享</h4>
            <p className="text-sm text-gray-500">轻松导出和分享您的作品</p>
          </div>
        </div>
      </div>

      {/* 底部提示 */}
      <div className="mt-10 text-center text-gray-500 text-sm">
        <p>在下方输入框中输入描述，选择风格标签，即可开始创建</p>
        <div className="mt-2">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="inline-block animate-bounce">
            <path d="M12 5v14M5 12l7 7 7-7"></path>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default HomePreview; 
/**
 * 调试数据库JSON问题
 */

const { sequelize } = require('./backend/src/config/database');

async function debugDatabaseJson() {
  try {
    console.log('🔍 调试数据库JSON问题...');
    
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 检查html_security_configs表数据
    console.log('\n📋 检查html_security_configs表数据:');
    const [configs] = await sequelize.query('SELECT * FROM html_security_configs');
    
    console.log(`数据条数: ${configs.length}`);
    
    configs.forEach((config, index) => {
      console.log(`\n📝 配置 ${index + 1}:`);
      console.log(`   ID: ${config.id}`);
      console.log(`   config_key: ${config.config_key}`);
      console.log(`   description: ${config.description}`);
      console.log(`   config_value类型: ${typeof config.config_value}`);
      console.log(`   config_value原始值: ${config.config_value}`);
      
      // 尝试解析JSON
      try {
        if (typeof config.config_value === 'string') {
          const parsed = JSON.parse(config.config_value);
          console.log(`   ✅ JSON解析成功: ${JSON.stringify(parsed, null, 2)}`);
        } else if (typeof config.config_value === 'object') {
          console.log(`   ⚠️ config_value已经是对象: ${JSON.stringify(config.config_value, null, 2)}`);
        } else {
          console.log(`   ❌ config_value类型异常: ${typeof config.config_value}`);
        }
      } catch (error) {
        console.log(`   ❌ JSON解析失败: ${error.message}`);
      }
    });
    
    // 检查html_security_violations表是否存在
    console.log('\n📋 检查html_security_violations表:');
    try {
      const [violations] = await sequelize.query('SELECT * FROM html_security_violations LIMIT 5');
      console.log(`违规记录数: ${violations.length}`);
      if (violations.length > 0) {
        console.log('前5条记录:');
        violations.forEach((violation, index) => {
          console.log(`   ${index + 1}. ID: ${violation.id}, 风险等级: ${violation.risk_level}`);
        });
      }
    } catch (error) {
      console.log(`❌ html_security_violations表查询失败: ${error.message}`);
      if (error.message.includes("doesn't exist")) {
        console.log('💡 需要创建html_security_violations表');
      }
    }
    
    await sequelize.close();
    console.log('\n✅ 调试完成');
    
  } catch (error) {
    console.log('❌ 调试失败:', error.message);
    console.log('错误详情:', error);
  }
}

debugDatabaseJson();

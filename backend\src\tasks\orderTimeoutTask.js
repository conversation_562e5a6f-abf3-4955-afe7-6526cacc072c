const { PaymentRecord } = require('../models');
const logger = require('../utils/logger');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 检查订单支付超时状态并自动关闭
 * 定时执行（每5分钟执行一次）
 */
const closeTimeoutOrders = async () => {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('开始执行订单超时自动关闭任务');
    
    // 计算15分钟前的时间点
    const timeoutThreshold = new Date();
    timeoutThreshold.setMinutes(timeoutThreshold.getMinutes() - 15);
    
    // 查找所有超时未支付的订单
    const timeoutOrders = await PaymentRecord.findAll({
      where: {
        payment_status: 'pending',
        created_at: {
          [Op.lt]: timeoutThreshold // 创建时间早于15分钟前
        }
      },
      transaction
    });
    
    if (timeoutOrders.length === 0) {
      logger.info('没有发现超时未支付订单');
      await transaction.commit();
      return;
    }
    
    logger.info(`发现${timeoutOrders.length}个超时未支付订单，开始处理`);
    
    // 更新超时订单状态
    for (const order of timeoutOrders) {
      await order.update({ 
        payment_status: 'closed' 
      }, { transaction });
      
      logger.info(`订单${order.id}(${order.order_no})已超时，状态已更新为closed，创建时间: ${order.created_at}`);
    }
    
    await transaction.commit();
    logger.info(`成功关闭${timeoutOrders.length}个超时订单`);
  } catch (error) {
    await transaction.rollback();
    logger.error('处理订单超时关闭任务失败:', error);
    throw error;
  }
};

module.exports = closeTimeoutOrders; 
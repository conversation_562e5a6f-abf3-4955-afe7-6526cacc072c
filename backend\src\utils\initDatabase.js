const { sequelize } = require('../config/database');
const { User, BasePrompt, StylePrompt, SystemConfig, AIServiceConfig } = require('../models');
const bcrypt = require('bcryptjs');
const logger = require('./logger');
const { encryptApiKey } = require('./encryption');

/**
 * 初始化数据库，添加基础数据
 */

const initDatabase = async () => {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    logger.info('数据库连接成功');

    // 同步数据库模型，但不自动修改表结构，避免索引问题
    // 注意：这里使用force: false和alter: false确保不会修改现有表结构
    await sequelize.sync({ force: false, alter: false });
    logger.info('数据库模型同步完成');

    // 检查是否已有管理员账号
    const adminExists = await User.findOne({
      where: { role: 'admin' }
    });

    // 如果没有管理员账号，检查是否有指定手机号的用户
    if (!adminExists) {
      // 先检查手机号为13800000000的用户是否已存在
      const existingUser = await User.findOne({
        where: { phone: '13800000000' }
      });

      if (existingUser) {
        // 如果已存在该手机号用户，则将其升级为管理员
        existingUser.role = 'admin';
        await existingUser.save();
        logger.info('已将现有用户升级为管理员账号');
      } else {
        // 如果不存在该手机号用户，则创建新的管理员账号
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123456', salt);

      await User.create({
        phone: '13800000000',
        password: hashedPassword,
        nickname: '系统管理员',
        role: 'admin',
          points: 999999,
          status: 'active',
          failed_login_attempts: 0,
          login_count: 0,
          has_set_password: true
      });

      logger.info('创建默认管理员账号成功');
      }
    } else {
      logger.info('管理员账号已存在，跳过创建');
    }

    // 检查并创建基础提示词模板
    const basePromptExists = await BasePrompt.count();
    if (basePromptExists === 0) {
      await BasePrompt.bulkCreate([
        {
          cover_type: 'xiaohongshu',
          cover_size: '1000x1500',
          prompt_content: '生成一张小红书风格的封面图片，尺寸为1000x1500像素，比例为2:3。使用干净的白色或浅色背景，搭配清晰的视觉元素和现代字体。图片要有高质量、明亮、清晰的特点，符合小红书平台的审美，能够吸引用户点击。'
        },
        {
          cover_type: 'wechat',
          cover_size: '900x383',
          prompt_content: '创建一张微信公众号文章的封面图片，尺寸为900x383像素，比例约为2.35:1。设计要简洁大方，主题突出，使用清晰的视觉元素和专业的排版。确保在小尺寸下文字依然清晰可读，视觉效果突出。'
        }
      ]);

      logger.info('创建基础提示词模板成功');
    }

    // 检查并创建风格提示词模板
    const stylePromptExists = await StylePrompt.count();
    if (stylePromptExists === 0) {
      await StylePrompt.bulkCreate([
        {
          style_name: 'simple_clean',
          prompt_content: '简约清新风格，使用极简设计元素，干净的留白，柔和的配色方案。文字排版整洁有序，视觉层次分明。整体给人清爽、舒适、现代的感觉。',
          display_order: 1,
          id_code: 'simple_clean',
          display_name: '简约清新风格'
        },
        {
          style_name: 'tech_modern',
          prompt_content: '科技现代风格，使用深色背景，霓虹色调点缀，几何线条和网格元素。可以添加适量的科技感纹理或数字化元素。文字使用现代无衬线字体，整体感觉前卫、高端、未来感强。',
          display_order: 2,
          id_code: 'tech_modern',
          display_name: '科技现代风格'
        },
        {
          style_name: 'warm_natural',
          prompt_content: '温暖自然风格，使用大地色系、米色、原木色等自然色调。可以添加植物、纹理等自然元素。文字使用有亲和力的字体，整体给人温馨、亲切、舒适的感觉。',
          display_order: 3,
          id_code: 'warm_natural',
          display_name: '温暖自然风格'
        },
        {
          style_name: 'vibrant_bold',
          prompt_content: '活力大胆风格，使用鲜艳对比色，大胆的色块和图形元素。文字可以使用粗体或创意字体，整体给人充满活力、年轻、引人注目的印象。',
          display_order: 4,
          id_code: 'vibrant_bold',
          display_name: '活力大胆风格'
        },
        {
          style_name: 'elegant_luxury',
          prompt_content: '优雅奢华风格，使用黑金、白金等高级配色，搭配精致的装饰元素。可以添加适量的金属质感、大理石纹理等高级感元素。文字使用优雅的衬线字体，整体给人高端、精致、专业的感觉。',
          display_order: 5,
          id_code: 'elegant_luxury',
          display_name: '优雅奢华风格'
        }
      ]);

      logger.info('创建风格提示词模板成功');
    }

    // 检查并创建系统配置
    const configExists = await SystemConfig.count();
    if (configExists === 0) {
      await SystemConfig.bulkCreate([
        {
          config_key: 'new_user_points',
          config_value: '50',
          description: '新用户注册赠送的积分数量'
        },
        {
          config_key: 'cover_points_cost',
          config_value: '10',
          description: '生成一张封面消耗的积分数量'
        }
      ]);

      logger.info('创建系统配置成功');
    }

    // 初始化AI服务配置
    const aiServiceExists = await AIServiceConfig.count();
    if (aiServiceExists === 0) {
      // 加密DeepSeek API Key
      const deepseekApiKey = '***********************************';
      const encryptedDeepSeekKey = await encryptApiKey(deepseekApiKey);

      // 加密xAI API Key（作为备选服务）
      const xaiApiKey = '************************************************************************************';
      const encryptedXaiKey = await encryptApiKey(xaiApiKey);

      await AIServiceConfig.bulkCreate([
        {
          service_name: 'DeepSeek',
          base_url: 'https://api.deepseek.com/v1',
          api_key: encryptedDeepSeekKey,
          model_name: 'deepseek-chat',
          is_active: true,
          request_format: JSON.stringify({
            "messages": [{"role": "user", "content": "${prompt}"}],
            "temperature": 0.7,
            "max_tokens": 8192
          }),
          response_format: JSON.stringify({
            "path": "data.message.content"
          }),
          description: 'DeepSeek API服务，用于生成封面'
        },
        {
          service_name: 'xAI',
          base_url: 'https://api.xai.com',
          api_key: encryptedXaiKey,
          model_name: 'grok-1',
          is_active: false,
          request_format: JSON.stringify({
            "messages": [{"role": "user", "content": "${prompt}"}],
            "temperature": 0.7,
            "max_tokens": 8192
          }),
          response_format: JSON.stringify({
            "choices": [{"message": {"content": ""}}]
          }),
          description: 'xAI API服务，作为备选服务'
        }
      ]);

      logger.info('创建AI服务配置成功');
    }

    logger.info('数据库初始化完成');
    return true; // 返回成功标志
  } catch (error) {
    logger.error('初始化数据库失败:', error);
    return false; // 返回失败标志
  }
};

module.exports = initDatabase;

import axios from 'axios';
import { Modal } from 'antd';
import { useNavigate } from 'react-router-dom';

/**
 * 检查用户是否已登录
 * @returns {Object} { isLoggedIn, user }
 */
export const checkLogin = () => {
  const token = localStorage.getItem('token');
  const userStr = localStorage.getItem('user');
  
  if (!token || !userStr) {
    return { isLoggedIn: false, user: null };
  }

  try {
    const user = JSON.parse(userStr);
    return { isLoggedIn: true, user };
  } catch (error) {
    console.error('解析用户数据失败:', error);
    // 清除可能损坏的数据
    localStorage.removeItem('user');
    return { isLoggedIn: false, user: null };
  }
};

/**
 * 获取操作所需的积分数量
 * @param {string} operationType - 操作类型，如'generate_cover'
 * @returns {Promise<number>} - 操作所需的积分数量
 */
export const getOperationCost = async (operationType) => {
  try {
    const response = await axios.get(`/api/system/operation-cost/${operationType}`);
    
    if (response.data.success) {
      return response.data.data.points_cost;
    }
    
    throw new Error('获取积分消耗失败');
  } catch (error) {
    console.error(`获取${operationType}所需积分失败:`, error);
    // 返回默认值
    return operationType === 'generate_cover' ? 10 : 0;
  }
};

/**
 * 检查用户是否有足够的积分进行操作
 * @param {string} operationType - 操作类型，如'generate_cover'
 * @returns {Promise<Object>} - 检查结果
 */
export const checkPoints = async (operationType) => {
  const { isLoggedIn, user } = checkLogin();
  
  if (!isLoggedIn) {
    return { hasEnough: false, message: '请先登录', required: 0, userPoints: 0 };
  }
  
  try {
    const requiredPoints = await getOperationCost(operationType);
    const hasEnough = user.points >= requiredPoints;
    
    return {
      hasEnough,
      required: requiredPoints,
      userPoints: user.points,
      message: hasEnough ? '' : `积分不足，需要${requiredPoints}积分，当前仅有${user.points}积分`
    };
  } catch (error) {
    console.error('检查积分失败:', error);
    return { hasEnough: false, message: '检查积分失败', required: 0, userPoints: 0 };
  }
};

/**
 * 确认用户登录和积分状态
 * @param {string} operationType - 操作类型，如'generate_cover'
 * @param {Function} onSuccess - 成功回调函数
 * @returns {Promise<void>}
 */
export const confirmAuthAndPoints = async (operationType, onSuccess) => {
  const { isLoggedIn, user } = checkLogin();
  const navigate = useNavigate();
  
  if (!isLoggedIn) {
    Modal.confirm({
      title: '需要登录',
      content: '该操作需要消耗积分，请先登录或注册账号',
      okText: '去登录',
      cancelText: '取消',
      onOk: () => {
        navigate('/auth');
      }
    });
    return;
  }
  
  const pointsCheck = await checkPoints(operationType);
  
  if (!pointsCheck.hasEnough) {
    Modal.error({
      title: '积分不足',
      content: pointsCheck.message,
      okText: '去充值',
      onOk: () => {
        navigate('/profile');
      }
    });
    return;
  }
  
  // 登录状态和积分都正常，执行成功回调
  if (typeof onSuccess === 'function') {
    onSuccess();
  }
}; 
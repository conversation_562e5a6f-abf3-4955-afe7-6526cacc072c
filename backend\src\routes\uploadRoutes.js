const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { nanoid } = require('nanoid');
const logger = require('../utils/logger');

const router = express.Router();

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../public/uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置 multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = nanoid(12);
    const extension = path.extname(file.originalname);
    cb(null, `${file.fieldname}-${Date.now()}-${uniqueSuffix}${extension}`);
  }
});

const fileFilter = (req, file, cb) => {
  // 只接受图片文件
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型，请上传图片文件!'), false);
  }
};

const upload = multer({ 
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 限制文件大小为10MB
  }
});

/**
 * @swagger
 * /api/upload/image:
 *   post:
 *     summary: 上传图片文件
 *     description: 上传单个图片文件，成功后返回图片的URL
 *     tags: [Upload]
 *     consumes:
 *       - multipart/form-data
 *     parameters:
 *       - in: formData
 *         name: image
 *         type: file
 *         required: true
 *         description: 要上传的图片文件
 *     responses:
 *       200:
 *         description: 上传成功
 *         schema:
 *           type: object
 *           properties:
 *             success:
 *               type: boolean
 *               example: true
 *             message:
 *               type: string
 *               example: "文件上传成功"
 *             url:
 *               type: string
 *               example: "/uploads/image-162988293847-xxxx.png"
 *       400:
 *         description: 上传失败，例如文件类型不支持或文件过大
 *       500:
 *         description: 服务器内部错误
 */
router.post('/image', upload.single('image'), (req, res, next) => {
  if (!req.file) {
    return res.status(400).json({ success: false, message: '没有检测到文件上传' });
  }

  // 构建可访问的URL
  const fileUrl = `/uploads/${req.file.filename}`;
  
  logger.info(`图片上传成功: ${req.file.filename}, 访问地址: ${fileUrl}`);

  res.status(200).json({
    success: true,
    message: '文件上传成功',
    url: fileUrl
  });

}, (error, req, res, next) => {
  // 处理 multer 的特定错误
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ success: false, message: '文件过大，请上传10MB以下的图片' });
    }
    return res.status(400).json({ success: false, message: error.message });
  } else if (error) {
    // 处理文件类型过滤器的错误
    return res.status(400).json({ success: false, message: error.message });
  }
  next();
});

module.exports = router; 
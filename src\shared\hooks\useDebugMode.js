/**
 * 调试模式自定义Hook
 * 管理调试模式的状态和逻辑
 */
import { useState, useCallback, useEffect } from 'react';
import logger from '../../services/logs/frontendLogger';

/**
 * 调试模式Hook
 * @returns {Object} 包含调试模式状态和处理函数的对象
 */
const useDebugMode = () => {
  // 检查URL参数中的debug标志
  const getInitialDebugMode = () => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('debug') === 'true';
  };

  // 调试模式状态
  const [debugMode, setDebugMode] = useState(getInitialDebugMode);
  
  // 监听URL变化
  useEffect(() => {
    const handleUrlChange = () => {
      const newDebugMode = getInitialDebugMode();
      if (newDebugMode !== debugMode) {
        setDebugMode(newDebugMode);
        logger.info('调试模式:', newDebugMode ? '开启' : '关闭');
      }
    };

    // 监听popstate事件（浏览器前进后退）
    window.addEventListener('popstate', handleUrlChange);

    return () => {
      window.removeEventListener('popstate', handleUrlChange);
    };
  }, [debugMode]);

  // 处理调试模式切换
  const handleDebugModeChange = useCallback((checked) => {
    setDebugMode(checked);
    logger.info('调试模式:', checked ? '开启' : '关闭');
  }, []);

  return {
    debugMode,
    setDebugMode,
    handleDebugModeChange
  };
};

export default useDebugMode;

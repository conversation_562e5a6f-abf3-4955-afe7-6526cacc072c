const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });
const logger = require('../utils/logger');

// 从环境变量中获取数据库配置
const {
  DB_HOST,
  DB_PORT,
  DB_NAME,
  DB_USER,
  DB_PASSWORD,
  NODE_ENV
} = process.env;

// 根据环境决定日志记录行为
const getLoggingFunction = () => {
  // 生产环境只记录错误
  if (NODE_ENV === 'production') {
    return (msg) => {
      if (msg.includes('ERROR') || msg.includes('error') || msg.includes('failed')) {
        logger.error(msg);
      }
    };
  }
  
  // 测试环境不记录日志
  if (NODE_ENV === 'test') {
    return false;
  }
  
  // 开发环境记录重要操作，但过滤掉常见查询
  return (msg) => {
    // 过滤掉SELECT查询和SHOW查询的日志，减少日志冗余
    const shouldSkip = 
      msg.startsWith('Executing (default): SELECT') || 
      msg.startsWith('Executing (default): SHOW') ||
      msg.includes('SHOW INDEX FROM') ||
      msg.includes('SHOW FULL COLUMNS');
    
    // 记录重要操作和错误
    if ((!shouldSkip || msg.includes('ERROR')) && process.env.SQL_DEBUG === 'true') {
      logger.debug(msg);
    }
  };
};

// 创建Sequelize实例
const sequelize = new Sequelize(DB_NAME, DB_USER, DB_PASSWORD, {
  host: DB_HOST,
  port: DB_PORT,
  dialect: 'mysql',
  logging: getLoggingFunction(),
  pool: {
    max: 25,         // 增加最大连接数，支持更多并发
    min: 0,
    acquire: 30000,  // 获取连接超时时间
    idle: 10000,     // 连接空闲时间
    evict: 30000,    // 驱逐检查时间间隔
    handleDisconnects: true, // 自动处理断开连接
  },
  retry: {
    max: 3,         // 连接失败时最大重试次数
    match: [/Deadlock/i, /Lock wait timeout/i], // 匹配需要重试的错误
  },
  define: {
    timestamps: true,    // 自动添加 createdAt 和 updatedAt 字段
    underscored: true,   // 使用下划线命名约定
    charset: 'utf8mb4',  // 支持完整的Unicode字符集
    collate: 'utf8mb4_general_ci'
  },
  timezone: '+08:00'     // 设置时区为北京时间
});

module.exports = {
  sequelize,
  Sequelize
};

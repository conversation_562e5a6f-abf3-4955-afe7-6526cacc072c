'use strict';
const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 获取表信息
    const tableInfo = await queryInterface.describeTable('cover_records');

    // 添加自定义尺寸相关字段
    if (!tableInfo.custom_size_enabled) {
      await queryInterface.addColumn('cover_records', 'custom_size_enabled', {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
        comment: '是否启用自定义尺寸'
      });
    }

    if (!tableInfo.custom_width) {
      await queryInterface.addColumn('cover_records', 'custom_width', {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '自定义宽度'
      });
    }

    if (!tableInfo.custom_height) {
      await queryInterface.addColumn('cover_records', 'custom_height', {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: '自定义高度'
      });
    }

    // 添加自定义风格描述相关字段
    if (!tableInfo.custom_style_description) {
      await queryInterface.addColumn('cover_records', 'custom_style_description', {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: '用户自定义风格描述'
      });
    }

    if (!tableInfo.custom_style_enhanced) {
      await queryInterface.addColumn('cover_records', 'custom_style_enhanced', {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
        comment: '是否启用风格增强'
      });
    }

    // 添加自定义背景图/插图相关字段
    if (!tableInfo.custom_image_url) {
      await queryInterface.addColumn('cover_records', 'custom_image_url', {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: '用户上传的背景图/插图URL'
      });
    }

    if (!tableInfo.custom_image_type) {
      await queryInterface.addColumn('cover_records', 'custom_image_type', {
        type: DataTypes.ENUM('background', 'illustration'),
        allowNull: true,
        comment: '图片类型，背景或插图'
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 获取表信息
    const tableInfo = await queryInterface.describeTable('cover_records');

    // 移除自定义尺寸相关字段
    if (tableInfo.custom_size_enabled) {
      await queryInterface.removeColumn('cover_records', 'custom_size_enabled');
    }
    if (tableInfo.custom_width) {
      await queryInterface.removeColumn('cover_records', 'custom_width');
    }
    if (tableInfo.custom_height) {
      await queryInterface.removeColumn('cover_records', 'custom_height');
    }

    // 移除自定义风格描述相关字段
    if (tableInfo.custom_style_description) {
      await queryInterface.removeColumn('cover_records', 'custom_style_description');
    }
    if (tableInfo.custom_style_enhanced) {
      await queryInterface.removeColumn('cover_records', 'custom_style_enhanced');
    }

    // 移除自定义背景图/插图相关字段
    if (tableInfo.custom_image_url) {
      await queryInterface.removeColumn('cover_records', 'custom_image_url');
    }
    if (tableInfo.custom_image_type) {
      await queryInterface.removeColumn('cover_records', 'custom_image_type');
    }
  }
};

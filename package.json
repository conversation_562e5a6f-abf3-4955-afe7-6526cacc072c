{"name": "fengmian-frontend", "version": "1.0.0", "description": "封面生成网站前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.0.0", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.6", "antd": "^5.4.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.7", "dom-to-image-more": "^3.6.0", "dompurify": "^3.2.6", "express": "^5.1.0", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "lucide-react": "^0.507.0", "prop-types": "^15.8.1", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.9", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^6.10.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.3.0", "terser": "^5.41.0", "vite": "^4.4.9"}}
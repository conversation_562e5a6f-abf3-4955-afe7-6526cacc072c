/**
 * 封面记录管理模块
 * 依赖utils.js中的getAuthHeaders函数进行API请求认证
 */
window.coverRecordsModule = (function() {
  // 页面元素
  const $coverRecordsTable = document.getElementById('coverRecordsTable');
  const $coverRecordsPagination = document.getElementById('coverRecordsPagination');
  const $coverRecordKeyword = document.getElementById('coverRecordKeyword');
  const $coverRecordType = document.getElementById('coverRecordType');
  const $coverRecordStyle = document.getElementById('coverRecordStyle');
  const $coverRecordStartDate = document.getElementById('coverRecordStartDate');
  const $coverRecordEndDate = document.getElementById('coverRecordEndDate');
  const $searchCoverRecordsBtn = document.getElementById('searchCoverRecordsBtn');
  const $resetCoverRecordsBtn = document.getElementById('resetCoverRecordsBtn');
  const $coverHtmlPreviewModal = document.getElementById('coverHtmlPreviewModal');
  const $coverHtmlPreviewFrame = document.getElementById('coverHtmlPreviewFrame');
  const $coverPreviewInfo = document.getElementById('coverPreviewInfo');
  // Assume these elements exist in the HTML
  const $selectAllCheckbox = document.getElementById('selectAllCheckbox'); 
  const $batchDeleteBtn = document.getElementById('batchDeleteBtn');
  const $batchHideBtn = document.getElementById('batchHideBtn');
  const $batchShowBtn = document.getElementById('batchShowBtn');
  const $totalRecordsCount = document.getElementById('totalRecordsCount');
  const $sortOrderSelect = document.getElementById('sortOrderSelect');
  const $pageSizeSelect = document.getElementById('pageSizeSelect');

  // 分页信息
  let currentPage = 1;
  let totalPages = 1;
  let pageSize = 10; // 默认每页10条
  let sortOrder = 'desc'; // 默认时间降序
  let totalRecords = 0; // 记录总数
  let selectedRecordIds = new Set(); // Use a Set for efficient add/delete

  // 初始化
  function init() {
    loadCoverRecords();
    loadStyles();
    bindEvents();
  }

  // 绑定事件
  function bindEvents() {
    if ($searchCoverRecordsBtn) {
      $searchCoverRecordsBtn.addEventListener('click', function() {
        currentPage = 1;
        loadCoverRecords();
      });
    }

    if ($resetCoverRecordsBtn) {
      $resetCoverRecordsBtn.addEventListener('click', function() {
        $coverRecordKeyword.value = '';
        $coverRecordType.value = '';
        $coverRecordStyle.value = '';
        $coverRecordStartDate.value = '';
        $coverRecordEndDate.value = '';
        currentPage = 1;
        loadCoverRecords();
      });
    }

    if ($selectAllCheckbox) {
      $selectAllCheckbox.addEventListener('change', handleSelectAllChange);
    }

    if ($batchDeleteBtn) {
      $batchDeleteBtn.addEventListener('click', batchDeleteSelected);
    }

    if ($batchHideBtn) {
      $batchHideBtn.addEventListener('click', function() {
        batchUpdateStatusSelected('隐藏');
      });
    }

    if ($batchShowBtn) {
      $batchShowBtn.addEventListener('click', function() {
        batchUpdateStatusSelected('显示');
      });
    }

    // 添加排序和分页大小选择事件
    if ($sortOrderSelect) {
      $sortOrderSelect.addEventListener('change', function() {
        sortOrder = this.value;
        currentPage = 1;
        loadCoverRecords();
      });
    }

    if ($pageSizeSelect) {
      $pageSizeSelect.addEventListener('change', function() {
        pageSize = parseInt(this.value);
        currentPage = 1;
        loadCoverRecords();
      });
    }
  }

  // 加载风格列表
  function loadStyles() {
    // 获取认证令牌
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('未登录或会话已过期，请重新登录');
      return;
    }

    fetch('/api/cover/styles', {
      headers: getAuthHeaders()
    })
      .then(response => response.json())
      .then(data => {
        if (data.success && data.data.styles) {
          // 先清空除第一个"全部"选项外的所有选项
          while ($coverRecordStyle.options.length > 1) {
            $coverRecordStyle.remove(1);
          }

          // 添加风格选项
          data.data.styles.forEach(style => {
            const option = document.createElement('option');
            option.value = style.style_name;
            option.textContent = style.display_name || style.style_name;
            $coverRecordStyle.appendChild(option);
          });
        }
      })
      .catch(error => {
        console.error('获取风格列表失败:', error);
      });
  }

  // 加载封面记录
  function loadCoverRecords() {
    showLoading($coverRecordsTable);

    // 构建查询参数
    const params = new URLSearchParams();
    params.append('page', currentPage);
    params.append('limit', pageSize);
    params.append('sort_order', sortOrder);

    if ($coverRecordKeyword.value) {
      params.append('keyword', $coverRecordKeyword.value);
    }

    if ($coverRecordType.value) {
      params.append('cover_type', $coverRecordType.value);
    }

    if ($coverRecordStyle.value) {
      params.append('style', $coverRecordStyle.value);
    }

    if ($coverRecordStartDate.value) {
      params.append('start_date', $coverRecordStartDate.value);
    }

    if ($coverRecordEndDate.value) {
      params.append('end_date', $coverRecordEndDate.value);
    }

    // 获取认证令牌
    const token = localStorage.getItem('token');
    if (!token) {
      showError($coverRecordsTable, '未登录或会话已过期，请重新登录');
      return;
    }

    // 发送请求 - 使用公共的getAuthHeaders函数
    fetch(`/api/admin/cover-records?${params.toString()}`, {
      headers: getAuthHeaders()
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          totalRecords = data.pagination?.total || 0;
          updateTotalRecordsDisplay();
          renderCoverRecords(data.data);
          renderPagination(data.pagination);
        } else {
          showError($coverRecordsTable, data.message || '获取封面记录失败');
        }
      })
      .catch(error => {
        console.error('获取封面记录失败:', error);
        showError($coverRecordsTable, '获取封面记录失败');
      });
  }

  // 渲染封面记录
  function renderCoverRecords(records) {
    const tbody = $coverRecordsTable.querySelector('tbody');
    tbody.innerHTML = ''; // 清空现有内容

    // 确保表格有基本样式类
    if (!$coverRecordsTable.classList.contains('table')) {
        $coverRecordsTable.classList.add('table', 'table-striped', 'table-bordered'); 
    }

    // 更新/创建表头 (如果不存在)
    let thead = $coverRecordsTable.querySelector('thead');
    if (!thead) {
        thead = document.createElement('thead');
        $coverRecordsTable.insertBefore(thead, tbody);
    }
    let headerRow = thead.querySelector('tr');
    if (!headerRow) {
        headerRow = document.createElement('tr');
        thead.appendChild(headerRow);
    } else {
        // If headerRow already exists, still ensure its content is correct
        // This handles cases where the structure might be altered externally or needs refresh
        headerRow.innerHTML = `
            <th class="col-checkbox"><input type="checkbox" id="selectAllCheckbox"></th>
            <th class="col-sequence">序号</th>
            <th>封面编码</th>
            <th>用户手机</th>
            <th>角色</th>
            <th>昵称</th>
            <th>封面类型</th>
            <th>记录类型</th>
            <th>风格</th>
            <th>生成时间</th>
            <th>状态</th>
            <th>操作</th> 
        `;
        // Re-bind selectAllCheckbox event after resetting innerHTML
        const existingSelectAllCheckbox = headerRow.querySelector('#selectAllCheckbox');
        if (existingSelectAllCheckbox) {
            existingSelectAllCheckbox.removeEventListener('change', handleSelectAllChange);
            existingSelectAllCheckbox.addEventListener('change', handleSelectAllChange);
        }
    }
    // Define headers matching the 11 columns in tbody
    // Set innerHTML every time to ensure consistency
    headerRow.innerHTML = `
        <th class="col-checkbox"><input type="checkbox" id="selectAllCheckbox"></th>
        <th class="col-sequence">序号</th>
        <th>封面编码</th>
        <th>用户手机</th>
        <th>角色</th>
        <th>昵称</th>
        <th>封面类型</th>
        <th>记录类型</th>
        <th>风格</th>
        <th>生成时间</th>
        <th>状态</th>
        <th>操作</th> 
    `;
    // Re-bind selectAllCheckbox event if header is potentially recreated or cleared/repopulated
    const newSelectAllCheckbox = headerRow.querySelector('#selectAllCheckbox');
    if (newSelectAllCheckbox) {
        // Remove previous listener to avoid duplicates if any
        // Note: This simple add/remove might need refinement if complex scenarios arise
        newSelectAllCheckbox.removeEventListener('change', handleSelectAllChange); 
        newSelectAllCheckbox.addEventListener('change', handleSelectAllChange);
    }

    if (!records || records.length === 0) {
      const rowCount = headerRow.children.length;
      tbody.innerHTML = `<tr><td colspan="${rowCount}" class="text-center">没有找到记录</td></tr>`;
      return;
    }

    // 渲染记录行
    records.forEach((record, index) => {
      const tr = document.createElement('tr');

      // 1. 复选框
      const checkboxTd = document.createElement('td');
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.className = 'record-checkbox';
      checkbox.value = record.id;
      checkbox.checked = selectedRecordIds.has(record.id);
      checkbox.addEventListener('change', handleSingleCheckboxChange);
      checkboxTd.appendChild(checkbox);
      tr.appendChild(checkboxTd);

      // 2. 序号
      const sequenceTd = document.createElement('td');
      sequenceTd.textContent = record.row_num; // 使用后端传来的序号
      sequenceTd.className = 'text-center';
      tr.appendChild(sequenceTd);

      // 3. 封面编码 (ID)
      const codeTd = document.createElement('td');
      codeTd.textContent = record.cover_code || '-'; // Display cover_code
      codeTd.style.wordBreak = 'break-all'; // Prevent long codes from breaking layout
      tr.appendChild(codeTd);

      // 4. 用户手机 (用户名)
      const phoneTd = document.createElement('td');
      phoneTd.textContent = record.user?.phone || '-';
      tr.appendChild(phoneTd);

      // 5. 角色
      const roleTd = document.createElement('td');
      roleTd.textContent = record.user?.role || '-';
      tr.appendChild(roleTd);

      // 6. 昵称
      const nicknameTd = document.createElement('td');
      nicknameTd.textContent = record.user?.nickname || '-';
      tr.appendChild(nicknameTd);

      // 7. 封面类型
      const typeTd = document.createElement('td');
      typeTd.textContent = record.cover_type_name || record.cover_type || '-';
      tr.appendChild(typeTd);

      // 8. 记录类型
      const recordTypeTd = document.createElement('td');
      recordTypeTd.textContent = record.record_type || 'AI生成';
      tr.appendChild(recordTypeTd);

      // 9. 风格
      const styleTd = document.createElement('td');
      styleTd.textContent = record.cover_style_name || record.cover_style || '-';
      tr.appendChild(styleTd);

      // 10. 生成时间
      const timeTd = document.createElement('td');
      // 改进时间处理逻辑，确保能够处理多种可能的时间格式
      let createdAt = '';
      // 同时兼容后端返回的created_at和createdAt字段
      const rawTime = record.created_at || record.createdAt;
      if (rawTime) {
        try {
          // 尝试转换为日期对象
          const dateObj = new Date(rawTime);
          // 检查是否得到了有效的日期
          if (!isNaN(dateObj.getTime())) {
            createdAt = dateObj.toLocaleString('zh-CN');
          } else {
            // 如果无法转换为有效日期，但仍有值，则直接显示
            createdAt = String(rawTime);
          }
        } catch (e) {
          // 如果发生异常，尝试直接显示原始值
          console.error('日期格式化错误:', e);
          createdAt = String(rawTime);
        }
      } else {
        createdAt = '未知';
      }
      timeTd.textContent = createdAt;
      tr.appendChild(timeTd);

      // 11. 状态 (Apply badge styling)
      const statusTd = document.createElement('td');
      const statusBadge = document.createElement('span');
      statusBadge.textContent = record.status || '-';
      statusBadge.classList.add('badge'); // Add base badge class
      if (record.status === '显示') {
          statusBadge.classList.add('bg-success');
      } else if (record.status === '隐藏') {
          statusBadge.classList.add('bg-danger');
      } else {
          statusBadge.classList.add('bg-secondary'); // Default for other statuses
      }
      statusTd.appendChild(statusBadge);
      tr.appendChild(statusTd);

      // 12. 操作 (Use solid button colors)
      const actionsTd = document.createElement('td');
      actionsTd.className = 'text-nowrap'; // Prevent wrapping for buttons
      actionsTd.innerHTML = `
          <button class="btn btn-sm btn-info me-1" onclick="coverRecordsModule.previewCover(${record.id})" title="预览">预览</button>
          <button class="btn btn-sm btn-warning me-1" onclick="coverRecordsModule.toggleStatus(${record.id}, '${record.status === '显示' ? '隐藏' : '显示'}')" title="切换状态">${record.status === '显示' ? '隐藏' : '显示'}</button>
          <button class="btn btn-sm btn-danger" onclick="coverRecordsModule.deleteCover(${record.id})" title="删除">删除</button>
      `;
      tr.appendChild(actionsTd);

      tbody.appendChild(tr);
    });

    updateSelectionState(); // Update select all checkbox state after rendering
  }

  // 预览封面
  function previewCover(coverId) {
    // 获取认证令牌
    const token = localStorage.getItem('token');
    if (!token) {
      alert('未登录或会话已过期，请重新登录');
      return;
    }

    fetch(`/api/admin/covers/${coverId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })
      .then(response => response.json())
      .then(data => {
        console.log('封面详情数据:', data); // 添加日志以便调试
        if (data.success) {
          // 兼容两种可能的数据结构
          const record = data.data.cover || data.data;

          // 格式化日期显示
          let createdAt = '';
          // 同时兼容后端返回的created_at和createdAt字段
          const rawTime = record.created_at || record.createdAt;
          if (rawTime) {
            try {
              // 尝试转换为日期对象
              const dateObj = new Date(rawTime);
              // 检查是否得到了有效的日期
              if (!isNaN(dateObj.getTime())) {
                createdAt = dateObj.toLocaleString('zh-CN');
              } else {
                // 如果无法转换为有效日期，但仍有值，则直接显示
                createdAt = String(rawTime);
              }
            } catch (e) {
              // 如果发生异常，尝试直接显示原始值
              console.error('日期格式化错误:', e);
              createdAt = String(rawTime);
            }
          } else {
            createdAt = '未知';
          }

          // 格式化用户信息
          const userDisplay = record.nickname ?
            `${record.phone || ''} (${record.nickname})` :
            record.phone || '未知用户';

          // 转换封面类型
          const coverType = record.cover_type === 'wechat' ? '微信公众号' :
                          record.cover_type === 'xiaohongshu' ? '小红书' :
                          record.cover_type;

          // 使用 display_name 显示风格名称，如果没有则使用 cover_style
          const styleDisplayName = record.cover_style_name || record.cover_style;

          // 填充预览信息
          $coverPreviewInfo.innerHTML = `
            <p><strong>用户手机:</strong> ${record.user?.phone || '-'}</p>
            <p><strong>角色:</strong> ${record.user?.role || '-'}</p>
            <p><strong>昵称:</strong> ${record.user?.nickname || '-'}</p>
            <p><strong>封面类型:</strong> ${record.cover_type_name || record.cover_type || '-'}</p>
            <p><strong>记录类型:</strong> ${record.record_type || 'AI生成'}</p>
            <p><strong>风格:</strong> ${record.cover_style_name || record.cover_style || '-'}</p>
            <p><strong>生成时间:</strong> ${createdAt}</p>
          `;

          // 获取HTML内容
          const htmlContent = record.html_content || '';

          // 获取编辑后的HTML内容
          const editedHtmlContent = record.edited_html_content || '';
          const hasEditedHtml = !!editedHtmlContent;

          // 添加HTML源码和下载按钮
          if (!document.getElementById('htmlSourceTab')) {
            // 创建HTML源码标签
            const tabsHTML = `
              <ul class="nav nav-tabs mt-3" role="tablist">
                <li class="nav-item" role="presentation">
                  <button class="nav-link active" id="preview-tab" data-bs-toggle="tab" data-bs-target="#preview-content" type="button" role="tab" aria-controls="preview-content" aria-selected="true">
                    <i class="bi bi-eye"></i> 预览效果
                  </button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="html-source-tab" data-bs-toggle="tab" data-bs-target="#html-source" type="button" role="tab" aria-controls="html-source" aria-selected="false">
                    <i class="bi bi-code-slash"></i> HTML源码
                  </button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="original-html-tab" data-bs-toggle="tab" data-bs-target="#original-html" type="button" role="tab" aria-controls="original-html" aria-selected="false">
                    <i class="bi bi-file-code"></i> 原始HTML
                  </button>
                </li>
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="prompt-tab" data-bs-toggle="tab" data-bs-target="#prompt-content" type="button" role="tab" aria-controls="prompt-content" aria-selected="false">
                    <i class="bi bi-chat-quote"></i> 提示词
                  </button>
                </li>
                ${hasEditedHtml ? `
                <li class="nav-item" role="presentation">
                  <button class="nav-link" id="edited-html-tab" data-bs-toggle="tab" data-bs-target="#edited-html" type="button" role="tab" aria-controls="edited-html" aria-selected="false">
                    <i class="bi bi-pencil-square"></i> 编辑后HTML
                  </button>
                </li>
                ` : ''}
              </ul>
              <div class="tab-content">
                <div class="tab-pane fade show active" id="preview-content" role="tabpanel" aria-labelledby="preview-tab">
                  <div id="iframe-container" style="height: 400px; border: 1px solid #ddd; margin-top: 10px;"></div>
                </div>
                <div class="tab-pane fade" id="html-source" role="tabpanel" aria-labelledby="html-source-tab">
                  <div class="d-flex justify-content-end mt-1 mb-1">
                    <button class="btn btn-sm btn-secondary me-2" id="copyHtmlBtn">
                      <i class="bi bi-clipboard"></i> 复制源码
                    </button>
                    <button class="btn btn-sm btn-primary" id="downloadHtmlBtn">
                      <i class="bi bi-download"></i> 下载HTML
                    </button>
                  </div>
                  <pre id="html-source-code" style="height: 360px; overflow: auto; border: 1px solid #ddd; padding: 10px; background-color: #f8f9fa; white-space: pre-wrap; word-wrap: break-word; word-break: break-all;"></pre>
                </div>
                <div class="tab-pane fade" id="original-html" role="tabpanel" aria-labelledby="original-html-tab">
                  <div class="d-flex justify-content-end mt-1 mb-1">
                    <button class="btn btn-sm btn-secondary me-2" id="copyOriginalHtmlBtn">
                      <i class="bi bi-clipboard"></i> 复制原始源码
                    </button>
                    <button class="btn btn-sm btn-primary" id="downloadOriginalHtmlBtn">
                      <i class="bi bi-download"></i> 下载原始HTML
                    </button>
                  </div>
                  <pre id="original-html-source-code" style="height: 360px; overflow: auto; border: 1px solid #ddd; padding: 10px; background-color: #f8f9fa; white-space: pre-wrap; word-wrap: break-word; word-break: break-all;"></pre>
                </div>
                <div class="tab-pane fade" id="prompt-content" role="tabpanel" aria-labelledby="prompt-tab">
                  <div class="d-flex justify-content-end mt-1 mb-1">
                    <button class="btn btn-sm btn-secondary" id="copyPromptBtn">
                      <i class="bi bi-clipboard"></i> 复制提示词
                    </button>
                  </div>
                  <pre id="prompt-content-code" style="height: 360px; overflow: auto; border: 1px solid #ddd; padding: 10px; background-color: #f8f9fa; white-space: pre-wrap; word-wrap: break-word; word-break: break-all;"></pre>
                </div>
                ${hasEditedHtml ? `
                <div class="tab-pane fade" id="edited-html" role="tabpanel" aria-labelledby="edited-html-tab">
                  <div class="d-flex justify-content-end mt-1 mb-1">
                    <button class="btn btn-sm btn-secondary me-2" id="copyEditedHtmlBtn">
                      <i class="bi bi-clipboard"></i> 复制源码
                    </button>
                    <button class="btn btn-sm btn-primary" id="downloadEditedHtmlBtn">
                      <i class="bi bi-download"></i> 下载HTML
                    </button>
                  </div>
                  <div id="edited-iframe-container" style="height: 360px; border: 1px solid #ddd; margin-top: 10px;"></div>
                </div>
                ` : ''}
              </div>
            `;

            try {
              // 清空modal内容并重新构建
              const modalBody = $coverHtmlPreviewModal.querySelector('.modal-body');
              // 保存原有的信息部分内容
              const infoContent = $coverPreviewInfo.innerHTML;
              
              // 清空modal body内容
              modalBody.innerHTML = '';
              
              // 重新创建信息区
              const infoDiv = document.createElement('div');
              infoDiv.id = 'coverPreviewInfo';
              infoDiv.innerHTML = infoContent;
              modalBody.appendChild(infoDiv);
              
              // 添加标签和内容
              const tabsDiv = document.createElement('div');
              tabsDiv.innerHTML = tabsHTML;
              modalBody.appendChild(tabsDiv);
              
              // 重新获取引用 - 使用let而不是重新赋值给const变量
              let previewInfoElement = document.getElementById('coverPreviewInfo');
              if (previewInfoElement) {
                // 只有当元素存在时才更新引用
                window.$coverPreviewInfo = previewInfoElement;
              }
              
              // 创建新的iframe元素
              const newFrame = document.createElement('iframe');
              newFrame.id = 'coverHtmlPreviewFrame';
              newFrame.style.width = '100%';
              newFrame.style.height = '100%';
              newFrame.style.border = 'none';
              newFrame.srcdoc = htmlContent;
              
              // 将iframe放入新容器
              const iframeContainer = document.getElementById('iframe-container');
              if (iframeContainer) {
                iframeContainer.appendChild(newFrame);
                
                // 更新引用 - 使用let而不是重新赋值给const变量
                let frameElement = document.getElementById('coverHtmlPreviewFrame');
                if (frameElement) {
                  // 只有当元素存在时才更新引用
                  window.$coverHtmlPreviewFrame = frameElement;
                }
              }

              // 设置HTML源码内容
              const sourceCodeElement = document.getElementById('html-source-code');
              if (sourceCodeElement) {
                sourceCodeElement.textContent = htmlContent;
              }
              
              // 设置原始HTML源码内容
              const originalSourceCodeElement = document.getElementById('original-html-source-code');
              if (originalSourceCodeElement) {
                const originalHtmlContent = record.original_html_content || htmlContent;
                originalSourceCodeElement.textContent = originalHtmlContent;
              }
              
              // 设置提示词内容
              const promptContentElement = document.getElementById('prompt-content-code');
              if (promptContentElement) {
                // 获取提示词 - 优先使用API返回的final_prompt字段，如果没有则组合基础提示词和风格提示词
                let finalPrompt = record.final_prompt || record.prompt_used || '';
                
                // 如果没有最终提示词，尝试从模板和风格组合
                if (!finalPrompt && (record.base_prompt || record.style_prompt)) {
                  finalPrompt = `${record.base_prompt || ''}\n${record.style_prompt || ''}`;
                }
                
                promptContentElement.textContent = finalPrompt || '未找到提示词信息';
              }

              // 为复制按钮添加事件监听
              const copyHtmlBtn = document.getElementById('copyHtmlBtn');
              if (copyHtmlBtn) {
                copyHtmlBtn.addEventListener('click', function() {
                  navigator.clipboard.writeText(htmlContent)
                    .then(() => alert('HTML源码已复制到剪贴板'))
                    .catch(err => console.error('复制失败:', err));
                });
              }
              
              // 为复制提示词按钮添加事件监听
              const copyPromptBtn = document.getElementById('copyPromptBtn');
              if (copyPromptBtn) {
                copyPromptBtn.addEventListener('click', function() {
                  const promptText = document.getElementById('prompt-content-code').textContent;
                  navigator.clipboard.writeText(promptText)
                    .then(() => alert('提示词已复制到剪贴板'))
                    .catch(err => console.error('复制失败:', err));
                });
              }
              
              // 为复制原始HTML按钮添加事件监听
              const copyOriginalHtmlBtn = document.getElementById('copyOriginalHtmlBtn');
              if (copyOriginalHtmlBtn) {
                copyOriginalHtmlBtn.addEventListener('click', function() {
                  const originalHtmlContent = record.original_html_content || htmlContent;
                  navigator.clipboard.writeText(originalHtmlContent)
                    .then(() => alert('原始HTML源码已复制到剪贴板'))
                    .catch(err => console.error('复制失败:', err));
                });
              }

              // 添加下载按钮事件
              if (document.getElementById('downloadHtmlBtn')) {
                document.getElementById('downloadHtmlBtn').addEventListener('click', function() {
                  const blob = new Blob([htmlContent], {type: 'text/html'});
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `封面_${coverId}_${record.cover_type}_${record.cover_style}.html`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                });
              }
              
              // 添加下载原始HTML按钮事件
              if (document.getElementById('downloadOriginalHtmlBtn')) {
                document.getElementById('downloadOriginalHtmlBtn').addEventListener('click', function() {
                  const originalHtmlContent = record.original_html_content || htmlContent;
                  const blob = new Blob([originalHtmlContent], {type: 'text/html'});
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `原始封面_${coverId}_${record.cover_type}_${record.cover_style}.html`;
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                });
              }

              // 如果有编辑后的HTML内容，添加复制和下载功能
              if (hasEditedHtml) {
                // 添加复制编辑后HTML按钮事件
                if (document.getElementById('copyEditedHtmlBtn')) {
                  document.getElementById('copyEditedHtmlBtn').addEventListener('click', function() {
                    navigator.clipboard.writeText(editedHtmlContent)
                      .then(() => alert('编辑后HTML源码已复制到剪贴板'))
                      .catch(err => console.error('复制失败:', err));
                  });
                }
                
                // 添加下载编辑后HTML按钮事件
                if (document.getElementById('downloadEditedHtmlBtn')) {
                  document.getElementById('downloadEditedHtmlBtn').addEventListener('click', function() {
                    const blob = new Blob([editedHtmlContent], {type: 'text/html'});
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `编辑后封面_${coverId}_${record.cover_type}_${record.cover_style}.html`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  });
                }
                
                // 创建编辑后HTML的iframe
                const editedIframeContainer = document.getElementById('edited-iframe-container');
                if (editedIframeContainer) {
                  // 清空容器
                  editedIframeContainer.innerHTML = '';
                  
                  // 创建新的iframe元素
                  const editedFrame = document.createElement('iframe');
                  editedFrame.id = 'editedHtmlPreviewFrame';
                  editedFrame.style.width = '100%';
                  editedFrame.style.height = '100%';
                  editedFrame.style.border = 'none';
                  editedFrame.srcdoc = editedHtmlContent;
                  
                  // 将iframe添加到容器
                  editedIframeContainer.appendChild(editedFrame);
                }
              }

              // 更新iframe内容
              const iframeElement = document.getElementById('coverHtmlPreviewFrame');
              if (iframeElement) {
                iframeElement.srcdoc = htmlContent;
              }
              
              // 更新编辑后的iframe内容（如果有）
              if (hasEditedHtml) {
                // 获取或创建编辑后HTML的iframe
                let editedIframeElement = document.getElementById('editedHtmlPreviewFrame');
                if (!editedIframeElement) {
                  const editedIframeContainer = document.getElementById('edited-iframe-container');
                  if (editedIframeContainer) {
                    // 清空容器
                    editedIframeContainer.innerHTML = '';
                    
                    // 创建新的iframe元素
                    editedIframeElement = document.createElement('iframe');
                    editedIframeElement.id = 'editedHtmlPreviewFrame';
                    editedIframeElement.style.width = '100%';
                    editedIframeElement.style.height = '100%';
                    editedIframeElement.style.border = 'none';
                    
                    // 将iframe添加到容器
                    editedIframeContainer.appendChild(editedIframeElement);
                  }
                }
                
                // 设置iframe内容
                if (editedIframeElement) {
                  editedIframeElement.srcdoc = editedHtmlContent;
                }
              }

              // 添加"在新窗口打开"按钮到模态框底部
              // 获取模态框底部元素
              let modalFooter = $coverHtmlPreviewModal.querySelector('.modal-footer');
              
              // 如果底部不存在，创建一个
              if (!modalFooter) {
                modalFooter = document.createElement('div');
                modalFooter.className = 'modal-footer';
                const modalContent = $coverHtmlPreviewModal.querySelector('.modal-content');
                if (modalContent) {
                  modalContent.appendChild(modalFooter);
                }
              }
              
              // 清空底部内容
              modalFooter.innerHTML = '';
              
              // 添加关闭按钮
              const closeButton = document.createElement('button');
              closeButton.type = 'button';
              closeButton.className = 'btn btn-secondary';
              closeButton.setAttribute('data-bs-dismiss', 'modal');
              closeButton.textContent = '关闭';
              modalFooter.appendChild(closeButton);
              
              // 添加新窗口打开按钮
              const openWindowButton = document.createElement('button');
              openWindowButton.type = 'button';
              openWindowButton.className = 'btn btn-primary';
              openWindowButton.textContent = '在新窗口打开';
              openWindowButton.addEventListener('click', function() {
                const previewWindow = window.open('', '_blank');
                if (previewWindow) {
                  previewWindow.document.write(htmlContent);
                  previewWindow.document.close();
                } else {
                  console.error('无法打开预览窗口，可能被浏览器阻止');
                  alert('无法打开预览窗口，请检查浏览器是否阻止弹出窗口');
                }
              });
              modalFooter.appendChild(openWindowButton);

              // 显示模态框
              const modal = new bootstrap.Modal($coverHtmlPreviewModal);
              modal.show();
            } catch (e) {
              console.error('DOM操作失败:', e);
              // 如果出错，使用简单方式显示iframe
              try {
                const modalBody = $coverHtmlPreviewModal.querySelector('.modal-body');
                if (modalBody) {
                  modalBody.innerHTML = `
                    <div id="coverPreviewInfo">${$coverPreviewInfo.innerHTML}</div>
                    <div style="height: 500px; border: 1px solid #ddd; margin-top: 10px;">
                      <iframe id="coverHtmlPreviewFrame" style="width:100%; height:100%; border:none;" srcdoc="${htmlContent.replace(/"/g, '&quot;')}"></iframe>
                    </div>
                  `;
                  
                  // 更新引用
                  let previewInfoElement = document.getElementById('coverPreviewInfo');
                  let frameElement = document.getElementById('coverHtmlPreviewFrame');
                  
                  if (previewInfoElement) window.$coverPreviewInfo = previewInfoElement;
                  if (frameElement) window.$coverHtmlPreviewFrame = frameElement;
                }
              } catch (err) {
                console.error('备用DOM操作也失败:', err);
                message.error('预览加载失败，请刷新页面重试');
              }
            }
          } else {
            // 如果标签已存在，更新原始源码内容
            const sourceCodeElem = document.getElementById('html-source-code');
            if (sourceCodeElem) {
              sourceCodeElem.textContent = htmlContent;
            }
            
            // 设置提示词内容
            const promptContentElement = document.getElementById('prompt-content-code');
            if (promptContentElement) {
              // 获取提示词 - 优先使用API返回的final_prompt字段，如果没有则组合基础提示词和风格提示词
              let finalPrompt = record.final_prompt || record.prompt_used || '';
              
              // 如果没有最终提示词，尝试从模板和风格组合
              if (!finalPrompt && (record.base_prompt || record.style_prompt)) {
                finalPrompt = `${record.base_prompt || ''}\n${record.style_prompt || ''}`;
              }
              
              promptContentElement.textContent = finalPrompt || '未找到提示词信息';
            }

            // 更新iframe内容
            const iframeElement = document.getElementById('coverHtmlPreviewFrame');
            if (iframeElement) {
              iframeElement.srcdoc = htmlContent;
            }
            
            // 更新编辑后的iframe内容（如果有）
            if (hasEditedHtml) {
              // 获取或创建编辑后HTML的iframe
              let editedIframeElement = document.getElementById('editedHtmlPreviewFrame');
              if (!editedIframeElement) {
                const editedIframeContainer = document.getElementById('edited-iframe-container');
                if (editedIframeContainer) {
                  // 清空容器
                  editedIframeContainer.innerHTML = '';
                  
                  // 创建新的iframe元素
                  editedIframeElement = document.createElement('iframe');
                  editedIframeElement.id = 'editedHtmlPreviewFrame';
                  editedIframeElement.style.width = '100%';
                  editedIframeElement.style.height = '100%';
                  editedIframeElement.style.border = 'none';
                  
                  // 将iframe添加到容器
                  editedIframeContainer.appendChild(editedIframeElement);
                }
              }
              
              // 设置iframe内容
              if (editedIframeElement) {
                editedIframeElement.srcdoc = editedHtmlContent;
              }
            }
          }
        } else {
          alert(data.message || '获取封面详情失败');
        }
      })
      .catch(error => {
        console.error('获取封面详情失败:', error);
        alert('获取封面详情失败');
      });
  }

  // 切换状态
  function toggleStatus(coverId, status) {
    // 获取认证令牌
    const token = localStorage.getItem('token');
    if (!token) {
      alert('未登录或会话已过期，请重新登录');
      return;
    }

    // 显示确认对话框
    if (!confirm(`确定要${status === '显示' ? '隐藏' : '显示'}该封面记录吗？`)) {
      return;
    }

    fetch(`/api/admin/cover-records/${coverId}/status`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status: status })
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('状态切换成功');
          loadCoverRecords();
        } else {
          alert(data.message || '状态切换失败');
        }
      })
      .catch(error => {
        console.error('状态切换失败:', error);
        alert('状态切换失败');
      });
  }

  // 删除封面
  function deleteCover(coverId) {
    // 获取认证令牌
    const token = localStorage.getItem('token');
    if (!token) {
      alert('未登录或会话已过期，请重新登录');
      return;
    }

    // 显示确认对话框
    if (!confirm('确定要删除该封面记录吗？此操作将永久删除该记录，无法恢复！')) {
      return;
    }

    fetch(`/api/admin/cover-records/${coverId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('删除成功');
          loadCoverRecords();
        } else {
          alert(data.message || '删除失败');
        }
      })
      .catch(error => {
        console.error('删除失败:', error);
        alert('删除失败');
      });
  }

  // 渲染分页控件
  function renderPagination(pagination) {
    if (!pagination) return;

    totalPages = pagination.total_pages;
    currentPage = pagination.page;

    $coverRecordsPagination.innerHTML = '';

    // 如果只有一页，不显示分页
    if (totalPages <= 1) return;

    // 上一页按钮
    const prevLi = document.createElement('li');
    prevLi.className = `page-item${currentPage === 1 ? ' disabled' : ''}`;
    prevLi.innerHTML = '<a class="page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>';
    if (currentPage > 1) {
      prevLi.addEventListener('click', function(e) {
        e.preventDefault();
        goToPage(currentPage - 1);
      });
    }
    $coverRecordsPagination.appendChild(prevLi);

    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);

    for (let i = startPage; i <= endPage; i++) {
      const pageLi = document.createElement('li');
      pageLi.className = `page-item${i === currentPage ? ' active' : ''}`;
      pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;

      if (i !== currentPage) {
        pageLi.addEventListener('click', function(e) {
          e.preventDefault();
          goToPage(i);
        });
      }

      $coverRecordsPagination.appendChild(pageLi);
    }

    // 下一页按钮
    const nextLi = document.createElement('li');
    nextLi.className = `page-item${currentPage === totalPages ? ' disabled' : ''}`;
    nextLi.innerHTML = '<a class="page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>';
    if (currentPage < totalPages) {
      nextLi.addEventListener('click', function(e) {
        e.preventDefault();
        goToPage(currentPage + 1);
      });
    }
    $coverRecordsPagination.appendChild(nextLi);
  }

  // 跳转到指定页
  function goToPage(page) {
    currentPage = page;
    loadCoverRecords();
  }

  // 显示加载中
  function showLoading(table) {
    const tbody = table.querySelector('tbody');
    // Adjust colspan for the new columns (checkbox + sequence + original columns)
    const columnCount = table.querySelector('thead tr').children.length;
    tbody.innerHTML = `<tr><td colspan="${columnCount}" class="text-center">加载中...</td></tr>`;
  }

  // 显示错误
  function showError(table, message) {
    const tbody = table.querySelector('tbody');
    // Adjust colspan for the new columns
    const columnCount = table.querySelector('thead tr').children.length;
    tbody.innerHTML = `<tr><td colspan="${columnCount}" class="text-center text-danger">${message}</td></tr>`;
  }

  // 监听页面导航事件
  document.addEventListener('pageNavigation', function(e) {
    if (e.detail.page === 'cover-records') {
      // 当切换到封面记录页面时刷新数据
      currentPage = 1;
      loadCoverRecords();
    }
  });

  // 处理单个复选框变化
  function handleSingleCheckboxChange(event) {
    const checkbox = event.target;
    const recordId = parseInt(checkbox.value);
    if (checkbox.checked) {
      selectedRecordIds.add(recordId);
    } else {
      selectedRecordIds.delete(recordId);
    }
    updateSelectionState();
  }

  // 处理全选复选框变化
  function handleSelectAllChange(event) {
    const isChecked = event.target.checked;
    const checkboxes = $coverRecordsTable.querySelectorAll('tbody .record-checkbox');
    checkboxes.forEach(checkbox => {
      const recordId = parseInt(checkbox.value);
      checkbox.checked = isChecked;
      if (isChecked) {
        selectedRecordIds.add(recordId);
      } else {
        selectedRecordIds.delete(recordId);
      }
    });
    // Note: This only selects/deselects visible rows on the current page.
    // True batch operations across pages might require server-side state.
  }

  // 更新全选框状态和批量按钮可见性
  function updateSelectionState() {
    const tbody = $coverRecordsTable.querySelector('tbody');
    const hasSelected = selectedRecordIds.size > 0;
    
    // 保持批量操作按钮始终显示，不再切换显示/隐藏
    // 更新全选框状态
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    if (selectAllCheckbox) {
      const totalCheckboxes = tbody.querySelectorAll('input[type="checkbox"]').length;
      selectAllCheckbox.checked = hasSelected && selectedRecordIds.size === totalCheckboxes;
      selectAllCheckbox.indeterminate = hasSelected && selectedRecordIds.size < totalCheckboxes;
    }
  }

  // 批量删除选中的记录
  function batchDeleteSelected() {
    if (selectedRecordIds.size === 0) {
      alert('请先选择要删除的记录');
      return;
    }
    if (!confirm(`确定要删除选中的 ${selectedRecordIds.size} 条记录吗？此操作将永久删除该记录，无法恢复。`)) {
      return;
    }

    const token = localStorage.getItem('token');
    if (!token) {
      alert('认证失败，请重新登录');
      return;
    }

    const idsToDelete = Array.from(selectedRecordIds);

    fetch('/api/admin/cover-records/batch', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ ids: idsToDelete })
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert(data.message || `成功删除 ${idsToDelete.length} 条记录`);
          selectedRecordIds.clear();
          loadCoverRecords(); // Reload data to reflect changes
        } else {
          alert(`批量删除失败: ${data.message || '未知错误'}`);
        }
      })
      .catch(error => {
        console.error('批量删除 API 调用失败:', error);
        alert('批量删除过程中发生网络或服务器错误');
      });
  }

  function batchUpdateStatusSelected(status) {
    if (selectedRecordIds.size === 0) {
      alert(`请先选择要${status}的记录`);
      return;
    }
    if (!confirm(`确定要将选中的 ${selectedRecordIds.size} 条记录状态更新为 "${status}" 吗？`)) {
      return;
    }

    const token = localStorage.getItem('token');
    if (!token) {
      alert('认证失败，请重新登录');
      return;
    }

    const idsToUpdate = Array.from(selectedRecordIds);

    fetch('/api/admin/cover-records/batch/status', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ ids: idsToUpdate, status: status })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (data.success) {
        alert(data.message || `成功将 ${idsToUpdate.length} 条记录状态更新为 ${status}`);
        selectedRecordIds.clear();
        loadCoverRecords(); // Reload data to reflect changes
      } else {
        alert(`批量更新状态失败: ${data.message || '未知错误'}`);
      }
    })
    .catch(error => {
      console.error('批量更新状态 API 调用失败:', error);
      alert(`批量更新状态为 ${status} 的过程中发生网络或服务器错误`);
    });
  }

  // 更新记录总数显示
  function updateTotalRecordsDisplay() {
    if ($totalRecordsCount) {
      $totalRecordsCount.textContent = `全部数据：${totalRecords}`;
    }
  }

  // 导出模块 - Export new functions for inline event handlers if needed
  return {
    init,
    previewCover, // Keep existing exports
    toggleStatus,
    deleteCover
    // Note: Batch handlers are bound via event listeners, no need to export usually
  };
})();

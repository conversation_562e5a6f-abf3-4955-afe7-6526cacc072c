/**
 * 短信验证码管理工具
 * 支持多种存储方式，便于后续迁移到云服务器
 */
const logger = require('./logger');
const { SystemConfig, VerifyCode } = require('../models');
const { Op } = require('sequelize');

// 获取验证码存储方式配置
async function getStorageType() {
  try {
    const config = await SystemConfig.findOne({
      where: { config_key: 'verify_code_storage_type' }
    });
    return config ? config.config_value : 'memory'; // 默认使用内存存储
  } catch (error) {
    logger.error('获取验证码存储方式配置失败:', error);
    return 'memory'; // 默认使用内存存储
  }
}

// 验证码存储接口
class VerifyCodeStorage {
  // 内存存储实现（本地测试用）
  static memoryStorage = new Map();

  // 存储验证码
  static async saveCode(phone, code, purpose, expireMinutes = 5) {
    const expireTime = new Date(Date.now() + expireMinutes * 60 * 1000);
    const storageType = await getStorageType();

    if (storageType === 'database') {
      // 数据库存储
      await VerifyCode.create({
        phone,
        code,
        purpose,
        expire_time: expireTime,
        is_used: false
      });
    } else {
      // 内存存储
      this.memoryStorage.set(phone, { code, expireTime, purpose });
    }

    return true;
  }

  // 验证验证码
  static async verifyCode(phone, code, purpose) {
    const storageType = await getStorageType();

    if (storageType === 'database') {
      // 数据库验证
      const storedCode = await VerifyCode.findOne({
        where: {
          phone,
          purpose,
          expire_time: { [Op.gt]: new Date() },
          is_used: false
        }
      });

      if (!storedCode || storedCode.code !== code) {
        return false;
      }

      // 标记为已使用
      storedCode.is_used = true;
      await storedCode.save();
      return true;
    } else {
      // 内存验证
      const storedInfo = this.memoryStorage.get(phone);

      // 验证码不存在
      if (!storedInfo) {
        return false;
      }

      // 验证码已过期
      if (new Date() > storedInfo.expireTime) {
        this.memoryStorage.delete(phone);
        return false;
      }

      // 用途不匹配
      if (storedInfo.purpose !== purpose) {
        return false;
      }

      // 验证码不匹配
      if (storedInfo.code !== code) {
        return false;
      }

      // 验证成功后删除验证码，防止重复使用
      this.memoryStorage.delete(phone);
      return true;
    }
  }

  // 清理过期验证码
  static async cleanExpiredCodes() {
    const storageType = await getStorageType();
    const now = new Date();
    let count = 0;

    if (storageType === 'database') {
      // 清理数据库中的过期验证码
      const result = await VerifyCode.destroy({
        where: {
          [Op.or]: [
            { expire_time: { [Op.lt]: now } },
            { is_used: true }
          ]
        }
      });
      count = result;
    } else {
      // 清理内存中的过期验证码
      for (const [phone, info] of this.memoryStorage.entries()) {
        if (now > info.expireTime) {
          this.memoryStorage.delete(phone);
          count++;
        }
      }
    }

    if (count > 0) {
      logger.info(`清理了${count}个过期验证码`);
    }
  }
}

/**
 * 生成验证码
 * @param {number} length - 验证码长度，默认6位
 * @returns {string} - 生成的验证码
 */
const generateCode = (length = 6) => {
  // 随机生成指定长度的数字字符串
  return Math.random().toString().substring(2, 2 + length);
};

/**
 * 发送验证码
 * @param {string} phone - 手机号
 * @param {string} purpose - 用途，如'login'、'register'等
 * @returns {object} - 结果对象，包含成功状态和消息
 */
const sendSmsCode = async (phone, purpose) => {
  try {
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return {
        success: false,
        message: '手机号格式不正确'
      };
    }

    // 生成验证码
    const code = generateCode();

    // 存储验证码
    await VerifyCodeStorage.saveCode(phone, code, purpose);

    logger.info(`[开发环境] 发送验证码到 ${phone}，用途: ${purpose}，验证码: ${code}`);

    // 预留短信服务对接接口
    // 实际环境中，这里应该调用SMS服务提供商的API
    // 例如：await smsServiceProvider.send(phone, `您的验证码是${code}，5分钟内有效`);

    return {
      success: true,
      message: '验证码发送成功',
      // 仅在开发环境返回验证码，方便测试
      devCode: process.env.NODE_ENV === 'development' ? code : undefined
    };
  } catch (error) {
    logger.error('发送验证码失败:', error);
    return {
      success: false,
      message: '发送验证码失败，请稍后再试'
    };
  }
};

/**
 * 验证短信验证码
 * @param {string} phone - 手机号
 * @param {string} code - 用户提交的验证码
 * @param {string} purpose - 用途，如'login'、'register'等，用于防止验证码复用
 * @returns {boolean} - 验证结果
 */
const verifySmsCode = async (phone, code, purpose) => {
  return await VerifyCodeStorage.verifyCode(phone, code, purpose);
};

// 每10分钟清理一次过期验证码
setInterval(() => {
  VerifyCodeStorage.cleanExpiredCodes().catch(err => {
    logger.error('清理过期验证码失败:', err);
  });
}, 10 * 60 * 1000);

module.exports = {
  generateCode,
  sendSmsCode,
  verifySmsCode
};

/**
 * 带认证的API测试
 */

const http = require('http');
const jwt = require('jsonwebtoken');

// 创建测试JWT令牌
function createTestToken() {
  const payload = {
    userId: 1,
    phone: '13800138000',
    role: 'admin'
  };
  return jwt.sign(payload, 'your_jwt_secret_key', { expiresIn: '1h' });
}

// 测试API接口
function testAPI(path, method = 'GET', data = null, token = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (token) {
      options.headers['Authorization'] = `Bearer ${token}`;
    }

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAPIsWithAuth() {
  try {
    console.log('🧪 开始API认证测试...');
    
    // 生成测试令牌
    const token = createTestToken();
    console.log(`🔑 生成测试令牌: ${token.substring(0, 50)}...`);
    
    // 等待后端启动
    console.log('⏳ 等待后端服务启动...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const tests = [
      {
        name: '获取渲染方式配置',
        path: '/api/admin/config/rendering-mode',
        method: 'GET'
      },
      {
        name: '获取安全规则配置',
        path: '/api/admin/config/security-rules',
        method: 'GET'
      },
      {
        name: '获取安全违规记录',
        path: '/api/admin/security/violations',
        method: 'GET'
      },
      {
        name: '获取安全统计',
        path: '/api/admin/security/statistics',
        method: 'GET'
      },
      {
        name: '设置渲染方式配置',
        path: '/api/admin/config/rendering-mode',
        method: 'POST',
        data: {
          defaultRenderingMode: 'SERVER_SIDE_RENDERING',
          enableAdvancedMode: true,
          fallbackMode: 'CLIENT_SIDE_SAFE_LOADING'
        }
      }
    ];
    
    let successCount = 0;
    let failCount = 0;
    
    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      console.log(`\n${i + 1}️⃣ 测试: ${test.name}`);
      console.log(`   ${test.method} ${test.path}`);
      
      try {
        const result = await testAPI(test.path, test.method, test.data, token);
        console.log(`   状态码: ${result.statusCode}`);
        
        if (result.statusCode === 200) {
          console.log('   ✅ 成功');
          successCount++;
          
          // 显示响应内容（截断）
          const responsePreview = result.body.length > 100 
            ? result.body.substring(0, 100) + '...' 
            : result.body;
          console.log(`   响应: ${responsePreview}`);
        } else {
          console.log('   ❌ 失败');
          failCount++;
          console.log(`   错误: ${result.body}`);
        }
      } catch (error) {
        console.log('   ❌ 请求失败');
        failCount++;
        console.log(`   错误: ${error.message}`);
        
        if (error.code === 'ECONNREFUSED') {
          console.log('   💡 提示: 后端服务可能未启动');
          break;
        }
      }
    }
    
    console.log('\n📊 测试结果汇总:');
    console.log(`   ✅ 成功: ${successCount}`);
    console.log(`   ❌ 失败: ${failCount}`);
    console.log(`   📈 成功率: ${((successCount / tests.length) * 100).toFixed(1)}%`);
    
    if (successCount === tests.length) {
      console.log('\n🎉 所有API测试通过！');
    } else if (successCount > 0) {
      console.log('\n⚠️ 部分API测试通过，需要进一步检查失败的接口');
    } else {
      console.log('\n❌ 所有API测试失败，请检查后端服务和数据库');
    }
    
  } catch (error) {
    console.log('❌ API测试失败:', error.message);
  }
}

testAPIsWithAuth();

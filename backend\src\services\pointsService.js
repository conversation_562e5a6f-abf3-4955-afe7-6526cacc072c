const { User, PointRecord, SystemConfig } = require('../models');
const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

/**
 * 获取操作所需的积分成本
 * @param {string} operationType 操作类型
 * @returns {Promise<number>} 操作所需积分
 */
const getPointsCost = async (operationType) => {
  try {
    // 配置项key格式: points_operation_{operationType}
    const configKey = `points_operation_${operationType}`;
    
    // 从系统配置中查询积分成本
    const config = await SystemConfig.findOne({
      where: { config_key: configKey }
    });
    
    if (config && config.config_value) {
      const cost = parseInt(config.config_value);
      if (!isNaN(cost)) {
        return cost;
      }
    }
    
    // 默认积分成本
    const defaultCosts = {
      generate_cover: 10, // 生成封面默认消耗10积分
      // 可以添加其他操作的默认消耗
    };
    
    // 如果没有配置或解析失败，返回默认值
    return defaultCosts[operationType] || 1;
  } catch (error) {
    logger.error(`获取操作[${operationType}]积分成本失败:`, error);
    
    // 发生错误时返回默认值
    return operationType === 'generate_cover' ? 10 : 1;
  }
};

/**
 * 为特定操作消费用户积分
 * @param {number} userId 用户ID
 * @param {string} operationType 操作类型
 * @param {string} description 操作描述
 * @param {string} operationId 操作唯一标识，用于防止重复操作
 * @returns {Promise<Object>} 消费结果
 */
const consumePointsForOperation = async (userId, operationType, description, operationId) => {
  try {
    // 查询用户信息
    const user = await User.findByPk(userId, {
      lock: true, // 添加行锁，防止并发问题
      transaction: sequelize.transaction() // 创建事务
    });
    
    if (!user) {
      return { success: false, message: '用户不存在' };
    }
    
    // 如果提供了operationId，检查是否已经处理过
    if (operationId) {
      const existingRecord = await PointRecord.findOne({
        where: {
          operation_id: operationId,
          user_id: userId
        }
      });

      // 如果已经存在记录，直接返回成功
      if (existingRecord) {
        logger.info(`操作ID ${operationId} 已存在记录，跳过重复处理`);
        return {
          success: true,
          message: '积分消费成功(已处理)',
          consumed: 0,
          points: user.points,
          duplicate: true
        };
      }
    }
    
    // 获取操作所需积分
    const pointsRequired = await getPointsCost(operationType);
    
    // 检查积分是否足够
    if (user.points < pointsRequired) {
      return {
        success: false,
        message: `积分不足，${operationType}需要${pointsRequired}积分，当前仅有${user.points}积分`
      };
    }
    
    // 开启事务
    const transaction = await sequelize.transaction();
    
    try {
      // 减少用户积分
      user.points -= pointsRequired;
      await user.save({ transaction });
      
      // 创建积分记录
      await PointRecord.create({
        user_id: userId,
        points_change: -pointsRequired,
        points_after: user.points,
        operation_type: operationType,
        description: description || `${operationType}操作消费`,
        operation_time: new Date(),
        operation_id: operationId || null // 保存操作ID
      }, { transaction });
      
      // 提交事务
      await transaction.commit();
      
      logger.info(`用户${userId}成功消费${pointsRequired}积分，操作类型：${operationType}${operationId ? '，操作ID：' + operationId : ''}`);
      
      return {
        success: true,
        message: '积分消费成功',
        consumed: pointsRequired,
        points: user.points
      };
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    logger.error(`操作[${operationType}]消费积分失败:${error.message}`, error);
    return { success: false, message: '消费积分失败，请稍后再试' };
  }
};

module.exports = {
  getPointsCost,
  consumePointsForOperation
}; 
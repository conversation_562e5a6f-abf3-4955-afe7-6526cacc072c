const { sequelize } = require('../config/database');
const { Sequelize } = require('sequelize');
const User = require('./User');
const VerifyCode = require('./VerifyCode');
const LoginLog = require('./LoginLog');
const SystemLog = require('./SystemLog');
const FeatureControl = require('./FeatureControl');
const BasePrompt = require('./BasePrompt');
const SystemConfig = require('./SystemConfig');
const StyleExample = require('./StyleExample');
const StylePrompt = require('./StylePrompt');
const CoverRecord = require('./CoverRecord');
const GenerationTask = require('./GenerationTask');
const PaymentRecord = require('./PaymentRecord');
const PointRecord = require('./PointRecord');
const AIServiceConfig = require('./aiServiceConfig');
const MemberPackage = require('./MemberPackage');
const PointPackage = require('./PointPackage');
const RefundRecord = require('./RefundRecord')(sequelize);
const PaymentConfig = require('./PaymentConfig')(sequelize);
const PaymentSecurityLog = require('./PaymentSecurityLog')(sequelize);
const HtmlSecurityConfig = require('./HtmlSecurityConfig');
const HtmlSecurityViolation = require('./HtmlSecurityViolation');

// 定义模型之间的关联关系
if (User.associate) {
  User.associate({
    CoverRecord,
    GenerationTask,
    PaymentRecord,
    RefundRecord,
    PointRecord
  });
}

if (CoverRecord.associate) {
  CoverRecord.associate({ User, StylePrompt });
}

if (GenerationTask.associate) {
  GenerationTask.associate({ User });
}

if (StylePrompt.associate) {
  StylePrompt.associate({ StyleExample });
}

if (StyleExample.associate) {
  StyleExample.associate({ StylePrompt });
}

if (PaymentRecord.associate) {
  PaymentRecord.associate({ User });
}

if (RefundRecord.associate) {
  RefundRecord.associate({ User, PaymentRecord });
}

if (PointRecord.associate) {
  PointRecord.associate({ User });
}

if (HtmlSecurityViolation.associate) {
  HtmlSecurityViolation.associate({ User });
}

module.exports = {
  sequelize,
  Sequelize,
  User,
  VerifyCode,
  LoginLog,
  SystemLog,
  FeatureControl,
  CoverRecord,
  GenerationTask,
  StylePrompt,
  BasePrompt,
  SystemConfig,
  StyleExample,
  AIServiceConfig,
  PaymentRecord,
  PointRecord,
  MemberPackage,
  PointPackage,
  RefundRecord,
  PaymentConfig,
  PaymentSecurityLog,
  HtmlSecurityConfig,
  HtmlSecurityViolation
};

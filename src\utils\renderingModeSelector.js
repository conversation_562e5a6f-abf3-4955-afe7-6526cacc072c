/**
 * 智能渲染方式选择器
 * 基于HTML复杂度和安全检测结果自动选择最佳渲染方式
 * 第二阶段：智能渲染方式选择实现 - 步骤1
 */

import logger from '../services/logs/frontendLogger.js';

/**
 * 渲染模式枚举
 */
export const RENDERING_MODES = {
  SERVER_SIDE_RENDERING: 'SERVER_SIDE_RENDERING',
  CLIENT_SIDE_SAFE_LOADING: 'CLIENT_SIDE_SAFE_LOADING',
  USER_CONFIRMATION_REQUIRED: 'USER_CONFIRMATION_REQUIRED'
};

/**
 * 复杂度分析权重配置
 */
export const COMPLEXITY_WEIGHTS = {
  // JavaScript相关
  scriptTags: 15,
  inlineEventHandlers: 10,
  externalScripts: 12,
  
  // 交互元素
  forms: 8,
  buttons: 5,
  inputs: 6,
  
  // 多媒体和复杂元素
  iframes: 10,
  videos: 8,
  audios: 6,
  canvases: 12,
  
  // 导航和链接
  navigationLinks: 7,
  anchors: 3,
  
  // 样式复杂度
  inlineStyles: 4,
  cssAnimations: 8,
  
  // 文档结构
  totalElements: 0.1,
  nestingDepth: 2
};

/**
 * 智能渲染方式选择器类
 */
export class RenderingModeSelector {
  /**
   * 选择最佳渲染方式
   * @param {string} htmlContent - HTML内容
   * @param {Object} securityResult - 安全检测结果
   * @param {Object} options - 选择选项
   * @returns {Object} 渲染方式选择结果
   */
  static selectMode(htmlContent, securityResult, options = {}) {
    try {
      logger.info('开始渲染方式选择分析');

      // 1. 检查安全检测结果
      if (!securityResult.passed) {
        return {
          mode: RENDERING_MODES.USER_CONFIRMATION_REQUIRED,
          reason: '安全检测未通过，需要用户确认是否使用安全加载方式',
          riskLevel: securityResult.riskLevel,
          securityDetails: securityResult.details
        };
      }

      // 2. 分析HTML复杂度
      const complexity = this.analyzeComplexity(htmlContent);
      
      // 3. 基于复杂度选择渲染方式（支持高级模式）
      const modeSelection = this.selectBasedOnComplexity(complexity, {
        ...options,
        advancedMode: options.advancedMode || false
      });
      
      logger.info(`渲染方式选择完成: ${modeSelection.mode}, 复杂度分数: ${complexity.totalScore}`);
      
      return {
        ...modeSelection,
        complexity,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('渲染方式选择失败:', error);
      return {
        mode: RENDERING_MODES.CLIENT_SIDE_SAFE_LOADING,
        reason: '选择器执行失败，降级到客户端安全加载',
        error: error.message
      };
    }
  }

  /**
   * 分析HTML复杂度
   * @param {string} htmlContent - HTML内容
   * @returns {Object} 复杂度分析结果
   */
  static analyzeComplexity(htmlContent) {
    try {
      // 检查是否在浏览器环境中
      if (typeof DOMParser === 'undefined') {
        // Node.js环境下的简化复杂度分析
        return this.analyzeComplexityWithRegex(htmlContent);
      }

      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlContent, 'text/html');
      
      const analysis = {
        // JavaScript相关
        scriptTags: this.countElements(doc, 'script'),
        inlineEventHandlers: this.countInlineEventHandlers(htmlContent),
        externalScripts: this.countExternalScripts(doc),
        
        // 交互元素
        forms: this.countElements(doc, 'form'),
        buttons: this.countElements(doc, 'button'),
        inputs: this.countElements(doc, 'input, textarea, select'),
        
        // 多媒体和复杂元素
        iframes: this.countElements(doc, 'iframe'),
        videos: this.countElements(doc, 'video'),
        audios: this.countElements(doc, 'audio'),
        canvases: this.countElements(doc, 'canvas'),
        
        // 导航和链接
        navigationLinks: this.countNavigationLinks(doc),
        anchors: this.countElements(doc, 'a'),
        
        // 样式复杂度
        inlineStyles: this.countInlineStyles(doc),
        cssAnimations: this.countCSSAnimations(htmlContent),
        
        // 文档结构
        totalElements: doc.querySelectorAll('*').length,
        nestingDepth: this.calculateNestingDepth(doc)
      };

      // 计算总复杂度分数
      analysis.totalScore = this.calculateComplexityScore(analysis);
      
      // 判断特征
      analysis.hasInteractiveElements = this.hasInteractiveElements(analysis);
      analysis.hasMultiplePages = this.hasMultiplePages(analysis);
      analysis.hasComplexScripts = this.hasComplexScripts(analysis);
      
      return analysis;

    } catch (error) {
      logger.error('HTML复杂度分析失败:', error);
      return {
        totalScore: 0,
        hasInteractiveElements: false,
        hasMultiplePages: false,
        hasComplexScripts: false,
        error: error.message
      };
    }
  }

  /**
   * 基于复杂度选择渲染方式
   * @param {Object} complexity - 复杂度分析结果
   * @param {Object} options - 选择选项
   * @returns {Object} 选择结果
   */
  static selectBasedOnComplexity(complexity, options = {}) {
    const { forceMode, complexityThreshold = 50, advancedMode = false } = options;

    // 如果强制指定模式
    if (forceMode && Object.values(RENDERING_MODES).includes(forceMode)) {
      return {
        mode: forceMode,
        reason: '强制指定渲染模式',
        advancedModeEnabled: advancedMode
      };
    }

    // 高级模式启用时降低服务端渲染阈值
    const effectiveThreshold = advancedMode ? Math.max(30, complexityThreshold * 0.6) : complexityThreshold;

    // 基于复杂度分数选择
    if (complexity.totalScore >= effectiveThreshold ||
        complexity.hasInteractiveElements ||
        complexity.hasMultiplePages ||
        complexity.hasComplexScripts ||
        (advancedMode && complexity.totalScore > 20)) {

      return {
        mode: RENDERING_MODES.SERVER_SIDE_RENDERING,
        reason: advancedMode ?
          '高级模式启用，使用服务端渲染获得更好性能和兼容性' :
          '检测到复杂HTML结构，使用服务端渲染保持完整功能',
        details: {
          complexityScore: complexity.totalScore,
          hasInteractiveElements: complexity.hasInteractiveElements,
          hasMultiplePages: complexity.hasMultiplePages,
          hasComplexScripts: complexity.hasComplexScripts,
          effectiveThreshold
        },
        advancedModeEnabled: advancedMode
      };
    }

    return {
      mode: RENDERING_MODES.CLIENT_SIDE_SAFE_LOADING,
      reason: '简单HTML结构，使用客户端安全加载',
      details: {
        complexityScore: complexity.totalScore,
        effectiveThreshold
      },
      advancedModeEnabled: advancedMode
    };
  }

  /**
   * 计算复杂度分数
   * @param {Object} analysis - 分析结果
   * @returns {number} 复杂度分数
   */
  static calculateComplexityScore(analysis) {
    let score = 0;
    
    Object.keys(COMPLEXITY_WEIGHTS).forEach(key => {
      if (analysis[key] !== undefined) {
        score += analysis[key] * COMPLEXITY_WEIGHTS[key];
      }
    });
    
    return Math.round(score);
  }

  /**
   * 检测是否有交互元素
   * @param {Object} analysis - 分析结果
   * @returns {boolean} 是否有交互元素
   */
  static hasInteractiveElements(analysis) {
    return analysis.forms > 0 || 
           analysis.buttons > 0 || 
           analysis.inputs > 0 ||
           analysis.scriptTags > 0 ||
           analysis.inlineEventHandlers > 0;
  }

  /**
   * 检测是否有多页面导航
   * @param {Object} analysis - 分析结果
   * @returns {boolean} 是否有多页面导航
   */
  static hasMultiplePages(analysis) {
    return analysis.navigationLinks > 3 || analysis.anchors > 10;
  }

  /**
   * 检测是否有复杂脚本
   * @param {Object} analysis - 分析结果
   * @returns {boolean} 是否有复杂脚本
   */
  static hasComplexScripts(analysis) {
    return analysis.scriptTags > 2 || 
           analysis.externalScripts > 1 ||
           analysis.canvases > 0;
  }

  /**
   * Node.js环境下的复杂度分析（使用正则表达式）
   * @param {string} htmlContent - HTML内容
   * @returns {Object} 复杂度分析结果
   */
  static analyzeComplexityWithRegex(htmlContent) {
    const complexity = {
      scriptTags: (htmlContent.match(/<script[^>]*>/gi) || []).length,
      inlineEventHandlers: this.countInlineEventHandlers(htmlContent),
      externalScripts: (htmlContent.match(/<script[^>]*src[^>]*>/gi) || []).length,
      forms: (htmlContent.match(/<form[^>]*>/gi) || []).length,
      buttons: (htmlContent.match(/<button[^>]*>/gi) || []).length,
      inputs: (htmlContent.match(/<input[^>]*>/gi) || []).length,
      iframes: (htmlContent.match(/<iframe[^>]*>/gi) || []).length,
      videos: (htmlContent.match(/<video[^>]*>/gi) || []).length,
      audios: (htmlContent.match(/<audio[^>]*>/gi) || []).length,
      canvases: (htmlContent.match(/<canvas[^>]*>/gi) || []).length,
      navigationLinks: (htmlContent.match(/<nav[^>]*>|<a[^>]*href[^>]*>/gi) || []).length,
      anchors: (htmlContent.match(/<a[^>]*>/gi) || []).length,
      inlineStyles: (htmlContent.match(/style\s*=/gi) || []).length,
      cssAnimations: (htmlContent.match(/@keyframes|animation:|transition:/gi) || []).length,
      totalElements: (htmlContent.match(/<[^>]+>/g) || []).length,
      nestingDepth: this.calculateNestingDepth(htmlContent)
    };

    // 计算总分数
    let totalScore = 0;
    Object.keys(COMPLEXITY_WEIGHTS).forEach(key => {
      if (complexity[key] !== undefined) {
        totalScore += complexity[key] * COMPLEXITY_WEIGHTS[key];
      }
    });

    return {
      ...complexity,
      totalScore,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 计算HTML嵌套深度
   * @param {string} htmlContent - HTML内容
   * @returns {number} 嵌套深度
   */
  static calculateNestingDepth(htmlContent) {
    let maxDepth = 0;
    let currentDepth = 0;

    // 简化的嵌套深度计算
    const tags = htmlContent.match(/<\/?[^>]+>/g) || [];
    tags.forEach(tag => {
      if (tag.startsWith('</')) {
        currentDepth--;
      } else if (!tag.endsWith('/>')) {
        currentDepth++;
        maxDepth = Math.max(maxDepth, currentDepth);
      }
    });

    return maxDepth;
  }

  /**
   * 统计元素数量
   * @param {Document} doc - 文档对象
   * @param {string} selector - 选择器
   * @returns {number} 元素数量
   */
  static countElements(doc, selector) {
    try {
      return doc.querySelectorAll(selector).length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 统计内联事件处理器
   * @param {string} htmlContent - HTML内容
   * @returns {number} 内联事件处理器数量
   */
  static countInlineEventHandlers(htmlContent) {
    const eventHandlerPattern = /on\w+\s*=/gi;
    const matches = htmlContent.match(eventHandlerPattern);
    return matches ? matches.length : 0;
  }

  /**
   * 统计外部脚本
   * @param {Document} doc - 文档对象
   * @returns {number} 外部脚本数量
   */
  static countExternalScripts(doc) {
    const scripts = doc.querySelectorAll('script[src]');
    return scripts.length;
  }

  /**
   * 统计导航链接
   * @param {Document} doc - 文档对象
   * @returns {number} 导航链接数量
   */
  static countNavigationLinks(doc) {
    const navElements = doc.querySelectorAll('nav a, .nav a, .navigation a, .menu a');
    return navElements.length;
  }

  /**
   * 统计内联样式
   * @param {Document} doc - 文档对象
   * @returns {number} 内联样式数量
   */
  static countInlineStyles(doc) {
    const elementsWithStyle = doc.querySelectorAll('[style]');
    return elementsWithStyle.length;
  }

  /**
   * 统计CSS动画
   * @param {string} htmlContent - HTML内容
   * @returns {number} CSS动画数量
   */
  static countCSSAnimations(htmlContent) {
    const animationPattern = /@keyframes|animation:|transition:/gi;
    const matches = htmlContent.match(animationPattern);
    return matches ? matches.length : 0;
  }

  /**
   * 计算嵌套深度
   * @param {Document} doc - 文档对象
   * @returns {number} 最大嵌套深度
   */
  static calculateNestingDepth(doc) {
    let maxDepth = 0;
    
    const calculateDepth = (element, currentDepth = 0) => {
      maxDepth = Math.max(maxDepth, currentDepth);
      
      for (const child of element.children) {
        calculateDepth(child, currentDepth + 1);
      }
    };
    
    if (doc.body) {
      calculateDepth(doc.body);
    }
    
    return maxDepth;
  }

  /**
   * 生成复杂度分析报告
   * @param {Object} complexity - 复杂度分析结果
   * @returns {string} 格式化的报告
   */
  static generateComplexityReport(complexity) {
    let report = `📊 HTML复杂度分析报告\n\n`;
    report += `总复杂度分数: ${complexity.totalScore}\n`;
    report += `交互元素: ${complexity.hasInteractiveElements ? '是' : '否'}\n`;
    report += `多页面导航: ${complexity.hasMultiplePages ? '是' : '否'}\n`;
    report += `复杂脚本: ${complexity.hasComplexScripts ? '是' : '否'}\n\n`;
    
    report += `详细统计:\n`;
    report += `- 脚本标签: ${complexity.scriptTags}\n`;
    report += `- 表单元素: ${complexity.forms}\n`;
    report += `- 按钮: ${complexity.buttons}\n`;
    report += `- 输入框: ${complexity.inputs}\n`;
    report += `- 总元素数: ${complexity.totalElements}\n`;
    report += `- 嵌套深度: ${complexity.nestingDepth}\n`;
    
    return report;
  }
}

export default RenderingModeSelector;

/**
 * 样式应用工具函数
 * 该模块包含用于处理文本样式应用的各种函数，如字体大小、颜色、粗体等
 */

/**
 * 应用字体大小到选中文本或目标元素
 * @param {Element} targetElement - 目标DOM元素
 * @param {string} fontSize - 要应用的字体大小值
 * @param {Document} iframeDoc - iframe的document对象
 */
export const applyFontSize = (targetElement, fontSize, iframeDoc) => {
  if (!fontSize) return; // 如果没有提供字体大小，则直接返回
  
  try {
    // 尝试让 iframe 获得焦点 - 确保使用与原始代码相同的方式处理焦点
    if (iframeDoc) {
      // 先尝试使用defaultView
      if (iframeDoc.defaultView) {
        iframeDoc.defaultView.focus();
      }
      
      // 如果iframe有contentWindow属性，也尝试使用它来设置焦点
      // 这是为了兼容PreviewEditorToolbar.jsx中的焦点处理方式
      const iframe = iframeDoc.defaultView?.frameElement;
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.focus();
      }
    }
    
    // 获取当前选区
    const selection = iframeDoc.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      // 如果有选中文本，只对选中文本应用字号
      if (!range.collapsed) {
        // 创建span包装选中文本
        const span = iframeDoc.createElement('span');

        try {
          // 提取选中的内容
          const selectedContent = range.extractContents();

          // 清除选中内容中的所有字号样式
          clearFontSizeStyles(selectedContent);

          // 将内容添加到span中
          span.appendChild(selectedContent);

          // 应用新字号到span
          span.style.setProperty('font-size', fontSize, 'important');

          // 插入span到原位置
          range.insertNode(span);

          // 重新选中span内容
          selection.removeAllRanges();
          const newRange = iframeDoc.createRange();
          newRange.selectNodeContents(span);
          selection.addRange(newRange);
        } catch (e) {
          // 如果失败，且有目标元素，则对整个文本框应用字号
          if (targetElement) {
            applyFontSizeToElement(targetElement, fontSize);
          } else {
            console.error("应用字体大小失败:", e);
          }
        }
      } else {
        // 没有选中文本，但有目标元素，对整个文本框应用字号
        if (targetElement) {
          applyFontSizeToElement(targetElement, fontSize);
        }
      }
    } else {
      // 没有选区，但有目标元素，对整个文本框应用字号
      if (targetElement) {
        applyFontSizeToElement(targetElement, fontSize);
      }
    }
  } catch (e) {
    // 最后的备用方案，如果有目标元素
    if (targetElement) {
      applyFontSizeToElement(targetElement, fontSize);
    } else {
      console.error("应用字体大小失败:", e);
    }
  }
};

/**
 * 应用字体大小到特定元素
 * @param {Element} element - 目标DOM元素
 * @param {string} fontSize - 要应用的字体大小值
 */
export const applyFontSizeToElement = (element, fontSize) => {
  // 清除元素的所有字号样式
  clearFontSizeStyles(element);

  // 强制应用新字号
  element.style.setProperty('font-size', fontSize, 'important');
};

/**
 * 清除元素的字体大小样式
 * @param {Element|DocumentFragment} element - 要清除样式的元素
 */
export const clearFontSizeStyles = (element) => {
  if (element.nodeType === Node.ELEMENT_NODE) {
    element.style.removeProperty('font-size');

    // 清除所有子元素的字号样式
    const allChildren = element.querySelectorAll ? element.querySelectorAll('*') : [];
    Array.from(allChildren).forEach(child => {
      child.style.removeProperty('font-size');
    });
  } else if (element.children) {
    // 处理DocumentFragment
    Array.from(element.children).forEach(clearFontSizeStyles);
  }
};

/**
 * 应用颜色到元素
 * @param {Element} targetElement - 目标DOM元素
 * @param {string} color - 要应用的颜色值
 * @param {Document} iframeDoc - iframe的document对象
 */
export const applyColor = (targetElement, color, iframeDoc) => {
  if (!color) return;
  try {
    if (targetElement) {
      // 1. 深度清洁所有子元素，移除局部颜色和特殊样式，让它们准备好继承
      const allChildren = targetElement.querySelectorAll('*');
      allChildren.forEach(child => {
        child.style.removeProperty('color');
        child.style.removeProperty('background-image');
        child.style.removeProperty('-webkit-background-clip');
        child.style.removeProperty('background-clip');
        child.style.removeProperty('-webkit-text-fill-color');
      });

      // 2. 清理父元素自身的冲突样式
      targetElement.style.removeProperty('background-image');
      // 移除下面这行，避免清除背景颜色
      // targetElement.style.removeProperty('background');
      targetElement.style.removeProperty('-webkit-background-clip');
      targetElement.style.removeProperty('background-clip');
      targetElement.style.removeProperty('-webkit-text-fill-color');

      // 3. 设置新的、高优先级的全局颜色
      targetElement.style.setProperty('color', color, 'important');
    }
  } catch (e) {
    console.error("应用颜色失败:", e);
  }
};

/**
 * 应用背景颜色到元素
 * @param {Element} targetElement - 目标DOM元素
 * @param {string} color - 要应用的背景颜色值
 * @param {Document} iframeDoc - iframe的document对象
 */
export const applyBackColor = (targetElement, color, iframeDoc) => {
  if (!color) return; // 如果没有提供颜色，则直接返回
  
  try {
    if (targetElement) {
      // 1. 深度清洁所有子元素，移除局部背景颜色
      const allChildren = targetElement.querySelectorAll('*');
      allChildren.forEach(child => {
        child.style.removeProperty('background-color');
      });

      // 2. 清理父元素自身的冲突样式
      targetElement.style.removeProperty('background-image');
      targetElement.style.removeProperty('-webkit-background-clip');
      targetElement.style.removeProperty('background-clip');
      
      // 3. 设置新的、高优先级的全局背景颜色
      targetElement.style.setProperty('background-color', color, 'important');
    }
  } catch (e) {
    console.error("应用背景颜色失败:", e);
  }
};

/**
 * 应用字体到元素
 * @param {Element} targetElement - 目标DOM元素
 * @param {string} fontFamily - 要应用的字体名称
 * @param {Document} iframeDoc - iframe的document对象，可选参数
 */
export const applyFontFamily = (targetElement, fontFamily, iframeDoc) => {
  if (!fontFamily) return;
  try {
    // 如果提供了iframeDoc，尝试让iframe获得焦点
    if (iframeDoc) {
      // 先尝试使用defaultView
      if (iframeDoc.defaultView) {
        iframeDoc.defaultView.focus();
      }
      
      // 如果iframe有contentWindow属性，也尝试使用它来设置焦点
      const iframe = iframeDoc.defaultView?.frameElement;
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.focus();
      }
    }
    
    if (targetElement) {
      // 1. 深度清洁所有子元素，移除局部字体设置
      const allChildren = targetElement.querySelectorAll('*');
      allChildren.forEach(child => {
        child.style.removeProperty('font-family');
      });

      // 2. 设置新的、高优先级的全局字体
      targetElement.style.setProperty('font-family', fontFamily, 'important');
    }
  } catch (e) {
    console.error("应用字体失败:", e);
  }
};

/**
 * 清除元素的颜色样式
 * @param {Element} element - 要清除样式的元素
 */
export const clearColorStyles = (element) => {
  // 该函数不再被applyColor调用，保留其原始功能以备其他模块可能的使用
  element.style.removeProperty('color');
  element.style.removeProperty('-webkit-text-fill-color');
  element.style.removeProperty('background');
  element.style.removeProperty('background-image');
  element.style.removeProperty('-webkit-background-clip');
  element.style.removeProperty('background-clip');
  element.style.removeProperty('text-shadow');

  // 清除所有子元素的颜色样式
  const allChildren = element.querySelectorAll('*');
  allChildren.forEach(child => {
    child.style.removeProperty('color');
    child.style.removeProperty('-webkit-text-fill-color');
    child.style.removeProperty('background');
    child.style.removeProperty('background-image');
    child.style.removeProperty('-webkit-background-clip');
    child.style.removeProperty('background-clip');
    child.style.removeProperty('text-shadow');
  });
};

/**
 * 应用粗体样式到元素
 * @param {Element} targetElement - 目标DOM元素
 */
export const applyBold = (targetElement) => {
  if (!targetElement) return;
  const currentWeight = window.getComputedStyle(targetElement).fontWeight;
  const newWeight = (currentWeight === 'bold' || parseInt(currentWeight) >= 700) ? 'normal' : 'bold';
  
  // 深度清洁子元素
  const allChildren = targetElement.querySelectorAll('*');
  allChildren.forEach(child => {
    child.style.removeProperty('font-weight');
  });
  // 应用到父元素
  targetElement.style.fontWeight = newWeight;
};

/**
 * 应用斜体样式到元素
 * @param {Element} targetElement - 目标DOM元素
 */
export const applyItalic = (targetElement) => {
  if (!targetElement) return;
  const currentStyle = window.getComputedStyle(targetElement).fontStyle;
  const newStyle = currentStyle === 'italic' ? 'normal' : 'italic';

  // 深度清洁子元素
  const allChildren = targetElement.querySelectorAll('*');
  allChildren.forEach(child => {
    child.style.removeProperty('font-style');
  });
  // 应用到父元素
  targetElement.style.fontStyle = newStyle;
};

/**
 * 应用下划线样式到元素
 * @param {Element} targetElement - 目标DOM元素
 */
export const applyUnderline = (targetElement) => {
  if (!targetElement) return;
  const currentDecoration = window.getComputedStyle(targetElement).textDecorationLine;
  const newDecoration = currentDecoration.includes('underline') ? 'none' : 'underline';

  // 深度清洁子元素
  const allChildren = targetElement.querySelectorAll('*');
  allChildren.forEach(child => {
    child.style.removeProperty('text-decoration');
    child.style.removeProperty('text-decoration-line');
  });
  // 应用到父元素
  targetElement.style.textDecoration = newDecoration;
}; 

/**
 * 应用边框样式到目标元素
 * @param {Element} element - 目标DOM元素
 * @param {string} style - 要应用的边框样式（none, solid, dashed, dotted）
 */
export const applyBorderStyle = (element, style) => {
  if (!element || !style || style === '') return;
  
  try {
    if (style === 'none') {
      // 当选择 "无线框" 时，强力地、完整地移除边框
      element.style.setProperty('border', 'none', 'important');
    } else {
      // 当应用新的边框样式时，确保有默认的宽度和颜色
      const computedStyle = window.getComputedStyle(element);
      
      // 如果没有宽度或宽度为0，设置一个默认值
      if (!computedStyle.borderWidth || computedStyle.borderWidth === '0px') {
        element.style.setProperty('border-width', '1px', 'important');
      }
      
      // 如果没有颜色或者是透明的，设置一个默认值
      const borderColor = computedStyle.borderColor;
      if (!borderColor || borderColor === 'transparent' || borderColor === 'rgba(0, 0, 0, 0)') {
        element.style.setProperty('border-color', '#000000', 'important');
      }

      // 最后，应用新的样式
      element.style.setProperty('border-style', style, 'important');
    }
  } catch (e) {
    console.error("应用边框样式失败:", e);
  }
};

/**
 * 应用边框宽度到目标元素
 * @param {Element} element - 目标DOM元素
 * @param {string} width - 要应用的边框宽度
 */
export const applyBorderWidth = (element, width) => {
  if (!element || !width) return;
  
  try {
    const computedStyle = window.getComputedStyle(element);
    // 如果没有边框样式，则默认为 solid
    if (computedStyle.borderStyle === 'none') {
      element.style.borderStyle = 'solid';
    }
    // 如果没有颜色，则默认为黑色
    if (!computedStyle.borderColor || computedStyle.borderColor === 'rgba(0, 0, 0, 0)') {
      element.style.borderColor = '#000000';
    }
    element.style.borderWidth = width;
  } catch (e) {
    console.error("应用边框宽度失败:", e);
  }
};

/**
 * 应用边框颜色到目标元素
 * @param {Element} element - 目标DOM元素
 * @param {string} color - 要应用的边框颜色
 */
export const applyBorderColor = (element, color) => {
  if (element && color) {
    element.style.setProperty('border-color', color, 'important');
  }
};

/**
 * 获取目标元素的当前边框样式
 * @param {Element} element - 目标DOM元素
 * @returns {Object|null} 包含边框样式、宽度和颜色的对象，如果获取失败则返回null
 */
export const getCurrentBorderStyles = (element) => {
  if (!element) return null;
  
  try {
    const computedStyle = window.getComputedStyle(element);
    
    const style = computedStyle.borderStyle;
    const width = computedStyle.borderWidth;
    const color = computedStyle.borderColor;
    
    // 核心修正：如果没有边框样式或宽度为0，则返回空字符串，以匹配下拉框的占位符
    if (style === 'none' || width === '0px') {
      return {
        style: '', // 返回空字符串而不是 'none'
        width: '0px',
        color: '#000000'
      };
    }
    
    return { style, width, color };
  } catch (e) {
    console.error("获取边框样式失败:", e);
    return null;
  }
};

/**
 * 应用行高到元素
 * @param {Element} targetElement - 目标DOM元素
 * @param {string} lineHeight - 要应用的行高值
 */
export const applyLineHeight = (targetElement, lineHeight) => {
  if (!targetElement || !lineHeight) return;
  
  try {
    // 深度清洁所有子元素，移除局部行高设置
    const allChildren = targetElement.querySelectorAll('*');
    allChildren.forEach(child => {
      child.style.removeProperty('line-height');
    });
    
    // 设置新的行高
    targetElement.style.setProperty('line-height', lineHeight, 'important');
  } catch (e) {
    console.error("应用行高失败:", e);
  }
};

/**
 * 应用字间距到元素
 * @param {Element} targetElement - 目标DOM元素
 * @param {string} letterSpacing - 要应用的字间距值
 */
export const applyLetterSpacing = (targetElement, letterSpacing) => {
  if (!targetElement || !letterSpacing) return;
  
  try {
    // 深度清洁所有子元素，移除局部字间距设置
    const allChildren = targetElement.querySelectorAll('*');
    allChildren.forEach(child => {
      child.style.removeProperty('letter-spacing');
    });
    
    // 设置新的字间距
    targetElement.style.setProperty('letter-spacing', letterSpacing, 'important');
  } catch (e) {
    console.error("应用字间距失败:", e);
  }
};

/**
 * 应用段落间距到元素
 * @param {Element} targetElement - 目标DOM元素
 * @param {string} paragraphSpacing - 要应用的段落间距值
 */
export const applyParagraphSpacing = (targetElement, paragraphSpacing) => {
  if (!targetElement || !paragraphSpacing) return;
  
  try {
    // 深度清洁所有子元素，移除局部段落间距设置
    const allChildren = targetElement.querySelectorAll('p');
    allChildren.forEach(child => {
      child.style.removeProperty('margin-bottom');
    });
    
    // 查找段落元素并设置间距
    const paragraphs = targetElement.querySelectorAll('p');
    if (paragraphs.length > 0) {
      paragraphs.forEach(p => {
        p.style.setProperty('margin-bottom', paragraphSpacing, 'important');
      });
    } else {
      // 如果没有段落元素，假设目标元素自身就是段落，设置其下边距
      targetElement.style.setProperty('margin-bottom', paragraphSpacing, 'important');
    }
  } catch (e) {
    console.error("应用段落间距失败:", e);
  }
};

/**
 * 应用垂直对齐方式到元素
 * @param {Element} targetElement - 目标DOM元素
 * @param {string} position - 垂直位置：'top', 'middle', 'bottom'，或 'reset' 重置对齐
 */
export const applyVerticalAlign = (targetElement, position) => {
  if (!targetElement) return;
  
  try {
    // 处理重置对齐的情况
    if (position === 'reset') {
      // 移除flex布局
      targetElement.style.removeProperty('display');
      targetElement.style.removeProperty('flex-direction');
      targetElement.style.removeProperty('justify-content');
      targetElement.style.removeProperty('align-items');
      
      // 恢复原始样式
      if (targetElement.dataset.originalWidth) {
        targetElement.style.width = targetElement.dataset.originalWidth;
        delete targetElement.dataset.originalWidth;
      }
      
      if (targetElement.dataset.originalHeight) {
        targetElement.style.height = targetElement.dataset.originalHeight;
        delete targetElement.dataset.originalHeight;
      }
      
      if (targetElement.dataset.originalPadding) {
        targetElement.style.padding = targetElement.dataset.originalPadding;
        delete targetElement.dataset.originalPadding;
      }
      
      // 移除垂直对齐状态标记
      targetElement.removeAttribute('data-vertical-align');
      
      // 确保resize-handle元素保持在正确的位置
      const resizeHandles = targetElement.querySelectorAll('.resize-handle');
      resizeHandles.forEach(handle => {
        handle.style.setProperty('position', 'absolute', 'important');
      });
      
      return;
    }
    
    // 如果不是重置，继续正常的对齐操作
    if (!position) return;
    
    // 获取计算样式
    const computedStyle = window.getComputedStyle(targetElement);
    const originalWidth = computedStyle.width;
    const originalHeight = computedStyle.height;
    const originalPadding = computedStyle.padding;
    
    // 保存原始样式到dataset
    if (!targetElement.dataset.originalWidth) {
      targetElement.dataset.originalWidth = originalWidth;
    }
    
    if (!targetElement.dataset.originalHeight) {
      targetElement.dataset.originalHeight = originalHeight;
    }
    
    if (!targetElement.dataset.originalPadding) {
      targetElement.dataset.originalPadding = originalPadding;
    }
    
    // 设置flex布局
    targetElement.style.setProperty('display', 'flex', 'important');
    targetElement.style.setProperty('flex-direction', 'column', 'important');
    targetElement.style.setProperty('align-items', 'stretch', 'important'); // 确保子元素横向拉伸
    
    // 设置明确的宽高，避免flex布局改变尺寸
    if (originalWidth !== 'auto') {
      targetElement.style.setProperty('width', originalWidth, 'important');
    }
    
    if (originalHeight !== 'auto') {
      targetElement.style.setProperty('height', originalHeight, 'important');
    }
    
    // 根据所需的垂直对齐方式设置justify-content
    switch (position) {
      case 'top':
        targetElement.style.setProperty('justify-content', 'flex-start', 'important');
        targetElement.setAttribute('data-vertical-align', 'top');
        break;
      case 'middle':
        targetElement.style.setProperty('justify-content', 'center', 'important');
        targetElement.setAttribute('data-vertical-align', 'middle');
        break;
      case 'bottom':
        targetElement.style.setProperty('justify-content', 'flex-end', 'important');
        targetElement.setAttribute('data-vertical-align', 'bottom');
        break;
      default:
        // 不处理未知的位置值
        return;
    }
    
    // 处理resize-handle元素
    const resizeHandles = targetElement.querySelectorAll('.resize-handle');
    resizeHandles.forEach(handle => {
      handle.style.setProperty('position', 'absolute', 'important');
      handle.style.setProperty('width', '10px', 'important');
      handle.style.setProperty('height', '10px', 'important');
      
      // 根据handle的类名确定其位置
      if (handle.classList.contains('top')) {
        handle.style.setProperty('top', '-5px', 'important');
        handle.style.setProperty('left', '50%', 'important');
        handle.style.setProperty('transform', 'translateX(-50%)', 'important');
      } else if (handle.classList.contains('right')) {
        handle.style.setProperty('right', '-5px', 'important');
        handle.style.setProperty('top', '50%', 'important');
        handle.style.setProperty('transform', 'translateY(-50%)', 'important');
      } else if (handle.classList.contains('left')) {
        handle.style.setProperty('left', '-5px', 'important');
        handle.style.setProperty('top', '50%', 'important');
        handle.style.setProperty('transform', 'translateY(-50%)', 'important');
      } else if (handle.classList.contains('bottom')) {
        handle.style.setProperty('bottom', '-5px', 'important');
        handle.style.setProperty('left', '50%', 'important');
        handle.style.setProperty('transform', 'translateX(-50%)', 'important');
      } else if (handle.classList.contains('top-left')) {
        handle.style.setProperty('top', '-5px', 'important');
        handle.style.setProperty('left', '-5px', 'important');
      } else if (handle.classList.contains('top-right')) {
        handle.style.setProperty('top', '-5px', 'important');
        handle.style.setProperty('right', '-5px', 'important');
      } else if (handle.classList.contains('bottom-left')) {
        handle.style.setProperty('bottom', '-5px', 'important');
        handle.style.setProperty('left', '-5px', 'important');
      } else if (handle.classList.contains('bottom-right')) {
        handle.style.setProperty('bottom', '-5px', 'important');
        handle.style.setProperty('right', '-5px', 'important');
      }
    });
  } catch (e) {
    console.error("应用垂直对齐失败:", e);
  }
};

/**
 * 日志工具函数
 * 包含日志记录中使用的各种辅助函数
 */

/**
 * 判断是否应该跳过日志记录 (现在主要供手动调用判断)
 * @param {string} path 请求路径
 * @param {Array} excludePaths 排除的路径列表
 * @returns {boolean} 是否跳过
 */
function shouldSkipLogging(path, excludePaths = []) {
  // 添加更多需要排除的路径
  const defaultExcludePaths = [
    // 基础健康与状态检查
    '/api/health',
    '/api/heartbeat',
    '/api/ping',
    '/api/system/status',
    '/api/system-info', // 系统信息API
    '/api/system/policies', // 系统策略API

    // 认证与用户信息（频繁调用）
    '/api/auth/me',
    '/api/auth/check',
    '/api/auth/token',
    '/api/auth/verify',
    '/api/profile/view', // 个人资料查看通常不需要记录

    // 用户封面相关查询（频繁调用）- 只排除高频查询操作，保留创建操作的日志
    '/api/user/covers', // 查询封面列表
    '/api/covers/templates', // 查询模板
    '/api/covers/search', // 搜索封面
    '/api/cover/base-prompts', // 基础提示词
    // 保留以下操作的日志记录
    // '/api/user/cover-records',
    // '/api/user/cover',
    // '/api/covers',
    // '/api/cover',
    // '/api/generate',
    // '/api/covers/generate',
    // '/api/covers/history',
    // '/api/covers/favorite',

    // 用户个人资料相关查询（频繁调用）
    '/api/user/profile',
    '/api/user/point-records',
    '/api/user/settings',
    '/api/user/preferences',

    // 其他高频查询
    '/api/styles/list',
    '/api/styles/categories',
    '/api/styles/popular',
    '/api/styles/search',
    '/api/prompts/list',
    '/api/prompts/categories',
    '/api/style', // 风格查询

    // 日志和统计相关（避免无限循环和冗余）
    '/api/logs',
    '/api/admin/logs',
    '/api/admin/logs/stats',
    '/api/admin/logs/test', // 测试日志API
    '/api/stats/summary',
    '/api/admin/statistics/summary', // 管理员统计摘要
    '/api/admin/statistics/trend', // 管理员趋势
    '/api/admin/statistics/list', // 管理员列表
    '/api/admin/statistics/dashboard', // 管理员仪表盘
    '/api/admin/stats', // 管理员统计

    // 设置获取（频繁调用）
    '/api/settings/get',
    '/api/admin/settings/list', // 管理员设置列表
    '/api/admin/settings', // 管理员设置
    '/api/configs', // 假设有配置相关的频繁获取
    '/api/system/config',

    // 任务状态轮询或检查（如果存在）
    '/api/tasks/status',
    '/api/admin/tasks/poll',
    '/api/admin/tasks', // 管理员任务列表
    '/api/tasks/poll',
    '/api/tasks/check',

    // AI服务相关
    '/api/admin/ai-services', // AI服务列表

    // 前端资源和静态文件请求
    '/static',
    '/assets',
    '/images',
    '/css',
    '/js',
    '/fonts',
    '/favicon.ico',
    '/manifest.json',
    '/robots.txt',
    '/sitemap.xml',
    '/admin/css',
    '/admin/js',
    '/admin/img',
    '/uploads',

    // 批量操作的子请求（如果存在）
    '/api/batch',

    // 特定第三方回调或webhook（通常不需要记录）
    '/api/webhook/payment',
    '/api/callback',

    // WebSocket连接或消息（通常单独处理）
    '/socket.io',
    '/ws'
  ];

  // 合并默认排除路径和传入的排除路径
  const allExcludePaths = [...defaultExcludePaths, ...(excludePaths || [])];

  // 更强大的路径匹配方法
  return allExcludePaths.some(excludePath => {
    if (excludePath instanceof RegExp) {
      return excludePath.test(path);
    } else if (typeof excludePath === 'string') {
      // 精确匹配
      if (excludePath === path) return true;
      // 前缀匹配（确保以/结尾或是完整路径段）
      if (path.startsWith(excludePath) && (excludePath.endsWith('/') || path.charAt(excludePath.length) === '/')) {
        return true;
      }
      // 检查是否是静态文件后缀
      if (path.match(/\.(jpg|jpeg|png|gif|svg|ico|css|js|woff|woff2|ttf|eot)$/i)) {
        return true;
      }
      // 后缀匹配
      if (excludePath.startsWith('/') && path.endsWith(excludePath)) {
         // 简单的后缀匹配，用于.ico, .json等
         if (!excludePath.includes('/', 1)) return true;
      }
    }
    return false;
  });
}

/**
 * 从请求方法获取操作类型
 * @param {string} method HTTP方法
 * @returns {string} 操作类型
 */
function getActionFromMethod(method) {
  switch (method.toUpperCase()) {
    case 'GET': return 'query';
    case 'POST': return 'create';
    case 'PUT': return 'update';
    case 'DELETE': return 'delete';
    default: return 'other';
  }
}

/**
 * 从请求路径获取模块名称
 * @param {string} path 请求路径
 * @returns {string} 模块名称
 */
function getModuleFromPath(path) {
  const parts = path.split('/').filter(Boolean);

  if (parts.length < 2 || parts[0] !== 'api') {
    // 非API路径或根路径
    if (path.includes('admin')) return 'admin_panel'; // 管理后台页面访问
    if (path.includes('dashboard')) return 'dashboard'; // 用户仪表盘页面访问
    // 检查是否是静态资源
    if (path.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i)) {
      return 'static_assets';
    }
    return 'frontend'; // 其他前端路径
  }

  // API路径解析 (parts[0] === 'api')
  const modulePart = parts[1];
  const subModulePart = parts[2];
  const actionPart = parts[parts.length - 1];

  // 1. 最高优先级：管理员接口
  if (modulePart === 'admin') {
    if (subModulePart) {
      // 细化管理员模块
      if (['users', 'user'].includes(subModulePart)) return 'admin_users';
      if (['covers', 'cover', 'cover-records'].includes(subModulePart)) return 'admin_covers';
      if (['styles', 'style', 'style-prompts', 'base-prompts'].includes(subModulePart)) return 'admin_styles';
      if (['features', 'feature'].includes(subModulePart)) return 'admin_features';
      if (['settings', 'setting', 'configs'].includes(subModulePart)) return 'admin_settings';
      if (['logs', 'log'].includes(subModulePart)) return 'admin_logs';
      if (['stats', 'statistics'].includes(subModulePart)) return 'admin_statistics';
      if (['tasks', 'task'].includes(subModulePart)) return 'admin_tasks';
      if (['payment', 'payments', 'point-records'].includes(subModulePart)) return 'admin_payment';
      if (['ai-services', 'ai_services'].includes(subModulePart)) return 'admin_ai_services';
      return `admin_${subModulePart}`; // 返回具体的管理子模块
    }
    return 'admin_general'; // 通用管理接口
  }

  // 2. 核心业务模块
  if (['covers', 'cover', 'generate'].includes(modulePart)) return 'covers';
  if (['styles', 'style', 'prompts', 'base-prompts', 'style-prompts'].includes(modulePart)) return 'styles';
  if (['users', 'user', 'profile'].includes(modulePart)) return 'users';
  if (['tasks', 'task'].includes(modulePart)) return 'tasks';
  if (['payment', 'payments', 'points', 'point-records', 'recharge', 'vip'].includes(modulePart)) return 'payment';

  // 3. 辅助模块
  if (['auth'].includes(modulePart)) return 'auth';
  if (['stats', 'statistics'].includes(modulePart)) return 'statistics';
  if (['settings', 'setting', 'configs', 'system'].includes(modulePart)) return 'settings';
  if (['features', 'feature'].includes(modulePart)) return 'features';
  if (['uploads', 'upload'].includes(modulePart)) return 'uploads';
  if (['logs', 'log'].includes(modulePart)) return 'logs';
  if (['sms', 'message'].includes(modulePart)) return 'sms';
  if (['ai-services', 'ai_services'].includes(modulePart)) return 'ai_services';

  // 4. 根据特定路径或动作判断
  if (path.includes('/login')) return 'auth';
  if (path.includes('/logout')) return 'auth';
  if (path.includes('/register')) return 'auth';
  if (path.includes('/vip/check')) return 'payment';

  // 5. 如果以上都未匹配，使用API后的第一部分作为模块名
  if (modulePart) {
    return modulePart;
  }

  return 'unknown'; // 最终无法识别的模块
}

/**
 * 从请求路径和方法获取操作描述
 * @param {string} path 请求路径
 * @param {string} method HTTP方法
 * @returns {string} 操作描述
 */
function getDescriptionFromPath(path, method) {
  // 根据路径和方法生成描述
  const module = getModuleFromPath(path);
  const action = getActionFromMethod(method);

  // 特殊路径处理
  if (path.includes('/login')) {
    return '用户登录';
  }
  if (path.includes('/logout')) {
    return '用户登出';
  }

  // 通用描述
  const actionMap = {
    query: '查询',
    create: '创建',
    update: '更新',
    delete: '删除'
  };

  const moduleMap = {
    users: '用户',
    covers: '封面模板',
    styles: '风格模板',
    'style-prompts': '样式提示词',
    'base-prompts': '基础提示词',
    'user-manage': '用户管理',
    settings: '系统设置',
    features: '功能控制',
    statistics: '数据统计',
    'ai-services': 'AI服务',
    auth: '认证',
    user: '用户信息',
    admin: '管理操作',
    unknown: '未知模块'
    // 添加更多中文映射
  };

  const actionText = actionMap[action] || action;
  const moduleText = moduleMap[module] || module;

  return `${actionText}${moduleText}`;
}

/**
 * 清理请求体，移除敏感信息和过大的数据
 * @param {Object} body 请求体数据
 * @returns {Object} 清理后的请求体
 */
function sanitizeRequestBody(body) {
  if (!body) return {};

  // 创建一个副本以避免修改原始对象
  const sanitized = {};

  // 只保留最重要的字段，最多10个字段
  let count = 0;
  for (const key in body) {
    // 跳过敏感字段
    if (['password', 'token', 'secret', 'apiKey', 'api_key', 'key', 'auth'].includes(key)) {
      sanitized[key] = '[REDACTED]';
      continue;
    }

    // 限制字段数量
    if (count >= 10) {
      sanitized['...'] = '截断了剩余字段';
      break;
    }

    // 处理大型字段
    const value = body[key];
    if (typeof value === 'string' && value.length > 100) {
      sanitized[key] = value.substring(0, 100) + '... [截断]';
    } else if (Array.isArray(value) && value.length > 5) {
      sanitized[key] = value.slice(0, 5).concat([`... 等 ${value.length - 5} 项`]);
    } else if (typeof value === 'object' && value !== null) {
      // 对于对象，可以简单标记或尝试序列化（注意大小）
      sanitized[key] = '[Object]';
    } else {
      sanitized[key] = value;
    }

    count++;
  }

  return sanitized;
}

/**
 * 清理响应数据，移除敏感信息和过大的数据
 * @param {Object} data 响应数据
 * @returns {Object} 清理后的响应数据
 */
function sanitizeResponseData(data) {
  if (!data) return {};

  // 只保留状态、消息和数据摘要
  const sanitized = {
    success: data.success,
    message: data.message
  };

  // 记录数据的存在性和类型，而不是完整数据
  if (data.data !== undefined) {
    if (Array.isArray(data.data)) {
      sanitized.dataType = 'array';
      sanitized.dataLength = data.data.length;
    } else if (typeof data.data === 'object' && data.data !== null) {
      sanitized.dataType = 'object';
      sanitized.dataKeys = Object.keys(data.data).slice(0, 5); // 最多记录5个键名
      if (Object.keys(data.data).length > 5) {
        sanitized.dataKeys.push('...');
      }
    } else {
      // 对于基本类型，可以记录值（如果安全且不大）
      if (typeof data.data !== 'object' && String(data.data).length < 50) {
        sanitized.dataValue = data.data;
      } else {
        sanitized.dataType = typeof data.data;
      }
    }
  }

  // 对于分页数据，记录分页信息
  if (data.pagination || data.page || data.totalPages) {
    sanitized.pagination = data.pagination || {
      page: data.page,
      limit: data.limit || data.pageSize,
      total: data.total || data.totalRecords,
      totalPages: data.totalPages
    };
  }

  return sanitized;
}

module.exports = {
  getActionFromMethod,
  getModuleFromPath,
  getDescriptionFromPath,
  sanitizeRequestBody,
  sanitizeResponseData,
  shouldSkipLogging
};
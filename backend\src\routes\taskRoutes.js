const express = require('express');
const router = express.Router();
const taskController = require('../controllers/taskController');
const { auth, adminAuth } = require('../middlewares/authMiddleware');

// 所有路由都需要认证和管理员权限
router.use(auth);
router.use(adminAuth);

// 获取任务列表
router.get('/', taskController.getTaskList);

// 获取任务详情
router.get('/:id', taskController.getTaskDetail);

// 取消任务
router.post('/:id/cancel', taskController.cancelTask);

// 删除任务记录
router.delete('/:id', taskController.deleteTask);

// 清理任务记录
router.post('/cleanup', taskController.cleanupTasks);

module.exports = router;

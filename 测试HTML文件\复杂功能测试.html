<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复杂功能测试 - 高级特性验证</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .title {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: bold;
            text-align: center;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .test-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }
        
        .card-title {
            color: #495057;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .card-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background: #667eea;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        .interactive-element {
            margin: 10px 0;
            padding: 15px;
            border: 2px dashed #ced4da;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .interactive-element:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .interactive-element.active {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .test-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .canvas-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            height: 200px;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .iframe-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            height: 200px;
        }
        
        .result-display {
            background: #e9ecef;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        .status-info { background: #17a2b8; }
        
        .feature-info {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 复杂功能测试</h1>
        <p class="subtitle">测试高级HTML特性在不同架构下的表现差异</p>
        
        <div class="feature-info">
            <strong>测试说明：</strong>此文件包含Canvas绘图、视频播放、iframe嵌入、WebGL等高级功能，用于验证新旧架构对复杂HTML特性的支持程度。
        </div>
        
        <div class="test-grid">
            <!-- Canvas绘图测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="card-icon">🎨</span>
                    Canvas绘图测试
                </div>
                <div class="canvas-container">
                    <canvas id="testCanvas" width="280" height="150"></canvas>
                </div>
                <button class="test-button" onclick="testCanvasDrawing()">开始绘图测试</button>
                <div class="result-display" id="canvasResult">
                    <span class="status-indicator status-info"></span>等待测试...
                </div>
            </div>
            
            <!-- 本地存储测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="card-icon">💾</span>
                    本地存储测试
                </div>
                <div class="interactive-element" onclick="testLocalStorage()">
                    点击测试localStorage功能
                </div>
                <div class="interactive-element" onclick="testSessionStorage()">
                    点击测试sessionStorage功能
                </div>
                <div class="result-display" id="storageResult">
                    <span class="status-indicator status-info"></span>等待测试...
                </div>
            </div>
            
            <!-- 地理位置测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="card-icon">📍</span>
                    地理位置API测试
                </div>
                <button class="test-button" onclick="testGeolocation()">获取地理位置</button>
                <div class="result-display" id="geolocationResult">
                    <span class="status-indicator status-info"></span>等待测试...
                </div>
            </div>
            
            <!-- 通知API测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="card-icon">🔔</span>
                    通知API测试
                </div>
                <button class="test-button" onclick="testNotification()">发送通知</button>
                <div class="result-display" id="notificationResult">
                    <span class="status-indicator status-info"></span>等待测试...
                </div>
            </div>
            
            <!-- WebGL测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="card-icon">🎮</span>
                    WebGL测试
                </div>
                <div class="canvas-container">
                    <canvas id="webglCanvas" width="280" height="150"></canvas>
                </div>
                <button class="test-button" onclick="testWebGL()">测试WebGL</button>
                <div class="result-display" id="webglResult">
                    <span class="status-indicator status-info"></span>等待测试...
                </div>
            </div>
            
            <!-- 文件API测试 -->
            <div class="test-card">
                <div class="card-title">
                    <span class="card-icon">📁</span>
                    文件API测试
                </div>
                <input type="file" id="fileInput" accept="image/*" style="margin: 10px 0;">
                <button class="test-button" onclick="testFileAPI()">测试文件读取</button>
                <div class="result-display" id="fileResult">
                    <span class="status-indicator status-info"></span>等待测试...
                </div>
            </div>
        </div>
        
        <!-- 综合测试结果 -->
        <div class="test-card" style="margin-top: 30px;">
            <div class="card-title">
                <span class="card-icon">📊</span>
                综合功能支持评估
            </div>
            <div id="overallAssessment" class="result-display" style="max-height: none; font-size: 14px;">
                <span class="status-indicator status-info"></span>请执行上方各项测试以获得综合评估...
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            canvas: null,
            localStorage: null,
            sessionStorage: null,
            geolocation: null,
            notification: null,
            webgl: null,
            fileAPI: null
        };
        
        function testCanvasDrawing() {
            try {
                const canvas = document.getElementById('testCanvas');
                const ctx = canvas.getContext('2d');
                
                // 清除画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制渐变背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 绘制文本
                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Canvas测试成功!', canvas.width/2, canvas.height/2);
                
                // 绘制动画圆圈
                let angle = 0;
                const animate = () => {
                    ctx.save();
                    ctx.translate(canvas.width/2, canvas.height/2 + 30);
                    ctx.rotate(angle);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(0, 0, 20, 0, Math.PI);
                    ctx.stroke();
                    ctx.restore();
                    angle += 0.1;
                    if (angle < Math.PI * 4) {
                        requestAnimationFrame(animate);
                    }
                };
                animate();
                
                document.getElementById('canvasResult').innerHTML = 
                    '<span class="status-indicator status-success"></span>Canvas绘图功能正常';
                testResults.canvas = 'success';
            } catch (error) {
                document.getElementById('canvasResult').innerHTML = 
                    '<span class="status-indicator status-error"></span>Canvas功能被限制: ' + error.message;
                testResults.canvas = 'error';
            }
            updateOverallAssessment();
        }
        
        function testLocalStorage() {
            try {
                const testKey = 'complex_test_' + Date.now();
                const testValue = { message: 'localStorage测试', timestamp: new Date().toISOString() };
                
                localStorage.setItem(testKey, JSON.stringify(testValue));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                localStorage.removeItem(testKey);
                
                if (retrieved && retrieved.message === testValue.message) {
                    document.getElementById('storageResult').innerHTML = 
                        '<span class="status-indicator status-success"></span>localStorage功能正常';
                    testResults.localStorage = 'success';
                } else {
                    throw new Error('数据不匹配');
                }
            } catch (error) {
                document.getElementById('storageResult').innerHTML = 
                    '<span class="status-indicator status-error"></span>localStorage被限制: ' + error.message;
                testResults.localStorage = 'error';
            }
            updateOverallAssessment();
        }
        
        function testSessionStorage() {
            try {
                const testKey = 'session_test_' + Date.now();
                const testValue = 'sessionStorage测试数据';
                
                sessionStorage.setItem(testKey, testValue);
                const retrieved = sessionStorage.getItem(testKey);
                sessionStorage.removeItem(testKey);
                
                if (retrieved === testValue) {
                    document.getElementById('storageResult').innerHTML += 
                        '<br><span class="status-indicator status-success"></span>sessionStorage功能正常';
                    testResults.sessionStorage = 'success';
                } else {
                    throw new Error('数据不匹配');
                }
            } catch (error) {
                document.getElementById('storageResult').innerHTML += 
                    '<br><span class="status-indicator status-error"></span>sessionStorage被限制: ' + error.message;
                testResults.sessionStorage = 'error';
            }
            updateOverallAssessment();
        }
        
        function testGeolocation() {
            if (!navigator.geolocation) {
                document.getElementById('geolocationResult').innerHTML = 
                    '<span class="status-indicator status-error"></span>地理位置API不支持';
                testResults.geolocation = 'error';
                updateOverallAssessment();
                return;
            }
            
            document.getElementById('geolocationResult').innerHTML = 
                '<span class="status-indicator status-warning"></span>正在获取地理位置...';
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    document.getElementById('geolocationResult').innerHTML = 
                        '<span class="status-indicator status-success"></span>地理位置获取成功<br>' +
                        `纬度: ${position.coords.latitude.toFixed(4)}<br>` +
                        `经度: ${position.coords.longitude.toFixed(4)}`;
                    testResults.geolocation = 'success';
                    updateOverallAssessment();
                },
                (error) => {
                    document.getElementById('geolocationResult').innerHTML = 
                        '<span class="status-indicator status-error"></span>地理位置获取失败: ' + error.message;
                    testResults.geolocation = 'error';
                    updateOverallAssessment();
                },
                { timeout: 10000 }
            );
        }
        
        function testNotification() {
            if (!('Notification' in window)) {
                document.getElementById('notificationResult').innerHTML = 
                    '<span class="status-indicator status-error"></span>通知API不支持';
                testResults.notification = 'error';
                updateOverallAssessment();
                return;
            }
            
            if (Notification.permission === 'granted') {
                new Notification('测试通知', {
                    body: '通知API功能正常工作',
                    icon: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="%23667eea"/></svg>'
                });
                document.getElementById('notificationResult').innerHTML = 
                    '<span class="status-indicator status-success"></span>通知发送成功';
                testResults.notification = 'success';
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then((permission) => {
                    if (permission === 'granted') {
                        testNotification();
                    } else {
                        document.getElementById('notificationResult').innerHTML = 
                            '<span class="status-indicator status-warning"></span>通知权限被拒绝';
                        testResults.notification = 'warning';
                    }
                    updateOverallAssessment();
                });
                return;
            } else {
                document.getElementById('notificationResult').innerHTML = 
                    '<span class="status-indicator status-warning"></span>通知权限被拒绝';
                testResults.notification = 'warning';
            }
            updateOverallAssessment();
        }
        
        function testWebGL() {
            try {
                const canvas = document.getElementById('webglCanvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (!gl) {
                    throw new Error('WebGL不支持');
                }
                
                // 设置视口
                gl.viewport(0, 0, canvas.width, canvas.height);
                
                // 清除颜色
                gl.clearColor(0.4, 0.5, 0.9, 1.0);
                gl.clear(gl.COLOR_BUFFER_BIT);
                
                // 简单的着色器
                const vertexShaderSource = `
                    attribute vec2 a_position;
                    void main() {
                        gl_Position = vec4(a_position, 0.0, 1.0);
                    }
                `;
                
                const fragmentShaderSource = `
                    precision mediump float;
                    void main() {
                        gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0);
                    }
                `;
                
                // 创建着色器
                const vertexShader = gl.createShader(gl.VERTEX_SHADER);
                gl.shaderSource(vertexShader, vertexShaderSource);
                gl.compileShader(vertexShader);
                
                const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER);
                gl.shaderSource(fragmentShader, fragmentShaderSource);
                gl.compileShader(fragmentShader);
                
                // 创建程序
                const program = gl.createProgram();
                gl.attachShader(program, vertexShader);
                gl.attachShader(program, fragmentShader);
                gl.linkProgram(program);
                gl.useProgram(program);
                
                // 绘制三角形
                const vertices = new Float32Array([
                    0.0,  0.5,
                   -0.5, -0.5,
                    0.5, -0.5
                ]);
                
                const buffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
                gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
                
                const positionLocation = gl.getAttribLocation(program, 'a_position');
                gl.enableVertexAttribArray(positionLocation);
                gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);
                
                gl.drawArrays(gl.TRIANGLES, 0, 3);
                
                document.getElementById('webglResult').innerHTML = 
                    '<span class="status-indicator status-success"></span>WebGL功能正常';
                testResults.webgl = 'success';
            } catch (error) {
                document.getElementById('webglResult').innerHTML = 
                    '<span class="status-indicator status-error"></span>WebGL功能被限制: ' + error.message;
                testResults.webgl = 'error';
            }
            updateOverallAssessment();
        }
        
        function testFileAPI() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                document.getElementById('fileResult').innerHTML = 
                    '<span class="status-indicator status-warning"></span>请先选择一个图片文件';
                return;
            }
            
            try {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('fileResult').innerHTML = 
                        '<span class="status-indicator status-success"></span>文件读取成功<br>' +
                        `文件名: ${file.name}<br>` +
                        `文件大小: ${(file.size / 1024).toFixed(2)} KB<br>` +
                        `文件类型: ${file.type}`;
                    testResults.fileAPI = 'success';
                    updateOverallAssessment();
                };
                
                reader.onerror = function() {
                    document.getElementById('fileResult').innerHTML = 
                        '<span class="status-indicator status-error"></span>文件读取失败';
                    testResults.fileAPI = 'error';
                    updateOverallAssessment();
                };
                
                reader.readAsDataURL(file);
            } catch (error) {
                document.getElementById('fileResult').innerHTML = 
                    '<span class="status-indicator status-error"></span>文件API被限制: ' + error.message;
                testResults.fileAPI = 'error';
                updateOverallAssessment();
            }
        }
        
        function updateOverallAssessment() {
            const completedTests = Object.values(testResults).filter(result => result !== null).length;
            const successfulTests = Object.values(testResults).filter(result => result === 'success').length;
            const errorTests = Object.values(testResults).filter(result => result === 'error').length;
            const warningTests = Object.values(testResults).filter(result => result === 'warning').length;
            
            if (completedTests === 0) return;
            
            let assessmentText = `测试进度: ${completedTests}/7 项完成<br>`;
            assessmentText += `✅ 成功: ${successfulTests} 项<br>`;
            assessmentText += `⚠️ 警告: ${warningTests} 项<br>`;
            assessmentText += `❌ 失败: ${errorTests} 项<br><br>`;
            
            let architectureType = '';
            let statusIcon = '';
            
            if (successfulTests >= 5) {
                architectureType = '🚀 高兼容性架构：支持大部分高级HTML特性';
                statusIcon = 'status-success';
            } else if (successfulTests >= 3) {
                architectureType = '⚖️ 平衡型架构：在功能和安全之间取得平衡';
                statusIcon = 'status-warning';
            } else {
                architectureType = '🛡️ 安全优先架构：限制了部分功能以确保安全';
                statusIcon = 'status-error';
            }
            
            assessmentText += `架构评估: ${architectureType}`;
            
            document.getElementById('overallAssessment').innerHTML = 
                `<span class="status-indicator ${statusIcon}"></span>${assessmentText}`;
        }
        
        // 页面加载完成后自动执行一些测试
        window.addEventListener('load', function() {
            console.log('复杂功能测试页面加载完成');
            
            // 自动测试Canvas
            setTimeout(() => testCanvasDrawing(), 1000);
        });
    </script>
</body>
</html>

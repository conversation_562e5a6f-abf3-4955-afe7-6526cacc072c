import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
// 导入多样化图标
import {
  InstagramIcon, // 用于小红书封面
  MessagesSquare, // 用于微信公众号
  MonitorPlay, // 用于视频类封面
  FileText, // 通用文件
  Image, // 通用图片
  PanelTop // 通用banner
} from 'lucide-react';

// 更简化的卡片组件
const WorkCard = ({ record, onClick }) => {
  const title = record?.cover_text || record?.cover_type_name || record?.cover_type_display || record?.cover_type || '未命名作品';
  const coverType = record?.cover_type_display || record?.cover_type || '';

  // 根据封面类型选择图标和颜色
  const getIconAndColor = () => {
    // 匹配小红书封面
    if (coverType.includes('小红书')) {
      return {
        icon: <InstagramIcon size={22} />,
        gradient: 'from-rose-500 to-pink-500',
        bgColor: 'bg-rose-50'
      };
    }
    // 匹配微信公众号
    else if (coverType.includes('公众号') || coverType.includes('微信')) {
      return {
        icon: <MessagesSquare size={22} />,
        gradient: 'from-green-500 to-emerald-500',
        bgColor: 'bg-green-50'
      };
    }
    // 匹配视频封面
    else if (coverType.includes('视频') || coverType.includes('B站')) {
      return {
        icon: <MonitorPlay size={22} />,
        gradient: 'from-blue-500 to-cyan-500',
        bgColor: 'bg-blue-50'
      };
    }
    // 匹配文章或文本
    else if (coverType.includes('文章')) {
      return {
        icon: <FileText size={22} />,
        gradient: 'from-amber-500 to-yellow-500',
        bgColor: 'bg-amber-50'
      };
    }
    // 默认
    return {
      icon: <Image size={22} />,
      gradient: 'from-indigo-500 to-purple-500',
      bgColor: 'bg-indigo-50'
    };
  };

  const { icon, gradient, bgColor } = getIconAndColor();

  const handleCardClick = () => {
    if (onClick && typeof onClick === 'function') {
      onClick(record);
    }
  };

  return (
          <Card
      className="shadow-sm w-4/5 mx-auto overflow-hidden flex flex-col cursor-pointer h-4/5 border-0 bg-gradient-to-br from-background to-muted"
            onClick={handleCardClick}
      title={title}
          >
      <div className={`aspect-video ${bgColor} flex items-center justify-center overflow-hidden p-2`}>
        <div className={`w-10 h-10 rounded-full bg-gradient-to-br ${gradient} flex items-center justify-center text-white shadow-md`}>
                {icon}
              </div>
            </div>
            <CardContent className="p-2 flex flex-col flex-grow">
              <h3 className="text-xs font-medium text-foreground leading-tight text-center line-clamp-2" title={title}>
                {title}
              </h3>
            </CardContent>
          </Card>
  );
};

export default WorkCard;
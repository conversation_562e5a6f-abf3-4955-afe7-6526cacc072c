/**
 * HTML内容处理器 - 第五阶段核心模块
 * 负责HTML内容的清理、优化和不同渲染模式的处理
 */

class HtmlContentProcessor {
  constructor() {
    this.renderingModeSelector = null;
    this.serverSideRenderer = null;
    this.securityDetector = null;
  }

  /**
   * 初始化处理器
   */
  async init() {
    try {
      // 动态导入其他模块，避免循环依赖
      const { RenderingModeSelector } = await import('./renderingModeSelector.js');
      const { ServerSideRenderer } = await import('./serverSideRenderer.js');
      const { SecurityDetector } = await import('./securityDetector.js');
      
      this.renderingModeSelector = new RenderingModeSelector();
      this.serverSideRenderer = new ServerSideRenderer();
      this.securityDetector = new SecurityDetector();
      
      console.log('✅ HTML内容处理器初始化成功');
    } catch (error) {
      console.error('❌ HTML内容处理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理HTML内容 - 主入口方法
   * @param {string} htmlContent - 原始HTML内容
   * @param {Object} options - 处理选项
   * @returns {Object} 处理结果
   */
  async processHtmlContent(htmlContent, options = {}) {
    try {
      const {
        mode = 'auto', // auto, standard, advanced
        enableSecurity = true,
        enableOptimization = true,
        preserveEditing = true
      } = options;

      console.log('🔄 开始处理HTML内容...');

      // 1. 安全检测
      let securityResult = null;
      if (enableSecurity && this.securityDetector) {
        securityResult = await this.securityDetector.detect(htmlContent);
        console.log(`🔍 安全检测完成，风险等级: ${securityResult.riskLevel}`);
      }

      // 2. 选择渲染模式
      let selectedMode = mode;
      if (mode === 'auto' && this.renderingModeSelector) {
        const complexity = await this.renderingModeSelector.analyzeComplexity(htmlContent);
        selectedMode = await this.renderingModeSelector.selectRenderingMode(complexity);
        console.log(`🎯 自动选择渲染模式: ${selectedMode}`);
      }

      // 3. 根据模式处理内容
      let processedContent = htmlContent;
      if (selectedMode === 'advanced' && this.serverSideRenderer) {
        // 高级模式：服务端渲染
        processedContent = await this.serverSideRenderer.generateCompleteHtml(
          htmlContent, 
          preserveEditing
        );
        console.log('🚀 高级模式处理完成');
      } else {
        // 标准模式：基础清理
        processedContent = this.cleanHtmlContent(htmlContent);
        console.log('✨ 标准模式处理完成');
      }

      // 4. 优化处理
      if (enableOptimization) {
        processedContent = this.optimizeForStorage(processedContent);
      }

      return {
        success: true,
        originalContent: htmlContent,
        processedContent: processedContent,
        renderingMode: selectedMode,
        securityResult: securityResult,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ HTML内容处理失败:', error);
      return {
        success: false,
        error: error.message,
        originalContent: htmlContent,
        processedContent: htmlContent, // 失败时返回原内容
        renderingMode: 'standard',
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 清理HTML内容
   * @param {string} htmlContent - HTML内容
   * @returns {string} 清理后的HTML
   */
  cleanHtmlContent(htmlContent) {
    if (!htmlContent || typeof htmlContent !== 'string') {
      return '';
    }

    let cleaned = htmlContent;

    // 移除编辑器相关的类和属性
    cleaned = cleaned.replace(/\s*class="[^"]*(?:resize-handle|editing-mode|selected)[^"]*"/g, '');
    cleaned = cleaned.replace(/\s*data-editing="[^"]*"/g, '');
    cleaned = cleaned.replace(/\s*contenteditable="[^"]*"/g, '');
    cleaned = cleaned.replace(/\s*draggable="[^"]*"/g, '');

    // 清理多余的空白
    cleaned = cleaned.replace(/\s+/g, ' ');
    cleaned = cleaned.replace(/>\s+</g, '><');

    return cleaned.trim();
  }

  /**
   * 存储优化
   * @param {string} htmlContent - HTML内容
   * @returns {string} 优化后的HTML
   */
  optimizeForStorage(htmlContent) {
    if (!htmlContent) return '';

    let optimized = htmlContent;

    // 压缩CSS
    optimized = optimized.replace(/<style[^>]*>([\s\S]*?)<\/style>/gi, (match, css) => {
      const compressedCss = css
        .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
        .replace(/\s+/g, ' ') // 压缩空白
        .replace(/;\s*}/g, '}') // 移除最后的分号
        .trim();
      return `<style>${compressedCss}</style>`;
    });

    // 压缩JavaScript
    optimized = optimized.replace(/<script[^>]*>([\s\S]*?)<\/script>/gi, (match, js) => {
      const compressedJs = js
        .replace(/\/\/.*$/gm, '') // 移除单行注释
        .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
        .replace(/\s+/g, ' ') // 压缩空白
        .trim();
      return `<script>${compressedJs}</script>`;
    });

    return optimized;
  }

  /**
   * 准备编辑模式
   * @param {string} htmlContent - HTML内容
   * @returns {string} 编辑就绪的HTML
   */
  prepareForEditing(htmlContent) {
    if (!htmlContent) return '';

    let prepared = htmlContent;

    // 为文本元素添加编辑属性
    const textTags = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'span', 'div'];
    textTags.forEach(tag => {
      const regex = new RegExp(`<${tag}([^>]*)>([^<]+)<\/${tag}>`, 'gi');
      prepared = prepared.replace(regex, (match, attrs, content) => {
        if (!attrs.includes('contenteditable')) {
          return `<${tag}${attrs} contenteditable="true" data-editing="text">${content}</${tag}>`;
        }
        return match;
      });
    });

    return prepared;
  }

  /**
   * 生成最终HTML
   * @param {string} htmlContent - HTML内容
   * @param {Object} options - 生成选项
   * @returns {string} 最终HTML
   */
  generateFinalHtml(htmlContent, options = {}) {
    const {
      removeEditingFeatures = true,
      optimize = true,
      addMetadata = true
    } = options;

    let finalHtml = htmlContent;

    if (removeEditingFeatures) {
      finalHtml = this.cleanHtmlContent(finalHtml);
    }

    if (optimize) {
      finalHtml = this.optimizeForStorage(finalHtml);
    }

    if (addMetadata) {
      // 添加生成元数据
      const metadata = `<!-- Generated by 封面生成系统 at ${new Date().toISOString()} -->`;
      finalHtml = metadata + '\n' + finalHtml;
    }

    return finalHtml;
  }
}

// 创建单例实例
const htmlContentProcessor = new HtmlContentProcessor();

// 导出类和实例
export { HtmlContentProcessor, htmlContentProcessor };
export default htmlContentProcessor;

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

/**
 * 用户模型
 * 对应数据库中的users表
 */
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '用户ID，唯一标识'
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: false,
    // 移除unique约束，避免重复创建索引
    // unique: true,
    validate: {
      is: /^1[3-9]\d{9}$/,  // 中国手机号验证
    },
    comment: '用户手机号，用于登录'
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: true,
    validate: {
      isEmail: true,  // 邮箱格式验证
    },
    comment: '用户邮箱地址'
  },
  password: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '用户密码，存储加密后的密码'
  },
  has_set_password: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '用户是否已设置密码，验证码登录自动注册的用户默认为false'
  },
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '用户昵称'
  },
  avatar: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '用户头像URL'
  },
  role: {
    type: DataTypes.ENUM('user', 'vip', 'admin'),
    defaultValue: 'user',
    comment: '用户角色：普通用户、VIP用户、管理员'
  },
  status: {
    type: DataTypes.ENUM('active', 'locked'),
    defaultValue: 'active',
    allowNull: false,
    comment: '用户状态：正常、锁定'
  },
  failed_login_attempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false,
    comment: '失败登录尝试次数'
  },
  last_failed_login_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '最后失败登录尝试时间'
  },
  lock_expires_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '账号锁定到期时间'
  },
  points: {
    type: DataTypes.INTEGER,
    defaultValue: 50,
    comment: '用户当前积分，新用户默认50积分'
  },
  vip_expire_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'VIP会员到期时间，非VIP为空'
  },
  last_points_reset: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '上次积分重置时间，用于VIP每日积分重置'
  },
  last_login_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '用户上次登录的时间'
  },
  login_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '用户登录总次数'
  },
  last_login_type: {
    type: DataTypes.ENUM('phone', 'password', 'wechat', 'qq', 'weibo'),
    allowNull: true,
    comment: '上次登录类型'
  },
  last_login_ip: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '上次登录IP地址'
  },
  daily_points: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: true,
    comment: '用户每日积分'
  },
  last_daily_points_update: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '上次每日积分更新时间'
  }
}, {
  tableName: 'users',
  timestamps: true,  // 启用 createdAt 和 updatedAt
  underscored: true, // 使用下划线命名约定
  hooks: {
    // 在保存前对密码进行加密
    beforeCreate: async (user) => {
      if (user.password) {
        const salt = await bcrypt.genSalt(10);
        user.password = await bcrypt.hash(user.password, salt);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        const salt = await bcrypt.genSalt(10);
        user.password = await bcrypt.hash(user.password, salt);
      }
    }
  }
});

// 实例方法：验证密码
User.prototype.validatePassword = async function(password) {
  try {
    return await bcrypt.compare(password, this.password);
  } catch (error) {
    console.error('密码验证错误:', error);
    return false;
  }
};

// 检查用户是否是VIP
User.prototype.isVip = function() {
  // 管理员默认拥有VIP权限
  if (this.role === 'admin') {
    return true;
  }

  // 不是VIP角色
  if (this.role !== 'vip') {
    return false;
  }

  // 没有设置过期时间
  if (!this.vip_expire_date) {
    return false;
  }

  // 检查是否过期
  return new Date(this.vip_expire_date) > new Date();
};

// 检查用户是否是管理员
User.prototype.isAdmin = function() {
  return this.role === 'admin';
};

/**
 * 检查并更新VIP状态
 * 如果VIP已过期，将角色更改为普通用户
 * @returns {Promise<boolean>} 是否进行了更新
 */
User.prototype.checkAndUpdateVipStatus = async function() {
  // 如果不是VIP角色，则不需要检查
  if (this.role !== 'vip') {
    return false;
  }

  // 如果是VIP但没有设置过期时间或者未过期，则不需要更新
  if (!this.vip_expire_date || new Date(this.vip_expire_date) > new Date()) {
    return false;
  }

  // VIP已过期，更新角色为普通用户
  this.role = 'user';
  await this.save();

  return true;
};

/**
 * 检查用户是否被锁定
 * @returns {boolean} 用户是否被锁定
 */
User.prototype.isLocked = function() {
  // 如果状态是锁定，直接返回true
  if (this.status === 'locked') {
    return true;
  }

  // 如果有锁定到期时间，检查是否已过期
  if (this.lock_expires_at && new Date(this.lock_expires_at) > new Date()) {
    return true;
  }

  // 如果锁定已过期，自动解锁
  if (this.lock_expires_at && new Date(this.lock_expires_at) <= new Date()) {
    this.status = 'active';
    this.lock_expires_at = null;
    this.failed_login_attempts = 0;
    // 异步保存，不等待结果
    this.save().catch(err => console.error('自动解锁用户失败:', err));
    return false;
  }

  return false;
};

/**
 * 增加失败登录尝试次数
 * 如果达到限制（5次），锁定账号24小时
 * @returns {Promise<boolean>} 是否锁定了账号
 */
User.prototype.incrementFailedLoginAttempts = async function() {
  const now = new Date();
  const tenMinutesAgo = new Date(now.getTime() - 10 * 60 * 1000);

  // 如果最后失败登录时间超过10分钟，重置失败次数
  if (this.last_failed_login_time && new Date(this.last_failed_login_time) < tenMinutesAgo) {
    this.failed_login_attempts = 1;
  } else {
    this.failed_login_attempts += 1;
  }

  this.last_failed_login_time = now;

  // 如果失败次数达到5次，锁定账号24小时
  if (this.failed_login_attempts >= 5) {
    this.status = 'locked';
    this.lock_expires_at = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24小时后
    await this.save();
    return true;
  }

  await this.save();
  return false;
};

/**
 * 重置失败登录尝试次数
 * @returns {Promise<void>}
 */
User.prototype.resetFailedLoginAttempts = async function() {
  this.failed_login_attempts = 0;
  this.last_failed_login_time = null;
  await this.save();
};

/**
 * 锁定用户账号
 * @param {number} hours - 锁定时长（小时）
 * @returns {Promise<void>}
 */
User.prototype.lockAccount = async function(hours = 24) {
  this.status = 'locked';
  const now = new Date();
  this.lock_expires_at = new Date(now.getTime() + hours * 60 * 60 * 1000);
  await this.save();
};

/**
 * 解锁用户账号
 * @returns {Promise<void>}
 */
User.prototype.unlockAccount = async function() {
  this.status = 'active';
  this.lock_expires_at = null;
  this.failed_login_attempts = 0;
  this.last_failed_login_time = null;
  await this.save();
};

// 添加关联关系方法
User.associate = (models) => {
  // 用户有多个支付记录
  User.hasMany(models.PaymentRecord, {
    foreignKey: 'user_id',
    as: 'paymentRecords'
  });

  // 用户有多个封面记录
  User.hasMany(models.CoverRecord, {
    foreignKey: 'user_id',
    as: 'coverRecords'
  });

  // 用户有多个生成任务
  User.hasMany(models.GenerationTask, {
    foreignKey: 'user_id',
    as: 'generationTasks'
  });

  // 用户有多个退款记录
  User.hasMany(models.RefundRecord, {
    foreignKey: 'user_id',
    as: 'refundRecords'
  });

  // 用户有多个积分记录
  User.hasMany(models.PointRecord, {
    foreignKey: 'user_id',
    as: 'pointRecords'
  });
};

module.exports = User;

-- 修改支付记录表(payment_records)，添加新字段
ALTER TABLE `payment_records`
ADD COLUMN `payment_time` datetime DEFAULT NULL COMMENT '支付完成时间' AFTER `transaction_id`,
ADD COLUMN `notify_data` text COMMENT '支付回调原始数据' AFTER `payment_time`,
ADD COLUMN `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP地址' AFTER `notify_data`,
ADD COLUMN `payment_type_detail` varchar(20) DEFAULT NULL COMMENT '支付方式详情(Native/H5/PC/Mobile)' AFTER `payment_type`,
ADD COLUMN `refund_status` enum('none','partial','full') DEFAULT 'none' COMMENT '退款状态' AFTER `payment_status`;

-- 修改积分记录表(point_records)，添加新的操作类型
-- 注意：由于operation_type是枚举类型，需要修改枚举值
ALTER TABLE `point_records`
MODIFY COLUMN `operation_type` enum('register','daily_reward','generate','admin_adjust','vip_upgrade','recharge') NOT NULL COMMENT '积分变动类型'; 
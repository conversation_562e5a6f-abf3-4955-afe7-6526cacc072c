import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { useAuthContext } from '@/contexts/AuthContext';
import { CreditCard, QrCode, RefreshCcw, Clock, ArrowLeft, WalletCards, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import dayjs from 'dayjs';
import axios from 'axios';
import { message } from 'antd';
// 导入URL参数解密工具
import { decryptPaymentType, decryptPackageId, decryptOrderNo, encryptOrderNo } from '@/utils/urlEncryption';

/**
 * 支付内容组件 - 适用于路由/payment/:orderNo
 * 作为会员中心的子功能，处理会员套餐和积分充值的支付流程
 */
// 日志工具函数
// const logDebug = (message, ...args) => {
//   if (process.env.NODE_ENV !== 'production') {
//     console.log(`[Payment] ${message}`, ...args);
//   }
// };

const PaymentContent = () => {
  const { user: userInfo, refreshUserInfo } = useAuthContext();
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams(); // 获取路由参数
  
  // 从路由参数获取订单号
  const encryptedOrderNo = params.orderNo;
  
  // 解密订单号，添加错误处理和降级逻辑
  let orderNo = null;
  try {
    // 如果有加密的订单号，尝试解密
    if (encryptedOrderNo) {
      orderNo = decryptOrderNo(encryptedOrderNo);
      
      // 如果解密结果与原始值相同，可能是解密失败但没抛出错误
      if (orderNo === encryptedOrderNo && encryptedOrderNo.startsWith('s') && encryptedOrderNo.endsWith('e')) {
        // 尝试移除可能的前缀和后缀
        orderNo = encryptedOrderNo.slice(1, -1);
      }
      
      // 如果订单号格式不符合要求，尝试使用原始值
      if (!orderNo || orderNo === '') {
        orderNo = encryptedOrderNo;
      }
    }
  } catch (error) {
    // 解密失败时，尝试使用原始订单号作为降级处理
    orderNo = encryptedOrderNo;
  }
  
  // 状态管理
  const [packageInfo, setPackageInfo] = useState(null);
  const [orderInfo, setOrderInfo] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('wechat');
  const [isLoading, setIsLoading] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [remainingTime, setRemainingTime] = useState(900); // 15分钟支付倒计时
  const [paymentCheckInterval, setPaymentCheckInterval] = useState(null); // 保存支付状态检查定时器ID
  
  // 格式化日期时间
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
  };
  
  // 返回会员中心
  const handleBackToMembership = () => {
    navigate('/?view=membership');
  };
  
  // 验证用户和参数
  // 使用ref防止重复提示
  const loginMessageShownRef = React.useRef(false);
  const orderErrorShownRef = React.useRef(false);
  
  useEffect(() => {
    // 验证用户是否已登录
    if (!userInfo || !userInfo.id) {
      if (!loginMessageShownRef.current) {
        message.error('请先登录后再进行支付');
        loginMessageShownRef.current = true;
      }
      navigate('/auth', { replace: true });
      return;
    }
    
    // 验证URL参数
    if (orderNo) {
      // 如果有订单号，则获取订单信息
      const fetchOrderInfo = async () => {
        setIsLoading(true);
        try {
          // 移除订单号日志
          const response = await axios.get(`/api/payment/order/${orderNo}`);
          
          if (response.data && response.data.success) {
            // 移除成功日志
            const order = response.data.data.order;
            
            // 安全检查：确保订单属于当前用户
            if (order.user_id !== userInfo.id) {
              message.error('无权访问此订单');
              handleBackToMembership();
              return;
            }
            
            setOrderInfo(order);
            setPaymentMethod(order.payment_type || 'wechat');
            
            // 设置支付二维码URL
            if (response.data.data.paymentParams && response.data.data.paymentParams.qrCodeUrl) {
              setQrCodeUrl(response.data.data.paymentParams.qrCodeUrl);
              // 移除二维码URL日志
            } else {
              // 降级处理：使用通用二维码服务
              const fallbackUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${order.order_no}_${order.payment_type}`;
              setQrCodeUrl(fallbackUrl);
              // 移除降级二维码URL日志
            }
            
            // 启动支付状态检查
            startPaymentCheck(order.order_no);
            
            // 获取套餐信息
            if (order.product_type === 'vip') {
              // 会员套餐数据
              const memberPackages = [
                { id: 1, name: '月度会员', duration: 30, price: 19.9, discount_price: 19.9, description: '体验高级会员一个月' },
                { id: 2, name: '季度会员', duration: 90, price: 49.9, discount_price: 39.9, description: '连续三个月高级会员服务，比月度更优惠' },
                { id: 3, name: '年度会员', duration: 365, price: 199.9, discount_price: 149.9, description: '整年高级会员服务，最超值选择' }
              ];
              
              const pkg = memberPackages.find(p => p.id === Number(order.package_id));
              if (pkg) {
                setPackageInfo({
                  ...pkg,
                  type: 'vip',
                  typeName: '会员套餐'
                });
                // 移除套餐信息设置日志
              } else {
                // 完全移除警告，即使在开发环境
                // 创建默认套餐信息，避免显示"无法加载套餐信息"
                setPackageInfo({
                  id: order.package_id || 0,
                  name: '会员套餐',
                  duration: 30,
                  price: order.amount,
                  discount_price: order.amount,
                  description: '会员服务',
                  type: 'vip',
                  typeName: '会员套餐'
                });
              }
            } else if (order.product_type === 'points') {
              // 积分套餐数据
              const pointPackages = [
                { id: 1, name: '小额充值', points: 100, price: 10, bonus_points: 0, description: '充值100积分' },
                { id: 2, name: '标准充值', points: 500, price: 50, bonus_points: 50, description: '充值500积分，赠送50积分' },
                { id: 3, name: '大额充值', points: 1200, price: 100, bonus_points: 200, description: '充值1200积分，赠送200积分' }
              ];
              
              const pkg = pointPackages.find(p => p.id === Number(order.package_id));
              if (pkg) {
                setPackageInfo({
                  ...pkg,
                  type: 'points',
                  typeName: '积分充值'
                });
                // 移除套餐信息设置日志
              } else {
                // 完全移除警告，即使在开发环境
                // 创建默认套餐信息，避免显示"无法加载套餐信息"
                setPackageInfo({
                  id: order.package_id || 0,
                  name: '积分充值',
                  points: order.amount * 10,
                  price: order.amount,
                  bonus_points: 0,
                  description: '积分充值',
                  type: 'points',
                  typeName: '积分充值'
                });
              }
            }
          } else {
            // 移除API响应错误日志
            throw new Error(response.data?.message || '获取订单信息失败');
          }
        } catch (error) {
          // 移除获取订单信息失败的错误日志
          // 添加更详细的错误提示
          if (!orderErrorShownRef.current) {
            message.error(`获取订单信息失败，请重新选择套餐`);
            orderErrorShownRef.current = true;
          }
          // 短暂延迟后返回会员中心，让用户有时间看到错误信息
          setTimeout(() => handleBackToMembership(), 2000);
        } finally {
          setIsLoading(false);
        }
      };
    
      fetchOrderInfo();
    } else {
      // 订单号不存在，返回会员中心
      message.error('订单号无效，请重新选择套餐');
      handleBackToMembership();
    }
  }, [orderNo, navigate, userInfo]);
  
  // 处理支付方式变更
  const handlePaymentMethodChange = (value) => {
    // 更新支付方式状态
    setPaymentMethod(value);
          
    // 如果没有订单信息，直接返回
    if (!orderInfo) return;
    
    // 更新本地订单状态
    setOrderInfo({
      ...orderInfo,
      payment_type: value
    });
    
    // 只有非模拟支付且订单状态为pending时才调用API
    if (value !== 'mock' && orderInfo.payment_status === 'pending') {
      updatePaymentMethod(value);
    } else {
      // 更新二维码（仅本地状态）
      setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderInfo.order_no}_${value}`);
    }
  };
  
  // 更新支付方式
  const updatePaymentMethod = async (selectedPaymentMethod) => {
    if (!orderInfo) return;
    
    // 再次检查订单状态，确保只处理pending状态的订单
    if (orderInfo.payment_status !== 'pending') {
      // 如果订单不是pending状态，只更新二维码，状态已在handlePaymentMethodChange中更新
      setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderInfo.order_no}_${selectedPaymentMethod}`);
      return;
    }
    
    try {
      setIsCreatingOrder(true);
      
        const response = await axios.put(`/api/payment/order/${orderInfo.order_no}/payment-method`, {
        payment_type: selectedPaymentMethod
        });
        
        if (response.data && response.data.success) {
          const orderData = response.data.data;
          
          // 设置支付二维码URL
        if (orderData.payment_params && orderData.payment_params.qrCodeUrl) {
          setQrCodeUrl(orderData.payment_params.qrCodeUrl);
        } else {
        // 使用通用二维码服务
          setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderInfo.order_no}_${selectedPaymentMethod}`);
        }
      } else {
        throw new Error(response.data?.message || '更新支付方式失败');
      }
    } catch (error) {
      // 出错时确保二维码显示正确
      setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderInfo.order_no}_${selectedPaymentMethod}`);
      
      // 静默处理API错误，不影响用户体验
      if (error.response && error.response.status === 404) {
        // 订单不存在或状态已变更，静默处理
      } else {
        console.error('更新支付方式失败:', error);
        // 只在非404错误时显示消息提示
      message.error('更新支付方式失败，请稍后再试');
      }
    } finally {
      setIsCreatingOrder(false);
    }
  };
  
  // 检查支付状态
  const checkPaymentStatus = async () => {
    if (!orderInfo) return;
    
    try {
      // 使用loading消息，自动关闭
      const loadingMessage = message.loading('正在查询支付状态...', 1);
      
      try {
        // 调用真实的API查询订单状态
        const response = await axios.get(`/api/payment/order/${orderInfo.order_no}`);
        
        if (response.data && response.data.success) {
          const orderData = response.data.data.order;
          
          // 更新本地订单状态
          setOrderInfo({
            ...orderInfo,
            payment_status: orderData.payment_status,
            payment_time: orderData.payment_time
          });
          
          // 如果支付成功，跳转到支付结果页面
          if (orderData.payment_status === 'success') {
            handlePaymentSuccess(orderData);
          } else if (orderData.payment_status === 'pending') {
            message.info('订单待支付，请扫码完成支付', 2);
          } else {
            message.error('订单状态异常，请联系客服', 3);
          }
        } else {
          throw new Error('查询订单失败');
        }
      } catch (apiError) {
        console.error('查询订单失败:', apiError);
        message.error('查询订单失败，请稍后再试', 3);
      }
    } catch (error) {
      console.error('查询支付状态失败:', error);
      message.error('查询支付状态失败，请稍后再试', 3);
    }
  };
  
  // 启动支付状态自动检查
  const startPaymentCheck = (orderNo) => {
    if (!orderNo) return;
    
    // 先清除可能存在的旧定时器
    if (paymentCheckInterval) {
      clearInterval(paymentCheckInterval);
    }
    
    // 设置30秒自动查询一次
    const interval = setInterval(() => {
      // 如果订单已不存在或已离开页面，停止轮询
      if (!orderInfo || orderInfo.order_no !== orderNo) {
        clearInterval(interval);
        setPaymentCheckInterval(null);
        return;
      }
      
      // 如果订单已支付成功，停止轮询
      if (orderInfo.payment_status === 'success') {
        clearInterval(interval);
        setPaymentCheckInterval(null);
        return;
      }
      
      // 静默查询，不提示用户
      axios.get(`/api/payment/order/${orderNo}`)
        .then(response => {
          if (response.data && response.data.success) {
            const orderData = response.data.data.order;
            
            // 更新本地订单状态
            setOrderInfo(prevState => ({
              ...prevState,
              payment_status: orderData.payment_status,
              payment_time: orderData.payment_time
            }));
            
            // 如果支付状态变更为成功，跳转到结果页
            if (orderData.payment_status === 'success') {
              handlePaymentSuccess(orderData);
              clearInterval(interval);
              setPaymentCheckInterval(null);
            }
          }
        })
        .catch(() => {
          // 静默处理错误，不中断轮询
        });
    }, 30000);
    
    // 保存定时器ID
    setPaymentCheckInterval(interval);
  };
  
  // 支付成功后的处理
  const handlePaymentSuccess = (orderData) => {
    try {
      // 先刷新用户信息，确保权限更新（使用强制刷新）
      refreshUserInfo && refreshUserInfo(true).catch(() => {
        // 静默处理刷新错误
      });
      
      // 使用加密的订单号构建URL
      const encryptedOrderNo = encryptOrderNo(orderData.order_no);
      
      // 跳转到支付结果页面，使用路由格式
      navigate(`/payment-result/${encryptedOrderNo}`, { replace: true });
    } catch (error) {
      // 降级处理：使用未加密的订单号
      navigate(`/payment-result/${orderData.order_no}`, { replace: true });
    }
  };
  
  // 添加关闭订单的处理函数
  const handleOrderClose = async () => {
    if (!orderInfo) return;
    
    try {
      // 调用关闭订单API
      await axios.put(`/api/payment/orders/${orderInfo.id}/close`);
      // 成功关闭订单后显示提示
      message.warning('支付超时，请重新选择套餐');
      // 重置订单信息
      setOrderInfo(null);
      setQrCodeUrl('');
    } catch (error) {
      // 即使API调用失败，也清空前端状态并提示用户
      message.warning('支付超时，请重新选择套餐');
      setOrderInfo(null);
      setQrCodeUrl('');
    }
  };
  
  // 倒计时效果
  useEffect(() => {
    let timer;
    if (orderInfo && remainingTime > 0) {
      timer = setInterval(() => {
        setRemainingTime(prev => prev - 1);
      }, 1000);
    } else if (remainingTime === 0 && orderInfo) {
      // 调用关闭订单函数
      handleOrderClose();
    }
    
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [remainingTime, orderInfo]);
  
  // 订单状态变更时强制更新UI
  useEffect(() => {
    // 当订单信息变化时，如果有订单且没有二维码，尝试设置一个默认二维码
    if (orderInfo && !qrCodeUrl) {
      const defaultQrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderInfo.order_no}_${orderInfo.payment_type || 'wechat'}`;
      // 移除设置默认二维码日志
      setQrCodeUrl(defaultQrUrl);
    }
  }, [orderInfo]); // 只依赖orderInfo，避免因qrCodeUrl变化再次触发
  
  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (paymentCheckInterval) {
        clearInterval(paymentCheckInterval);
      }
    };
  }, [paymentCheckInterval]);
  
  // 格式化剩余时间
  const formatRemainingTime = () => {
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  
  // 处理模拟支付
  const handleMockPayment = async () => {
    if (!orderInfo) return;
    
    try {
      // 使用loading消息，自动关闭
      const loadingMessage = message.loading('正在处理支付...', 1);
      
      // 调用模拟支付API
      const response = await axios.post(`/api/payment/mock-pay/${orderInfo.order_no}`);
      
      if (response.data && response.data.success) {
        const orderData = response.data.data.order;
        
        // 更新本地订单状态，确保UI一致性
        setOrderInfo({
          ...orderInfo,
          payment_status: 'success',
          payment_time: orderData.payment_time || new Date().toISOString()
        });
        
        // 支付成功，跳转到支付结果页面
        handlePaymentSuccess(orderData);
      } else {
        throw new Error(response.data?.message || '支付失败');
      }
    } catch (error) {
      console.error('模拟支付失败:', error);
      message.error('支付失败，请稍后再试');
    }
  };
  
  // 显示加载状态
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
        <p className="text-lg text-muted-foreground">正在加载套餐信息...</p>
      </div>
    );
  }
  
  // 套餐信息不存在
  if (!packageInfo) {
    return (
      <div className="max-w-lg mx-auto py-8 px-4 text-center">
        <p className="text-lg text-red-500 mb-4">无法加载套餐信息</p>
        <Button onClick={handleBackToMembership}>返回会员中心</Button>
      </div>
    );
  }
  
  return (
    <div className="h-full flex flex-col max-w-xl mx-auto">
      {/* 标题区域 */}
      <div className="bg-muted p-4 flex items-center mb-4 rounded-md">
          <Button 
            variant="ghost" 
            size="icon"
            className="mr-2" 
            onClick={handleBackToMembership}
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
        <h1 className="text-xl font-bold">支付中心</h1>
        {orderInfo && (
          <div className="ml-auto text-sm text-muted-foreground">
            订单号：{orderInfo.order_no}
          </div>
        )}
      </div>
        
      {/* 内容区域 */}
      <div className="flex-1">
        {isLoading ? (
          // 加载中状态
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">加载中...</p>
            </div>
          </div>
        ) : (
          <div>
            {/* 套餐信息 */}
            {packageInfo && (
              <div className="mb-4">
                <Card>
                  <CardContent className="pt-4">
                    <h2 className="text-lg font-semibold mb-3">{packageInfo.typeName}详情</h2>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">名称</span>
                        <span>{packageInfo.name}</span>
                      </div>
                      
                      {packageInfo.type === 'vip' && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">有效期</span>
                          <span>{packageInfo.duration}天</span>
                        </div>
                      )}
                      
                      {packageInfo.type === 'points' && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">充值积分</span>
                            <span>{packageInfo.points} 积分</span>
                          </div>
                          
                          {packageInfo.bonus_points > 0 && (
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">赠送积分</span>
                              <span className="text-green-600">+{packageInfo.bonus_points} 积分</span>
                            </div>
                          )}
                        </>
                      )}
                      
                      <div className="flex justify-between items-center mt-4 pt-3 border-t">
                        <span className="text-base font-medium">支付金额</span>
                        <span className="text-2xl font-bold text-primary">¥{packageInfo.discount_price || packageInfo.price}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
            
            {/* 支付方式和二维码区域 */}
            {orderInfo ? (
              <div className="flex flex-col gap-4">
                {/* 支付方式选择 */}
                <Card>
                  <CardContent className="pt-4">
                    <h2 className="text-lg font-semibold mb-3">选择支付方式</h2>
                    
                    <RadioGroup 
                      value={paymentMethod} 
                      onValueChange={handlePaymentMethodChange}
                      className="grid grid-cols-1 gap-2"
                    >
                      <div className="flex items-center space-x-2 border rounded-md p-3 relative">
                        <RadioGroupItem value="wechat" id="wechat" />
                        <Label htmlFor="wechat" className="flex items-center cursor-pointer flex-1">
                          <QrCode className="h-5 w-5 text-green-600 mr-2" />
                          <span>微信支付</span>
                          {paymentMethod === 'wechat' && (
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 h-6 w-6 bg-primary/10 rounded-full flex items-center justify-center">
                              <div className="h-3 w-3 bg-primary rounded-full"></div>
                            </div>
                          )}
                        </Label>
                      </div>
                      
                      <div className="flex items-center space-x-2 border rounded-md p-3 relative">
                        <RadioGroupItem value="alipay" id="alipay" />
                        <Label htmlFor="alipay" className="flex items-center cursor-pointer flex-1">
                          <CreditCard className="h-5 w-5 text-blue-600 mr-2" />
                          <span>支付宝</span>
                          {paymentMethod === 'alipay' && (
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 h-6 w-6 bg-primary/10 rounded-full flex items-center justify-center">
                              <div className="h-3 w-3 bg-primary rounded-full"></div>
                            </div>
                          )}
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2 border rounded-md p-3 relative">
                        <RadioGroupItem value="mock" id="mock" />
                        <Label htmlFor="mock" className="flex items-center cursor-pointer flex-1">
                          <Check className="h-5 w-5 text-purple-600 mr-2" />
                          <span>模拟支付</span>
                          {paymentMethod === 'mock' && (
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 h-6 w-6 bg-primary/10 rounded-full flex items-center justify-center">
                              <div className="h-3 w-3 bg-primary rounded-full"></div>
                            </div>
                          )}
                        </Label>
                      </div>
                    </RadioGroup>
                  </CardContent>
                </Card>
          
                {/* 订单信息和支付二维码 */}
                <Card>
                  <CardContent className="pt-4 text-center">
                    <h2 className="text-lg font-semibold mb-3">
                      {paymentMethod === 'mock' ? '确认支付' : '扫码支付'}
                    </h2>
                    <p className="text-sm text-muted-foreground mb-3">订单号: {orderInfo.order_no}</p>
                    
                    {paymentMethod === 'mock' ? (
                      <div className="flex flex-col items-center py-8">
                        <Button 
                          onClick={handleMockPayment}
                          className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-6 text-lg"
                        >
                          <Check className="h-5 w-5 mr-2" />
                          确认支付 ¥{packageInfo.discount_price || packageInfo.price}
                        </Button>
                        <p className="text-sm text-muted-foreground mt-4">
                          点击按钮即可完成支付，无需真实付款
                        </p>
                      </div>
                    ) : qrCodeUrl ? (
                      <div className="flex flex-col items-center">
                        <img 
                          src={qrCodeUrl} 
                          alt="支付二维码" 
                          className="w-40 h-40 border p-2 rounded-md"
                          onError={(e) => {
                            // 尝试使用备用二维码
                            setQrCodeUrl(`https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${orderInfo.order_no}`);
                          }}
                        />
                        <div className="mt-4 flex items-center justify-center space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={checkPaymentStatus}
                            disabled={isLoading}
                          >
                            <RefreshCcw className="h-4 w-4 mr-1" />
                            刷新状态
                          </Button>
                        </div>
                        <div className="mt-3 flex items-center justify-center space-x-1 text-sm text-muted-foreground">
                          <Clock className="h-4 w-4" />
                          <span>支付倒计时：{formatRemainingTime()}</span>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center h-40 border rounded-md">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
                        <p className="text-sm text-muted-foreground">正在加载二维码...</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            ) : (
              // 参数错误或未找到订单
              <div className="text-center py-12">
                <div className="inline-block p-3 rounded-full bg-red-100 mb-4">
                  <WalletCards className="h-8 w-8 text-red-500" />
                </div>
                <h2 className="text-xl font-semibold mb-2">未找到有效订单</h2>
                <p className="text-muted-foreground mb-6">该订单可能已过期或不存在，请返回会员中心重新选择套餐</p>
                <Button onClick={handleBackToMembership}>返回会员中心</Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentContent; 
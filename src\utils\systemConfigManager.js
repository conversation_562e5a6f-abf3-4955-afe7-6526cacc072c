/**
 * 系统配置管理器
 * 用于前端获取和管理系统配置信息
 */

import logger from '../services/logs/frontendLogger.js';

/**
 * 系统配置管理器类
 */
export class SystemConfigManager {
  constructor() {
    this.configCache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存过期时间
  }

  /**
   * 获取渲染方式配置
   * @returns {Promise<Object>} 渲染方式配置
   */
  async getRenderingModeConfig() {
    const cacheKey = 'rendering_mode_config';
    
    // 检查缓存
    if (this.configCache.has(cacheKey)) {
      const cached = this.configCache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      }
    }

    try {
      const response = await fetch('/api/system/rendering-mode-config', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // 更新缓存
        this.configCache.set(cacheKey, {
          data: result.data,
          timestamp: Date.now()
        });
        
        return result.data;
      } else {
        throw new Error(result.message || '获取渲染方式配置失败');
      }
    } catch (error) {
      logger.error('获取渲染方式配置失败:', error);
      
      // 返回默认配置
      return {
        defaultRenderingMode: 'SERVER_SIDE_RENDERING',
        enableAdvancedMode: true,
        fallbackMode: 'CLIENT_SIDE_SAFE_LOADING'
      };
    }
  }

  /**
   * 设置渲染方式配置
   * @param {Object} config 渲染方式配置
   * @returns {Promise<boolean>} 是否设置成功
   */
  async setRenderingModeConfig(config) {
    try {
      const response = await fetch('/api/admin/config/rendering-mode', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // 清除缓存
        this.configCache.delete('rendering_mode_config');
        logger.info('渲染方式配置更新成功');
        return true;
      } else {
        throw new Error(result.message || '设置渲染方式配置失败');
      }
    } catch (error) {
      logger.error('设置渲染方式配置失败:', error);
      return false;
    }
  }

  /**
   * 获取安全检测配置
   * @returns {Promise<Object>} 安全检测配置
   */
  async getSecurityConfig() {
    const cacheKey = 'security_config';
    
    // 检查缓存
    if (this.configCache.has(cacheKey)) {
      const cached = this.configCache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      }
    }

    try {
      // 使用公共API而不是admin API，避免权限问题
      const response = await fetch('/api/system/security-rules', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // 更新缓存
        this.configCache.set(cacheKey, {
          data: result.data,
          timestamp: Date.now()
        });
        
        logger.info('获取安全检测配置成功', { source: 'public_api' });
        return result.data;
      } else {
        throw new Error(result.message || '获取安全检测配置失败');
      }
    } catch (error) {
      logger.error('获取安全检测配置失败:', error);
      
      // 返回默认配置作为fallback
      const defaultConfig = {
        enableXssDetection: true,
        enableMaliciousScriptDetection: true,
        enableDangerousTagDetection: true,
        enableExternalResourceValidation: true,
        riskThreshold: 'MEDIUM',
        autoBlock: false
      };
      
      logger.info('使用默认安全检测配置', defaultConfig);
      return defaultConfig;
    }
  }

  /**
   * 更新安全检测规则
   * @param {Object} rules 安全检测规则配置
   * @returns {Promise<boolean>} 是否更新成功
   */
  async updateSecurityRules(rules) {
    try {
      const response = await fetch('/api/admin/config/security-rules', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(rules)
      });

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // 清除缓存
        this.configCache.delete('security_config');
        logger.info('安全检测规则更新成功');
        return true;
      } else {
        throw new Error(result.message || '更新安全检测规则失败');
      }
    } catch (error) {
      logger.error('更新安全检测规则失败:', error);
      return false;
    }
  }

  /**
   * 获取当前有效的渲染方式
   * @param {string} htmlContent HTML内容
   * @param {Object} securityResult 安全检测结果
   * @returns {Promise<string>} 渲染方式
   */
  async getEffectiveRenderingMode(htmlContent, securityResult) {
    try {
      const config = await this.getRenderingModeConfig();
      
      // 如果安全检测未通过，需要用户确认
      if (!securityResult.passed) {
        return 'USER_CONFIRMATION_REQUIRED';
      }
      
      // 根据配置返回默认渲染方式
      return config.defaultRenderingMode || 'SERVER_SIDE_RENDERING';
    } catch (error) {
      logger.error('获取有效渲染方式失败:', error);
      return 'CLIENT_SIDE_SAFE_LOADING'; // 安全的回退方式
    }
  }

  /**
   * 清除配置缓存
   */
  clearCache() {
    this.configCache.clear();
    logger.info('系统配置缓存已清除');
  }

  /**
   * 获取缓存状态
   * @returns {Object} 缓存状态信息
   */
  getCacheStatus() {
    const status = {
      totalCached: this.configCache.size,
      items: []
    };

    for (const [key, value] of this.configCache.entries()) {
      const age = Date.now() - value.timestamp;
      const expired = age >= this.cacheExpiry;
      
      status.items.push({
        key,
        age: Math.round(age / 1000), // 秒
        expired
      });
    }

    return status;
  }
}

// 创建单例实例
const systemConfigManager = new SystemConfigManager();

export default systemConfigManager;

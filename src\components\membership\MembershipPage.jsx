import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '@/contexts/AuthContext';
import { Crown, Check, X, CreditCard, ArrowRight, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import axios from 'axios';
import { message } from 'antd';
// 导入URL参数加密工具
import { encryptPaymentType, encryptPackageId, encryptUrlParams, encryptOrderNo } from '@/utils/urlEncryption';

// 环境感知的日志工具函数
const logDebug = (message, ...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(message, ...args);
  }
};

/**
 * 会员服务页面组件
 * 
 * 展示会员权益对比、会员套餐选择和积分充值区域
 */
const MembershipPage = () => {
  const { user: userInfo, isLoggedIn, refreshUserInfo } = useAuthContext();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('membership');
  const [memberPackages, setMemberPackages] = useState([]);
  const [pointPackages, setPointPackages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  // 添加选中套餐状态
  const [selectedMemberPackageId, setSelectedMemberPackageId] = useState(2); // 默认选中季度会员
  const [selectedPointPackageId, setSelectedPointPackageId] = useState(2); // 默认选中标准充值

  // 获取会员套餐和积分套餐数据
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        setIsLoading(true);
        
        // 从API获取会员套餐和积分套餐数据
        // 获取会员套餐
        const memberResponse = await axios.get('/api/payment/membership/packages');
        if (memberResponse.data.success) {
          setMemberPackages(memberResponse.data.data.packages || []);
          // 如果有套餐数据，默认选中中间的套餐
          if (memberResponse.data.data.packages && memberResponse.data.data.packages.length > 0) {
            const middleIndex = Math.floor(memberResponse.data.data.packages.length / 2);
            setSelectedMemberPackageId(memberResponse.data.data.packages[middleIndex].id);
        }
        } else {
          // 如果API请求失败，使用默认数据
        setMemberPackages([
          { id: 1, name: '月度会员', duration: 30, price: 19.9, discount_price: 19.9, description: '体验高级会员一个月' },
          { id: 2, name: '季度会员', duration: 90, price: 49.9, discount_price: 39.9, description: '连续三个月高级会员服务，比月度更优惠' },
          { id: 3, name: '年度会员', duration: 365, price: 199.9, discount_price: 149.9, description: '整年高级会员服务，最超值选择' }
        ]);
        }
        
        // 获取积分套餐
        const pointResponse = await axios.get('/api/payment/points/packages');
        if (pointResponse.data.success) {
          setPointPackages(pointResponse.data.data.packages || []);
          // 如果有套餐数据，默认选中中间的套餐
          if (pointResponse.data.data.packages && pointResponse.data.data.packages.length > 0) {
            const middleIndex = Math.floor(pointResponse.data.data.packages.length / 2);
            setSelectedPointPackageId(pointResponse.data.data.packages[middleIndex].id);
          }
        } else {
          // 如果API请求失败，使用默认数据
        setPointPackages([
          { id: 1, name: '小额充值', points: 100, price: 10, bonus_points: 0, description: '充值100积分' },
          { id: 2, name: '标准充值', points: 500, price: 50, bonus_points: 50, description: '充值500积分，赠送50积分' },
          { id: 3, name: '大额充值', points: 1200, price: 100, bonus_points: 200, description: '充值1200积分，赠送200积分' }
        ]);
        }
      } catch (error) {
        // 捕获可能的错误，使用默认数据
        console.error('获取套餐信息失败:', error);
        
        // 使用默认数据
        setMemberPackages([
          { id: 1, name: '月度会员', duration: 30, price: 19.9, discount_price: 19.9, description: '体验高级会员一个月' },
          { id: 2, name: '季度会员', duration: 90, price: 49.9, discount_price: 39.9, description: '连续三个月高级会员服务，比月度更优惠' },
          { id: 3, name: '年度会员', duration: 365, price: 199.9, discount_price: 149.9, description: '整年高级会员服务，最超值选择' }
        ]);
        
        setPointPackages([
          { id: 1, name: '小额充值', points: 100, price: 10, bonus_points: 0, description: '充值100积分' },
          { id: 2, name: '标准充值', points: 500, price: 50, bonus_points: 50, description: '充值500积分，赠送50积分' },
          { id: 3, name: '大额充值', points: 1200, price: 100, bonus_points: 200, description: '充值1200积分，赠送200积分' }
        ]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchPackages();
  }, []);

  // 计算VIP剩余天数
  const getRemainingDays = () => {
    if (!userInfo?.vip_expire_date) return 0;
    
    const expireDate = new Date(userInfo.vip_expire_date);
    const today = new Date();
    const diffTime = expireDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 ? diffDays : 0;
  };

  // 处理会员套餐购买
  const handleBuyMembership = async (packageId) => {
    if (!isLoggedIn) {
      message.warning('请先登录后再购买会员');
      navigate('/auth');
      return;
    }
    
    try {
      // 设置加载状态
      setIsLoading(true);
      
      // 获取选中的套餐信息
      const selectedPackage = memberPackages.find(pkg => pkg.id === packageId);
      if (!selectedPackage) {
        message.error('未找到套餐信息');
        return;
      }
      
      // 创建订单
      const response = await axios.post('/api/payment/create-order', {
        product_type: 'vip',
        payment_type: 'wechat', // 默认为微信支付，用户可在支付页面修改
        package_id: packageId,
        amount: selectedPackage.discount_price || selectedPackage.price,
      });
      
      if (response.data && response.data.success) {
        const orderData = response.data.data;
        // 完全移除订单数据日志
        
        try {
          // 使用加密的订单号构建URL
          const encryptedOrderNo = encryptOrderNo(orderData.order_no);
          // 移除加密结果日志
          
          // 跳转到支付页面，使用新的路由格式
          navigate(`/payment/${encryptedOrderNo}`, { replace: true });
        } catch (encryptError) {
          logDebug('订单号加密失败:', encryptError);
          // 加密失败时使用原始订单号
          message.warning('订单处理异常，使用备用方式跳转');
          navigate(`/payment/${orderData.order_no}`, { replace: true });
        }
      } else {
        throw new Error(response.data?.message || '创建订单失败');
      }
    } catch (error) {
      logDebug('创建订单失败:', error);
      message.error('创建订单失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理积分充值
  const handleBuyPoints = async (packageId) => {
    if (!isLoggedIn) {
      message.warning('请先登录后再充值积分');
      navigate('/auth');
      return;
    }
    
    try {
      // 设置加载状态
      setIsLoading(true);
      
      // 获取选中的套餐信息
      const selectedPackage = pointPackages.find(pkg => pkg.id === packageId);
      if (!selectedPackage) {
        message.error('未找到套餐信息');
        return;
      }
      
      // 创建订单
      const response = await axios.post('/api/payment/create-order', {
        product_type: 'points',
        payment_type: 'wechat', // 默认为微信支付，用户可在支付页面修改
        package_id: packageId,
        amount: selectedPackage.price,
      });
      
      if (response.data && response.data.success) {
        const orderData = response.data.data;
        // 完全移除订单数据日志
        
        try {
          // 使用加密的订单号构建URL
          const encryptedOrderNo = encryptOrderNo(orderData.order_no);
          // 移除加密结果日志
          
          // 跳转到支付页面，使用新的路由格式
          navigate(`/payment/${encryptedOrderNo}`, { replace: true });
        } catch (encryptError) {
          logDebug('订单号加密失败:', encryptError);
          // 加密失败时使用原始订单号
          message.warning('订单处理异常，使用备用方式跳转');
          navigate(`/payment/${orderData.order_no}`, { replace: true });
        }
      } else {
        throw new Error(response.data?.message || '创建订单失败');
      }
    } catch (error) {
      logDebug('创建订单失败:', error);
      message.error('创建订单失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 会员权益对比数据
  const benefitsComparison = [
    { feature: '注册赠送积分', normal: '50点', vip: '50点' },
    { feature: '封面模板', normal: '任选', vip: '任选' },
    { feature: '预设风格', normal: '任选', vip: '任选' },
    { feature: 'AI提炼文案', normal: '支持', vip: '支持' },
    { feature: '封面生成', normal: '支持', vip: '支持' },
    { feature: '下载封面', normal: '支持', vip: '支持' },
    { feature: '自定义图片', normal: '不支持', vip: '支持' },
    { feature: '编辑封面', normal: '不支持', vip: '支持' },
    { feature: '下载HTML', normal: '不支持', vip: '支持' },
    { feature: '分享链接', normal: '不支持', vip: '支持' },
    { feature: '每日积分赠送', normal: '不支持', vip: '支持' }
  ];

  return (
    <div className="container mx-auto py-8 px-4">
      {/* 顶部区域 - 用户会员状态 */}
      <div className="mb-10 text-center">
        <div className="inline-block p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mb-4">
          <Crown size={32} className="text-white" />
        </div>
        <h1 className="text-3xl font-bold mb-2">会员服务中心</h1>
        {isLoggedIn ? (
          <div>
            {userInfo?.is_vip ? (
              <div className="text-lg">
                <span className="font-medium">您当前是 </span>
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-pink-500 font-bold">
                  高级会员
                </span>
                <span className="ml-2 text-sm bg-yellow-100 text-yellow-800 py-0.5 px-2 rounded-full">
                  剩余 {getRemainingDays()} 天
                </span>
              </div>
            ) : (
              <div className="text-gray-600">
                您当前是<span className="font-medium">普通会员</span>，开通高级会员获取更多特权
              </div>
            )}
            <div className="mt-2 text-sm text-gray-500">
              当前积分余额: <span className="font-medium text-primary">{userInfo?.points || 0}</span> 点
            </div>
          </div>
        ) : (
          <div className="text-gray-600">
            请<Button variant="link" className="p-0 h-auto" onClick={() => navigate('/auth')}>登录</Button>后查看您的会员状态
          </div>
        )}
      </div>

      {/* 选项卡切换区域 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 mb-8">
          <TabsTrigger value="membership">会员套餐</TabsTrigger>
          <TabsTrigger value="points">积分充值</TabsTrigger>
        </TabsList>

        {/* 会员套餐内容 */}
        <TabsContent value="membership">
          {/* 会员套餐选择 */}
          <div className="mb-10">
            <h2 className="text-2xl font-bold mb-6 text-center">选择会员套餐</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {memberPackages.map((pkg) => (
                <Card 
                  key={pkg.id} 
                  className={cn(
                    "flex flex-col cursor-pointer transition-all relative",
                    selectedMemberPackageId === pkg.id 
                      ? "border-primary border-2 shadow-lg" 
                      : "border hover:border-primary hover:shadow-md"
                  )}
                  onClick={() => setSelectedMemberPackageId(pkg.id)}
                  role="radio"
                  aria-checked={selectedMemberPackageId === pkg.id}
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      setSelectedMemberPackageId(pkg.id);
                      e.preventDefault();
                    }
                  }}
                >
                  {/* 添加选中标记 */}
                  {selectedMemberPackageId === pkg.id && (
                    <div className="absolute top-2 right-2 bg-primary text-white p-1 rounded-full">
                      <Check className="h-4 w-4" />
                    </div>
                  )}
                  
                  <CardHeader>
                    <CardTitle>{pkg.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex-grow">
                    <div className="flex items-baseline mb-4">
                      <span className="text-3xl font-bold">¥{pkg.discount_price}</span>
                        <span className="ml-2 text-sm text-muted-foreground line-through">¥{pkg.price}</span>
                    </div>
                    <div className="text-sm text-muted-foreground mb-3">
                      有效期: {pkg.duration} 天
                    </div>
                    <div className="text-sm text-gray-500 mb-2">
                      {pkg.description}
                    </div>
                    {pkg.name === '季度会员' && (
                      <div className="mt-2 inline-block bg-primary/10 text-primary text-xs px-2 py-1 rounded-full">
                        推荐套餐
                      </div>
                    )}
                  </CardContent>
                  <CardFooter>
                    {selectedMemberPackageId === pkg.id ? (
                    <Button 
                        className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                        onClick={(e) => {
                          e.stopPropagation(); // 防止触发卡片的点击事件
                          handleBuyMembership(pkg.id);
                        }}
                    >
                      立即开通
                    </Button>
                    ) : (
                      <div className="w-full h-10"></div> // 占位，保持卡片高度一致
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>

          {/* 会员权益对比表 */}
          <div>
            <h2 className="text-2xl font-bold mb-6 text-center">会员权益对比</h2>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-muted">
                    <th className="p-3 text-left font-medium">功能特权</th>
                    <th className="p-3 text-center font-medium">普通会员</th>
                    <th className="p-3 text-center font-medium bg-purple-50">高级会员</th>
                  </tr>
                </thead>
                <tbody>
                  {benefitsComparison.map((item, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="p-3 border-t">{item.feature}</td>
                      <td className="p-3 border-t text-center">
                        {item.normal === '支持' ? (
                          <Check size={18} className="mx-auto text-green-500" />
                        ) : item.normal === '不支持' ? (
                          <X size={18} className="mx-auto text-red-500" />
                        ) : (
                          item.normal
                        )}
                      </td>
                      <td className="p-3 border-t text-center bg-purple-50">
                        {item.vip === '支持' ? (
                          <Check size={18} className="mx-auto text-green-500" />
                        ) : item.vip === '不支持' ? (
                          <X size={18} className="mx-auto text-red-500" />
                        ) : (
                          <span className="font-medium">{item.vip}</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>

        {/* 积分充值内容 */}
        <TabsContent value="points">
          <div className="mb-10">
            <h2 className="text-2xl font-bold mb-6 text-center">积分充值</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {pointPackages.map((pkg) => (
                <Card 
                  key={pkg.id} 
                  className={cn(
                    "flex flex-col cursor-pointer transition-all relative",
                    selectedPointPackageId === pkg.id 
                      ? "border-primary border-2 shadow-lg" 
                      : "border hover:border-primary hover:shadow-md"
                  )}
                  onClick={() => setSelectedPointPackageId(pkg.id)}
                  role="radio"
                  aria-checked={selectedPointPackageId === pkg.id}
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      setSelectedPointPackageId(pkg.id);
                      e.preventDefault();
                    }
                  }}
                >
                  {/* 添加选中标记 */}
                  {selectedPointPackageId === pkg.id && (
                    <div className="absolute top-2 right-2 bg-primary text-white p-1 rounded-full">
                      <Check className="h-4 w-4" />
                    </div>
                  )}
                  
                  <CardHeader>
                    <CardTitle>{pkg.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="flex-grow">
                    <div className="flex items-baseline mb-4">
                      <span className="text-3xl font-bold">¥{pkg.price}</span>
                    </div>
                    <div className="flex items-center text-sm text-muted-foreground mb-3">
                      <span className="mr-1">获得:</span>
                      <CreditCard className="h-4 w-4 mr-1 text-primary" />
                      <span className="font-medium">{pkg.points} 积分</span>
                    </div>
                    <div className="text-sm text-gray-500 mb-2">
                      {pkg.description}
                    </div>
                    {pkg.bonus_points > 0 && (
                      <div className="mt-2 text-xs text-green-600 font-medium">
                        赠送 {pkg.bonus_points} 积分
                      </div>
                    )}
                    {pkg.name === '标准充值' && (
                      <div className="mt-2 inline-block bg-primary/10 text-primary text-xs px-2 py-1 rounded-full">
                        最受欢迎
                      </div>
                    )}
                  </CardContent>
                  <CardFooter>
                    {selectedPointPackageId === pkg.id ? (
                    <Button 
                        className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
                        onClick={(e) => {
                          e.stopPropagation(); // 防止触发卡片的点击事件
                          handleBuyPoints(pkg.id);
                        }}
                    >
                      立即充值
                    </Button>
                    ) : (
                      <div className="w-full h-10"></div> // 占位，保持卡片高度一致
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>
          
          <div className="mt-8 bg-amber-50 border border-amber-200 rounded-lg p-4">
            <h3 className="font-medium text-amber-800 mb-2 flex items-center">
              <Star className="h-4 w-4 mr-1 text-amber-500" />
              积分使用说明
            </h3>
            <ul className="space-y-1 text-sm text-amber-700 list-disc pl-5">
              <li>生成每个封面需消耗 10 积分</li>
              <li>普通会员每日自动赠送 0 积分，高级会员每日自动赠送 5 积分</li>
              <li>充值的积分永久有效，不会过期</li>
              <li>如有其他问题，请联系客服</li>
            </ul>
          </div>
        </TabsContent>
      </Tabs>
      
      {/* 会员特权说明区域 */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold mb-6 text-center">高级会员特权详解</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* 特权卡片1 */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">自定义图片上传</h3>
            <p className="text-gray-600">
              高级会员可以上传自己的图片作为封面背景，不再局限于系统提供的素材，让您的封面更加个性化。
            </p>
          </div>
          
          {/* 特权卡片2 */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">封面编辑功能</h3>
            <p className="text-gray-600">
              高级会员可以对生成的封面进行细节调整，包括字体、颜色、位置等，打造完美符合需求的专业封面。
            </p>
          </div>
          
          {/* 特权卡片3 */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">下载HTML</h3>
            <p className="text-gray-600">
              高级会员可以下载封面的HTML源代码，便于在自己的网站或博客中直接嵌入使用，实现更灵活的应用。
            </p>
          </div>
          
          {/* 特权卡片4 */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">分享链接</h3>
            <p className="text-gray-600">
              高级会员可以生成封面的专属分享链接，方便分享给他人查看，无需下载再发送，提升协作效率。
            </p>
          </div>
          
          {/* 特权卡片5 */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">每日积分赠送</h3>
            <p className="text-gray-600">
              高级会员每天自动赠送5点积分，长期使用更加划算，无需担心积分不足的问题。
            </p>
          </div>
          
          {/* 特权卡片6 */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">优先客服支持</h3>
            <p className="text-gray-600">
              高级会员享受优先客服支持服务，问题反馈和需求建议将获得更快速的响应和处理。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MembershipPage;
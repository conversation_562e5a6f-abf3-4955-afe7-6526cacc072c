1.请用中文和我对话
2.请不要在配置文件之外使用密码或者key等关键信息的明文
3.不要泄露我的配置文件
4.项目概览
    技术栈（可以根据项目具体技术栈进行修改，仅做案例）
        - Vue 3.3.11
        - TypeScript 5.3.3
        - Vite 5.0.10
        - Element Plus 2.4.4
        - Vue Router 4.2.5
        - Pinia 2.1.7
        - Axios 1.6.2
        - node 23.8.0
    项目使用 ESLint, Stylelint 和 Prettier 进行代码规范控制
5.代码风格和命名风格尽量和文件里别的代码保持统一
6.修改范围控制
    严格遵循指定的修改范围，其他建议性修改以咨询形式提出
7.上下文依赖
    基于已有上下文回答，遇到信息不足时主动请求补充
8.完整代码阅读(超过文件200行代码)
    完整阅读相关文件(包括HTML、JS、CSS等),确保理解完整上下文
6.回复规范
    - 回复语言与文件类型保持一致(TS/JS)
    - 使用中文回复
    - 标注代码引用位置
7.解答质量
    - 提供详尽的代码逻辑解释
    - 确保回答的完整性和准确性
    - 主动提出问题的不明确之处
8.性能考虑
    解释代码时关注性能影响，包括时间复杂度、内存使用等
9.最佳实践建议
    在解答时主动提供相关的编程最佳实践和设计模式建议
10.依赖关系
    说明代码与其他模块的依赖关系，包括外部库的版本兼容性
11.测试建议
    提供相关的测试建议，包括单元测试、集成测试的思路
12.文档引用
    需要时引用官方文档或可靠的技术文档，支持解答的准确性
13.代码规范
    注意项目中已有的代码规范(如ESLint规则、Prettier规则等),并在建议中遵循
14.向后兼容性
    在提供解决方案时考虑代码的向后兼容性，特别是在修改公共组件时
15.安全与质量保证 
    - 关注代码安全性(XSS防护、数据验证等)
    - 统一的错误处理机制 
    - 完善的调试和监控建议
16.用户体验保障 
    - 移动端适配与响应式设计 
    - Web可访问性(A11Y)标准遵循 
    - 国际化(i18n)支持考虑
17.代码质量规范 
    - 规范的代码注释要求 
    - 明确的组件/函数复用原则 
    - 统一的状态管理规范(Vuex/Pinia)
18.工程化考虑 
    - 构建性能和部署策略 
    - 不同环境兼容性(开发、测试、生产) 
    - 监控和调试便利性

import React, { createContext, useState, useEffect, useMemo } from 'react';

// 定义支持的主题类型
export const THEMES = {
  COURTYARD_PURPLE: 'courtyard-purple',
  MORNING_AZURE: 'morning-azure',
  VIBRANT_ORANGE: 'vibrant-orange',
};

const ThemeContext = createContext({
  theme: THEMES.COURTYARD_PURPLE, // 默认主题
  setTheme: () => {},
});

export const ThemeProvider = ({ children }) => {
  const [theme, setThemeState] = useState(() => {
    // 尝试从 localStorage 获取已保存的主题
    const savedTheme = localStorage.getItem('app-theme');
    // 校验保存的主题是否有效，无效则使用默认主题
    return Object.values(THEMES).includes(savedTheme) ? savedTheme : THEMES.COURTYARD_PURPLE;
  });

  useEffect(() => {
    // 当主题变化时，更新 localStorage
    localStorage.setItem('app-theme', theme);
    // 同时更新 HTML 根元素的 data-theme 属性，以便 CSS 生效
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  const setTheme = (newTheme) => {
    if (Object.values(THEMES).includes(newTheme)) {
      setThemeState(newTheme);
    } else {
      console.warn(`Attempted to set an invalid theme: ${newTheme}`);
    }
  };

  // 使用 useMemo 优化 context 的值，避免不必要的重渲染
  const contextValue = useMemo(() => ({ theme, setTheme }), [theme, setTheme]);

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext; 
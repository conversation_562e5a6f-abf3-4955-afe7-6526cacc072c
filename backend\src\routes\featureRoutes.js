const express = require('express');
const router = express.Router();
const featureController = require('../controllers/featureController');
const { auth } = require('../middlewares/authMiddleware');
const adminAuth = require('../middlewares/adminMiddleware');
const resetDailyPoints = require('../tasks/dailyPointsResetTask'); // 导入每日积分重置任务
const { User, PointRecord, FeatureControl } = require('../models'); // 导入User、PointRecord和FeatureControl模型
const logger = require('../utils/logger');
const { sequelize } = require('../config/database');
const { Op } = require('sequelize');

// 后台管理接口 - 必须放在前面，避免被通配参数路由捕获
router.get('/admin/list', auth, adminAuth, featureController.getFeaturesList);
router.get('/admin/detail/:id', auth, adminAuth, featureController.getFeatureDetail);
router.post('/admin/create', auth, adminAuth, featureController.createFeature);
router.put('/admin/update/:id', auth, adminAuth, featureController.updateFeature);
router.delete('/admin/delete/:id', auth, adminAuth, featureController.deleteFeature);

// 手动触发每日积分重置任务（仅用于测试）
router.post('/admin/trigger-daily-points-reset', auth, adminAuth, async (req, res) => {
  try {
    logger.info('手动触发每日积分重置任务');
    const success = await resetDailyPoints();
    if (success) {
      logger.info('手动触发每日积分重置任务成功');
      res.status(200).json({ message: '每日积分重置任务已手动触发并完成' });
    } else {
      logger.error('手动触发每日积分重置任务返回失败');
      res.status(500).json({ message: '任务执行失败' });
    }
  } catch (error) {
    logger.error(`手动触发每日积分重置任务出错: ${error.message}`, error);
    res.status(500).json({ message: '任务执行时出错', error: error.message });
  }
});

// 临时接口：直接更新用户每日积分（仅用于测试）
router.post('/admin/update-daily-points', auth, adminAuth, async (req, res) => {
  try {
    // 获取每日积分配置
    const normalPointsFeature = await FeatureControl.findOne({
      where: {
        feature_name: '每日积分（普通）',
        is_active: true
      }
    });
    
    const premiumPointsFeature = await FeatureControl.findOne({
      where: {
        feature_name: '每日积分（高级）',
        is_active: true
      }
    });
    
    // 获取配置的积分值，如果没有配置则使用默认值
    const normalPointsAmount = normalPointsFeature ? normalPointsFeature.points_cost : 0;
    const premiumPointsAmount = premiumPointsFeature ? premiumPointsFeature.points_cost : 0;
    
    logger.info(`从功能控制获取到的每日积分配置 - 普通用户: ${normalPointsAmount}, 高级用户: ${premiumPointsAmount}`);
    
    const transaction = await sequelize.transaction();
    
    try {
      const now = new Date();
      
      // 批量更新普通用户的每日积分
      const [normalUpdated] = await User.update(
        {
          daily_points: normalPointsAmount,
          last_daily_points_update: now
        },
        {
          where: {
            role: 'user'
          },
          transaction
        }
      );
      
      // 批量更新高级用户的每日积分
      const [premiumUpdated] = await User.update(
        {
          daily_points: premiumPointsAmount,
          last_daily_points_update: now
        },
        {
          where: {
            role: 'vip'
          },
          transaction
        }
      );
      
      // 获取所有用户ID和积分，用于创建积分记录
      const normalUsers = await User.findAll({
        attributes: ['id', 'points'],
        where: { role: 'user' },
        transaction
      });
      
      const premiumUsers = await User.findAll({
        attributes: ['id', 'points'],
        where: { role: 'vip' },
        transaction
      });
        
      // 准备批量创建的积分记录
      const pointRecords = [
        ...normalUsers.map(user => ({
          user_id: user.id,
          points_change: normalPointsAmount,
          points_after: user.points,
          operation_type: 'daily_points_normal',
          description: '每日积分（普通）手动更新',
          operation_time: now,
          created_at: now,
          updated_at: now
        })),
        ...premiumUsers.map(user => ({
          user_id: user.id,
          points_change: premiumPointsAmount,
          points_after: user.points,
          operation_type: 'daily_points_advanced',
          description: '每日积分（高级）手动更新',
          operation_time: now,
          created_at: now,
          updated_at: now
        }))
      ];
      
      // 批量创建积分记录
      if (pointRecords.length > 0) {
        await PointRecord.bulkCreate(pointRecords, { transaction });
      }
      
      await transaction.commit();
      
      logger.info(`批量更新完成 - 普通用户: ${normalUpdated}个, 高级用户: ${premiumUpdated}个`);
      
      res.status(200).json({ 
        message: '用户每日积分已手动更新', 
        updated: {
          normal: normalUpdated,
          premium: premiumUpdated,
          total: normalUpdated + premiumUpdated
        },
        normal_points: normalPointsAmount,
        premium_points: premiumPointsAmount
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    logger.error('手动更新用户每日积分失败:', error);
    res.status(500).json({ message: '更新失败', error: error.message });
  }
});

// 前台接口 - 放在后面，确保特定路径先被匹配
router.get('/', auth, featureController.getClientFeatures);
router.get('/:featureName/check', auth, featureController.checkFeatureAvailability);

module.exports = router; 
const { User, FeatureControl, PointRecord } = require('../models');
const logger = require('../utils/logger');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 处理角色配置，确保其为数组格式
 * @param {string|array} roles 角色配置
 * @returns {array} 格式化后的角色数组
 */
const formatRoles = (roles) => {
  if (typeof roles === 'string') {
    try {
      return JSON.parse(roles);
    } catch (e) {
      return [roles];
    }
  }
  
  if (!Array.isArray(roles)) {
    return [String(roles)];
  }
  
  return roles;
};

/**
 * 重置用户每日积分
 * 每日0点执行，将符合条件的用户每日积分重置为配置的值
 * @returns {Promise<boolean>} 任务执行结果
 */
const resetDailyPoints = async () => {
  logger.info('开始执行每日积分重置任务');
  
  const transaction = await sequelize.transaction();
  
  try {
    // 查询每日积分（普通）功能配置
    const normalPointsFeature = await FeatureControl.findOne({
      where: {
        feature_name: '每日积分（普通）',
        is_active: true
      }
    });
    
    // 查询每日积分（高级）功能配置
    const premiumPointsFeature = await FeatureControl.findOne({
      where: {
        feature_name: '每日积分（高级）',
        is_active: true
      }
    });
    
    // 记录找到的配置信息
    const normalPointsValue = normalPointsFeature ? normalPointsFeature.points_cost : 0;
    const premiumPointsValue = premiumPointsFeature ? premiumPointsFeature.points_cost : 0;
    logger.info(`每日积分配置 - 普通用户: ${normalPointsValue}, 高级用户: ${premiumPointsValue}`);
    
    // 处理普通用户每日积分
    if (normalPointsFeature) {
      const normalRoles = formatRoles(normalPointsFeature.user_roles);
      
      if (normalRoles.length > 0) {
        // 批量更新普通用户每日积分
        const [normalUpdated] = await User.update(
          {
            daily_points: normalPointsValue,
            last_daily_points_update: new Date()
          },
          {
            where: {
              role: {
                [Op.in]: normalRoles
              }
            },
            transaction
          }
        );
        
        // 获取更新后的普通用户列表，用于创建积分记录
        const normalUsers = await User.findAll({
          attributes: ['id', 'points'],
          where: {
            role: {
              [Op.in]: normalRoles
            }
          },
          transaction
        });
        
        // 准备批量创建的积分记录
        const normalPointRecords = normalUsers.map(user => ({
          user_id: user.id,
          points_change: normalPointsValue,
          points_after: user.points,
          operation_type: 'daily_points_normal',
          description: '每日积分（普通）重置',
          operation_time: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        }));
        
        // 批量创建积分记录
        if (normalPointRecords.length > 0) {
          await PointRecord.bulkCreate(normalPointRecords, { transaction });
        }
        
        logger.info(`已更新 ${normalUpdated} 个普通用户的每日积分为 ${normalPointsValue}`);
      }
    }
    
    // 处理高级用户每日积分
    if (premiumPointsFeature) {
      const premiumRoles = formatRoles(premiumPointsFeature.user_roles);
      
      if (premiumRoles.length > 0) {
        // 批量更新高级用户每日积分
        const [premiumUpdated] = await User.update(
          {
            daily_points: premiumPointsValue,
            last_daily_points_update: new Date()
          },
          {
            where: {
              role: {
                [Op.in]: premiumRoles
              }
            },
            transaction
          }
        );
        
        // 获取更新后的高级用户列表，用于创建积分记录
        const premiumUsers = await User.findAll({
          attributes: ['id', 'points'],
          where: {
            role: {
              [Op.in]: premiumRoles
            }
          },
          transaction
        });
        
        // 准备批量创建的积分记录
        const premiumPointRecords = premiumUsers.map(user => ({
          user_id: user.id,
          points_change: premiumPointsValue,
          points_after: user.points,
          operation_type: 'daily_points_advanced',
          description: '每日积分（高级）重置',
          operation_time: new Date(),
          created_at: new Date(),
          updated_at: new Date()
        }));
        
        // 批量创建积分记录
        if (premiumPointRecords.length > 0) {
          await PointRecord.bulkCreate(premiumPointRecords, { transaction });
        }
        
        logger.info(`已更新 ${premiumUpdated} 个高级用户的每日积分为 ${premiumPointsValue}`);
      }
    }
    
    await transaction.commit();
    logger.info('每日积分重置任务执行完成');
    return true;
  } catch (error) {
    await transaction.rollback();
    logger.error(`每日积分重置任务执行失败: ${error.message}`);
    return false;
  }
};

module.exports = resetDailyPoints;
-- 创建生成任务记录表 generation_tasks
CREATE TABLE IF NOT EXISTS `generation_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` varchar(64) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `status` enum('pending','processing','completed','failed','canceled') NOT NULL DEFAULT 'pending',
  `task_type` varchar(50) NOT NULL DEFAULT 'cover',
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `duration_ms` int(11) DEFAULT NULL,
  `parameters` text DEFAULT NULL,
  `result_id` int(11) DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `client_info` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_task_id` (`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_task_type` (`task_type`),
  CONSTRAINT `fk_generation_tasks_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

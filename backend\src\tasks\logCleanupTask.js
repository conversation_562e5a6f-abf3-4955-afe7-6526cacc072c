const LogService = require('../services/logService');
const logger = require('../utils/logger');
const SystemConfig = require('../models/SystemConfig'); // 用于获取配置

/**
 * 定时清理系统日志任务
 */
async function cleanupSystemLogs() {
  logger.info('开始执行定时日志清理任务...');
  try {
    // 从数据库或环境变量获取清理配置，提供默认值
    let infoKeepDays = 7;  // 减少为7天，更符合商业网站需求
    let warningKeepDays = 30;
    let errorKeepDays = 90; // 增加为90天，保留更长时间的错误日志
    let maxLogs = 10000;

    try {
      const infoDaysConfig = await SystemConfig.findOne({ where: { config_key: 'log_info_keep_days' } });
      if (infoDaysConfig) infoKeepDays = parseInt(infoDaysConfig.config_value, 10);

      const warningDaysConfig = await SystemConfig.findOne({ where: { config_key: 'log_warning_keep_days' } });
      if (warningDaysConfig) warningKeepDays = parseInt(warningDaysConfig.config_value, 10);

      const errorDaysConfig = await SystemConfig.findOne({ where: { config_key: 'log_error_keep_days' } });
      if (errorDaysConfig) errorKeepDays = parseInt(errorDaysConfig.config_value, 10);

      const maxLogsConfig = await SystemConfig.findOne({ where: { config_key: 'log_max_records' } });
      if (maxLogsConfig) maxLogs = parseInt(maxLogsConfig.config_value, 10);

    } catch (configError) {
      logger.warn('从数据库读取日志清理配置失败，将使用默认值:', configError.message);
      // 使用环境变量作为备选
      infoKeepDays = parseInt(process.env.LOG_INFO_KEEP_DAYS || '7', 10);
      warningKeepDays = parseInt(process.env.LOG_WARNING_KEEP_DAYS || '30', 10);
      errorKeepDays = parseInt(process.env.LOG_ERROR_KEEP_DAYS || '90', 10);
      maxLogs = parseInt(process.env.LOG_MAX_RECORDS || '10000', 10);
    }

    const options = {
      infoKeepDays: Math.max(1, infoKeepDays),
      warningKeepDays: Math.max(1, warningKeepDays),
      errorKeepDays: Math.max(1, errorKeepDays),
      maxLogs: Math.max(100, maxLogs)
    };

    logger.info('使用日志清理配置:', options);

    // 执行清理
    const result = await LogService.cleanupLogs(options);
    logger.info('日志清理任务完成:', result);

  } catch (error) {
    logger.error('执行日志清理任务失败:', error);
  }
}

module.exports = cleanupSystemLogs;